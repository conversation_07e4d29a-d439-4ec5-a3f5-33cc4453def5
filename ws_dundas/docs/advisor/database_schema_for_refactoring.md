# Database Schema Documentation for Advisor Profile Refactoring

This document maintains the database table structures relevant to the advisor profile refactoring project. It serves as the authoritative reference for all SQL queries and entity mappings.

## Table Relationships Overview

```
ADVISOR (ADVISOR_INT_ID) 
    ↓ (1:1)
CONTACT (CONTACT_INT_ID)
    ↓ (1:N)
COMPANY (CONTACT field references CONTACT.CONTACT_INT_ID)
```

## Core Tables

### 1. ADVISOR Table ⭐ **CORE TABLE**
```sql
create table SKYTEST.ADVISOR
(
    ADVISOR_INT_ID          NUMBER(8) not null primary key
        constraint ADVISOR_CONTACT references SKYTEST.CONTACT,
    ADVISOR_ID              NUMBER(8) not null,
    NAME                    VARCHAR2(256) not null,
    STATUS                  NUMBER(4),
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    SIN                     VARCHAR2(11),
    INSURFACT_CUSTOMER      VARCHAR2(1),
    ADVISOR_NUMBER          VARCHAR2(15),
    AIRMILES_NUM            VARCHAR2(15),
    AIRMILES_NAME_ON_CARD   VARCHAR2(70),
    SUN_TERMINATED          VARCHAR2(1),
    SUN_DEL_DATE            DATE,
    FIRST_CONTACT           DATE,
    BLKS_OLD                VARCHAR2(1),
    BLKS_OLD_DATE           DATE,
    BLKS_OLD_AGENT_ID       NUMBER(11),
    DRIVERS_LICENSE         VARCHAR2(30),
    SUN_TERM_DATE           DATE,
    TRANS_DATE              DATE,
    TRANS_AGENT_ID          NUMBER(11),
    SUNLIFE_BONUS           NUMBER(5,2),
    SUNLIFE_BONUS_DATE      DATE,
    SUN_VIP                 VARCHAR2(1),
    COPOADMIN               NUMBER(11),
    SALES_DIR_PCNT          NUMBER(5,2),
    SUNLIFE_ADVISOR         VARCHAR2(20),
    NOTE                    VARCHAR2(1024),
    CORPORATE_NAME          VARCHAR2(256),
    ADVISOR_TYPE            NUMBER(4),
    SALES_DIRECTOR          VARCHAR2(1),
    INVEST_PERCENT_SHARE    NUMBER(5,2),
    LAST_UPDATE_USER        VARCHAR2(64),
    MASTER_CODE             VARCHAR2(16),
    MASTER_GROUP            NUMBER(8) references SKYTEST.MASTER_GROUP,
    USER_HOME               VARCHAR2(256),
    TOTAL_STORAGE_GIGABYTES NUMBER(5,2),
    OLD_AGA_ID              NUMBER(8),
    OTHER_NAME              VARCHAR2(256),
    AGA_TYPE                NUMBER(4),
    EXCLUSIVE_ASSOCIATION   VARCHAR2(1),
    SHARE_ADDRESS           VARCHAR2(1),
    SHARE_LIABILITY         VARCHAR2(1),
    AGA_AGENCY_ID           NUMBER(8),
    SHARED_LIABILITY_ID     NUMBER(8),
    ONBOARDING              NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationship**: `ADVISOR.ADVISOR_INT_ID` = `CONTACT.CONTACT_INT_ID` (1:1)

**Critical Notes**:
- `NAME` is VARCHAR2(256), not VARCHAR2(128)
- `ADVISOR_NUMBER` is VARCHAR2(15), not VARCHAR2(64)
- `CORPORATE_NAME` is marked as 'to be deleted' in schema comments
- `AGA_TYPE`: AGA = 1, AGENCY = 2

### 2. CONTACT Table ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.CONTACT
(
    CONTACT_INT_ID           NUMBER(11) not null primary key,
    FIRSTNAME                VARCHAR2(35),
    MIDDLENAME               VARCHAR2(35),
    LASTNAME                 VARCHAR2(55),
    JOB_TITLE                VARCHAR2(100),
    JOB_ROLE                 VARCHAR2(100),
    DEPARTMENT               NUMBER(8),
    DO_NOT_CALL              CHAR(1),
    DEATH_DATE               DATE,
    ASSIGNED_TO              VARCHAR2(30),
    RETIREMENT_DATE          DATE,
    MARITAL_STATUS           NUMBER(8),
    BIRTH_PLACE              VARCHAR2(128),
    EMPLOYER                 VARCHAR2(128),
    CREATION_DATE            DATE,
    LAST_MODIFICATION_DATE   DATE,
    INCOME                   NUMBER(19,4),
    LENGTH_OF_EMPLOYMENT     NUMBER(8),
    BIRTH_DATE               DATE,
    FACEBOOK                 VARCHAR2(128),
    TWITTER                  VARCHAR2(128),
    SALUTATION               NUMBER,
    CUSTOM_FIELD1_NAME       VARCHAR2(128),
    CUSTOM_FIELD1_VALUE      VARCHAR2(128),
    MASTER_CODE              VARCHAR2(32),
    GENDER                   NUMBER(4),
    PREFERRED_LANGUAGE       NUMBER(4),
    CONTACT_TYPE             NUMBER(4),
    PUBLIC_NAME              VARCHAR2(128),
    CUSTOM_FIELD2_VALUE      VARCHAR2(128),
    CUSTOM_FIELD3_VALUE      VARCHAR2(128),
    CONTACT_SOURCE           NUMBER(4),
    ORGANIZATION             NUMBER(8) references SKYTEST.ORGANIZATION,
    WEBSITE                  VARCHAR2(128),
    CUSTOM_FIELD1_ID         NUMBER(4),
    CUSTOM_FIELD2_ID         NUMBER(4),
    CUSTOM_FIELD3_ID         NUMBER(4),
    CUSTOM_FIELD0_VALUE      VARCHAR2(128),
    AGENCY_CONTACT           VARCHAR2(1),
    IMPORT_FLAG              VARCHAR2(1),
    MAIDENNAME               VARCHAR2(35),
    CONTACT_LATER            VARCHAR2(1),
    FIRST_CONTACTED          DATE,
    AGENCY_PRIMARY_CONTACT   VARCHAR2(1),
    LAST_UPDATE_USER         VARCHAR2(64),
    ADVISOR_PROFILE          NUMBER(8) references SKYTEST.ADVISOR_PROFILE,
    SKILL_1                  NUMBER(4),
    SKILL_2                  NUMBER(4),
    SKILL_3                  NUMBER(4),
    SKILL_4                  NUMBER(4),
    SKILL_5                  NUMBER(4),
    SKILL_6                  NUMBER(4),
    SKILL_7                  NUMBER(4),
    SKILL_8                  NUMBER(4),
    FRENCH_ENGLISH_BILINGUAL VARCHAR2(1),
    ABOUT_ME_ENGLISH         VARCHAR2(1596),
    ABOUT_ME_FRENCH          VARCHAR2(1596),
    MARKET_PLACE             VARCHAR2(1),
    BRANCH                   NUMBER(8) references SKYTEST.BRANCH,
    TITLE                    NUMBER(4),
    OLD_CLIENT_ID            NUMBER(11),
    OLD_AGENT_ID             NUMBER(11),
    REFERRED_BY              VARCHAR2(128),
    TYPE                     NUMBER(4),
    HOUSEHOLD_TYPE           NUMBER(4) not null,
    EXCLUSIVE_ONLY           VARCHAR2(2),
    PROVIDER_TYPE            NUMBER(4),
    USER_CREATOR             NUMBER(8) references SKYTEST.USERS,
    ONBOARDING               NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationships**:
- Referenced by `ADVISOR.ADVISOR_INT_ID` (1:1 - same ID)
- Referenced by `COMPANY.CONTACT`
- Referenced by `CONTACT_ADDRESS.CONTACT`
- Referenced by `CONTACT_PHONE.CONTACT`
- Referenced by `CONTACT_EMAIL.CONTACT`

**Critical Notes**:
- `CONTACT_INT_ID` is NUMBER(11), not NUMBER(8)
- `FIRSTNAME` is VARCHAR2(35), not VARCHAR2(128)
- `LASTNAME` is VARCHAR2(55), not VARCHAR2(128)
- `INCOME` is NUMBER(19,4), not NUMBER(15,5)

### 3. COMPANY Table ⭐ **CRITICAL FOR THIS REFACTORING**
```sql
create table SKYTEST.COMPANY
(
    COMPANY_INT_ID         NUMBER(8) not null primary key,
    COMPANY_ID             NUMBER(8),
    NAME_EN                VARCHAR2(128),
    NAME_FR                VARCHAR2(128),
    CONTACT                NUMBER(8) references SKYTEST.CONTACT,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ACTIVE                 VARCHAR2(1),
    COMPANY_TYPE           NUMBER(4),
    ASSIGNABLE_COMMISSIONS VARCHAR2(1),
    PROV_BUSINESS_NUMBER   VARCHAR2(64),
    CONTRACT_EFT           NUMBER(9),
    BUSINESS_START_DATE    DATE,
    ADVISOR                NUMBER(8),
    AGENCY                 NUMBER(8),
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    FILE_NAME              VARCHAR2(256),
    PRIMARY_NAME           VARCHAR2(256),
    OTHER_NAME             VARCHAR2(256),
    BUILDING_TYPE          NUMBER(4),
    ONBOARDING             NUMBER(10)
)
```

**Key Relationship**: `COMPANY.CONTACT` = `CONTACT.CONTACT_INT_ID` (N:1)

**Comments from Schema**:
- `NAME_EN` is 'to be deleted'
- `NAME_FR` is 'to be deleted'  
- `CONTACT` is 'if is a building is the manager fk'

### 4. ADDRESS Table ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.ADDRESS
(
    ADDRESS_INT_ID         NUMBER(8) not null primary key,
    ADDRESS_LINE1          VARCHAR2(40) not null,
    ADDRESS_LINE2          VARCHAR2(40),
    ADDRESS_LINE3          VARCHAR2(40),
    CITY                   VARCHAR2(40),
    PROVINCE               VARCHAR2(5) not null references SKYTEST.PROVINCE,
    COUNTRY                VARCHAR2(3) not null references SKYTEST.COUNTRY,
    POSTAL_CODE            VARCHAR2(10) not null,
    IS_PRIMARY             VARCHAR2(1),
    TYPE                   NUMBER(8),
    CARE_OF                VARCHAR2(254),
    SEND_SOLICITATION      VARCHAR2(1),
    LATITUDE               VARCHAR2(64),
    LONGITUDE              VARCHAR2(64),
    OWNER_INT_ID           NUMBER(8),
    SHAREABLE              VARCHAR2(1),
    OWNER_TYPE             NUMBER(4),
    SEND_TYPE              NUMBER(4),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    LAST_UPDATE_USER       VARCHAR2(64),
    CARE_OF_OTHER_NAME     VARCHAR2(254),
    ONBOARDING             NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Critical Notes**:
- `ADDRESS_LINE1` is VARCHAR2(40), not VARCHAR2(254)
- `ADDRESS_LINE2` is VARCHAR2(40), not VARCHAR2(254)
- `ADDRESS_LINE3` is VARCHAR2(40), not VARCHAR2(254)
- `CITY` is VARCHAR2(40), not VARCHAR2(128)
- `POSTAL_CODE` is VARCHAR2(10), not VARCHAR2(16)
- `PROVINCE` is VARCHAR2(5), not VARCHAR2(2)
- `COUNTRY` is VARCHAR2(3), not VARCHAR2(2)

### 5. Relationship Tables

#### CONTACT_ADDRESS (Many-to-Many)
```sql
create table SKYTEST.CONTACT_ADDRESS
(
    CONTACT NUMBER(8) references SKYTEST.CONTACT,
    ADDRESS NUMBER(8) references SKYTEST.ADDRESS,
    primary key (CONTACT, ADDRESS)
)
```

#### CONTACT_PHONE (Many-to-Many)
```sql
create table SKYTEST.CONTACT_PHONE
(
    CONTACT NUMBER(8) references SKYTEST.CONTACT,
    PHONE   NUMBER(8) references SKYTEST.PHONE,
    primary key (CONTACT, PHONE)
)
```

#### CONTACT_EMAIL (Many-to-Many)
```sql
create table SKYTEST.CONTACT_EMAIL
(
    CONTACT NUMBER(8) references SKYTEST.CONTACT,
    EMAIL   NUMBER(8) references SKYTEST.EMAIL,
    primary key (CONTACT, EMAIL)
)
```

## Critical Findings for CompanyFacade Bug Fix

### Original CompanyFacade Bug Analysis
**File**: `sky_ejb/CompanyFacade.java`, lines 194-199

**Buggy Code**:
```java
public List<Company> companiesByContact(Integer contactId) {
    TypedQuery<Company> nq = em.createQuery(
        "SELECT a FROM Company a WHERE a.contact.contactIntId = :contactId", 
        Company.class);
    nq.setParameter("contactIntId", contactId);  // ❌ WRONG PARAMETER NAME
    return nq.getResultList();
}
```

**Problem**: 
- Query uses parameter `:contactId` 
- But `setParameter()` uses `"contactIntId"`
- This causes runtime `ParameterNotFoundException`

### Correct SQL Query for JDBC Template
Based on the actual schema, the correct query should be:

```sql
SELECT 
    c.COMPANY_INT_ID,
    c.COMPANY_ID,
    c.NAME_EN,
    c.NAME_FR,
    c.PRIMARY_NAME,
    c.OTHER_NAME,
    c.PROV_BUSINESS_NUMBER,
    c.COMPANY_TYPE,
    c.BUSINESS_START_DATE,
    c.ACTIVE,
    c.ASSIGNABLE_COMMISSIONS,
    c.BUILDING_TYPE,
    c.CREATION_DATE,
    c.LAST_MODIFICATION_DATE,
    c.CONTACT
FROM COMPANY c
WHERE c.CONTACT = ?
ORDER BY c.COMPANY_INT_ID
```

**Key Points**:
1. `COMPANY.CONTACT` field references `CONTACT.CONTACT_INT_ID`
2. Use `c.CONTACT = ?` not `c.CONTACT_INT_ID = ?`
3. Field names are `NAME_EN`, `NAME_FR` (not `FIRST_NAME`, `LAST_NAME`)

## Query Patterns for This Refactoring

### 1. Find Companies by Contact ID
```sql
SELECT c.* 
FROM COMPANY c 
WHERE c.CONTACT = ?
```

### 2. Find Companies with Contact Information
```sql
SELECT 
    c.*,
    ct.FIRSTNAME,
    ct.LASTNAME,
    ct.CONTACT_INT_ID
FROM COMPANY c
LEFT JOIN CONTACT ct ON c.CONTACT = ct.CONTACT_INT_ID
WHERE c.CONTACT = ?
```

### 3. Find Companies with Address Information
```sql
SELECT 
    c.*,
    ct.FIRSTNAME,
    ct.LASTNAME,
    a.ADDRESS_LINE1,
    a.ADDRESS_LINE2,
    a.CITY,
    a.POSTAL_CODE,
    a.IS_PRIMARY
FROM COMPANY c
LEFT JOIN CONTACT ct ON c.CONTACT = ct.CONTACT_INT_ID
LEFT JOIN CONTACT_ADDRESS ca ON ct.CONTACT_INT_ID = ca.CONTACT
LEFT JOIN ADDRESS a ON ca.ADDRESS = a.ADDRESS_INT_ID
WHERE c.CONTACT = ?
  AND (a.IS_PRIMARY = 'Y' OR a.ADDRESS_INT_ID IS NULL)
ORDER BY c.COMPANY_INT_ID, a.IS_PRIMARY DESC
```

## Entity Field Mappings

### Company Entity Expected Fields
Based on the schema and existing code analysis:

```java
// Primary fields (confirmed in schema)
company.setCompanyIntId(rs.getInt("COMPANY_INT_ID"));
company.setCompanyId(rs.getInt("COMPANY_ID"));
company.setNameEn(rs.getString("NAME_EN"));
company.setNameFr(rs.getString("NAME_FR"));
company.setPrimaryName(rs.getString("PRIMARY_NAME"));
company.setOtherName(rs.getString("OTHER_NAME"));
company.setProvBusinessNumber(rs.getString("PROV_BUSINESS_NUMBER"));
company.setCompanyType(rs.getInt("COMPANY_TYPE"));
company.setBusinessStartDate(rs.getDate("BUSINESS_START_DATE"));
company.setActive(rs.getString("ACTIVE"));
company.setAssignableCommissions(rs.getString("ASSIGNABLE_COMMISSIONS"));
company.setBuildingType(rs.getInt("BUILDING_TYPE"));
company.setCreationDate(rs.getTimestamp("CREATION_DATE"));
company.setLastModificationDate(rs.getTimestamp("LAST_MODIFICATION_DATE"));

// Relationship field
company.setContact(contactEntity); // Set Contact entity, not ID
```

### 5. PHONE Table ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.PHONE
(
    PHONE_INT_ID           NUMBER(8) not null primary key,
    PHONE_NUMBER           VARCHAR2(20),
    AREA_CODE              VARCHAR2(4),
    EXTENSION              VARCHAR2(16),
    IS_PRIMARY             VARCHAR2(1),
    TYPE                   NUMBER(8),
    OWNER_INT_ID           NUMBER(8),
    OWNER_TYPE             NUMBER(4),
    SHAREABLE              VARCHAR2(1),
    DO_NOT_CALL            VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    LAST_UPDATE_USER       VARCHAR2(64),
    ONBOARDING             NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Critical Notes**:
- `PHONE_NUMBER` is VARCHAR2(20), not VARCHAR2(32)
- `AREA_CODE` is VARCHAR2(4), not VARCHAR2(3)
- `EXTENSION` is VARCHAR2(16), not VARCHAR2(10)

### 6. EMAIL Table ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.EMAIL
(
    EMAIL_INT_ID           NUMBER(8) not null primary key,
    EMAIL_ADDRESS          VARCHAR2(100),
    IS_PRIMARY             VARCHAR2(1),
    TYPE                   NUMBER(8),
    SEND_SOLICITATION      VARCHAR2(1),
    OWNER_INT_ID           NUMBER(8),
    OWNER_TYPE             NUMBER(4),
    SHAREABLE              VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    LAST_UPDATE_USER       VARCHAR2(64),
    FLAG                   VARCHAR2(1),
    ONBOARDING             NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Critical Notes**:
- `EMAIL_ADDRESS` is VARCHAR2(100), not VARCHAR2(128)

### 7. USERS Table ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.USERS
(
    USER_INT_ID             NUMBER(8) not null primary key,
    USERNAME                VARCHAR2(128),
    PASSWORD                VARCHAR2(128),
    ACTIVE                  VARCHAR2(1),
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    LAST_LOGIN_DATE         DATE,
    LAST_PASSWORD_CHANGE    DATE,
    FAILED_LOGIN_ATTEMPTS   NUMBER(4),
    LOCKED_OUT              VARCHAR2(1),
    LOCKOUT_DATE            DATE,
    USER_TYPE               NUMBER(4),
    TESTING_MODE            VARCHAR2(1),
    USER_HOME               VARCHAR2(256),
    FIRST_TIME_LOGIN        VARCHAR2(1),
    MASTER_CODE             VARCHAR2(16),
    ADVISOR_LOCK            NUMBER(8),
    AGENCY_LOCK             NUMBER(8),
    EFFECTIVE_DATE          DATE,
    VALID_UNTIL             DATE,
    AUTH_METHOD             NUMBER(4),
    LAST_PASSWORD_RESET     DATE,
    -- Many other fields...
    ADVISOR_PROFILE         VARCHAR2(1),
    AGA_PROFILE             VARCHAR2(1),
    BRANCH_PROFILE          VARCHAR2(1),
    REGIONAL_PROFILE        VARCHAR2(1),
    HEAD_OFFICE_PROFILE     VARCHAR2(1)
)
```

**Key Relationship**: `USERS.USER_INT_ID` can equal `CONTACT.CONTACT_INT_ID` for advisor users

### 8. PROVINCE and COUNTRY Tables
```sql
create table SKYTEST.PROVINCE
(
    PROVINCE_CODE VARCHAR2(5) not null primary key,
    NAME_EN       VARCHAR2(128),
    NAME_FR       VARCHAR2(128),
    COUNTRY       VARCHAR2(3) references SKYTEST.COUNTRY
)

create table SKYTEST.COUNTRY
(
    COUNTRY_CODE    VARCHAR2(3) not null primary key,
    COUNTRY_NAME_EN VARCHAR2(128),
    COUNTRY_NAME_FR VARCHAR2(128)
)
```

## Tables Added in Version 3.0 (Based on AdvisorFacade Analysis)

### **Core Business Tables from AdvisorFacade**:

#### **LICENSE Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.LICENSE
(
    LICENSE_INT_ID         NUMBER(8)    not null primary key,
    LICENSE_NUMBER         VARCHAR2(25) not null,
    PROVINCE               VARCHAR2(2)  not null,
    START_DATE             DATE,
    END_DATE               DATE,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    AGENCY                 NUMBER(8) references SKYTEST.AGENCY,
    REQUEST_DATE           DATE,
    SENT_DATE              DATE,
    LICENSE_LIABILITY      NUMBER(8) references SKYTEST.LICENSE_LIABILITY,
    OLD_LICENSE_ID         NUMBER(8),
    LICENSE_DESC           VARCHAR2(30),
    CLIENT_NUMBER          NUMBER(15),
    ADVISOR                NUMBER(8) references SKYTEST.ADVISOR,
    COMPANY                NUMBER(8) references SKYTEST.COMPANY,
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    FILE_NAME              VARCHAR2(256),
    STATUS                 NUMBER(4),
    SELECTED               NUMBER(1),
    LICENSE_CATEGORY       NUMBER(8) references SKYTEST.LICENSE_CATEGORY,
    ONBOARDING             NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationships**:
- `LICENSE.ADVISOR` -> `ADVISOR.ADVISOR_INT_ID`
- `LICENSE.LICENSE_LIABILITY` -> `LICENSE_LIABILITY.LICENSE_LIABILITY_INT_ID`
- `LICENSE.COMPANY` -> `COMPANY.COMPANY_INT_ID`
- `LICENSE.AGENCY` -> `AGENCY.AGENCY_INT_ID`

#### **LICENSE_LIABILITY Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.LICENSE_LIABILITY
(
    LICENSE_LIABILITY_INT_ID NUMBER(8)    not null primary key,
    LIABILITY_NUMBER         VARCHAR2(25) not null,
    START_DATE               DATE,
    END_DATE                 DATE,
    CREATION_DATE            DATE,
    LAST_MODIFICATION_DATE   DATE,
    NOTE                     VARCHAR2(1024),
    REQUEST_DATE             DATE,
    SENT_DATE                DATE,
    OWNER_INT_ID             NUMBER(8),
    OWNER_TYPE               NUMBER(4),
    SHAREABLE                VARCHAR2(1),
    OLD_LIABILITY_ID         NUMBER(8),
    COMPANY_ID               NUMBER(8),
    PROVINCE                 VARCHAR2(2),
    INSURANCE_COMPANY        VARCHAR2(128),
    NAME                     VARCHAR2(256),
    DELETED                  VARCHAR2(1),
    DELETED_DATE             DATE,
    FILE_NAME                VARCHAR2(256),
    COMPANY                  NUMBER(8) references SKYTEST.COMPANY,
    STATUS                   NUMBER(4),
    ONBOARDING               NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationships**:
- Many-to-many with ADVISOR via `ADVISOR_LICENSE_LIABILITY` table
- `LICENSE_LIABILITY.COMPANY` -> `COMPANY.COMPANY_INT_ID`

#### **CONTRACT_SETUP Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.CONTRACT_SETUP
(
    CONTRACT_SETUP_INT_ID  NUMBER(8) not null primary key,
    PRODUCT_SUPPLIER       NUMBER(8) references SKYTEST.PRODUCT_SUPPLIER,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    CONTRACT_SETUP         NUMBER(8),
    AGENCY                 NUMBER(8) references SKYTEST.AGENCY,
    COMPANY                NUMBER(8) references SKYTEST.COMPANY,
    ADVISOR                NUMBER(8) references SKYTEST.ADVISOR,
    AGENCY_NAME            VARCHAR2(128),
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    SERVICING_AGENCY       NUMBER(8) references SKYTEST.AGENCY,
    AGA_ADVISOR            NUMBER(8) references SKYTEST.ADVISOR,
    MGA_PRODUCT_SUPPLIER   NUMBER(8) references SKYTEST.PRODUCT_SUPPLIER,
    AGA_PRODUCT_SUPPLIER   NUMBER(8) references SKYTEST.PRODUCT_SUPPLIER,
    AGA2_PRODUCT_SUPPLIER  NUMBER(8) references SKYTEST.PRODUCT_SUPPLIER,
    ONBOARDING             NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationships**:
- `CONTRACT_SETUP.ADVISOR` -> `ADVISOR.ADVISOR_INT_ID`
- `CONTRACT_SETUP.COMPANY` -> `COMPANY.COMPANY_INT_ID` (Owning Licensee)
- `CONTRACT_SETUP.AGENCY` -> `AGENCY.AGENCY_INT_ID` (Owning Agency)
- `CONTRACT_SETUP.AGA_ADVISOR` -> `ADVISOR.ADVISOR_INT_ID` (AGA for this Contract Setup)
- Multiple Product Supplier relationships for complex hierarchies

#### **CONTRACT_EFT Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.CONTRACT_EFT
(
    CONTRACT_EFT_INT_ID    NUMBER(8) not null primary key,
    SENT_DATE              DATE,
    BANK_HOLDER            VARCHAR2(75),
    BANK_ADDRESS           VARCHAR2(75),
    TRANSIT                VARCHAR2(15),
    ACCOUNT_NUMBER         VARCHAR2(15),
    COPO_EFT               VARCHAR2(1),
    STMTS_BY_EMAIL         VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    BANK                   VARCHAR2(3),
    EFT_INFO_ID            NUMBER(8),
    ADVISOR                NUMBER(8) references SKYTEST.ADVISOR,
    COMPANY                NUMBER(8) references SKYTEST.COMPANY,
    AGENCY                 NUMBER(8) references SKYTEST.AGENCY,
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    FILE_NAME              VARCHAR2(256),
    ONBOARDING             NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationships**:
- `CONTRACT_EFT.ADVISOR` -> `ADVISOR.ADVISOR_INT_ID`
- `CONTRACT_EFT.COMPANY` -> `COMPANY.COMPANY_INT_ID`
- `CONTRACT_EFT.AGENCY` -> `AGENCY.AGENCY_INT_ID`
- Referenced by `CONTRACT.CONTRACT_EFT` and `COMPANY.CONTRACT_EFT`

#### **CONTRACT Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
create table SKYTEST.CONTRACT
(
    CONTRACT_INT_ID         NUMBER(8) not null primary key references SKYTEST.CONTRACT_SETUP,
    CONTRACT_TYPE           NUMBER(4) not null,
    CONTRACT_NUMBER         VARCHAR2(25),
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    CONTRACT_STATUS         NUMBER(4),
    EFFECTIVE_DATE          DATE,
    INACTIVE_DATE           DATE,
    CONTRACT_EFT            NUMBER(8) references SKYTEST.CONTRACT_EFT,
    SALESREP_CODE           VARCHAR2(5),
    OLD_CONTRACT_ID         NUMBER(8),
    COMMISSION_PAY_TO       VARCHAR2(128),
    MFCODE                  VARCHAR2(20),
    DEALER_CODE             VARCHAR2(4),
    DISTRICT                VARCHAR2(10),
    IS_PRIMARY              CHAR,
    DIRECT_DEPOSIT          CHAR,
    EFT_FORM_MINCL          CHAR,
    EFT_SENT_DATE           DATE,
    HEALTH_CODE             VARCHAR2(20),
    TRANSFER_IN_PRICE       NUMBER(10, 2),
    TRANSFER_OUT_PRICE      NUMBER(10, 2),
    TRANSFER_FROM           NUMBER(6) references SKYTEST.PRODUCT_SUPPLIER,
    TRANSFER_TO             NUMBER(6) references SKYTEST.PRODUCT_SUPPLIER,
    TRANSFER_IN_DATE        DATE,
    TRANSFER_OUT_DATE       DATE,
    COMPANY_ASSOCIATION     NUMBER(8),
    TRANS_PAID_DATE         DATE,
    PAYSCALE                CHAR,
    -- Additional fields for custom fields, commission rates, etc.
    ONBOARDING              NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationships**:
- `CONTRACT.CONTRACT_INT_ID` -> `CONTRACT_SETUP.CONTRACT_SETUP_INT_ID` (1:1)
- `CONTRACT.CONTRACT_EFT` -> `CONTRACT_EFT.CONTRACT_EFT_INT_ID`
- `CONTRACT.TRANSFER_FROM/TO` -> `PRODUCT_SUPPLIER.PRODUCT_SUPPLIER_INT_ID`

#### **AGA_ADVISORS Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
-- Need to extract from sql_schema.txt
-- Used in: AdvisorFacade AGA relationship management
-- Relationships: Many-to-many between AGA (Advisor) and regular Advisors
```

#### **AGENCY Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
-- Need to extract from sql_schema.txt
-- Used in: AdvisorFacade agency-related queries
-- Relationships: AGENCY -> multiple advisors via AGENCY_ADVISOR
```

#### **AGENCY_ADVISOR Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
-- Need to extract from sql_schema.txt
-- Used in: AdvisorFacade agency-advisor relationship queries
-- Relationships: Many-to-many between AGENCY and ADVISOR
```

#### **PRODUCT_SUPPLIER Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
-- Need to extract from sql_schema.txt
-- Used extensively in: AdvisorFacade contract and product supplier queries
-- Relationships: Referenced by CONTRACT_SETUP
```

#### **PROFILE and PROFILE_USERS Tables** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
-- Need to extract from sql_schema.txt
-- Used in: AdvisorFacade tool and profile-based queries
-- Relationships: PROFILE_USERS links PROFILE to USERS (advisors)
```

#### **ADDRESS_BOOK Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
-- Need to extract from sql_schema.txt
-- Used in: AdvisorFacade address book functionality
-- Relationships: ADDRESS_BOOK -> CONTACT (advisor contacts)
```

#### **ACTIVITY Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
-- Need to extract from sql_schema.txt
-- Used in: AdvisorFacade activity tracking
-- Relationships: ACTIVITY -> ADVISOR (owner or contact)
```

#### **BRANCH Table** ⭐ **CRITICAL FOR ADVISOR PROFILE**
```sql
-- Need to extract from sql_schema.txt
-- Used in: AdvisorFacade branch-based filtering
-- Relationships: CONTACT -> BRANCH
```

---

**Document Version**: 5.0
**Last Updated**: 2025-01-01
**Purpose**: Advisor Profile Refactoring - Task 2.3 & 2.4 Contract and EFT Information Tab Implementation
**Next Update**: Continue with remaining tabs for Task 2.5+

## License Related Tables Added in Version 4.0

### **LICENSE Table** ⭐ **CRITICAL FOR TASK 2.2**
```sql
create table SKYTEST.LICENSE
(
    LICENSE_INT_ID         NUMBER(8)    not null primary key,
    LICENSE_NUMBER         VARCHAR2(25) not null,
    PROVINCE               VARCHAR2(2)  not null,
    START_DATE             DATE,
    END_DATE               DATE,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    AGENCY                 NUMBER(8) references SKYTEST.AGENCY,
    REQUEST_DATE           DATE,
    SENT_DATE              DATE,
    LICENSE_LIABILITY      NUMBER(8) references SKYTEST.LICENSE_LIABILITY,
    OLD_LICENSE_ID         NUMBER(8),
    LICENSE_DESC           VARCHAR2(30),
    CLIENT_NUMBER          NUMBER(15),
    ADVISOR                NUMBER(8) references SKYTEST.ADVISOR,
    COMPANY                NUMBER(8) references SKYTEST.COMPANY,
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    FILE_NAME              VARCHAR2(256),
    STATUS                 NUMBER(4),
    SELECTED               NUMBER(1),
    LICENSE_CATEGORY       NUMBER(8) references SKYTEST.LICENSE_CATEGORY,
    ONBOARDING             NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationships**:
- `LICENSE.ADVISOR` -> `ADVISOR.ADVISOR_INT_ID` (N:1)
- `LICENSE.COMPANY` -> `COMPANY.COMPANY_INT_ID` (N:1) 
- `LICENSE.LICENSE_LIABILITY` -> `LICENSE_LIABILITY.LICENSE_LIABILITY_INT_ID` (N:1)
- `LICENSE.AGENCY` -> `AGENCY.AGENCY_INT_ID` (N:1)
- `LICENSE.LICENSE_CATEGORY` -> `LICENSE_CATEGORY.LICENSE_CATEGORY_INT_ID` (N:1)

**Critical Notes**:
- `LICENSE_NUMBER` is VARCHAR2(25), not VARCHAR2(50)
- `PROVINCE` is VARCHAR2(2), matches PROVINCE.PROVINCE_CODE
- `LICENSE_DESC` is VARCHAR2(30) for description
- `STATUS` is NUMBER(4): 1=Active, others for different states
- `CLIENT_NUMBER` is NUMBER(15) for client identification

### **LICENSE_LIABILITY Table** ⭐ **CRITICAL FOR TASK 2.2**
```sql
create table SKYTEST.LICENSE_LIABILITY
(
    LICENSE_LIABILITY_INT_ID NUMBER(8)    not null primary key,
    LIABILITY_NUMBER         VARCHAR2(25) not null,
    START_DATE               DATE,
    END_DATE                 DATE,
    CREATION_DATE            DATE,
    LAST_MODIFICATION_DATE   DATE,
    NOTE                     VARCHAR2(1024),
    REQUEST_DATE             DATE,
    SENT_DATE                DATE,
    OWNER_INT_ID             NUMBER(8),
    OWNER_TYPE               NUMBER(4),
    SHAREABLE                VARCHAR2(1),
    OLD_LIABILITY_ID         NUMBER(8),
    COMPANY_ID               NUMBER(8),
    PROVINCE                 VARCHAR2(2),
    INSURANCE_COMPANY        VARCHAR2(128),
    NAME                     VARCHAR2(256),
    DELETED                  VARCHAR2(1),
    DELETED_DATE             DATE,
    FILE_NAME                VARCHAR2(256),
    COMPANY                  NUMBER(8) references SKYTEST.COMPANY,
    STATUS                   NUMBER(4),
    ONBOARDING               NUMBER(10) references SKYTEST.ONBOARDING_STATUS
)
```

**Key Relationships**:
- `LICENSE_LIABILITY.COMPANY` -> `COMPANY.COMPANY_INT_ID` (N:1)
- Many-to-many with ADVISOR via `ADVISOR_LICENSE_LIABILITY` table

**Critical Notes**:
- `LIABILITY_NUMBER` is VARCHAR2(25), not VARCHAR2(50)
- `NOTE` is VARCHAR2(1024) for detailed notes
- `INSURANCE_COMPANY` is VARCHAR2(128) for insurance company name
- `NAME` is VARCHAR2(256) for liability name/description
- `SHAREABLE` is VARCHAR2(1): Y/N flag

### **Relationship Tables**

#### ADVISOR_LICENSE_LIABILITY (Many-to-Many)
```sql
create table SKYTEST.ADVISOR_LICENSE_LIABILITY
(
    ADVISOR           NUMBER not null references SKYTEST.ADVISOR,
    LICENSE_LIABILITY NUMBER not null references SKYTEST.LICENSE_LIABILITY,
    primary key (ADVISOR, LICENSE_LIABILITY)
)
```

#### LICENSE_LIABILITY_COMPANY (Many-to-Many)
```sql
create table SKYTEST.LICENSE_LIABILITY_COMPANY
(
    LICENSE_LIABILITY NUMBER(8) not null references SKYTEST.LICENSE_LIABILITY,
    COMPANY           NUMBER(8) not null references SKYTEST.COMPANY,
    primary key (LICENSE_LIABILITY, COMPANY)
)
```

#### AGENCY_LICENSE_LIABILITY (Many-to-Many)
```sql
create table SKYTEST.AGENCY_LICENSE_LIABILITY
(
    AGENCY            NUMBER not null references SKYTEST.AGENCY,
    LICENSE_LIABILITY NUMBER not null references SKYTEST.LICENSE_LIABILITY,
    primary key (AGENCY, LICENSE_LIABILITY)
)
```

### **LICENSE_CATEGORY Table** ⭐ **REFERENCE TABLE**
```sql
create table SKYTEST.LICENSE_CATEGORY
(
    LICENSE_CATEGORY_INT_ID NUMBER(8)   not null primary key,
    LICENSE_CATEGORY_CODE   VARCHAR2(8),
    CATEGORY_DESC_EN        VARCHAR2(128),
    CATEGORY_DESC_FR        VARCHAR2(128),
    PROVINCE                VARCHAR2(2) not null,
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE
)
```

## Business Logic Patterns from LicenseFacade Analysis

### **Query Patterns Discovered**:
1. **License by Number**: `UPPER(l.licenseNumber) LIKE :number`
2. **License by Status**: `l.status = :status AND l.endDate IS NOT NULL`
3. **License by Province**: `l.province = :province`
4. **About to Expire**: `l.endDate >= CURRENT_DATE AND l.endDate <= :date`
5. **Expired Licenses**: `l.endDate <= CURRENT_DATE AND l.endDate >= :date`
6. **By Advisor**: `l.advisor IN :advisors`

### **Key Business Rules**:
1. **License Status**: 1 = Active, other values for different states
2. **Date Validations**: endDate used for expiry checks
3. **Province Filtering**: Common pattern for regulatory compliance
4. **Advisor Association**: Direct foreign key relationship
5. **Company Association**: Licensed entity relationship

### **LicenseLiabilityFacade Patterns**:
1. **By Liability Number**: Exact match queries
2. **Request Date Tracking**: Updated when requests are made
3. **Old ID Mapping**: Legacy system ID references

## Query Patterns for License Repository Implementation

### 1. Find Licenses by Advisor ID
```sql
SELECT l.* 
FROM LICENSE l 
WHERE l.ADVISOR = ? 
  AND l.DELETED != 'Y' OR l.DELETED IS NULL
ORDER BY l.END_DATE DESC, l.LICENSE_NUMBER
```

### 2. Find License Liabilities by Advisor ID (via relationship table)
```sql
SELECT ll.*
FROM LICENSE_LIABILITY ll
INNER JOIN ADVISOR_LICENSE_LIABILITY all ON ll.LICENSE_LIABILITY_INT_ID = all.LICENSE_LIABILITY
WHERE all.ADVISOR = ?
  AND (ll.DELETED != 'Y' OR ll.DELETED IS NULL)
ORDER BY ll.END_DATE DESC, ll.LIABILITY_NUMBER
```

### 3. Find Licenses with License Liability Details
```sql
SELECT 
    l.*,
    ll.LIABILITY_NUMBER,
    ll.NAME as LIABILITY_NAME,
    ll.INSURANCE_COMPANY,
    ll.START_DATE as LIABILITY_START_DATE,
    ll.END_DATE as LIABILITY_END_DATE,
    ll.STATUS as LIABILITY_STATUS
FROM LICENSE l
LEFT JOIN LICENSE_LIABILITY ll ON l.LICENSE_LIABILITY = ll.LICENSE_LIABILITY_INT_ID
WHERE l.ADVISOR = ?
  AND (l.DELETED != 'Y' OR l.DELETED IS NULL)
ORDER BY l.END_DATE DESC, l.LICENSE_NUMBER
```

### 4. Find License Categories
```sql
SELECT lc.*
FROM LICENSE_CATEGORY lc
WHERE lc.PROVINCE = ?
ORDER BY lc.CATEGORY_DESC_EN
```

## Entity Field Mappings for License

### License Entity Expected Fields
```java
// Primary fields
license.setLicenseIntId(rs.getInt("LICENSE_INT_ID"));
license.setLicenseNumber(rs.getString("LICENSE_NUMBER"));
license.setProvince(rs.getString("PROVINCE"));
license.setStartDate(rs.getDate("START_DATE"));
license.setEndDate(rs.getDate("END_DATE"));
license.setLicenseDesc(rs.getString("LICENSE_DESC"));
license.setClientNumber(rs.getLong("CLIENT_NUMBER"));
license.setStatus(rs.getInt("STATUS"));
license.setRequestDate(rs.getDate("REQUEST_DATE"));
license.setSentDate(rs.getDate("SENT_DATE"));
license.setDeleted(rs.getString("DELETED"));
license.setCreationDate(rs.getTimestamp("CREATION_DATE"));
license.setLastModificationDate(rs.getTimestamp("LAST_MODIFICATION_DATE"));

// Relationship fields
license.setAdvisor(advisorEntity);
license.setCompany(companyEntity);
license.setLicenseLiability(licenseliabilityEntity);
license.setAgency(agencyEntity);
```

### LicenseLiability Entity Expected Fields
```java
// Primary fields
liability.setLicenseLiabilityIntId(rs.getInt("LICENSE_LIABILITY_INT_ID"));
liability.setLiabilityNumber(rs.getString("LIABILITY_NUMBER"));
liability.setName(rs.getString("NAME"));
liability.setInsuranceCompany(rs.getString("INSURANCE_COMPANY"));
liability.setStartDate(rs.getDate("START_DATE"));
liability.setEndDate(rs.getDate("END_DATE"));
liability.setProvince(rs.getString("PROVINCE"));
liability.setNote(rs.getString("NOTE"));
liability.setStatus(rs.getInt("STATUS"));
liability.setShareable(rs.getString("SHAREABLE"));
liability.setDeleted(rs.getString("DELETED"));
liability.setCreationDate(rs.getTimestamp("CREATION_DATE"));
liability.setLastModificationDate(rs.getTimestamp("LAST_MODIFICATION_DATE"));

// Relationship fields
liability.setCompany(companyEntity);
```

---

## Tables Added in Version 2.0
- **PHONE**: Phone number information for contacts
- **EMAIL**: Email address information for contacts
- **USERS**: User account information linked to contacts
- **PROVINCE/COUNTRY**: Geographic reference tables

## Task 2.2 Implementation Summary ✅ **COMPLETED - 2025-01-01**

### **Implementation Overview**
Task 2.2 successfully implemented the License Information Tab functionality following the established refactoring workflow:

**Completed Components**:
1. **DTO Layer**: Created `LicenseDTO` and `LicenseLiabilityDTO` with complete field mapping
2. **Repository Layer**: Implemented `LicenseRepository` with complex SQL queries for license data
3. **Service Layer**: Extended `AdvisorService` with license enrichment functionality
4. **Mapper Layer**: Added `toLicenseDTO()` and `toLicenseLiabilityDTO()` methods to `AdvisorMapper`
5. **API Integration**: Extended `AdvisorProfileDTO` to include `licenses` field

**Key Technical Achievements**:
- **Single Query Optimization**: Complex SQL joins to fetch licenses, liabilities, companies, and agencies in one query
- **Defensive Programming**: All entity method calls wrapped in try-catch blocks for missing methods
- **Proper Relationships**: Correctly implemented LICENSE → LICENSE_LIABILITY → COMPANY relationships
- **Data Sorting**: Intelligent sorting by active status, end date, and license number
- **Error Handling**: Comprehensive error handling with fallback to empty lists

**SQL Query Pattern Used**:
```sql
SELECT l.*, ll.*, c.*, a.*
FROM LICENSE l
LEFT JOIN LICENSE_LIABILITY ll ON l.LICENSE_LIABILITY = ll.LICENSE_LIABILITY_INT_ID
LEFT JOIN COMPANY c ON l.COMPANY = c.COMPANY_INT_ID
LEFT JOIN AGENCY a ON l.AGENCY = a.AGENCY_INT_ID
WHERE l.ADVISOR = ? AND (l.DELETED != 'Y' OR l.DELETED IS NULL)
ORDER BY l.END_DATE DESC, l.LICENSE_NUMBER
```

**Business Logic Implemented**:
- Active licenses prioritized in sorting
- License liability information included for each license
- Company and agency names included for context
- Status descriptions computed from status codes
- Expiration checking with convenience methods

**Files Modified/Created**:
- `LicenseDTO.java` - New license data transfer object
- `LicenseLiabilityDTO.java` - New license liability data transfer object
- `LicenseRepository.java` - New repository for license data access
- `AdvisorService.java` - Extended with license enrichment
- `AdvisorMapper.java` - Added license mapping methods
- `AdvisorProfileDTO.java` - Added licenses field

## Key Findings from AdvisorFacade Analysis

### **Business Logic Patterns Discovered**:
1. **License Management**: Complex many-to-many relationships between advisors and licenses via LICENSE_LIABILITY
2. **Contract Management**: Multiple product supplier relationships (PRODUCT_SUPPLIER, MGA_PRODUCT_SUPPLIER, AGA_PRODUCT_SUPPLIER)
3. **Agency Relationships**: Hierarchical advisor relationships via AGA_ADVISORS and AGENCY_ADVISOR
4. **Profile-Based Access**: Tool and profile-based advisor filtering via PROFILE_USERS
5. **Activity Tracking**: Advisor activities tracked via ACTIVITY table

### **Critical Query Patterns**:
1. **Advisor-Contact Join**: `advisor a inner join contact c on c.contact_int_id = a.advisor_int_id`
2. **Contract-Product Supplier**: `CONTRACT_SETUP cs on adv.ADVISOR_INT_ID = cs.advisor`
3. **Address Lookup**: `CONTACT_ADDRESS ca on ca.CONTACT = c.contact_int_id inner join ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS`
4. **Agency Relationships**: `AGENCY_ADVISOR aa where aa.AGENCY = ? and aa.ADVISOR = a.ADVISOR_INT_ID`
5. **Profile Access**: `PROFILE_USERS pu on a.advisor_int_id = pu.USERS`

### **Schema Corrections from AdvisorFacade**:
- Confirmed: `ADVISOR.ADVISOR_INT_ID` = `CONTACT.CONTACT_INT_ID` (1:1 relationship)
- Confirmed: Multiple product supplier fields in CONTRACT_SETUP
- Confirmed: AGA_ADVISORS table for advisor hierarchies
- Confirmed: AGENCY_ADVISOR many-to-many relationship table

## Key Findings for AdvisorProfileRepository
1. **Field Size Corrections**: Many VARCHAR2 fields are smaller than initially assumed
2. **Data Type Corrections**: All ID fields are NUMBER(8) or NUMBER(11), not BIGINT
3. **Relationship Patterns**: All relationships use integer foreign keys, not composite keys
4. **Naming Conventions**: Field names are consistent (e.g., `CONTACT_INT_ID`, `ADDRESS_INT_ID`)

## Critical Schema Corrections Made
- `CONTACT.CONTACT_INT_ID`: NUMBER(11), not NUMBER(8)
- `CONTACT.FIRSTNAME`: VARCHAR2(35), not VARCHAR2(128)
- `CONTACT.LASTNAME`: VARCHAR2(55), not VARCHAR2(128)
- `ADDRESS.ADDRESS_LINE1/2/3`: VARCHAR2(40), not VARCHAR2(254)
- `ADDRESS.CITY`: VARCHAR2(40), not VARCHAR2(128)
- `ADDRESS.POSTAL_CODE`: VARCHAR2(10), not VARCHAR2(16)
- `PHONE.PHONE_NUMBER`: VARCHAR2(20), not VARCHAR2(32)
- `EMAIL.EMAIL_ADDRESS`: VARCHAR2(100), not VARCHAR2(128)

## File Storage Related Tables

This section details the tables involved in storing and managing files within the system. The primary mechanism involves storing metadata in `STORED_FILE` and actual file content as BLOBs in `STORED_FILE_DESC`.

### **STORED_FILE Table** ⭐ **CRITICAL FOR DOCUMENT CENTER**
```sql
-- Extracted from com.insurfact.skynet.entity.StoredFile.java
CREATE TABLE SKYTEST.STORED_FILE (
    STORED_FILE_INT_ID NUMBER(10) NOT NULL PRIMARY KEY, -- Sequence: STORED_FILE_INT_SEQ
    TYPE_ NUMBER(8), -- Indicates the type of entity this file is associated with (e.g., 11 for Advisor)
    TYPE_ID NUMBER(10), -- The ID of the associated entity (e.g., ADVISOR_INT_ID)
    CREATION_DATE DATE,
    NAME VARCHAR2(255), -- Original filename or display name
    EXTENSION VARCHAR2(10), -- File extension (e.g., "pdf", "docx")
    URL VARCHAR2(1024), -- Critical: May store full URL, relative path, or identifier for external storage. For new system, will store path/key to file system/S3.
    AVAILABLE_DATE DATE,
    UNAVAILABLE_DATE DATE,
    NOTE VARCHAR2(4000),
    FILE_TYPE NUMBER(8), -- Likely references TYPES table for more specific classification
    CREATED_BY NUMBER(8) REFERENCES SKYTEST.USERS(USER_INT_ID),
    STORED_FILE_DESC_INT_ID NUMBER(10) UNIQUE, -- FK to STORED_FILE_DESC, indicates BLOB storage if populated
    LAST_MODIFICATION_DATE DATE,
    MODIFIED_BY NUMBER(8) REFERENCES SKYTEST.USERS(USER_INT_ID)
    -- Other fields like version, checksum might exist or be desirable
);

-- Sequence for STORED_FILE_INT_ID
CREATE SEQUENCE SKYTEST.STORED_FILE_INT_SEQ START WITH 1 INCREMENT BY 1;

-- Example of how it's used in StoredFileFacade for Advisors:
-- SELECT * FROM STORED_FILE WHERE TYPE_ = 11 AND TYPE_ID = :advisorId;
```
**Key Relationships**:
- `STORED_FILE.TYPE_` and `STORED_FILE.TYPE_ID` create polymorphic associations to various entities (e.g., `ADVISOR`, `LICENSE`, `ACTIVITY`).
- `STORED_FILE.CREATED_BY` -> `USERS.USER_INT_ID`
- `STORED_FILE.MODIFIED_BY` -> `USERS.USER_INT_ID`
- `STORED_FILE.STORED_FILE_DESC_INT_ID` -> `STORED_FILE_DESC.STORED_FILE_INT_ID` (1:1, for BLOB content)
- ManyToMany with `ACTIVITY` via `ACTIVITY_ATTACHMENT` join table.

**Critical Notes**:
- The `URL` field is pivotal for the new storage strategy.
- `TYPE_ = 11` is used to link files to `ADVISOR` entities via `ADVISOR.ADVISOR_INT_ID`.

### **STORED_FILE_DESC Table** ⭐ **CRITICAL FOR DOCUMENT CENTER (Legacy BLOB Storage)**
```sql
-- Extracted from com.insurfact.skynet.entity.StoredFileDesc.java
CREATE TABLE SKYTEST.STORED_FILE_DESC (
    STORED_FILE_INT_ID NUMBER(10) NOT NULL PRIMARY KEY, -- Maps to STORED_FILE.STORED_FILE_DESC_INT_ID and STORED_FILE.STORED_FILE_INT_ID
    FILE_EN BLOB, -- Actual file content (English version)
    FILE_FR BLOB  -- Actual file content (French version)
);
```
**Key Relationships**:
- `STORED_FILE_DESC.STORED_FILE_INT_ID` is a foreign key to `STORED_FILE.STORED_FILE_DESC_INT_ID` and typically matches `STORED_FILE.STORED_FILE_INT_ID`.

**Critical Notes**:
- This table holds the actual file content if stored as BLOBs in the database.
- For the new refactored system, new files will be stored in a dedicated file system/S3, and this table will only be accessed for legacy files.

### **STORED_FILE_NOTE Table**
```sql
-- Extracted from com.insurfact.skynet.entity.StoredFileNote.java
CREATE TABLE SKYTEST.STORED_FILE_NOTE (
    STORED_FILE_NOTE_INT_ID NUMBER(10) NOT NULL PRIMARY KEY, -- Sequence: STORED_FILE_NOTE_INT_SEQ
    STORED_FILE_INT_ID NUMBER(10) NOT NULL REFERENCES SKYTEST.STORED_FILE(STORED_FILE_INT_ID),
    NOTE CLOB, -- Using CLOB for potentially long notes
    CREATION_DATE DATE,
    CREATED_BY NUMBER(8) REFERENCES SKYTEST.USERS(USER_INT_ID)
);

-- Sequence for STORED_FILE_NOTE_INT_ID
CREATE SEQUENCE SKYTEST.STORED_FILE_NOTE_INT_SEQ START WITH 1 INCREMENT BY 1;
```
**Key Relationships**:
- `STORED_FILE_NOTE.STORED_FILE_INT_ID` -> `STORED_FILE.STORED_FILE_INT_ID` (Many:1)
- `STORED_FILE_NOTE.CREATED_BY` -> `USERS.USER_INT_ID`

### **ACTIVITY_ATTACHMENT Table (Join Table)**
```sql
-- Deduced from ActivityFacade.java and StoredFile.java (attachmentsFiles field in Activity)
-- This table facilitates the ManyToMany relationship between ACTIVITY and STORED_FILE
CREATE TABLE SKYTEST.ACTIVITY_ATTACHMENT (
    ACTIVITY_INT_ID NUMBER(10) NOT NULL REFERENCES SKYTEST.ACTIVITY(ACTIVITY_INT_ID),
    STORED_FILE_INT_ID NUMBER(10) NOT NULL REFERENCES SKYTEST.STORED_FILE(STORED_FILE_INT_ID),
    PRIMARY KEY (ACTIVITY_INT_ID, STORED_FILE_INT_ID)
);
```
**Key Relationships**:
- Links `ACTIVITY` table to `STORED_FILE` table.

---

**Document Version**: 6.0
**Last Updated**: 2025-06-09
**Purpose**: Added File Storage related table schemas for Document Center feature.

### **COMPLIANCE_DOCUMENT Table** ⭐ **RELEVANT FOR DOCUMENT CENTER (Separate Storage)**
```sql
-- Extracted from com.insurfact.skynet.entity.ComplianceDocument.java
CREATE TABLE SKYTEST.COMPLIANCE_DOCUMENT (
    COMPLIANCE_DOC_INT_ID NUMBER NOT NULL PRIMARY KEY, -- Sequence: COMPLIANCE_DOCUMENT_SEQ
    TITLE_EN VARCHAR2(256),
    FILE_PATH VARCHAR2(512), -- Direct path to the compliance document file
    CREATION_DATE DATE,
    LAST_MODIFICATION_DATE DATE,
    PRINT_DATE DATE,
    SIGNATURE_DATE DATE,
    TYPE NUMBER, -- Likely references TYPES table (typeClass: COMPLIANCE_DOC_TYPE)
    CONTACT NUMBER REFERENCES SKYTEST.CONTACT(CONTACT_INT_ID), -- Link to Contact
    OWNER NUMBER REFERENCES SKYTEST.USERS(USER_INT_ID) -- Link to User (Advisor)
);

-- Sequence for COMPLIANCE_DOC_INT_ID
CREATE SEQUENCE SKYTEST.COMPLIANCE_DOCUMENT_SEQ START WITH 1 INCREMENT BY 1;
```
**Key Relationships**:
- `COMPLIANCE_DOCUMENT.CONTACT` -> `CONTACT.CONTACT_INT_ID`
- `COMPLIANCE_DOCUMENT.OWNER` -> `USERS.USER_INT_ID`

**Critical Notes**:
- Stores metadata for compliance documents.
- **Crucially, uses a direct `FILE_PATH` field for file storage, not `STORED_FILE` or `STORED_FILE_DESC`**.
- May need to be included in the Document Center API if relevant to advisor documents.

### **A_FILE Table** (Likely Out of Scope for Advisor Document Center)
```sql
-- Extracted from com.insurfact.skynet.entity.AFile.java
CREATE TABLE SKYTEST.A_FILE (
    A_FILE_INT_ID NUMBER NOT NULL PRIMARY KEY, -- Assuming a sequence like A_FILE_SEQ
    FILENAME VARCHAR2(255),
    FILE_PATH VARCHAR2(512), -- Direct path to the file
    VERSION VARCHAR2(50),
    MGMT_COMPANY_CODE VARCHAR2(50),
    DEALER_CODE VARCHAR2(50),
    RECORD_COUNT NUMBER,
    CREATION_DATE DATE,
    EFFECTIVE_DATE DATE,
    DAY_CODE VARCHAR2(2),
    MONTH_CODE VARCHAR2(2),
    YEAR_CODE VARCHAR2(4),
    FILE_CODE VARCHAR2(50),
    FILE_TYPE VARCHAR2(50),
    SEQUENCE NUMBER
);

-- Sequence for A_FILE_INT_ID (assumption)
CREATE SEQUENCE SKYTEST.A_FILE_SEQ START WITH 1 INCREMENT BY 1;
```
**Critical Notes**:
- Represents metadata for more structured, possibly batch-oriented or system-level files.
- Uses a direct `FILE_PATH` field for storage.
- **Unlikely to be part of the general advisor-facing Document Center API** due to its specialized nature.

---

**Document Version**: 7.0
**Last Updated**: 2025-06-09
**Purpose**: Added schemas for COMPLIANCE_DOCUMENT and A_FILE tables.
