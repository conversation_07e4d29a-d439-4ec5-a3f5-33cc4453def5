/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.avue.navigation.common;

import com.insurfact.avue.navigation.ApplicationMainBean;
import com.insurfact.avue.navigation.advisor.AdvisorNavigationBean;
import com.insurfact.avue.util.CloudDocument;
import com.insurfact.avue.util.ProductSupplierEnglishComparator;
import com.insurfact.avue.util.ProductSupplierFrenchComparator;
import com.insurfact.sdk.mail.Attachment;
import com.insurfact.skynet.Recipient;
import com.insurfact.skynet.constant.Constants;
import com.insurfact.skynet.ejb.ActivityFacade;
import com.insurfact.skynet.ejb.AddressBookFacade;
import com.insurfact.skynet.ejb.AdvisorFacade;
import com.insurfact.skynet.ejb.AlertFacade;
import com.insurfact.skynet.ejb.CompanyFacade;
import com.insurfact.skynet.ejb.ContactFacade;
import com.insurfact.skynet.ejb.ContractSetupFacade;
import com.insurfact.skynet.ejb.EmailManager;
import com.insurfact.skynet.ejb.LeadFacade;
import com.insurfact.skynet.ejb.ProductSupplierFacade;
import com.insurfact.skynet.ejb.ProvinceFacade;
import com.insurfact.skynet.ejb.StoredFileDescFacade;
import com.insurfact.skynet.ejb.StoredFileFacade;
import com.insurfact.skynet.ejb.StoredFileNoteFacade;
import com.insurfact.skynet.ejb.TypeClassEntity;
import com.insurfact.skynet.ejb.TypesEntity;
import com.insurfact.skynet.ejb.WebContentFacade;
import com.insurfact.skynet.entity.Activity;
import com.insurfact.skynet.entity.AddressBook;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Alert;
import com.insurfact.skynet.entity.Company;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.ContactAction;
import com.insurfact.skynet.entity.ContractSetup;
import com.insurfact.skynet.entity.Email;
import com.insurfact.skynet.entity.Lead;
import com.insurfact.skynet.entity.License;
import com.insurfact.skynet.entity.LicenseLiability;
import com.insurfact.skynet.entity.Phone;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.ProductSupplierType;
import com.insurfact.skynet.entity.Province;
import com.insurfact.skynet.entity.StoredFile;
import com.insurfact.skynet.entity.StoredFileDesc;
import com.insurfact.skynet.entity.StoredFileNote;
import com.insurfact.skynet.entity.Types;
import com.insurfact.skynet.entity.WebContent;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import jakarta.annotation.PostConstruct;
import jakarta.ejb.EJB;
import jakarta.enterprise.context.SessionScoped;
import jakarta.faces.application.FacesMessage;
import jakarta.faces.context.FacesContext;
import jakarta.faces.event.PhaseId;
import jakarta.faces.model.SelectItem;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.servlet.http.HttpServletRequest;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.TabChangeEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.file.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Named("documentCentreBean")
@SessionScoped
public class DocumentCentreBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@EJB
	AdvisorFacade advisorFacade;

	@EJB
	private ActivityFacade activityFacade;

	@EJB
	StoredFileFacade storedFileFacade;

	@EJB
	StoredFileDescFacade storedFileDescFacade;

	@EJB
	CompanyFacade companyFacade;

	@EJB
	private WebContentFacade contentFacade;

	@EJB
	private LeadFacade leadFacade;

	@EJB
	private AddressBookFacade addressBookFacade;

	@EJB
	private ProductSupplierFacade productSupplierFacade;

	@EJB
	private EmailManager emailManager;

	@EJB
	private ContractSetupFacade contractSetupFacade;

	@EJB
	ProvinceFacade provinceFacade;

	@EJB
	private ContactFacade contactFacade;

	@Inject
	private CommonContractingBean commonContractingBean;

	@Inject
	private AdvisorNavigationBean advisorNavigationBean;

	@Inject
	private ApplicationMainBean applicationBean;

	private List<Advisor> allAdvisors = new ArrayList<>();
	private List<Contact> allContacts = new ArrayList<>();
	private List<AddressBook> addressBooks = new ArrayList<>();
	private List<AddressBook> allAddressBooks = new ArrayList<>();
	private List<AddressBook> allAddressBooksCompose = new ArrayList<>();
	private List<AddressBook> allAddressBooksView = new ArrayList<>();
	private List<AddressBook> toSend = new ArrayList<>();
	private List<ProductSupplier> suppliersSelected = new ArrayList<>();
	private Integer selectedAgency;
	// private List<Advisor> advisorList = new ArrayList<>();
	private List<Province> provinces = new ArrayList<>();
	private Advisor selectedAdvisor;

	private List<AddressBook> recipientList;

	private List<ProductSupplier> agenciesList = new ArrayList<>();
	private List<Integer> selectedAgencies = new ArrayList<>();
	private List<Contact> contactsAgency = new ArrayList<>();
	private List<ProductSupplier> agenciesFilled = new ArrayList<>();
	private List<Contact> contactsAgencyFilter = new ArrayList<>();
	private List<Contact> toSendContacts = new ArrayList<>();
	private List<Contact> contactsAgencyList = new ArrayList<>();
	private List<StoredFile> storedFileList = new ArrayList<>();

	private boolean masterMode = false;
	private boolean toolMaster = false;

	protected boolean documentPartVisible = false;
	protected boolean enableLists = false;

	private boolean viewId = true; // true - license; false - liability;

	// *******************************
	// remove the unused later
	private boolean searchAdvisor = false;
	private boolean sendAll = false;
	private boolean reply = false;
	private boolean sendEmailBool = true;
	private int searchBy = 1;
	private int toogle = 1; // if tool master 1 - Advisor 2 - Agency 3 - All Advisors 4 - All Agencies 5 -
							// All All
	private String advisorNameSearch;
	private String advisorNameSearch2;
	private String advisorIdStatus;
	private Integer advisorStatusId;
	private boolean searchAct = false;

	private Integer selectedTopic;
	private Integer selectedDocumentType;

	private UploadedFile uploadedFile;

	private Recipient recipient;
	private List<Recipient> recipients;

	private String message;
	private String title;
	private List<Attachment> attachments;

	private Integer selectedProductSuplier;
	private List<Integer> selectedProductSupliers;
	private List<ProductSupplier> productSuppliers;
	private Integer selectedProdSuppTypeId;
	private Integer selectedCompanyId = 0;

	protected int viewTabId = 0;

	private AddressBook selectedAddressBook;
	private Activity lastActivity;

	private StoredFile selectedFileToDelete;
	private StoredFile selectedFile;
	private StoredFile selectedFileNotes;
	private StoredFileNote selectedNote;
	private StoredFileNote selectedNoteToDelete;
	private List<Activity> sentMessages;

	// private int fileType = 0;
	private Integer searchProdSuppTypeId;
	private ProductSupplier selectedProductSupplier;
	private List<ProductSupplierType> productSupplierTypes;

	private boolean hasLicense = false;
	private boolean hasLiability = false;

	private boolean renderingMgaName = false;

	private List<StoredFile> filesToSendAdvisor;

	@EJB
	private TypesEntity typesFacade;

	@EJB
	private TypeClassEntity typeClassFacade;

	@EJB
	private StoredFileNoteFacade storedFileNoteFacade;

	@EJB
	private AlertFacade alertFacade;

	public DocumentCentreBean() {
	}
	
	public void cleanFilesToSend() {
		filesToSendAdvisor = new ArrayList<>();
	}

	public void initialize() {
		masterMode = applicationBean.getUsers().isGodMode();
		toolMaster = applicationBean.getUsers().isToolMaster();
		allAdvisors = new ArrayList<>();
		attachments = new ArrayList<>();
		productSuppliers = new ArrayList<>();
		selectedProductSuplier = 0;
		selectedDocumentType = 0;
		toogle = 1; // if tool master 1 - Advisor 2 - Agency 3 - All Advisors 4 - All Agencies 5 -
					// All All
		enableLists = false;
		toSend.clear();
		suppliersSelected.clear();
		agenciesList.clear();
		contactsAgency.clear();
		contactsAgencyFilter.clear();
		selectedAgencies.clear();

		loadLists();
		/*
		 * if (!masterMode && !toolMaster) { selectedAdvisor =
		 * applicationBean.getUsers().getContact().getAdvisor();
		 * setSelectedAdvisor(selectedAdvisor); }
		 */
		reply = false;
		searchAdvisor = false;
		sendAll = false;
		searchBy = 1;
		searchAct = false;

		if (!masterMode && !toolMaster) {
			if (selectedAdvisor.getLicenseList() != null && !selectedAdvisor.getLicenseList().isEmpty()) {
				for (License li : selectedAdvisor.getLicenseList()) {
					if (!li.isExpired()) {
						hasLicense = true;
					}
				}
			}
			if (selectedAdvisor.getLicenseLiabilityList() != null
					&& !selectedAdvisor.getLicenseLiabilityList().isEmpty()) {
				for (LicenseLiability li : selectedAdvisor.getLicenseLiabilityList()) {
					if (!li.isExpired()) {
						hasLiability = true;
					}
				}
			}
		}

		selectedProductSupliers = new ArrayList<>();
		viewId = true;
		sendEmailBool = true;
	}

	public void initializeDocument() {
		masterMode = applicationBean.getUsers().isGodMode();
		toolMaster = applicationBean.getUsers().isToolMaster();
		allAdvisors = new ArrayList<>();
		attachments = new ArrayList<>();
		productSuppliers = new ArrayList<>();
		allAddressBooks = addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		selectedProductSuplier = 0;
		selectedDocumentType = 0;
		toogle = 1; // if tool master 1 - Advisor 2 - Agency 3 - All Advisors 4 - All Agencies 5 -
					// All All
		selectedAgency = 0;
		enableLists = false;
		toSend.clear();
		suppliersSelected.clear();
		agenciesList.clear();
		contactsAgency.clear();
		contactsAgencyFilter.clear();
		selectedAgencies.clear();
		toSendContacts.clear();
		allContacts = new ArrayList<>();

		if (!masterMode && !toolMaster) {
			for (ContractSetup contract : applicationBean.getUsers().getContact().getAdvisor().getContractSetupList()) {
				if (contract.getMgaProductSupplier() != null) {
					contract.getProductSupplier()
							.setContractAttachedProductSupplierMga(contract.getMgaProductSupplier());
				}
				if (contract.getAgaProductSupplier() != null) {
					contract.getProductSupplier()
							.setContractAttachedProductSupplierAga(contract.getAgaProductSupplier());
				}
				if (contract.getAga2ProductSupplier() != null) {
					contract.getProductSupplier()
							.setContractAttachedProductSupplierAga2(contract.getAga2ProductSupplier());
				}
				productSuppliers.add(contract.getProductSupplier());
				allContacts.addAll(contract.getProductSupplier().getOrganization().getContactList());
			}
		}

		if (toolMaster) {
			agenciesList = productSupplierFacade
					.findAllStructure(applicationBean.getUsers().getProfileTool().getProductSupplier());
			agenciesList.remove(applicationBean.getUsers().getProfileTool().getProductSupplier());
			if (applicationBean.getLanguage().isEnglish()) {
				Collections.sort(agenciesList, new ProductSupplierEnglishComparator());
			} else {
				Collections.sort(agenciesList, new ProductSupplierFrenchComparator());
			}
		}

		reply = false;
		searchAdvisor = false;
		sendAll = false;
		searchBy = 1;
		searchAct = false;

		if (!masterMode && !toolMaster) {
			if (selectedAdvisor.getLicenseList() != null && !selectedAdvisor.getLicenseList().isEmpty()) {
				for (License li : selectedAdvisor.getLicenseList()) {
					if (!li.isExpired()) {
						hasLicense = true;
					}
				}
			}
			if (selectedAdvisor.getLicenseLiabilityList() != null
					&& !selectedAdvisor.getLicenseLiabilityList().isEmpty()) {
				for (LicenseLiability li : selectedAdvisor.getLicenseLiabilityList()) {
					if (!li.isExpired()) {
						hasLiability = true;
					}
				}
			}
		}

		selectedProductSupliers = new ArrayList<>();
		viewId = true;
		sendEmailBool = true;
	}

	public void initializeDocumentAdvisor(Advisor adv) {
		masterMode = applicationBean.getUsers().isGodMode();
		toolMaster = applicationBean.getUsers().isToolMaster();
		allAdvisors = new ArrayList<>();
		attachments = new ArrayList<>();
		productSuppliers = new ArrayList<>();
		allAddressBooks = addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		allAddressBooksView = allAddressBooks;
		selectedProductSuplier = 0;
		selectedDocumentType = 0;
		toogle = 1; // if tool master 1 - Advisor 2 - Agency 3 - All Advisors 4 - All Agencies 5 -
					// All All
		selectedAgency = 0;
		enableLists = false;
		toSend.clear();
		suppliersSelected.clear();
		agenciesList.clear();
		contactsAgency.clear();
		contactsAgencyFilter.clear();
		selectedAgencies.clear();
		toSendContacts.clear();
		allContacts = new ArrayList<>();
		// toSendContacts.add(adv.getContact());

		AddressBook ab = new AddressBook();
		ab.setFirstname(adv.getContact().getFirstname());
		ab.setLastname(adv.getContact().getLastname());
		if (adv.getContact().getDefaultEmail() != null) {
			ab.setAddressEmail(adv.getContact().getDefaultEmail().getEmailAddress());
		}
		if (adv.getContact().getDefaultPhone() != null) {
			ab.setPhoneNumber(adv.getContact().getDefaultPhone().getAreaCode()
					+ adv.getContact().getDefaultPhone().getPhoneNumber());
		}
		// ab.setNickname(applicationBean.getLanguage().getString("Advisor"));
		ab.setAssociation(applicationBean.getUsers().getToolName());
		ab.setAddressBookType(5);
		ab.setOwner(applicationBean.getUsers());
		ab.setContact(adv.getContact());
		ab.setSelected(true);

		enableFieldsBetter(ab);

		if (toolMaster) {
			agenciesList = productSupplierFacade
					.findAllStructure(applicationBean.getUsers().getProfileTool().getProductSupplier());
			agenciesList.remove(applicationBean.getUsers().getProfileTool().getProductSupplier());
			if (applicationBean.getLanguage().isEnglish()) {
				Collections.sort(agenciesList, new ProductSupplierEnglishComparator());
			} else {
				Collections.sort(agenciesList, new ProductSupplierFrenchComparator());
			}
		}

		reply = false;
		searchAdvisor = false;
		sendAll = false;
		searchBy = 1;
		searchAct = false;

		selectedProductSupliers = new ArrayList<>();
		viewId = true;
		sendEmailBool = true;
	}

	public void initializeBook() {
		allAddressBooks = addressBookFacade.findByOwner(applicationBean.getUsers());
		System.out.println("allAddressBooks: " + allAddressBooks);
		searchBy = 1;
		advisorNameSearch = "";
		advisorNameSearch2 = "";
		searchAct = false;
		selectedAddressBook = null;
		addressBooks = new ArrayList<>();
		allAddressBooksView = allAddressBooks;
		System.out.println("allAddressBooksView: " + allAddressBooksView);
	}

	public void initializeBookBussines() {
		allAddressBooks = addressBookFacade.findByOwner(applicationBean.getUsers());
		Iterator<AddressBook> it = allAddressBooks.iterator();
		while (it.hasNext()) {
			AddressBook next = it.next();
			if (next.getAddressBookType() == null || next.getAddressBookType() != 8) {
				it.remove();
			}
		}
		searchBy = 1;
		advisorNameSearch = "";
		advisorNameSearch2 = "";
		searchAct = false;
		selectedAddressBook = null;
		addressBooks = new ArrayList<>();
		allAddressBooksView = allAddressBooks;
	}

	public void initialize(Advisor advisor) {
		// masterMode = applicationBean.getUsers().isGodMode();
		// toolMaster = applicationBean.getUsers().isToolMaster();
		allAdvisors = new ArrayList<>();
		allAddressBooks = addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		loadLists();
		setSelectedAdvisor(advisor);
	}

	public void refreshAddress() {
		allAddressBooksCompose = addressBookFacade.findByOwnerEmailLimit50(applicationBean.getUsers());
		allAddressBooksView = applicationBean.getUsers().getAddressBookList();
	}

	@PostConstruct
	protected void init() {
		masterMode = applicationBean.getUsers().isGodMode();
		toolMaster = applicationBean.getUsers().isToolMaster();
		allAddressBooksCompose = new ArrayList<>();
		allAddressBooksCompose.addAll(addressBookFacade.findByOwnerEmailLimit50(applicationBean.getUsers()));
		allAddressBooksView = applicationBean.getUsers().getAddressBookList();
		if (!masterMode && !toolMaster) {
			selectedAdvisor = applicationBean.getUsers().getContact().getAdvisor();
			allAddressBooks = addressBookFacade.findByOwnerLimit50(applicationBean.getUsers());
		}
		toogle = 1;
		// allAddressBooks =
		// addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		/*
		 * allAdvisors = new ArrayList<>(); loadLists(); if (!masterMode && !toolMaster)
		 * { selectedAdvisor = applicationBean.getUsers().getContact().getAdvisor();
		 * setAdvisorEdit(selectedAdvisor); }
		 */
		loadCanadaProvinces();

	}

	public List<SelectItem> getBuildTopicListView() {

		List<SelectItem> typesSelectItems = new ArrayList<>();

		List<Types> result = typesFacade.findTypesByTypeClass(typeClassFacade.find(66417));

		for (Types ty : result) {
			if (applicationBean.getLanguage().isEnglish()) {
				typesSelectItems.add(new SelectItem(ty.getSubType(), ty.getDescEn()));
			} else {
				typesSelectItems.add(new SelectItem(ty.getSubType(), ty.getDescFr()));
			}
		}

		return typesSelectItems;
	}

	public String buildTopicDesc(Integer id) {
		if (id == null) {
			id = 0;
		}
		return getTypesNameWithLocale(id);
	}

	public List<SelectItem> getBuildDocumentTypeListView() {
		List<SelectItem> typesSelectItems = new ArrayList<>();

		if (selectedTopic == null) {
			typesSelectItems.add(0, (new SelectItem(null, applicationBean.getLanguage().getString("Select"))));
		} else {
			typesSelectItems.add(0, (new SelectItem(0, applicationBean.getLanguage().getString("Select"))));
			for (Types ty : typesFacade.findTypesByTypeClass(typeClassFacade.find(selectedTopic))) {
				if (applicationBean.getLanguage().isEnglish()) {
					typesSelectItems.add(new SelectItem(ty.getTypeIntId(), ty.getDescEn()));
				} else {
					typesSelectItems.add(new SelectItem(ty.getTypeIntId(), ty.getDescFr()));
				}
			}
		}

		return typesSelectItems;
	}

	public List<SelectItem> getBuildTitlesList() {
		List<SelectItem> typesSelectItems = new ArrayList<>();

		typesSelectItems.add(0, (new SelectItem(0, applicationBean.getLanguage().getString("Select"))));
		for (Types ty : typesFacade.findTypesByTypeClass(typeClassFacade.find(1828))) {
			if (applicationBean.getLanguage().isEnglish()) {
				typesSelectItems.add(new SelectItem(ty.getDescEn(), ty.getDescEn()));
			} else {
				typesSelectItems.add(new SelectItem(ty.getDescFr(), ty.getDescFr()));
			}
		}

		return typesSelectItems;
	}

	public String documentTypeDesc(Integer id) {
		if (id == null) {
			id = 0;
		}
		return getTypesNameWithLocale(id);
	}

	public String getTypesNameWithLocale(int typeIntId) {

		Types types = typesFacade.find(typeIntId);

		if (types == null) {
			return applicationBean.getLanguage().getString("Undefined");
		}

		if (applicationBean.getLanguage().getLocale() == Locale.CANADA) {
			return types.getDescEn();
		} else {
			return types.getDescFr();
		}
	}

	/**
	 * send email to SMTP server
	 */
	public void sendEmail() {
		// System.out.println("start the send of the email");

		if (!getDocumentMimeType(uploadedFile).equals("application/pdf")
				&& !getDocumentMimeType(uploadedFile).equals("image/jpeg")) {
			FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR,
					applicationBean.getLanguage().getString("Error"),
					applicationBean.getLanguage().getString("File.Format.Error"));
			FacesContext.getCurrentInstance().addMessage(null, msg);
			return;
		}

		Attachment att = new Attachment(uploadedFile.getFileName(), getDocumentMimeType(uploadedFile),
				uploadedFile.getContent());
		attachments.clear();
		attachments.add(att);

		WebContent content = null;

		int cont = 0;

		if (reply) {
			content = contentFacade.findWebContentById(1068);
		} else {
			content = contentFacade.findWebContentById(65351);
		}

		// replace the content
		HashMap<String, String> privateDocSec = new HashMap<>();
		privateDocSec.put("#CREATION_DATE#", Calendar.getInstance().getTime().toString());
		privateDocSec.put("#YEAR#", Integer.toString(Calendar.getInstance().get(Calendar.YEAR)));
		privateDocSec.put("#FROM#", applicationBean.getUsers().getContact().getFullName());

		Types selectedType = new Types();

		for (Types ty : typesFacade.findTypesByTypeClass(typeClassFacade.find(66417))) {
			if (Objects.equals(ty.getSubType(), selectedTopic)) {
				selectedType = ty;
			}
		}

		String defaultEmailBodySecFR = substitueKeyValues(privateDocSec, content.getContentFr());

		String defaultEmailBodySecEN = substitueKeyValues(privateDocSec, content.getContentEn());

		List<StoredFile> attachs = new ArrayList<>();
		StoredFileDesc fileDes;
		StoredFile fileItem;
		for (Attachment atts : attachments) {
			fileItem = new StoredFile();
			fileItem.setCreatedBy(applicationBean.getUsers());
			fileItem.setCreatedFrom("Messaging");
			fileItem.setCreationDate(Calendar.getInstance().getTime());
			fileItem.setName(atts.getFileName());
			storedFileFacade.create(fileItem);
			fileDes = new StoredFileDesc();
			fileDes.setFileEn(atts.getAttachmentByteArray());
			// fileDes.setStoredFile(fileItem);
			fileDes.setStoredFileIntId(fileItem.getStoredFileIntId());
			// fileItem.setStoredFileDesc(fileDes);
			storedFileDescFacade.create(fileDes);
			// storedFileFacade.edit(fileItem);
			attachs.add(fileItem);

		}

		try {

			// String from = "<EMAIL>"; //
			// advisorNavigationBean.getSelectedAdvisor().getContact().getDefaultEmail().getEmailAddress();

//            boolean updateEntity = false;
			// boolean emailSent = false;

			// Send Head Office
			if (sendAll) {
				if (masterMode || toolMaster) {
					for (AddressBook rec : allAddressBooks) {

						cont++;

						Email email = new Email();
						// System.out.println("email: " + email.getEmailAddress());
						if (rec.getContact() != null) {
							email = rec.getContact().getDefaultEmail();
						}

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							privateDocSec.put("#SUBJECT#", selectedType.getDescFr() + " ("
									+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
						} else {
							privateDocSec.put("#SUBJECT#", selectedType.getDescEn() + " ("
									+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
						}

						String contentEn = substitueKeyValues(privateDocSec, defaultEmailBodySecEN);
						String contentFr = substitueKeyValues(privateDocSec, defaultEmailBodySecFR);

						recipient = new Recipient();

						// reply to
						recipient.setReplyTo(
								applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

						// Creating the emailTemplate containing the subject and body according to the
						// prefred language of the Advisor
						// getting the preferred language of the contact
						if (rec.getContact() != null) {
							recipient.setName(rec.getContact().getFullName());
							recipient.setContact(rec.getContact());
						} else {
							recipient.setName(rec.getFirstname() + " " + rec.getLastname());
						}

						/*
						 * if (contact.getDepartment() != null) {
						 * recipient.setDepartment(contact.getDepartment()); }
						 */
						if (email.getEmailIntId() != null) {
							recipient.setAddress(email.getEmailAddress());
						} else {
							rec.getAddressEmail();
						}

						recipient.setEmailType(20); // Document Sending

						// getting the web content
						if (sendEmailBool) {
							if (rec.getContact() != null) {
								if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French
										.getCode()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							} else {
								if (applicationBean.getLanguage().isFrench()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							}
						}

						logActivity(recipient, attachs);

					}
				} else {
					for (Contact rec : allContacts) {

						cont++;

						Email email = rec.getDefaultEmail();
						// System.out.println("email: " + email.getEmailAddress());

						recipient = new Recipient();

						// reply to
						recipient.setReplyTo(
								applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

						// Creating the emailTemplate containing the subject and body according to the
						// prefred language of the Advisor
						// getting the preferred language of the contact
						recipient.setName(rec.getFullName());
						recipient.setContact(rec);

						if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							privateDocSec.put("#SUBJECT#", selectedType.getDescFr() + " ("
									+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
						} else {
							privateDocSec.put("#SUBJECT#", selectedType.getDescEn() + " ("
									+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
						}

						String contentEn = substitueKeyValues(privateDocSec, defaultEmailBodySecEN);
						String contentFr = substitueKeyValues(privateDocSec, defaultEmailBodySecFR);

						/*
						 * if (contact.getDepartment() != null) {
						 * recipient.setDepartment(contact.getDepartment()); }
						 */
						if (email != null) {
							recipient.setAddress(email.getEmailAddress());
						}

						recipient.setEmailType(20); // Document Sending

						// getting the web content
						if (sendEmailBool) {
							if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								emailManager.sendEmailWithAttachments(recipient.getAddress(), recipient.getReplyTo(),
										content.getContentTitleFr(), content.getContentFr(), attachments);
								title = content.getContentTitleFr();
								message = contentFr;
							} else {
								emailManager.sendEmailWithAttachments(recipient.getAddress(), recipient.getReplyTo(),
										content.getContentTitleEn(), content.getContentEn(), attachments);
								title = content.getContentTitleEn();
								message = contentEn;
							}
						}

						logActivity(recipient, attachs);

					}
				}
			} else {
				if (masterMode || toolMaster) {
					for (AddressBook rec : toSend) {
						// if (rec.isSelected()) {

						cont++;

						Email email = new Email();
						// System.out.println("email: " + email.getEmailAddress());
						if (rec.getContact() != null) {
							email = rec.getContact().getDefaultEmail();
						}

						recipient = new Recipient();

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							privateDocSec.put("#SUBJECT#", selectedType.getDescFr() + " ("
									+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
						} else {
							privateDocSec.put("#SUBJECT#", selectedType.getDescEn() + " ("
									+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
						}

						String contentEn = substitueKeyValues(privateDocSec, defaultEmailBodySecEN);
						String contentFr = substitueKeyValues(privateDocSec, defaultEmailBodySecFR);

						// reply to
						recipient.setReplyTo(
								applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

						// Creating the emailTemplate containing the subject and body according to the
						// prefred language of the Advisor
						// getting the preferred language of the contact
						if (rec.getContact() != null) {
							recipient.setName(rec.getContact().getFullName());
							recipient.setContact(rec.getContact());
						} else {
							recipient.setName(rec.getFirstname() + " " + rec.getLastname());
						}

						/*
						 * if (contact.getDepartment() != null) {
						 * recipient.setDepartment(contact.getDepartment()); }
						 */
						if (email.getEmailIntId() != null) {
							recipient.setAddress(email.getEmailAddress());
						} else {
							rec.getAddressEmail();
						}

						recipient.setEmailType(20); // Document Sending

						// getting the web content
						if (sendEmailBool) {
							if (rec.getContact() != null) {
								if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French
										.getCode()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							} else {
								if (applicationBean.getLanguage().isFrench()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							}
						}

						logActivity(recipient, attachs);

						// }
					}
					// shared ones
					if (toSend.size() > 1) {
						for (ProductSupplier ps : suppliersSelected) {
							if (ps.isSelected()) {
								for (Contact rec : ps.getOrganization().getContactList()) {
									if (rec.canRecieveDocuments()) {
										cont++;

										Email email = rec.getDefaultEmail();
										// System.out.println("email: " + email.getEmailAddress());

										recipient = new Recipient();

										// reply to
										recipient.setReplyTo(applicationBean.getUsers().getContact().getDefaultEmail()
												.getEmailAddress());

										// Creating the emailTemplate containing the subject and body according to the
										// prefred language of the Advisor
										// getting the preferred language of the contact
										recipient.setName(rec.getFullName());
										recipient.setContact(rec);

										/*
										 * if (contact.getDepartment() != null) {
										 * recipient.setDepartment(contact.getDepartment()); }
										 */
										if (email != null) {
											recipient.setAddress(email.getEmailAddress());
										}

										recipient.setEmailType(20); // Document Sending

										// getting the web content
										if (sendEmailBool) {
											if (rec.getPreferredLanguage() == Constants.Language_Type.French
													.getCode()) {
												// emailManager.sendEmailWithAttachments(recipient.getAddress(),
												// recipient.getReplyTo(), content.getContentTitleFr(),
												// content.getContentFr(), attachments);
												title = content.getContentTitleFr();
												message = content.getContentFr();
											} else {
												// emailManager.sendEmailWithAttachments(recipient.getAddress(),
												// recipient.getReplyTo(), content.getContentTitleEn(),
												// content.getContentEn(), attachments);
												title = content.getContentTitleEn();
												message = content.getContentEn();
											}
										}
										logActivity(recipient, attachs);
									}
								}
							}
						}

					} else if (toSend.size() == 1) {

						if (toSend.get(0).getMga() != null && toSend.get(0).getMga().isSelected()) {
							for (Contact rec : toSend.get(0).getMga().getOrganization().getContactList()) {
								if (rec.canRecieveDocuments()) {
									cont++;

									Email email = rec.getDefaultEmail();
									// System.out.println("email: " + email.getEmailAddress());

									recipient = new Recipient();

									// reply to
									recipient.setReplyTo(applicationBean.getUsers().getContact().getDefaultEmail()
											.getEmailAddress());

									// Creating the emailTemplate containing the subject and body according to the
									// prefred language of the Advisor
									// getting the preferred language of the contact
									recipient.setName(rec.getFullName());
									recipient.setContact(rec);

									if (email != null) {
										recipient.setAddress(email.getEmailAddress());
									}

									recipient.setEmailType(20); // Document Sending

									// getting the web content
									if (sendEmailBool) {
										if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
											// emailManager.sendEmailWithAttachments(recipient.getAddress(),
											// recipient.getReplyTo(), content.getContentTitleFr(),
											// content.getContentFr(), attachments);
											title = content.getContentTitleFr();
											message = content.getContentFr();
										} else {
											// emailManager.sendEmailWithAttachments(recipient.getAddress(),
											// recipient.getReplyTo(), content.getContentTitleEn(),
											// content.getContentEn(), attachments);
											title = content.getContentTitleEn();
											message = content.getContentEn();
										}
									}
									logActivity(recipient, attachs);
								}
							}

						}

						if (toSend.get(0).getAga() != null && toSend.get(0).getAga().isSelected()) {
							for (Contact rec : toSend.get(0).getAga().getOrganization().getContactList()) {
								if (rec.canRecieveDocuments()) {
									cont++;

									Email email = rec.getDefaultEmail();
									// System.out.println("email: " + email.getEmailAddress());

									recipient = new Recipient();

									// reply to
									recipient.setReplyTo(applicationBean.getUsers().getContact().getDefaultEmail()
											.getEmailAddress());

									// Creating the emailTemplate containing the subject and body according to the
									// prefred language of the Advisor
									// getting the preferred language of the contact
									recipient.setName(rec.getFullName());
									recipient.setContact(rec);

									if (email != null) {
										recipient.setAddress(email.getEmailAddress());
									}

									recipient.setEmailType(20); // Document Sending

									// getting the web content
									if (sendEmailBool) {
										if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
											// emailManager.sendEmailWithAttachments(recipient.getAddress(),
											// recipient.getReplyTo(), content.getContentTitleFr(),
											// content.getContentFr(), attachments);
											title = content.getContentTitleFr();
											message = content.getContentFr();
										} else {
											// emailManager.sendEmailWithAttachments(recipient.getAddress(),
											// recipient.getReplyTo(), content.getContentTitleEn(),
											// content.getContentEn(), attachments);
											title = content.getContentTitleEn();
											message = content.getContentEn();
										}
									}
									logActivity(recipient, attachs);
								}
							}

						}

						if (toSend.get(0).getAga2() != null && toSend.get(0).getAga2().isSelected()) {
							for (Contact rec : toSend.get(0).getAga2().getOrganization().getContactList()) {
								if (rec.canRecieveDocuments()) {
									cont++;

									Email email = rec.getDefaultEmail();
									// System.out.println("email: " + email.getEmailAddress());

									recipient = new Recipient();

									// reply to
									recipient.setReplyTo(applicationBean.getUsers().getContact().getDefaultEmail()
											.getEmailAddress());

									// Creating the emailTemplate containing the subject and body according to the
									// prefred language of the Advisor
									// getting the preferred language of the contact
									recipient.setName(rec.getFullName());
									recipient.setContact(rec);

									if (email != null) {
										recipient.setAddress(email.getEmailAddress());
									}

									recipient.setEmailType(20); // Document Sending

									// getting the web content
									if (sendEmailBool) {
										if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
											// emailManager.sendEmailWithAttachments(recipient.getAddress(),
											// recipient.getReplyTo(), content.getContentTitleFr(),
											// content.getContentFr(), attachments);
											title = content.getContentTitleFr();
											message = content.getContentFr();
										} else {
											// emailManager.sendEmailWithAttachments(recipient.getAddress(),
											// recipient.getReplyTo(), content.getContentTitleEn(),
											// content.getContentEn(), attachments);
											title = content.getContentTitleEn();
											message = content.getContentEn();
										}
									}
									logActivity(recipient, attachs);
								}
							}
						}

					}

				} else {
					if (selectedProductSuplier != null) {

						// if (toogle == 1) { //company
						for (Contact rec : toSendContacts) {

							// if (rec.isSelected()) {
							cont++;

							Email email = rec.getDefaultEmail();
							// System.out.println("email: " + email.getEmailAddress());

							recipient = new Recipient();

							if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								privateDocSec.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDocSec.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}

							String contentEn = substitueKeyValues(privateDocSec, defaultEmailBodySecEN);
							String contentFr = substitueKeyValues(privateDocSec, defaultEmailBodySecFR);

							// reply to
							recipient.setReplyTo(
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

							// Creating the emailTemplate containing the subject and body according to the
							// prefred language of the Advisor
							// getting the preferred language of the contact
							recipient.setName(rec.getFullName());
							recipient.setContact(rec);

							/*
							 * if (contact.getDepartment() != null) {
							 * recipient.setDepartment(contact.getDepartment()); }
							 */
							if (email != null) {
								recipient.setAddress(email.getEmailAddress());
							}

							recipient.setEmailType(20); // Document Sending

							// getting the web content
							if (sendEmailBool) {
								if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							}

							logActivity(recipient, attachs);
							// }
						}
						/*
						 * } else if (toogle == 2) {//both for (ProductSupplier ps : productSuppliers) {
						 * if (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier)) {
						 * if (ps.getContractAttachedProductSupplierMga() != null) { for (Contact rec :
						 * ps.getContractAttachedProductSupplierMga().getOrganization().getContactList()
						 * ) { if (rec.canRecieveDocuments()) {
						 * 
						 * cont++;
						 * 
						 * Email email = rec.getDefaultEmail(); //System.out.println("email: " +
						 * email.getEmailAddress());
						 * 
						 * recipient = new Recipient();
						 * 
						 * // reply to
						 * recipient.setReplyTo(applicationBean.getUsers().getContact().getDefaultEmail(
						 * ).getEmailAddress());
						 * 
						 * //Creating the emailTemplate containing the subject and body according to the
						 * prefred language of the Advisor //getting the preferred language of the
						 * contact recipient.setName(rec.getFullName()); recipient.setContact(rec);
						 * 
						 * 
						 * 
						 * if (email != null) { recipient.setAddress(email.getEmailAddress()); }
						 * 
						 * recipient.setEmailType(20); // Document Sending
						 * 
						 * //getting the web content if (sendEmailBool) { if (rec.getPreferredLanguage()
						 * == Constants.Language_Type.French.getCode()) {
						 * //emailManager.sendEmailWithAttachments(recipient.getAddress(),
						 * recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
						 * attachments); title = content.getContentTitleFr(); message =
						 * content.getContentFr(); } else {
						 * //emailManager.sendEmailWithAttachments(recipient.getAddress(),
						 * recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
						 * attachments); title = content.getContentTitleEn(); message =
						 * content.getContentEn(); } }
						 * 
						 * logActivity(recipient, attachs); } } } for (Contact rec :
						 * ps.getOrganization().getContactList()) { if (rec.canRecieveDocuments()) {
						 * 
						 * cont++;
						 * 
						 * Email email = rec.getDefaultEmail(); //System.out.println("email: " +
						 * email.getEmailAddress());
						 * 
						 * recipient = new Recipient();
						 * 
						 * // reply to
						 * recipient.setReplyTo(applicationBean.getUsers().getContact().getDefaultEmail(
						 * ).getEmailAddress());
						 * 
						 * //Creating the emailTemplate containing the subject and body according to the
						 * prefred language of the Advisor //getting the preferred language of the
						 * contact recipient.setName(rec.getFullName()); recipient.setContact(rec);
						 * 
						 * 
						 * 
						 * if (email != null) { recipient.setAddress(email.getEmailAddress()); }
						 * 
						 * recipient.setEmailType(20); // Document Sending
						 * 
						 * //getting the web content if (sendEmailBool) { if (rec.getPreferredLanguage()
						 * == Constants.Language_Type.French.getCode()) {
						 * //emailManager.sendEmailWithAttachments(recipient.getAddress(),
						 * recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
						 * attachments); title = content.getContentTitleFr(); message =
						 * content.getContentFr(); } else {
						 * //emailManager.sendEmailWithAttachments(recipient.getAddress(),
						 * recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
						 * attachments); title = content.getContentTitleEn(); message =
						 * content.getContentEn(); } }
						 * 
						 * logActivity(recipient, attachs); } } } } } else { // only mga/aga for
						 * (ProductSupplier ps : productSuppliers) { if
						 * (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier) &&
						 * ps.getContractAttachedProductSupplierMga() != null) { for (Contact rec :
						 * ps.getContractAttachedProductSupplierMga().getOrganization().getContactList()
						 * ) { if (rec.canRecieveDocuments()) { cont++;
						 * 
						 * Email email = rec.getDefaultEmail(); //System.out.println("email: " +
						 * email.getEmailAddress());
						 * 
						 * recipient = new Recipient();
						 * 
						 * // reply to
						 * recipient.setReplyTo(applicationBean.getUsers().getContact().getDefaultEmail(
						 * ).getEmailAddress());
						 * 
						 * //Creating the emailTemplate containing the subject and body according to the
						 * prefred language of the Advisor //getting the preferred language of the
						 * contact recipient.setName(rec.getFullName()); recipient.setContact(rec);
						 * 
						 * 
						 * if (email != null) { recipient.setAddress(email.getEmailAddress()); }
						 * 
						 * recipient.setEmailType(20); // Document Sending
						 * 
						 * //getting the web content if (sendEmailBool) { if (rec.getPreferredLanguage()
						 * == Constants.Language_Type.French.getCode()) {
						 * //emailManager.sendEmailWithAttachments(recipient.getAddress(),
						 * recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
						 * attachments); title = content.getContentTitleFr(); message =
						 * content.getContentFr(); } else {
						 * //emailManager.sendEmailWithAttachments(recipient.getAddress(),
						 * recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
						 * attachments); title = content.getContentTitleEn(); message =
						 * content.getContentEn(); } } logActivity(recipient, attachs); } } } } } } else
						 * { FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error",
						 * applicationBean.getLanguage().getString("No.Companies.Available"));
						 * FacesContext.getCurrentInstance().addMessage(null, msg); return;
						 */
					}
				}
			}

			if (masterMode || toolMaster) {
				content = contentFacade.findWebContentById(65372);
				HashMap<String, String> privateDoc = new HashMap<>();
				HttpServletRequest origRequest = (HttpServletRequest) FacesContext.getCurrentInstance()
						.getExternalContext().getRequest();
				privateDoc.put("#CREATION_DATE#", Calendar.getInstance().getTime().toString());
				privateDoc.put("#YEAR#", Integer.toString(Calendar.getInstance().get(Calendar.YEAR)));

				StringBuffer url = origRequest.getRequestURL();
				String uri = origRequest.getRequestURI();
				String ctx = origRequest.getContextPath();
				String base = url.substring(0, url.length() - uri.length() + ctx.length()) + "/";

				privateDoc.put("#URL#", base);
				privateDoc.put("#FROM#", applicationBean.getUsers().getContact().getFullName());
				privateDoc.put("#sender_email_address#",
						applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

				if (applicationBean.getUsers().getProfileTool() == null) {
					privateDoc.put("#PROFILE_SHORT_NAME#", "InsurFact");
				} else {
					privateDoc.put("#PROFILE_SHORT_NAME#", applicationBean.getUsers().getProfileTool().getAppName());
				}

				String defaultEmailBodyFR = substitueKeyValues(privateDoc, content.getContentFr());

				String defaultEmailBodyEN = substitueKeyValues(privateDoc, content.getContentEn());

				if (sendAll) {
					for (AddressBook rec : allAddressBooks) {
						if (rec.getContact() != null) {
							privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getContact().getFullName());
							if (rec.getContact().getUsers() != null) {
								privateDoc.put("#USERNAME#", rec.getContact().getUsers().getUsername());
							}
							if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						} else {
							if (applicationBean.getLanguage().isFrench()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						}
						String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
						String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleFr(),
									contentFr);
						} else {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleEn(),
									contentEn);
						}

					}
				} else {
					for (AddressBook rec : toSend) {
						// if (rec.isSelected()) {
						if (rec.getContact() != null) {
							privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getContact().getFullName());
							if (rec.getContact().getUsers() != null) {
								privateDoc.put("#USERNAME#", rec.getContact().getUsers().getUsername());
							}
							if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						} else {
							if (applicationBean.getLanguage().isFrench()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						}
						String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
						String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleFr(),
									contentFr);
						} else {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleEn(),
									contentEn);
						}

						// reset the selections
						rec.setSelected(false);
						// }

					}
					// shared ones
					if (toSend.size() > 1) {
						for (ProductSupplier ps : suppliersSelected) {
							if (ps.isSelected()) {
								for (Contact rec : ps.getOrganization().getContactList()) {
									if (rec.canRecieveDocuments()) {

										privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
										if (rec.getUsers() != null) {
											privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
										}
										if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
											privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
													+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
										} else {
											privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
													+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
										}

										String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
										String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

										if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
											emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
													applicationBean.getUsers().getContact().getDefaultEmail()
															.getEmailAddress(),
													applicationBean.getUsers().getContact().getFullName(),
													content.getContentTitleFr(), contentFr);
										} else {
											emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
													applicationBean.getUsers().getContact().getDefaultEmail()
															.getEmailAddress(),
													applicationBean.getUsers().getContact().getFullName(),
													content.getContentTitleEn(), contentEn);
										}
									}
								}
							}
						}
					} else if (toSend.size() == 1) {

						if (toSend.get(0).getMga() != null && toSend.get(0).getMga().isSelected()) {
							for (Contact rec : toSend.get(0).getMga().getOrganization().getContactList()) {
								if (rec.canRecieveDocuments()) {

									privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
									if (rec.getUsers() != null) {
										privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
									}
									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
												+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
									} else {
										privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
												+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
									}

									String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
									String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
												applicationBean.getUsers().getContact().getDefaultEmail()
														.getEmailAddress(),
												applicationBean.getUsers().getContact().getFullName(),
												content.getContentTitleFr(), contentFr);
									} else {
										emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
												applicationBean.getUsers().getContact().getDefaultEmail()
														.getEmailAddress(),
												applicationBean.getUsers().getContact().getFullName(),
												content.getContentTitleEn(), contentEn);
									}
								}
							}

						}

						if (toSend.get(0).getAga() != null && toSend.get(0).getAga().isSelected()) {
							for (Contact rec : toSend.get(0).getAga().getOrganization().getContactList()) {
								if (rec.canRecieveDocuments()) {

									privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
									if (rec.getUsers() != null) {
										privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
									}
									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
												+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
									} else {
										privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
												+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
									}

									String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
									String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
												applicationBean.getUsers().getContact().getDefaultEmail()
														.getEmailAddress(),
												applicationBean.getUsers().getContact().getFullName(),
												content.getContentTitleFr(), contentFr);
									} else {
										emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
												applicationBean.getUsers().getContact().getDefaultEmail()
														.getEmailAddress(),
												applicationBean.getUsers().getContact().getFullName(),
												content.getContentTitleEn(), contentEn);
									}
								}
							}

						}

						if (toSend.get(0).getAga2() != null && toSend.get(0).getAga2().isSelected()) {
							for (Contact rec : toSend.get(0).getAga2().getOrganization().getContactList()) {
								if (rec.canRecieveDocuments()) {

									privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
									if (rec.getUsers() != null) {
										privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
									}
									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
												+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
									} else {
										privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
												+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
									}

									String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
									String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
												applicationBean.getUsers().getContact().getDefaultEmail()
														.getEmailAddress(),
												applicationBean.getUsers().getContact().getFullName(),
												content.getContentTitleFr(), contentFr);
									} else {
										emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
												applicationBean.getUsers().getContact().getDefaultEmail()
														.getEmailAddress(),
												applicationBean.getUsers().getContact().getFullName(),
												content.getContentTitleEn(), contentEn);
									}
								}
							}
						}

					}
				}

			} else {
				content = contentFacade.findWebContentById(65372);
				HashMap<String, String> privateDoc = new HashMap<>();
				HttpServletRequest origRequest = (HttpServletRequest) FacesContext.getCurrentInstance()
						.getExternalContext().getRequest();
				privateDoc.put("#CREATION_DATE#", Calendar.getInstance().getTime().toString());
				privateDoc.put("#YEAR#", Integer.toString(Calendar.getInstance().get(Calendar.YEAR)));

				StringBuffer url = origRequest.getRequestURL();
				String uri = origRequest.getRequestURI();
				String ctx = origRequest.getContextPath();
				String base = url.substring(0, url.length() - uri.length() + ctx.length()) + "/";

				privateDoc.put("#URL#", base);
				privateDoc.put("#FROM#", applicationBean.getUsers().getContact().getFullName());
				privateDoc.put("#sender_email_address#",
						applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

				if (applicationBean.getUsers().getProfileTool() == null) {
					privateDoc.put("#PROFILE_SHORT_NAME#", "InsurFact");
				} else {
					privateDoc.put("#PROFILE_SHORT_NAME#", applicationBean.getUsers().getProfileTool().getAppName());
				}

				String defaultEmailBodyFR = substitueKeyValues(privateDoc, content.getContentFr());

				String defaultEmailBodyEN = substitueKeyValues(privateDoc, content.getContentEn());

				// if (toogle == 1) {//company
				for (Contact rec : toSendContacts) {
					// if (rec.isSelected()) {
					privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
					if (rec.getUsers() != null) {
						privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
					}
					if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
						privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
								+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
					} else {
						privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
								+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
					}
					String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
					String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

					if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
						emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
								applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
								applicationBean.getUsers().getContact().getFullName(), content.getContentTitleFr(),
								contentFr);
					} else {
						emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
								applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
								applicationBean.getUsers().getContact().getFullName(), content.getContentTitleEn(),
								contentEn);
					}
					// }
				}
				/*
				 * } else if (toogle == 2) {//both for (ProductSupplier ps : productSuppliers) {
				 * if (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier)) {
				 * if (ps.getContractAttachedProductSupplierMga() != null) { for (Contact rec :
				 * ps.getContractAttachedProductSupplierMga().getOrganization().getContactList()
				 * ) { if (rec.canRecieveDocuments()) { privateDoc.put("#ADVISOR_FIRSTNAME#",
				 * rec.getFullName()); if (rec.getUsers() != null) {
				 * privateDoc.put("#USERNAME#", rec.getUsers().getUsername()); } if
				 * (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
				 * privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " (" +
				 * typesFacade.find(selectedDocumentType).getDescFr() + ")"); } else {
				 * privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " (" +
				 * typesFacade.find(selectedDocumentType).getDescEn() + ")"); } String contentEn
				 * = substitueKeyValues(privateDoc, defaultEmailBodyEN); String contentFr =
				 * substitueKeyValues(privateDoc, defaultEmailBodyFR);
				 * 
				 * if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
				 * emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getFullName(),
				 * content.getContentTitleFr(), contentFr); } else {
				 * emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getFullName(),
				 * content.getContentTitleEn(), contentEn); } } } } for (Contact rec :
				 * ps.getOrganization().getContactList()) { if (rec.canRecieveDocuments()) {
				 * privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName()); if (rec.getUsers()
				 * != null) { privateDoc.put("#USERNAME#", rec.getUsers().getUsername()); } if
				 * (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
				 * privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " (" +
				 * typesFacade.find(selectedDocumentType).getDescFr() + ")"); } else {
				 * privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " (" +
				 * typesFacade.find(selectedDocumentType).getDescEn() + ")"); } String contentEn
				 * = substitueKeyValues(privateDoc, defaultEmailBodyEN); String contentFr =
				 * substitueKeyValues(privateDoc, defaultEmailBodyFR);
				 * 
				 * if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
				 * emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getFullName(),
				 * content.getContentTitleFr(), contentFr); } else {
				 * emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getFullName(),
				 * content.getContentTitleEn(), contentEn); } } } } } } else {//mga/aga for
				 * (ProductSupplier ps : productSuppliers) { if
				 * (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier) &&
				 * ps.getContractAttachedProductSupplierMga() != null) { for (Contact rec :
				 * ps.getContractAttachedProductSupplierMga().getOrganization().getContactList()
				 * ) { if (rec.canRecieveDocuments()) { privateDoc.put("#ADVISOR_FIRSTNAME#",
				 * rec.getFullName()); if (rec.getUsers() != null) {
				 * privateDoc.put("#USERNAME#", rec.getUsers().getUsername()); } if
				 * (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
				 * privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " (" +
				 * typesFacade.find(selectedDocumentType).getDescFr() + ")"); } else {
				 * privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " (" +
				 * typesFacade.find(selectedDocumentType).getDescEn() + ")"); } String contentEn
				 * = substitueKeyValues(privateDoc, defaultEmailBodyEN); String contentFr =
				 * substitueKeyValues(privateDoc, defaultEmailBodyFR);
				 * 
				 * if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
				 * emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getFullName(),
				 * content.getContentTitleFr(), contentFr); } else {
				 * emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
				 * applicationBean.getUsers().getContact().getFullName(),
				 * content.getContentTitleEn(), contentEn); } } } } } }
				 */

			}
			if (applicationBean.getLanguage().getLocale() == Locale.CANADA) {
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Success",
						"Your have sent " + cont + " emails");
				FacesContext.getCurrentInstance().addMessage(null, msg);
			} else {
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Successfr",
						"Your have sent " + cont + " emailsfr");
				FacesContext.getCurrentInstance().addMessage(null, msg);
			}

			enableLists = false;
			toSend.clear();
			suppliersSelected.clear();

		} catch (Exception ex) {

			ex.printStackTrace();

			FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error",
					"An Error occured during the send of your email please contact the system administrator or try again in a few minutes.");
			FacesContext.getCurrentInstance().addMessage(null, msg);

		}
	}

	/**
	 * send email to SMTP server
	 */
	public void sendEmailNew() {
		// System.out.println("start the send of the email");
		attachments.clear();
		if (uploadedFile != null && uploadedFile.getContent() != null && uploadedFile.getContent().length > 0) {
			if (!getDocumentMimeType(uploadedFile).equals("application/pdf")
					&& !getDocumentMimeType(uploadedFile).equals("image/jpeg")) {
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR,
						applicationBean.getLanguage().getString("Error"),
						applicationBean.getLanguage().getString("File.Format.Error"));
				FacesContext.getCurrentInstance().addMessage(null, msg);
				return;
			}

			Attachment att = new Attachment(uploadedFile.getFileName(), getDocumentMimeType(uploadedFile),
					uploadedFile.getContent());
			attachments.add(att);
		}

		WebContent content = null;

		int cont = 0;

		if (reply) {
			content = contentFacade.findWebContentById(1068);
		} else {
			content = contentFacade.findWebContentById(65351);
		}

		// replace the content
		HashMap<String, String> privateDocSec = new HashMap<>();
		privateDocSec.put("#CREATION_DATE#", Calendar.getInstance().getTime().toString());
		privateDocSec.put("#YEAR#", Integer.toString(Calendar.getInstance().get(Calendar.YEAR)));
		privateDocSec.put("#FROM#", applicationBean.getUsers().getContact().getFullName());

		Types selectedType = new Types();

		for (Types ty : typesFacade.findTypesByTypeClass(typeClassFacade.find(66417))) {
			if (Objects.equals(ty.getSubType(), selectedTopic)) {
				selectedType = ty;
			}
		}

		String defaultEmailBodySecFR = substitueKeyValues(privateDocSec, content.getContentFr());

		String defaultEmailBodySecEN = substitueKeyValues(privateDocSec, content.getContentEn());

		List<StoredFile> attachs = new ArrayList<>();
		StoredFileDesc fileDes;
		StoredFile fileItem;
		for (Attachment atts : attachments) {
			fileItem = new StoredFile();
			fileItem.setCreatedBy(applicationBean.getUsers());
			fileItem.setCreatedFrom("Messaging");
			fileItem.setCreationDate(Calendar.getInstance().getTime());
			fileItem.setName(atts.getFileName());
			storedFileFacade.create(fileItem);
			fileDes = new StoredFileDesc();
			fileDes.setFileEn(atts.getAttachmentByteArray());
			// fileDes.setStoredFile(fileItem);
			fileDes.setStoredFileIntId(fileItem.getStoredFileIntId());
			// fileItem.setStoredFileDesc(fileDes);
			storedFileDescFacade.create(fileDes);
			// storedFileFacade.edit(fileItem);
			attachs.add(fileItem);

		}

		try {

			// String from = "<EMAIL>"; //
			// advisorNavigationBean.getSelectedAdvisor().getContact().getDefaultEmail().getEmailAddress();

//            boolean updateEntity = false;
			// boolean emailSent = false;

			// Send Head Office
			if (masterMode || toolMaster) {
				if (toogle == 1) { // advisor
					for (AddressBook rec : toSend) {
						// if (rec.isSelected()) {

						cont++;

						Email email = new Email();
						// System.out.println("email: " + email.getEmailAddress());
						if (rec.getContact() != null) {
							email = rec.getContact().getDefaultEmail();
						}

						recipient = new Recipient();

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							privateDocSec.put("#SUBJECT#", selectedType.getDescFr() + " ("
									+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
						} else {
							privateDocSec.put("#SUBJECT#", selectedType.getDescEn() + " ("
									+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
						}

						String contentEn = substitueKeyValues(privateDocSec, defaultEmailBodySecEN);
						String contentFr = substitueKeyValues(privateDocSec, defaultEmailBodySecFR);

						// reply to
						recipient.setReplyTo(
								applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

						// Creating the emailTemplate containing the subject and body according to the
						// prefred language of the Advisor
						// getting the preferred language of the contact
						if (rec.getContact() != null) {
							recipient.setName(rec.getContact().getFullName());
							recipient.setContact(rec.getContact());
						} else {
							recipient.setName(rec.getFirstname() + " " + rec.getLastname());
						}

						/*
						 * if (contact.getDepartment() != null) {
						 * recipient.setDepartment(contact.getDepartment()); }
						 */
						if (email.getEmailIntId() != null) {
							recipient.setAddress(email.getEmailAddress());
						} else {
							rec.getAddressEmail();
						}

						recipient.setEmailType(20); // Document Sending

						// getting the web content
						if (sendEmailBool) {
							if (rec.getContact() != null) {
								if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French
										.getCode()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							} else {
								if (applicationBean.getLanguage().isFrench()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							}
						}

						logActivity(recipient, attachs);

						// }
					}

					if (toSend.size() == 1) {

						for (ProductSupplier ps : getTreeContractListAdvisor()) {
							for (Contact rec : ps.getDocumentRecievers()) {
								if (rec.isSelected()) {
									cont++;

									Email email = rec.getDefaultEmail();
									// System.out.println("email: " + email.getEmailAddress());

									recipient = new Recipient();

									// reply to
									recipient.setReplyTo(applicationBean.getUsers().getContact().getDefaultEmail()
											.getEmailAddress());

									// Creating the emailTemplate containing the subject and body according to the
									// prefred language of the Advisor
									// getting the preferred language of the contact
									recipient.setName(rec.getFullName());
									recipient.setContact(rec);

									if (email != null) {
										recipient.setAddress(email.getEmailAddress());
									}

									recipient.setEmailType(20); // Document Sending

									// getting the web content
									if (sendEmailBool) {
										if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
											// emailManager.sendEmailWithAttachments(recipient.getAddress(),
											// recipient.getReplyTo(), content.getContentTitleFr(),
											// content.getContentFr(), attachments);
											title = content.getContentTitleFr();
											message = content.getContentFr();
										} else {
											// emailManager.sendEmailWithAttachments(recipient.getAddress(),
											// recipient.getReplyTo(), content.getContentTitleEn(),
											// content.getContentEn(), attachments);
											title = content.getContentTitleEn();
											message = content.getContentEn();
										}
									}
									logActivity(recipient, attachs);
								}
							}

						}

					}

				} else if (toogle == 2) { // agency
					for (Contact rec : toSendContacts) {
						if (rec.canRecieveDocuments()) {
							cont++;

							Email email = rec.getDefaultEmail();
							// System.out.println("email: " + email.getEmailAddress());

							recipient = new Recipient();

							// reply to
							recipient.setReplyTo(
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

							// Creating the emailTemplate containing the subject and body according to the
							// prefred language of the Advisor
							// getting the preferred language of the contact
							recipient.setName(rec.getFullName());
							recipient.setContact(rec);

							if (email != null) {
								recipient.setAddress(email.getEmailAddress());
							}

							recipient.setEmailType(20); // Document Sending

							// getting the web content
							if (sendEmailBool) {
								if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = content.getContentFr();
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = content.getContentEn();
								}
							}
							logActivity(recipient, attachs);
						}
					}
				} else if (toogle == 3) { // all advisors
					for (AddressBook rec : allAddressBooks) {

						cont++;

						Email email = new Email();
						// System.out.println("email: " + email.getEmailAddress());
						if (rec.getContact() != null) {
							email = rec.getContact().getDefaultEmail();
						}

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							privateDocSec.put("#SUBJECT#", selectedType.getDescFr() + " ("
									+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
						} else {
							privateDocSec.put("#SUBJECT#", selectedType.getDescEn() + " ("
									+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
						}

						String contentEn = substitueKeyValues(privateDocSec, defaultEmailBodySecEN);
						String contentFr = substitueKeyValues(privateDocSec, defaultEmailBodySecFR);

						recipient = new Recipient();

						// reply to
						recipient.setReplyTo(
								applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

						// Creating the emailTemplate containing the subject and body according to the
						// prefred language of the Advisor
						// getting the preferred language of the contact
						if (rec.getContact() != null) {
							recipient.setName(rec.getContact().getFullName());
							recipient.setContact(rec.getContact());
						} else {
							recipient.setName(rec.getFirstname() + " " + rec.getLastname());
						}

						/*
						 * if (contact.getDepartment() != null) {
						 * recipient.setDepartment(contact.getDepartment()); }
						 */
						if (email.getEmailIntId() != null) {
							recipient.setAddress(email.getEmailAddress());
						} else {
							rec.getAddressEmail();
						}

						recipient.setEmailType(20); // Document Sending

						// getting the web content
						if (sendEmailBool) {
							if (rec.getContact() != null) {
								if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French
										.getCode()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							} else {
								if (applicationBean.getLanguage().isFrench()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							}
						}

						logActivity(recipient, attachs);

					}
				} else if (toogle == 4) { // all agencies
					for (ProductSupplier ps : agenciesList) {
						for (Contact rec : ps.getOrganization().getContactList()) {
							if (rec.canRecieveDocuments()) {
								cont++;

								Email email = rec.getDefaultEmail();
								// System.out.println("email: " + email.getEmailAddress());

								recipient = new Recipient();

								// reply to
								recipient.setReplyTo(
										applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

								// Creating the emailTemplate containing the subject and body according to the
								// prefred language of the Advisor
								// getting the preferred language of the contact
								recipient.setName(rec.getFullName());
								recipient.setContact(rec);

								if (email != null) {
									recipient.setAddress(email.getEmailAddress());
								}

								recipient.setEmailType(20); // Document Sending

								// getting the web content
								if (sendEmailBool) {
									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										// emailManager.sendEmailWithAttachments(recipient.getAddress(),
										// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
										// attachments);
										title = content.getContentTitleFr();
										message = content.getContentFr();
									} else {
										// emailManager.sendEmailWithAttachments(recipient.getAddress(),
										// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
										// attachments);
										title = content.getContentTitleEn();
										message = content.getContentEn();
									}
								}
								logActivity(recipient, attachs);
							}
						}
					}
				} else { // all all
					for (ProductSupplier ps : agenciesList) {
						for (Contact rec : ps.getOrganization().getContactList()) {
							if (rec.canRecieveDocuments()) {
								cont++;

								Email email = rec.getDefaultEmail();
								// System.out.println("email: " + email.getEmailAddress());

								recipient = new Recipient();

								// reply to
								recipient.setReplyTo(
										applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

								// Creating the emailTemplate containing the subject and body according to the
								// prefred language of the Advisor
								// getting the preferred language of the contact
								recipient.setName(rec.getFullName());
								recipient.setContact(rec);

								if (email != null) {
									recipient.setAddress(email.getEmailAddress());
								}

								recipient.setEmailType(20); // Document Sending

								// getting the web content
								if (sendEmailBool) {
									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										// emailManager.sendEmailWithAttachments(recipient.getAddress(),
										// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
										// attachments);
										title = content.getContentTitleFr();
										message = content.getContentFr();
									} else {
										// emailManager.sendEmailWithAttachments(recipient.getAddress(),
										// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
										// attachments);
										title = content.getContentTitleEn();
										message = content.getContentEn();
									}
								}
								logActivity(recipient, attachs);
							}
						}
					}

					for (AddressBook rec : allAddressBooks) {

						cont++;

						Email email = new Email();
						// System.out.println("email: " + email.getEmailAddress());
						if (rec.getContact() != null) {
							email = rec.getContact().getDefaultEmail();
						}

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							privateDocSec.put("#SUBJECT#", selectedType.getDescFr() + " ("
									+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
						} else {
							privateDocSec.put("#SUBJECT#", selectedType.getDescEn() + " ("
									+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
						}

						String contentEn = substitueKeyValues(privateDocSec, defaultEmailBodySecEN);
						String contentFr = substitueKeyValues(privateDocSec, defaultEmailBodySecFR);

						recipient = new Recipient();

						// reply to
						recipient.setReplyTo(
								applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

						// Creating the emailTemplate containing the subject and body according to the
						// prefred language of the Advisor
						// getting the preferred language of the contact
						if (rec.getContact() != null) {
							recipient.setName(rec.getContact().getFullName());
							recipient.setContact(rec.getContact());
						} else {
							recipient.setName(rec.getFirstname() + " " + rec.getLastname());
						}

						/*
						 * if (contact.getDepartment() != null) {
						 * recipient.setDepartment(contact.getDepartment()); }
						 */
						if (email.getEmailIntId() != null) {
							recipient.setAddress(email.getEmailAddress());
						} else {
							rec.getAddressEmail();
						}

						recipient.setEmailType(20); // Document Sending

						// getting the web content
						if (sendEmailBool) {
							if (rec.getContact() != null) {
								if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French
										.getCode()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							} else {
								if (applicationBean.getLanguage().isFrench()) {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
									// attachments);
									title = content.getContentTitleFr();
									message = contentFr;
								} else {
									// emailManager.sendEmailWithAttachments(recipient.getAddress(),
									// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
									// attachments);
									title = content.getContentTitleEn();
									message = contentEn;
								}
							}
						}

						logActivity(recipient, attachs);

					}
				}
			} else {
				if (selectedProductSuplier != null) {

					for (ProductSupplier ps : treeContractListElement) {
						for (Contact rec : ps.getDocumentRecievers()) {
							if (rec.isSelected()) {

								cont++;

								Email email = rec.getDefaultEmail();
								// System.out.println("email: " + email.getEmailAddress());

								recipient = new Recipient();

								if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
									privateDocSec.put("#SUBJECT#", selectedType.getDescFr() + " ("
											+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
								} else {
									privateDocSec.put("#SUBJECT#", selectedType.getDescEn() + " ("
											+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
								}

								String contentEn = substitueKeyValues(privateDocSec, defaultEmailBodySecEN);
								String contentFr = substitueKeyValues(privateDocSec, defaultEmailBodySecFR);

								// reply to
								recipient.setReplyTo(
										applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

								// Creating the emailTemplate containing the subject and body according to the
								// prefred language of the Advisor
								// getting the preferred language of the contact
								recipient.setName(rec.getFullName());
								recipient.setContact(rec);

								/*
								 * if (contact.getDepartment() != null) {
								 * recipient.setDepartment(contact.getDepartment()); }
								 */
								if (email != null) {
									recipient.setAddress(email.getEmailAddress());
								}

								recipient.setEmailType(20); // Document Sending

								// getting the web content
								if (sendEmailBool) {
									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										// emailManager.sendEmailWithAttachments(recipient.getAddress(),
										// recipient.getReplyTo(), content.getContentTitleFr(), content.getContentFr(),
										// attachments);
										title = content.getContentTitleFr();
										message = contentFr;
									} else {
										// emailManager.sendEmailWithAttachments(recipient.getAddress(),
										// recipient.getReplyTo(), content.getContentTitleEn(), content.getContentEn(),
										// attachments);
										title = content.getContentTitleEn();
										message = contentEn;
									}
								}

								logActivity(recipient, attachs);
							}
						}
					}

				} else {
					FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error",
							applicationBean.getLanguage().getString("No.Companies.Available"));
					FacesContext.getCurrentInstance().addMessage(null, msg);
					return;
				}
			}

			if (masterMode || toolMaster) {
				content = contentFacade.findWebContentById(65372);
				HashMap<String, String> privateDoc = new HashMap<>();
				HttpServletRequest origRequest = (HttpServletRequest) FacesContext.getCurrentInstance()
						.getExternalContext().getRequest();
				privateDoc.put("#CREATION_DATE#", Calendar.getInstance().getTime().toString());
				privateDoc.put("#YEAR#", Integer.toString(Calendar.getInstance().get(Calendar.YEAR)));

				StringBuffer url = origRequest.getRequestURL();
				String uri = origRequest.getRequestURI();
				String ctx = origRequest.getContextPath();
				String base = url.substring(0, url.length() - uri.length() + ctx.length()) + "/";

				privateDoc.put("#URL#", base);
				privateDoc.put("#FROM#", applicationBean.getUsers().getContact().getFullName());
				privateDoc.put("#sender_email_address#",
						applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

				if (applicationBean.getUsers().getProfileTool() == null) {
					privateDoc.put("#PROFILE_SHORT_NAME#", "InsurFact");
				} else {
					privateDoc.put("#PROFILE_SHORT_NAME#", applicationBean.getUsers().getProfileTool().getAppName());
				}

				String defaultEmailBodyFR = substitueKeyValues(privateDoc, content.getContentFr());

				String defaultEmailBodyEN = substitueKeyValues(privateDoc, content.getContentEn());

				if (toogle == 1) {// advisor
					for (AddressBook rec : toSend) {
						// if (rec.isSelected()) {
						if (rec.getContact() != null) {
							privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getContact().getFullName());
							if (rec.getContact().getUsers() != null) {
								privateDoc.put("#USERNAME#", rec.getContact().getUsers().getUsername());
							}
							if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						} else {
							if (applicationBean.getLanguage().isFrench()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						}
						String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
						String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleFr(),
									contentFr);
						} else {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleEn(),
									contentEn);
						}

						// reset the selections
						rec.setSelected(false);
						// }

					}
					if (toSend.size() == 1) {

						for (ProductSupplier ps : getTreeContractListAdvisor()) {
							for (Contact rec : ps.getDocumentRecievers()) {
								if (rec.isSelected()) {

									privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
									if (rec.getUsers() != null) {
										privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
									}
									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
												+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
									} else {
										privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
												+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
									}

									String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
									String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

									if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
										emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
												applicationBean.getUsers().getContact().getDefaultEmail()
														.getEmailAddress(),
												applicationBean.getUsers().getContact().getFullName(),
												content.getContentTitleFr(), contentFr);
									} else {
										emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
												applicationBean.getUsers().getContact().getDefaultEmail()
														.getEmailAddress(),
												applicationBean.getUsers().getContact().getFullName(),
												content.getContentTitleEn(), contentEn);
									}
								}
							}

						}

					}
				} else if (toogle == 2) {// agency
					for (Contact rec : toSendContacts) {
						if (rec.canRecieveDocuments()) {

							privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
							if (rec.getUsers() != null) {
								privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
							}
							if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}

							String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
							String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

							if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
										applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
										applicationBean.getUsers().getContact().getFullName(),
										content.getContentTitleFr(), contentFr);
							} else {
								emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
										applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
										applicationBean.getUsers().getContact().getFullName(),
										content.getContentTitleEn(), contentEn);
							}
						}
					}
				} else if (toogle == 3) { // all advisor
					for (AddressBook rec : allAddressBooks) {
						if (rec.getContact() != null) {
							privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getContact().getFullName());
							if (rec.getContact().getUsers() != null) {
								privateDoc.put("#USERNAME#", rec.getContact().getUsers().getUsername());
							}
							if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						} else {
							if (applicationBean.getLanguage().isFrench()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						}
						String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
						String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleFr(),
									contentFr);
						} else {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleEn(),
									contentEn);
						}

					}
				} else if (toogle == 4) {// all agency
					for (ProductSupplier ps : agenciesList) {
						for (Contact rec : ps.getOrganization().getContactList()) {
							if (rec.canRecieveDocuments()) {

								privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
								if (rec.getUsers() != null) {
									privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
								}
								if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
									privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
											+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
								} else {
									privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
											+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
								}

								String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
								String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

								if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
									emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
											applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
											applicationBean.getUsers().getContact().getFullName(),
											content.getContentTitleFr(), contentFr);
								} else {
									emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
											applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
											applicationBean.getUsers().getContact().getFullName(),
											content.getContentTitleEn(), contentEn);
								}
							}
						}
					}

				} else if (toogle == 5) {// all all
					for (ProductSupplier ps : agenciesList) {
						for (Contact rec : ps.getOrganization().getContactList()) {
							if (rec.canRecieveDocuments()) {

								privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
								if (rec.getUsers() != null) {
									privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
								}
								if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
									privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
											+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
								} else {
									privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
											+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
								}

								String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
								String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

								if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
									emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
											applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
											applicationBean.getUsers().getContact().getFullName(),
											content.getContentTitleFr(), contentFr);
								} else {
									emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
											applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
											applicationBean.getUsers().getContact().getFullName(),
											content.getContentTitleEn(), contentEn);
								}
							}
						}
					}
					for (AddressBook rec : allAddressBooks) {
						if (rec.getContact() != null) {
							privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getContact().getFullName());
							if (rec.getContact().getUsers() != null) {
								privateDoc.put("#USERNAME#", rec.getContact().getUsers().getUsername());
							}
							if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						} else {
							if (applicationBean.getLanguage().isFrench()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
						}
						String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
						String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

						if (rec.getContact().getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleFr(),
									contentFr);
						} else {
							emailManager.sendEmailAsHtml(rec.getAddressEmail(),
									applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
									applicationBean.getUsers().getContact().getFullName(), content.getContentTitleEn(),
									contentEn);
						}

					}
				}

			} else {
				content = contentFacade.findWebContentById(65372);
				HashMap<String, String> privateDoc = new HashMap<>();
				HttpServletRequest origRequest = (HttpServletRequest) FacesContext.getCurrentInstance()
						.getExternalContext().getRequest();
				privateDoc.put("#CREATION_DATE#", Calendar.getInstance().getTime().toString());
				privateDoc.put("#YEAR#", Integer.toString(Calendar.getInstance().get(Calendar.YEAR)));

				StringBuffer url = origRequest.getRequestURL();
				String uri = origRequest.getRequestURI();
				String ctx = origRequest.getContextPath();
				String base = url.substring(0, url.length() - uri.length() + ctx.length()) + "/";

				privateDoc.put("#URL#", base);
				privateDoc.put("#FROM#", applicationBean.getUsers().getContact().getFullName());
				privateDoc.put("#sender_email_address#",
						applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress());

				if (applicationBean.getUsers().getProfileTool() == null) {
					privateDoc.put("#PROFILE_SHORT_NAME#", "InsurFact");
				} else {
					privateDoc.put("#PROFILE_SHORT_NAME#", applicationBean.getUsers().getProfileTool().getAppName());
				}

				String defaultEmailBodyFR = substitueKeyValues(privateDoc, content.getContentFr());

				String defaultEmailBodyEN = substitueKeyValues(privateDoc, content.getContentEn());

				for (ProductSupplier ps : treeContractListElement) {
					for (Contact rec : ps.getDocumentRecievers()) {
						if (rec.isSelected()) {
							privateDoc.put("#ADVISOR_FIRSTNAME#", rec.getFullName());
							if (rec.getUsers() != null) {
								privateDoc.put("#USERNAME#", rec.getUsers().getUsername());
							}
							if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								privateDoc.put("#SUBJECT#", selectedType.getDescFr() + " ("
										+ typesFacade.find(selectedDocumentType).getDescFr() + ")");
							} else {
								privateDoc.put("#SUBJECT#", selectedType.getDescEn() + " ("
										+ typesFacade.find(selectedDocumentType).getDescEn() + ")");
							}
							String contentEn = substitueKeyValues(privateDoc, defaultEmailBodyEN);
							String contentFr = substitueKeyValues(privateDoc, defaultEmailBodyFR);

							if (rec.getPreferredLanguage() == Constants.Language_Type.French.getCode()) {
								emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
										applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
										applicationBean.getUsers().getContact().getFullName(),
										content.getContentTitleFr(), contentFr);
							} else {
								emailManager.sendEmailAsHtml(rec.getDefaultEmail().getEmailAddress(),
										applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress(),
										applicationBean.getUsers().getContact().getFullName(),
										content.getContentTitleEn(), contentEn);
							}
						}
					}
				}

			}
			if (applicationBean.getLanguage().getLocale() == Locale.CANADA) {
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Success",
						"Your have sent " + cont + " emails");
				FacesContext.getCurrentInstance().addMessage(null, msg);
			} else {
				FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Successfr",
						"Your have sent " + cont + " emailsfr");
				FacesContext.getCurrentInstance().addMessage(null, msg);
			}

			enableLists = false;
			toSend.clear();
			selectedAgency = 0;
			suppliersSelected.clear();
			selectedAgencies.clear();
			contactsAgency.clear();
			contactsAgencyFilter.clear();

		} catch (Exception ex) {

			ex.printStackTrace();

			FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error",
					"An Error occured during the send of your email please contact the system administrator or try again in a few minutes.");
			FacesContext.getCurrentInstance().addMessage(null, msg);

		}
	}

	protected String substitueKeyValues(HashMap<String, String> hashMap, String txt) {

		String newTxt = new String(txt);

		for (String key : hashMap.keySet()) {
			String value = hashMap.get(key);
			if (value == null) {
				value = "";
			}
			newTxt = newTxt.replaceAll(key, value);
		}
		return newTxt;
	}

	private void loadLists() {

		loadAllAdvisors();

		/*
		 * filterAllAdvisorData();
		 * 
		 * Iterator<Advisor> all = allAdvisors.iterator(); while (all.hasNext()) {
		 * Advisor next = all.next(); if ((next.getAllActivityFiltered() == null ||
		 * next.getAllActivityFiltered().isEmpty()) && (next.getActivitiesArchived() ==
		 * null || next.getActivitiesArchived().isEmpty()) &&
		 * (next.getActivitiesRemoved() == null ||
		 * next.getActivitiesRemoved().isEmpty())) { all.remove(); } }
		 */
//        loadContractStatuses();
	}

	private void loadAllAdvisors() {
		allAdvisors.clear();
		allContacts.clear();

		if (masterMode || toolMaster) {
			if (masterMode) {
				allAdvisors = advisorFacade.findAll();
			} else {
				allAdvisors = advisorFacade.findByTool(applicationBean.getUsers().getProfileTool());
			}
		} else {
			for (ContractSetup contract : applicationBean.getUsers().getContact().getAdvisor().getContractSetupList()) {
				productSuppliers.add(contract.getProductSupplier());
				allContacts.addAll(contract.getProductSupplier().getOrganization().getContactList());
			}

			// allAdvisors.add(applicationBean.getUsers().getContact().getAdvisor());
		}

		if (applicationBean.getUsers().getContact().getAdvisor() != null && (masterMode || toolMaster)) {
			allAdvisors.remove(applicationBean.getUsers().getContact().getAdvisor());
		}

	}

	public void resetSearch() {
		advisorNameSearch = null;
		advisorNameSearch2 = null;
		searchBy = 1;
		searchAct = false;
		enableLists = false;
		toSend.clear();
		suppliersSelected.clear();
		allAddressBooks = addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		addressBooks.clear();
		// loadAllAdvisors();
	}

	public void resetSearchBussines() {
		advisorNameSearch = null;
		advisorNameSearch2 = null;
		searchBy = 1;
		searchAct = false;
		enableLists = false;
		toSend.clear();
		suppliersSelected.clear();
		allAddressBooks = addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		Iterator<AddressBook> it = allAddressBooks.iterator();
		while (it.hasNext()) {
			AddressBook next = it.next();
			if (next.getAddressBookType() == null || next.getAddressBookType() != 8) {
				it.remove();
			}
		}
		addressBooks.clear();
		// loadAllAdvisors();
	}

	public void resetSearchNew() {
		advisorNameSearch = null;
		advisorNameSearch2 = null;
		searchAct = false;
		enableLists = false;
		toSend.clear();
		suppliersSelected.clear();
		allAddressBooks = addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		addressBooks.clear();
		// loadAllAdvisors();
	}

	public void resetSearch2() {
		advisorNameSearch = null;
		advisorNameSearch2 = null;
		searchBy = 1;
		searchAct = false;
		enableLists = false;
		toSend.clear();
		suppliersSelected.clear();
		contactsAgencyFilter.clear();
	}

	public void search() {

		// 1 = Advisor Name
		// 2 = Advisor Email Address
		// 3 = Advisor Phone Number
		// 5 = Status
		System.out.println("searchBy: " + searchBy);

		if (advisorNameSearch != null && advisorNameSearch.contains("%")) {

			applicationBean.postWarningMessage("Unsupported characters detected, cannot continue",
					"Rien de valide a été entré, ne peut pas continuer", "", "");
			return;
		}
		searchAct = true;

		switch (searchBy) {

		// 1 = Advisor Name
		case 1: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			String name = advisorNameSearch.trim().toLowerCase();

			String[] names = name.split(" ");

			if (names.length > 2) {
				applicationBean.postWarningMessage("More than two keywords, cannot continue",
						"Plus de deux mots-clés, je ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			for (AddressBook adv : allAddressBooks) {
				if (names.length == 1) {
					if ((adv.getFirstname().toLowerCase() + " " + adv.getLastname().toLowerCase()).contains(name)) {
						addressBooks.add(adv);
					}
				} else {
					if ((adv.getFirstname().toLowerCase().startsWith(names[0])
							&& adv.getLastname().toLowerCase().startsWith(names[1]))) {
						addressBooks.add(adv);
					}
				}
			}

			break;

		}

		// 2 = Advisor Email Address
		case 2: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			String name = advisorNameSearch.toLowerCase();

			for (AddressBook adv : allAddressBooks) {
				if ((adv.getAddressEmail() != null && !adv.getAddressEmail().isEmpty()
						&& adv.getAddressEmail().toLowerCase().startsWith(name)
						|| (adv.getContact() != null && adv.getContact().getDefaultEmail().getEmailAddress()
								.toLowerCase().startsWith(name)))) {
					addressBooks.add(adv);
				}
			}
			break;
		}

		// 3 = Advisor Phone Number
		case 3: {

			addressBooks.clear();

			Integer area = null;
			if (advisorNameSearch2 != null && !advisorNameSearch2.isEmpty()) {
				advisorNameSearch2 = advisorNameSearch2.trim();

				if (advisorNameSearch2.length() != 3) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
				try {
					area = Integer.parseInt(advisorNameSearch2);
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			Integer number = null;
			if (advisorNameSearch != null && !advisorNameSearch.isEmpty()) {

				try {
					number = Integer.parseInt(Phone.cleanupNumber(advisorNameSearch));
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Phone Number, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			String a = null;
			if (area != null) {
				a = area.toString();
			}

			String n = null;
			if (number != null) {
				n = number.toString();
			}

			if (a == null && n == null) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			if (a != null && n == null) {
				applicationBean.postWarningMessage("Cannot Search on Area Code Alone",
						"Vous ne pouvez pas Recherche sur Code régional seulement", "", "");
				return;
			}

			for (AddressBook adv : allAddressBooks) {
				if ((adv.getPhoneNumber() != null && adv.getPhoneNumber().toString().startsWith(a + n))
						|| (adv.getContact() != null && adv.getContact().getDefaultPhone().getAreaCode().equals(a)
								&& adv.getContact().getDefaultPhone().getPhoneNumber().startsWith(n))) {
					addressBooks.add(adv);
				}
			}

			break;
		}

		default: {
			break;
		}

		}

	}

	public void searchContacts() {

		// 1 = Advisor Name
		// 2 = Advisor Email Address
		// 3 = Advisor Phone Number
		// 5 = Status
		System.out.println("searchBy: " + searchBy);

		if (advisorNameSearch != null && advisorNameSearch.contains("%")) {

			applicationBean.postWarningMessage("Unsupported characters detected, cannot continue",
					"Rien de valide a été entré, ne peut pas continuer", "", "");
			return;
		}
		searchAct = true;

		switch (searchBy) {

		// 1 = Advisor Name
		case 1: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			String name = advisorNameSearch.trim().toLowerCase();

			String[] names = name.split(" ");

			if (names.length > 2) {
				applicationBean.postWarningMessage("More than two keywords, cannot continue",
						"Plus de deux mots-clés, je ne peut pas continuer", "", "");
				return;
			}

			contactsAgencyFilter.clear();

			for (Contact adv : contactsAgency) {
				if (names.length == 1) {
					if ((adv.getFirstname().toLowerCase() + " " + adv.getLastname().toLowerCase()).contains(name)) {
						contactsAgencyFilter.add(adv);
					}
				} else {
					if ((adv.getFirstname().toLowerCase().startsWith(names[0])
							&& adv.getLastname().toLowerCase().startsWith(names[1]))) {
						contactsAgencyFilter.add(adv);
					}
				}
			}

			break;

		}

		// 2 = Advisor Email Address
		case 2: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			contactsAgencyFilter.clear();

			String name = advisorNameSearch.toLowerCase();

			for (Contact adv : contactsAgency) {
				if (adv.getDefaultEmail() != null
						&& adv.getDefaultEmail().getEmailAddress().toLowerCase().startsWith(name)) {
					contactsAgencyFilter.add(adv);
				}
			}
			break;
		}

		// 3 = Advisor Phone Number
		case 3: {

			contactsAgencyFilter.clear();

			Integer area = null;
			if (advisorNameSearch2 != null && !advisorNameSearch2.isEmpty()) {
				advisorNameSearch2 = advisorNameSearch2.trim();

				if (advisorNameSearch2.length() != 3) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
				try {
					area = Integer.parseInt(advisorNameSearch2);
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			Integer number = null;
			if (advisorNameSearch != null && !advisorNameSearch.isEmpty()) {

				try {
					number = Integer.parseInt(Phone.cleanupNumber(advisorNameSearch));
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Phone Number, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			String a = null;
			if (area != null) {
				a = area.toString();
			}

			String n = null;
			if (number != null) {
				n = number.toString();
			}

			if (a == null && n == null) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			if (a != null && n == null) {
				applicationBean.postWarningMessage("Cannot Search on Area Code Alone",
						"Vous ne pouvez pas Recherche sur Code régional seulement", "", "");
				return;
			}

			for (Contact adv : contactsAgency) {
				if (adv.getDefaultPhone().getAreaCode().equals(a)
						&& adv.getDefaultPhone().getPhoneNumber().startsWith(n)) {
					contactsAgencyFilter.add(adv);
				}
			}

			break;
		}

		default: {
			break;
		}

		}

	}

	public void resetUploadFile() {
		uploadedFile = null;
		selectedFile = new StoredFile();
	}

	public void searchBookBussines() {

		// 1 = Advisor Name
		// 2 = Advisor Email Address
		// 3 = Advisor Phone Number
		// 5 = Status
		System.out.println("searchBy: " + searchBy);

		if (advisorNameSearch != null && advisorNameSearch.contains("%")) {

			applicationBean.postWarningMessage("Please enter only alphabets", "Veuillez n'entrer que des alphabets", "",
					"");
			return;
		}
		searchAct = true;

		switch (searchBy) {

		// 1 = Advisor Name
		case 1: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			String name = advisorNameSearch.trim().toLowerCase();

			String[] names = name.split(" ");

			if (names.length > 2) {
				applicationBean.postWarningMessage("More than two keywords, cannot continue",
						"Plus de deux mots-clés, je ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if (names.length == 1) {
					if (adv.getFirstname() != null && adv.getLastname() != null) {
						if ((adv.getFirstname().toLowerCase() + " " + adv.getLastname().toLowerCase()).contains(name)) {
							addressBooks.add(adv);
						}
					} else if (adv.getFirstname() != null && adv.getLastname() == null) {
						adv.getFirstname().toLowerCase().contains(name);
					}
				} else {
					if ((adv.getFirstname() != null && adv.getLastname() != null)
							&& ((adv.getFirstname().toLowerCase().startsWith(names[0])
									&& adv.getLastname().toLowerCase().startsWith(names[1])))) {
						addressBooks.add(adv);
					}
				}
			}

			break;

		}

		// 2 = Advisor Email Address
		case 2: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			String name = advisorNameSearch.toLowerCase();

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if ((adv.getAddressEmail() != null && !adv.getAddressEmail().isEmpty()
						&& adv.getAddressEmail().toLowerCase().startsWith(name)
						|| (adv.getContact() != null && adv.getContact().getDefaultEmail().getEmailAddress()
								.toLowerCase().startsWith(name)))) {
					addressBooks.add(adv);
				}
			}
			break;
		}

		// 3 = Advisor Phone Number
		case 3: {

			addressBooks.clear();

			Integer area = null;
			if (advisorNameSearch2 != null && !advisorNameSearch2.isEmpty()) {
				advisorNameSearch2 = advisorNameSearch2.trim();

				if (advisorNameSearch2.length() != 3) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
				try {
					area = Integer.parseInt(advisorNameSearch2);
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			Integer number = null;
			if (advisorNameSearch != null && !advisorNameSearch.isEmpty()) {

				try {
					number = Integer.parseInt(Phone.cleanupNumber(advisorNameSearch));
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Phone Number, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			String a = null;
			if (area != null) {
				a = area.toString();
			}

			String n = null;
			if (number != null) {
				n = number.toString();
			}

			if (a == null && n == null) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			if (a != null && n == null) {
				applicationBean.postWarningMessage("Cannot Search on Area Code Alone",
						"Vous ne pouvez pas Recherche sur Code régional seulement", "", "");
				return;
			}

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if ((adv.getPhoneNumber() != null && adv.getPhoneNumber().toString().startsWith(a + n))
						|| (adv.getContact() != null && adv.getContact().getDefaultPhone().getAreaCode().equals(a)
								&& adv.getContact().getDefaultPhone().getPhoneNumber().startsWith(n))) {
					addressBooks.add(adv);
				}
			}

			break;
		}

		default: {
			break;
		}

		}

		Iterator<AddressBook> it = addressBooks.iterator();
		while (it.hasNext()) {
			AddressBook next = it.next();
			if (next.getAddressBookType() == null || next.getAddressBookType() != 8) {
				it.remove();
			}
		}

	}

	public void searchBook() {

		// 1 = Advisor Name
		// 2 = Advisor Email Address
		// 3 = Advisor Phone Number
		// 5 = Status
		System.out.println("searchBy: " + searchBy);

		if (advisorNameSearch != null && advisorNameSearch.contains("%")) {

			applicationBean.postWarningMessage("Please enter only alphabets", "Veuillez n'entrer que des alphabets", "",
					"");
			return;
		}
		searchAct = true;

		switch (searchBy) {

		// 1 = Advisor Name
		case 1: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			String name = advisorNameSearch.trim().toLowerCase();

			String[] names = name.split(" ");

			if (names.length > 2) {
				applicationBean.postWarningMessage("More than two keywords, cannot continue",
						"Plus de deux mots-clés, je ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if (names.length == 1) {
					if (adv.getFirstname() != null && adv.getLastname() != null) {
						if ((adv.getFirstname().toLowerCase() + " " + adv.getLastname().toLowerCase()).contains(name)) {
							addressBooks.add(adv);
						}
					} else if (adv.getFirstname() != null && adv.getLastname() == null) {
						adv.getFirstname().toLowerCase().contains(name);
					}
				} else {
					if ((adv.getFirstname() != null && adv.getLastname() != null)
							&& ((adv.getFirstname().toLowerCase().startsWith(names[0])
									&& adv.getLastname().toLowerCase().startsWith(names[1])))) {
						addressBooks.add(adv);
					}
				}
			}

			break;

		}

		// 2 = Advisor Email Address
		case 2: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			String name = advisorNameSearch.toLowerCase();

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if ((adv.getAddressEmail() != null && !adv.getAddressEmail().isEmpty()
						&& adv.getAddressEmail().toLowerCase().contains(name)
						|| (adv.getContact() != null && adv.getContact().getDefaultEmail() != null && adv.getContact()
								.getDefaultEmail().getEmailAddress().toLowerCase().contains(name)))) {
					addressBooks.add(adv);
				}
			}
			break;
		}

		// 3 = Advisor Phone Number
		case 3: {

			addressBooks.clear();

			Integer area = null;
			if (advisorNameSearch2 != null && !advisorNameSearch2.isEmpty()) {
				advisorNameSearch2 = advisorNameSearch2.trim();

				if (advisorNameSearch2.length() != 3) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
				try {
					area = Integer.parseInt(advisorNameSearch2);
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			Integer number = null;
			if (advisorNameSearch != null && !advisorNameSearch.isEmpty()) {

				try {
					number = Integer.parseInt(Phone.cleanupNumber(advisorNameSearch));
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Phone Number, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			String a = null;
			if (area != null) {
				a = area.toString();
			}

			String n = null;
			if (number != null) {
				n = number.toString();
			}

			if (a == null && n == null) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			if (a != null && n == null) {
				applicationBean.postWarningMessage("Cannot Search on Area Code Alone",
						"Vous ne pouvez pas Recherche sur Code régional seulement", "", "");
				return;
			}

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if ((adv.getPhoneNumber() != null && adv.getPhoneNumber().toString().startsWith(a + n))
						|| (adv.getContact() != null && adv.getContact().getDefaultPhone().getAreaCode().equals(a)
								&& adv.getContact().getDefaultPhone().getPhoneNumber().startsWith(n))) {
					addressBooks.add(adv);
				}
			}

			break;
		}

		default: {
			break;
		}

		}

	}

	public void searchBookAdvisorOnly() {

		// 1 = Advisor Name
		// 2 = Advisor Email Address
		// 3 = Advisor Phone Number
		// 5 = Status
		System.out.println("searchBy: " + searchBy);

		if (advisorNameSearch != null && advisorNameSearch.contains("%")) {

			applicationBean.postWarningMessage("Please enter only alphabets", "Veuillez n'entrer que des alphabets", "",
					"");
			return;
		}
		searchAct = true;

		switch (searchBy) {

		// 1 = Advisor Name
		case 1: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			String name = advisorNameSearch.trim().toLowerCase();

			String[] names = name.split(" ");

			if (names.length > 2) {
				applicationBean.postWarningMessage("More than two keywords, cannot continue",
						"Plus de deux mots-clés, je ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			for (AddressBook ab : allAddressBooks) {
				if (names.length > 1) {
					if (ab.getContact().getAdvisor() != null
							&& (ab.getContact().getFirstname().toLowerCase().contains(names[0])
									&& ab.getContact().getLastname().toLowerCase().contains(names[1]))) {
						addressBooks.add(ab);
					}
				} else {
					if (ab.getContact().getAdvisor() != null
							&& ab.getContact().getFirstname().toLowerCase().contains(names[0])) {
						addressBooks.add(ab);
					}
				}
			}
			/*
			 * if (names.length == 1) { if (adv.getFirstname() != null && adv.getLastname()
			 * != null) { if (adv.getAddressBookType().equals(5) &&
			 * (adv.getFirstname().toLowerCase() + " " +
			 * adv.getLastname().toLowerCase()).contains(name)) { addressBooks.add(adv); } }
			 * else if (adv.getFirstname() != null && adv.getLastname() == null) {
			 * adv.getFirstname().toLowerCase().contains(name); } } else { if
			 * (adv.getAddressBookType().equals(5) && (adv.getFirstname() != null &&
			 * adv.getLastname() != null) &&
			 * ((adv.getFirstname().toLowerCase().startsWith(names[0]) &&
			 * adv.getLastname().toLowerCase().startsWith(names[1])))) {
			 * addressBooks.add(adv); } }
			 */

			break;

		}

		// 2 = Advisor Email Address
		case 2: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			String name = advisorNameSearch.toLowerCase();

			for (AddressBook adv : allAddressBooks) {
				if ((adv.getAddressEmail() != null && !adv.getAddressEmail().isEmpty()
						&& adv.getAddressEmail().toLowerCase().contains(name)
						|| (adv.getContact() != null && adv.getContact().getDefaultEmail() != null && adv.getContact()
								.getDefaultEmail().getEmailAddress().toLowerCase().contains(name)))) {
					addressBooks.add(adv);
				}
				/*
				 * if (adv.getAddressBookType().equals(5) && (adv.getAddressEmail() != null &&
				 * !adv.getAddressEmail().isEmpty() &&
				 * adv.getAddressEmail().toLowerCase().startsWith(name) || (adv.getContact() !=
				 * null &&
				 * adv.getContact().getDefaultEmail().getEmailAddress().toLowerCase().startsWith
				 * (name)))) { addressBooks.add(adv); }
				 */
			}
			break;
		}

		// 3 = Advisor Phone Number
		case 3: {

			addressBooks.clear();

			Integer area = null;
			if (advisorNameSearch2 != null && !advisorNameSearch2.isEmpty()) {
				advisorNameSearch2 = advisorNameSearch2.trim();

				if (advisorNameSearch2.length() != 3) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
				try {
					area = Integer.parseInt(advisorNameSearch2);
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			Integer number = null;
			if (advisorNameSearch != null && !advisorNameSearch.isEmpty()) {

				try {
					number = Integer.parseInt(Phone.cleanupNumber(advisorNameSearch));
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Phone Number, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			String a = null;
			if (area != null) {
				a = area.toString();
			}

			String n = null;
			if (number != null) {
				n = number.toString();
			}

			if (a == null && n == null) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			if (a != null && n == null) {
				applicationBean.postWarningMessage("Cannot Search on Area Code Alone",
						"Vous ne pouvez pas Recherche sur Code régional seulement", "", "");
				return;
			}

			for (AddressBook ab : allAddressBooks) {
				if (a == null) {
					if (ab.getContact().getAdvisor() != null
							&& ab.getContact().getDefaultPhone().getPhoneNumber().contains(n)) {
						addressBooks.add(ab);
					}
				} else {
					if (ab.getContact().getAdvisor() != null
							&& ab.getContact().getDefaultPhone().getAreaCode().contains(a)
							&& ab.getContact().getDefaultPhone().getPhoneNumber().contains(n)) {
						addressBooks.add(ab);
					}
				}
				/*
				 * if (adv.getAddressBookType().equals(5) && (adv.getPhoneNumber() != null &&
				 * adv.getPhoneNumber().toString().startsWith(a + n)) || (adv.getContact() !=
				 * null && adv.getContact().getDefaultPhone().getAreaCode().equals(a) &&
				 * adv.getContact().getDefaultPhone().getPhoneNumber().startsWith(n))) {
				 * addressBooks.add(adv); }
				 */
			}

			break;
		}

		default: {
			break;
		}

		}

	}

	public boolean isSearchAct() {
		return searchAct;
	}

	public void setSearchAct(boolean searchAct) {
		this.searchAct = searchAct;
	}

	public void searchBookCompose() {

		// 1 = Advisor Name
		// 2 = Advisor Email Address
		// 3 = Advisor Phone Number
		// 5 = Status
		System.out.println("searchBy: " + searchBy);

		if (advisorNameSearch != null && advisorNameSearch.contains("%")) {

			applicationBean.postWarningMessage("Please enter only alphabets", "Veuillez n'entrer que des alphabets", "",
					"");
			return;
		}
		searchAct = true;

		switch (searchBy) {

		// 1 = Advisor Name
		case 1: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			String name = advisorNameSearch.trim().toLowerCase();

			String[] names = name.split(" ");

			if (names.length > 2) {
				applicationBean.postWarningMessage("More than two keywords, cannot continue",
						"Plus de deux mots-clés, je ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if (names.length == 1) {
					if (adv.getFirstname() != null && adv.getLastname() != null) {
						if ((adv.getFirstname().toLowerCase() + " " + adv.getLastname().toLowerCase()).contains(name)
								&& adv.getAddressEmail() != null && !adv.getAddressEmail().isEmpty()) {
							addressBooks.add(adv);
						}
					} else if (adv.getFirstname() != null && adv.getLastname() == null && adv.getAddressEmail() != null
							&& !adv.getAddressEmail().isEmpty()) {
						adv.getFirstname().toLowerCase().contains(name);
						addressBooks.add(adv);
					}
				} else {
					if ((adv.getFirstname() != null && adv.getLastname() != null)
							&& ((adv.getFirstname().toLowerCase().startsWith(names[0])
									&& adv.getLastname().toLowerCase().startsWith(names[1])))
							&& adv.getAddressEmail() != null && !adv.getAddressEmail().isEmpty()) {
						addressBooks.add(adv);
					}
				}
			}

			break;

		}

		// 2 = Advisor Email Address
		case 2: {

			if (advisorNameSearch == null || advisorNameSearch.isEmpty()) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			addressBooks.clear();

			String name = advisorNameSearch.toLowerCase();

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if ((adv.getAddressEmail() != null && !adv.getAddressEmail().isEmpty()
						&& adv.getAddressEmail().toLowerCase().startsWith(name)
						|| (adv.getContact() != null && adv.getContact().getDefaultEmail().getEmailAddress()
								.toLowerCase().startsWith(name)))) {
					addressBooks.add(adv);
				}
			}
			break;
		}

		// 3 = Advisor Phone Number
		case 3: {

			addressBooks.clear();

			Integer area = null;
			if (advisorNameSearch2 != null && !advisorNameSearch2.isEmpty()) {
				advisorNameSearch2 = advisorNameSearch2.trim();

				if (advisorNameSearch2.length() != 3) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
				try {
					area = Integer.parseInt(advisorNameSearch2);
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Area Code, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			Integer number = null;
			if (advisorNameSearch != null && !advisorNameSearch.isEmpty()) {

				try {
					number = Integer.parseInt(Phone.cleanupNumber(advisorNameSearch));
				} catch (NumberFormatException e) {
					applicationBean.postWarningMessage("Wrong Format for the Phone Number, cannot continue",
							"Rien n'a été entré, ne peut pas continuer", "", "");
					return;
				}
			}

			String a = null;
			if (area != null) {
				a = area.toString();
			}

			String n = null;
			if (number != null) {
				n = number.toString();
			}

			if (a == null && n == null) {
				applicationBean.postWarningMessage("Nothing was entered, cannot continue",
						"Rien n'a été entré, ne peut pas continuer", "", "");
				return;
			}

			if (a != null && n == null) {
				applicationBean.postWarningMessage("Cannot Search on Area Code Alone",
						"Vous ne pouvez pas Recherche sur Code régional seulement", "", "");
				return;
			}

			for (AddressBook adv : applicationBean.getUsers().getAddressBookList()) {
				if ((adv.getPhoneNumber() != null && adv.getPhoneNumber().toString().startsWith(a + n))
						|| (adv.getContact() != null && adv.getContact().getDefaultPhone().getAreaCode().equals(a)
								&& adv.getContact().getDefaultPhone().getPhoneNumber().startsWith(n))
								&& adv.getAddressEmail() != null && !adv.getAddressEmail().isEmpty()) {
					addressBooks.add(adv);
				}
			}

			break;
		}

		default: {
			break;
		}

		}

	}

	public void onTabChange(TabChangeEvent<?> event) {

		String id = event.getTab().getId();

		// Contact Info View
		if (id.equalsIgnoreCase("CONTACT_SUMMARY_TAB")) {
			setViewTabId(0);
		}

		if (id.equalsIgnoreCase("GABINET_TAB")) {
			setViewTabId(1);
			productSupplierTypes = loadSupplierTypesList();
		}
		if (id.equalsIgnoreCase("ARCHIVED_EMAIL_TAB")) {
			setViewTabId(2);
			if (selectedAddressBook.getLead() != null) {
				sentMessages = selectedAddressBook.getLead().getActivityList();
			}
			if (selectedAddressBook.getContact() != null) {
				sentMessages = selectedAddressBook.getContact().getActivityList();
			}
		}

	}

	public List<AddressBook> getAdvisorsSearch() {
		if (!searchAct) {
			return allAddressBooks;
		} else {
			return addressBooks;
		}
	}

	public List<Contact> getContactsSearch() {
		if (contactsAgencyFilter.isEmpty()) {
			return contactsAgency;
		} else {
			return contactsAgencyFilter;
		}
	}

	public void onAddressBookSelect(SelectEvent<?> event) {
		if (event.getObject() == null) {
			setSelectedAddressBook(null);
			return;
		}

		AddressBook ab = (AddressBook) event.getObject();
		setSelectedAddressBook(ab);
		storedFileList.clear();
		if (selectedAddressBook != null && selectedAddressBook.getLead() != null) {
			storedFileList = storedFileFacade.findStoredFileAll(selectedAddressBook.getLead());

			/*
			 * if (selectedAddressBook.getContact() != null) { lastActivity =
			 * activityFacade.getLastActivityByContact(selectedAddressBook.getContact());
			 * for (Activity act : selectedAddressBook.getContact().getActivityList()) {
			 * storedFileList.addAll(act.getAttachmentsFiles()); } } if
			 * (selectedAddressBook.getLead() != null) {
			 */
			lastActivity = activityFacade.getLastActivityByLead(selectedAddressBook.getLead());
			for (Activity act : selectedAddressBook.getLead().getActivityList()) {
				storedFileList.addAll(act.getAttachmentsFiles());
			}
			// }
		}
	}

	public List<AddressBook> getAdvisorsSearchBook() {
		if (!searchAct) {
			return allAddressBooksView;
		} else {
			return addressBooks;
		}
	}

	public List<AddressBook> getAdvisorsSearchBookCompose() {
		if (!searchAct) {
			return allAddressBooksCompose;
		} else {
			return addressBooks;
		}
	}

	public boolean isSendEnable() {
		if (!searchAct) {
			for (AddressBook ab : allAddressBooksCompose) {
				if (ab.isSelected()) {
					return true;
				}
			}
		} else {
			for (AddressBook ab : addressBooks) {
				if (ab.isSelected()) {
					return true;
				}
			}
		}

		return false;
	}

	public List<Advisor> getAllAdvisors() {
		return allAdvisors;
	}

	public void setAllAdvisors(List<Advisor> allAdvisors) {
		this.allAdvisors = allAdvisors;
	}

	public Advisor getSelectedAdvisor() {
		return selectedAdvisor;
	}

	public void setSelectedAdvisor(Advisor selectedAdvisor) {
		this.selectedAdvisor = selectedAdvisor;
	}

	public String getDocumentMimeType(UploadedFile uploadedFile) {

		if (uploadedFile == null) {
			return null;
		}

		String mime = "text/plain";

		// System.out.println("uploadedFile.getFileName(): "
		// +uploadedFile.getFileName());
		int p = uploadedFile.getFileName().lastIndexOf(".");
		String ext = uploadedFile.getFileName().substring(p + 1);

		if (ext.equalsIgnoreCase(CloudDocument.PDF_FILE_TYPE)) {
			mime = "application/pdf";
		}

		if (ext.equalsIgnoreCase(CloudDocument.MP3_AUDIO_FILE_TYPE)) {
			mime = "audio/mpeg3";
		}

		if (ext.equalsIgnoreCase(CloudDocument.WAV_AUDIO_FILE_TYPE)) {
			mime = "audio/wav";
		}

		if (ext.equalsIgnoreCase(CloudDocument.JPG_IMAGE_FILE_TYPE)) {
			mime = "image/jpeg";
		}

		if (ext.equalsIgnoreCase(CloudDocument.PNG_IMAGE_FILE_TYPE)) {
			mime = "image/png";
		}

		if (ext.equalsIgnoreCase(CloudDocument.TIFF_IMAGE_FILE_TYPE)) {
			mime = "image/tiff";
		}

		if (ext.equalsIgnoreCase(CloudDocument.MOV_VIDEO_FILE_TYPE)) {
			mime = "video/quicktime";
		}

		return mime;
	}

	public String getDocumentMimeType(StoredFile uploadedFile) {

		if (uploadedFile == null) {
			return null;
		}

		String mime = "text/plain";

		// System.out.println("uploadedFile.getFileName(): "
		// +uploadedFile.getFileName());
		int p = uploadedFile.getName().lastIndexOf(".");
		String ext = uploadedFile.getName().substring(p + 1);

		if (ext.equalsIgnoreCase(CloudDocument.PDF_FILE_TYPE)) {
			mime = "application/pdf";
		}

		if (ext.equalsIgnoreCase(CloudDocument.MP3_AUDIO_FILE_TYPE)) {
			mime = "audio/mpeg3";
		}

		if (ext.equalsIgnoreCase(CloudDocument.WAV_AUDIO_FILE_TYPE)) {
			mime = "audio/wav";
		}

		if (ext.equalsIgnoreCase(CloudDocument.JPG_IMAGE_FILE_TYPE)) {
			mime = "image/jpeg";
		}

		if (ext.equalsIgnoreCase(CloudDocument.PNG_IMAGE_FILE_TYPE)) {
			mime = "image/png";
		}

		if (ext.equalsIgnoreCase(CloudDocument.TIFF_IMAGE_FILE_TYPE)) {
			mime = "image/tiff";
		}

		if (ext.equalsIgnoreCase(CloudDocument.MOV_VIDEO_FILE_TYPE)) {
			mime = "video/quicktime";
		}

		return mime;
	}

	public void addAttachment() {

		if (uploadedFile == null) {
			return;
		}
		Attachment att = new Attachment(uploadedFile.getFileName(), "", uploadedFile.getContent());
		if (attachments != null) {
			attachments.add(att);
		} else {
			attachments = new ArrayList<>();
			attachments.add(att);
		}

		uploadedFile = null;

	}

	public void handleUpload(FileUploadEvent event) {
		uploadedFile = event.getFile();

		System.out.println("uploadedFile: " + uploadedFile.getFileName());

		if (uploadedFile == null) {
			return;
		}
		Attachment att = new Attachment(uploadedFile.getFileName(), "", uploadedFile.getContent());
		if (attachments != null) {
			attachments.add(att);
		} else {
			attachments = new ArrayList<>();
			attachments.add(att);
		}

		uploadedFile = null;

	}

	private void logActivity(Recipient recipient, List<StoredFile> attachs) {
		Date now = Calendar.getInstance().getTime();

		Activity newActivity = new Activity();

		newActivity.setCreationDate(now);
		newActivity.setDueDate(now);
		newActivity.setEffectiveDate(now);

		Integer groupId = activityFacade.getNextActivityGroupId();
		newActivity.setActivityGroup(groupId);

		newActivity.setStyleClass(Constants.Activity_StyleClass.task.getCode());

		newActivity.setActivityType(20); // 20 new for send documents

		newActivity.setFirstAlert("5m");

		String recipientName = "";

		recipientName += recipient.getName() + "<" + recipient.getAddress() + ">";

		newActivity.setRecipient(recipientName);

		newActivity.setRecipientFullname(recipient.getName());

		newActivity.setCategory(recipient.getEmailType()); // licensing

		if (reply) {
			newActivity.setStatus(10);
			newActivity.setState(10);
		} else {
			newActivity.setStatus(11);
			newActivity.setState(11);
		}

		newActivity.setOwner(applicationBean.getUsers());

		newActivity.setContact(recipient.getContact());

		// setting the type of the reference - should be a value from entity type
		// activity.setReferenceType(referencedType);
//        activity.setMsgFrom(advisorNavigationBean.getSelectedAdvisor().getContact().getDefaultEmail().getEmailAddress());
//        activity.setMsgTo(recipient.getAddress());
		newActivity.setTitle(title);

		String body = message;

		if (body.length() > 3998) {

			newActivity.setDescription(body.substring(0, 3998));

		} else {

			newActivity.setDescription(body);
		}

		if (!attachs.isEmpty()) {
			newActivity.setHasAttachment(true);
			newActivity.setAttachments(concatAttachmentNames(attachments));
			newActivity.setAttachmentsFiles(attachs);

		} else {

			newActivity.setHasAttachment(false);
		}

		activityFacade.create(newActivity);

		// create an Alert for the activity
		// if (newActivity.getFirstAlert() != null) {
		System.out.println("creating alert");

		Alert alert = new Alert();

		alert.setOwner(recipient.getContact().getUsers());
		alert.setCreationDate(applicationBean.now());
		alert.setActivity(newActivity);

		alert.setTitle(newActivity.getTitle());
		alert.setMessage(newActivity.getDescription());

		Calendar cal = Calendar.getInstance();
		cal.setTime(newActivity.getEffectiveDate());

		/*
		 * String firsrAlert = newActivity.getFirstAlert(); if
		 * (firsrAlert.equalsIgnoreCase("5m")) { cal.add(Calendar.MINUTE, -5); } if
		 * (firsrAlert.equalsIgnoreCase("15m")) { cal.add(Calendar.MINUTE, -15); } if
		 * (firsrAlert.equalsIgnoreCase("30m")) { cal.add(Calendar.MINUTE, -30); } if
		 * (firsrAlert.equalsIgnoreCase("1h")) { cal.add(Calendar.HOUR_OF_DAY, -1); } if
		 * (firsrAlert.equalsIgnoreCase("2h")) { cal.add(Calendar.HOUR_OF_DAY, -2); } if
		 * (firsrAlert.equalsIgnoreCase("1d")) { cal.add(Calendar.DAY_OF_MONTH, -1); }
		 * if (firsrAlert.equalsIgnoreCase("2d")) { cal.add(Calendar.DAY_OF_MONTH, -2);
		 * } if (firsrAlert.equalsIgnoreCase("1w")) { cal.add(Calendar.WEEK_OF_YEAR,
		 * -1); }
		 */
		alert.setStartDate(cal.getTime()); // set to - alert time
		cal.add(Calendar.DAY_OF_MONTH, 7);
		alert.setEndDate(cal.getTime()); // set to a whole week

		alertFacade.create(alert);
		// }
	}

	@SuppressWarnings("unused")
	private void createAlert(Activity act, Contact contact) {
		Alert alert = new Alert();

		alert.setOwner(contact.getUsers());
		alert.setCreationDate(applicationBean.now());
		alert.setActivity(act);

		alert.setTitle(act.getTitle());
		alert.setMessage(act.getDescription());

		Calendar cal = Calendar.getInstance();
		cal.setTime(act.getEffectiveDate());

		/*
		 * String firsrAlert = act.getFirstAlert(); if
		 * (firsrAlert.equalsIgnoreCase("5m")) { cal.add(Calendar.MINUTE, -5); } if
		 * (firsrAlert.equalsIgnoreCase("15m")) { cal.add(Calendar.MINUTE, -15); } if
		 * (firsrAlert.equalsIgnoreCase("30m")) { cal.add(Calendar.MINUTE, -30); } if
		 * (firsrAlert.equalsIgnoreCase("1h")) { cal.add(Calendar.HOUR_OF_DAY, -1); } if
		 * (firsrAlert.equalsIgnoreCase("2h")) { cal.add(Calendar.HOUR_OF_DAY, -2); } if
		 * (firsrAlert.equalsIgnoreCase("1d")) { cal.add(Calendar.DAY_OF_MONTH, -1); }
		 * if (firsrAlert.equalsIgnoreCase("2d")) { cal.add(Calendar.DAY_OF_MONTH, -2);
		 * } if (firsrAlert.equalsIgnoreCase("1w")) { cal.add(Calendar.WEEK_OF_YEAR,
		 * -1); }
		 */
		alert.setStartDate(cal.getTime()); // set to - alert time
		cal.add(Calendar.DAY_OF_MONTH, 7);
		alert.setEndDate(cal.getTime()); // set to a whole week

		alertFacade.create(alert);
	}

	private String concatAttachmentNames(List<Attachment> attachedFiles) {
		StringBuilder sb = new StringBuilder();

		if (attachedFiles != null && !attachedFiles.isEmpty()) {
			for (Attachment att : attachedFiles) {
				String fileName = null;
				if (att != null) {
					fileName = att.getFileName();
				}
				sb.append((fileName == null) ? "_" : fileName).append(" ");
			}
			return sb.toString();
		} else {
			return null;
		}
	}

	public void addRecipient() {

		Recipient recipientNew = new Recipient();
		recipientNew.setName(" N.A. ");
		recipientNew.setAddress(" N.A. ");
		recipientNew.setTitle(title);

		recipientNew.setMessage(message);
		recipientNew.setSend(true);

		// Email address of sender and reply to
		String replyTo = applicationBean.getUsers().getContact().getDefaultEmail().getEmailAddress();

		recipientNew.setReplyTo(replyTo);

		recipients.add(recipientNew);
	}

	public void verifyEmails() {

		for (Recipient recipient : recipients) {

			if (recipient.isBadAddress()) {
				recipient.setSend(false);
			} else {
				recipient.setSend(true);
			}
		}
	}

	public void deleteRecipient(Recipient r) {

//        System.out.println(r);
		Iterator<Recipient> iterator = recipients.iterator();

		while (iterator.hasNext()) {

			Recipient recipient = iterator.next();

			if (recipient.equals(r)) {
				iterator.remove();
				break;
			}
		}
	}

	public List<SelectItem> getAvailableAttachments() {
		List<SelectItem> items = new ArrayList<>();

		if (attachments == null || attachments.isEmpty()) {
			items.add(new SelectItem(null, "- No attachments found -"));
		} else {

			for (Attachment at : attachments) {
				if (at != null) {
					items.add(new SelectItem(at, at.getFileName()));
				}
			}
		}

		return items;

	}

	public boolean isMasterMode() {
		return masterMode;
	}

	public void setMasterMode(boolean masterMode) {
		this.masterMode = masterMode;
	}

	public boolean isToolMaster() {
		return toolMaster;
	}

	public void setToolMaster(boolean toolMaster) {
		this.toolMaster = toolMaster;
	}

	public Recipient getRecipient() {
		return recipient;
	}

	public void setRecipient(Recipient recipient) {
		this.recipient = recipient;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public boolean isReply() {
		return reply;
	}

	public void setReply(boolean reply) {
		this.reply = reply;
	}

	public UploadedFile getUploadedFile() {
		return uploadedFile;
	}

	public void setUploadedFile(UploadedFile uploadedFile) {
		this.uploadedFile = uploadedFile;
	}

	public List<Recipient> getRecipients() {
		return recipients;
	}

	public void setRecipients(List<Recipient> recipients) {
		this.recipients = recipients;
	}

	public List<Attachment> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<Attachment> attachments) {
		this.attachments = attachments;
	}

	public StreamedContent downloadAttachments(Activity act) {

		// System.out.println("501 OppNavBean quote.length" +
		// opp.getQuoteFileTem().length);
		FacesContext context = FacesContext.getCurrentInstance();
		DefaultStreamedContent attach = new DefaultStreamedContent();

		if (context.getCurrentPhaseId() == PhaseId.RENDER_RESPONSE) {
			attach = new DefaultStreamedContent();
		} else {
			if (act.getAttachmentsFiles().size() > 1) {
				ByteArrayOutputStream baos = new ByteArrayOutputStream();
				ZipOutputStream zos = new ZipOutputStream(baos);
				ZipEntry entry;

				for (StoredFile att : act.getAttachmentsFiles()) {
					try {
						entry = new ZipEntry(att.getName());
						entry.setSize(att.getStoredFileDesc().getFileEn().length);
						zos.putNextEntry(entry);
						zos.write(att.getStoredFileDesc().getFileEn());
						zos.closeEntry();
					} catch (IOException ex) {
						Logger.getLogger(AdminCommonContractingBean.class.getName()).log(Level.SEVERE, null, ex);
					}
				}
				try {
					zos.finish();
				} catch (IOException ex) {
					Logger.getLogger(AdminCommonContractingBean.class.getName()).log(Level.SEVERE, null, ex);
				}
				// attach = new DefaultStreamedContent(new
				// ByteArrayInputStream(baos.toByteArray()), "application/zip",
				// "attachments.zip");
				attach = DefaultStreamedContent.builder().contentType("application/zip").name("attachments.zip")
						.stream(() -> new ByteArrayInputStream(baos.toByteArray())).build();
			} else if (act.getAttachmentsFiles().size() == 1) {
				// attach = new DefaultStreamedContent(new
				// ByteArrayInputStream(act.getAttachmentsFiles().get(0).getStoredFileDesc().getFileEn()),
				// getDocumentMimeType(act.getAttachmentsFiles().get(0)),
				// act.getAttachmentsFiles().get(0).getName());
				attach = DefaultStreamedContent.builder()
						.contentType(getDocumentMimeType(act.getAttachmentsFiles().get(0)))
						.name(act.getAttachmentsFiles().get(0).getName()).stream(() -> new ByteArrayInputStream(
								act.getAttachmentsFiles().get(0).getStoredFileDesc().getFileEn()))
						.build();
			}
		}

		return attach;
	}

	public StreamedContent downloadAttachment(StoredFile att) {

		// System.out.println("501 OppNavBean quote.length" +
		// opp.getQuoteFileTem().length);
		FacesContext context = FacesContext.getCurrentInstance();
		DefaultStreamedContent attach;

		if (context.getCurrentPhaseId() == PhaseId.RENDER_RESPONSE) {
			attach = new DefaultStreamedContent();
		} else {

			// attach = new DefaultStreamedContent(new
			// ByteArrayInputStream(att.getStoredFileDesc().getFileEn()), "",
			// att.getName());
			attach = DefaultStreamedContent.builder().name(att.getName())
					.stream(() -> new ByteArrayInputStream(att.getStoredFileDesc().getFileEn())).build();

		}

		return attach;
	}

	public boolean isSearchAdvisor() {
		return searchAdvisor;
	}

	public void setSearchAdvisor(boolean searchAdvisor) {
		this.searchAdvisor = searchAdvisor;
		if (searchAdvisor && sendAll) {
			sendAll = false;
		}
		if (searchAdvisor) {
			applicationBean.showDialog("messageContainer");
		}
	}

	public boolean isSendAll() {
		return sendAll;
	}

	public void setSendAll(boolean sendAll) {
		this.sendAll = sendAll;
		if (searchAdvisor && sendAll) {
			searchAdvisor = false;
		}
	}

	public boolean getViewId() {
		return viewId;
	}

	public void setViewId(boolean viewId) {
		this.viewId = viewId;
	}

	public Integer getSelectedTopic() {
		return selectedTopic;
	}

	public void setSelectedTopic(Integer selectedTopic) {
		this.selectedTopic = selectedTopic;
	}

	public Integer getSelectedDocumentType() {
		return selectedDocumentType;
	}

	public void setSelectedDocumentType(Integer selectedDocumentType) {
		this.selectedDocumentType = selectedDocumentType;
	}

	public int getSearchBy() {
		return searchBy;
	}

	public void setSearchBy(int searchBy) {
		this.searchBy = searchBy;
	}

	public String getAdvisorNameSearch() {
		return advisorNameSearch;
	}

	public void setAdvisorNameSearch(String advisorNameSearch) {
		this.advisorNameSearch = advisorNameSearch;
		if (advisorNameSearch == null) {
			toSend.clear();
			suppliersSelected.clear();
			allAddressBooks = addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		}
	}

	public void setAdvisorNameSearchReset(String advisorNameSearch) {
		this.advisorNameSearch = advisorNameSearch;
		toSend.clear();
		suppliersSelected.clear();
		resetSearchNew();
	}

	public String getAdvisorNameSearch2() {
		return advisorNameSearch2;
	}

	public void setAdvisorNameSearch2(String advisorNameSearch2) {
		this.advisorNameSearch2 = advisorNameSearch2;
	}

	public String getAdvisorIdStatus() {
		return advisorIdStatus;
	}

	public void setAdvisorIdStatus(String advisorIdStatus) {
		this.advisorIdStatus = advisorIdStatus;
	}

	public Integer getAdvisorStatusId() {
		return advisorStatusId;
	}

	public void setAdvisorStatusId(Integer advisorStatusId) {
		this.advisorStatusId = advisorStatusId;
	}

	public List<Contact> getAllContacts() {
		return allContacts;
	}

	public void setAllContacts(List<Contact> allContacts) {
		this.allContacts = allContacts;
	}

	public List<SelectItem> getProductSuppliers() {

		List<ProductSupplier> productSuppliersFiltered = new ArrayList<>();
		for (ProductSupplier ps : productSuppliers) {
			for (Contact co : ps.getOrganization().getContactList()) {
				for (ContactAction ca : co.getContactActionList()) {
					if (ca.getContactActionType() == 13) {
						productSuppliersFiltered.add(ps);
					}
				}
			}
		}

		List<SelectItem> selectItems = new ArrayList<>();

		List<Integer> checking = new ArrayList<>();

		if (selectedAdvisor == null || productSuppliersFiltered.isEmpty()) {
			selectItems.add(new SelectItem(null, applicationBean.getLanguage().getString("No.Companies.Available")));
		} else {

			selectItems.add(0, (new SelectItem(0, applicationBean.getLanguage().getString("Select"))));

			for (ProductSupplier ps : productSuppliersFiltered) {
				if (!checking.contains(ps.getProductSupplierIntId())) {
					checking.add(ps.getProductSupplierIntId());
					if (applicationBean.getLanguage().isEnglish()) {
						selectItems.add(new SelectItem(ps.getProductSupplierIntId(),
								ps.getOrganization().getOrganizationDescEn()));
					} else {
						selectItems.add(new SelectItem(ps.getProductSupplierIntId(),
								ps.getOrganization().getOrganizationDescFr()));
					}
				}
			}
		}

		return selectItems;
	}

	public List<SelectItem> getAgencyList() {

		List<SelectItem> selectItems = new ArrayList<>();

		if (agenciesList.isEmpty()) {
			selectItems.add(new SelectItem(null, applicationBean.getLanguage().getString("No.Companies.Available")));
		} else {
			// selectItems.add(0, (new SelectItem(0,
			// applicationBean.getLanguage().getString("Select"))));

			for (ProductSupplier ps : agenciesList) {
				if (applicationBean.getLanguage().isEnglish()) {
					selectItems.add(
							new SelectItem(ps.getProductSupplierIntId(), ps.getOrganization().getOrganizationDescEn()));
				} else {
					selectItems.add(
							new SelectItem(ps.getProductSupplierIntId(), ps.getOrganization().getOrganizationDescFr()));
				}
			}
		}

		return selectItems;
	}

	public void updateLists() {
		if (selectedTopic == null || selectedTopic == 0) {
			selectedTopic = typesFacade.findTypesByTypeClass(typeClassFacade.find(66417)).get(0).getSubType();
		}
		toSendContacts.clear();
		// updateMgaRightPart();
		// fillContactListForAdvisor();
		buildTreeContractListView();
	}

	public void fillContactListForAdvisor() {
		ProductSupplier psSelected = productSupplierFacade.find(selectedProductSuplier);
		contactsAgency.clear();
		for (Contact c : psSelected.getOrganization().getContactList()) {
			if (c.canRecieveDocuments()) {
				contactsAgency.add(c);
			}
		}
	}

	public Integer getSelectedProductSuplier() {
		return selectedProductSuplier;
	}

	public void setSelectedProductSuplier(Integer selectedProductSuplier) {
		this.selectedProductSuplier = selectedProductSuplier;
	}

	public void enableFields() {
		enableLists = true;
		updateLists();
	}

	public List<AddressBook> getToSend() {
		return toSend;
	}

	public void setToSend(List<AddressBook> toSend) {
		this.toSend = toSend;
	}

	public List<Contact> getToSendContacts() {
		return toSendContacts;
	}

	public void setToSendContacts(List<Contact> toSendContacts) {
		this.toSendContacts = toSendContacts;
	}

	public void enableFieldsBetter(AddressBook address) {
		if (address.isSelected()) {
			if (isToolMaster() && address.getContact() != null && address.getContact().getAdvisor() != null) {
				if (applicationBean.getUsers().getProfileTool().getProductSupplier().getProductSupplierType()
						.getProductSupplierType() != 11) { // is insuranse company
					for (ContractSetup cs : address.getContact().getAdvisor().getContractSetupList()) {
						if (Objects.equals(cs.getProductSupplier().getProductSupplierIntId(), applicationBean.getUsers()
								.getProfileTool().getProductSupplier().getProductSupplierIntId())) {
							if (cs.getMgaProductSupplier() != null
									&& !suppliersSelected.contains(cs.getMgaProductSupplier())) {
								suppliersSelected.add(cs.getMgaProductSupplier());
								address.setMga(cs.getMgaProductSupplier());
							}
							if (cs.getAgaProductSupplier() != null
									&& !suppliersSelected.contains(cs.getAgaProductSupplier())) {
								productSuppliers.add(cs.getAgaProductSupplier());
								address.setAga(cs.getAgaProductSupplier());
							}
							if (cs.getAga2ProductSupplier() != null
									&& !suppliersSelected.contains(cs.getAga2ProductSupplier())) {
								suppliersSelected.add(cs.getAga2ProductSupplier());
								address.setAga2(cs.getAga2ProductSupplier());
							}
						}
					}
				} else {// todo
					for (ContractSetup cs : address.getContact().getAdvisor().getContractSetupList()) {
						// if (Objects.equals(cs.getProductSupplier().getProductSupplierIntId(),
						// applicationBean.getUsers().getProfileTool().getProductSupplier().getProductSupplierIntId()))
						// {
						if (cs.getMgaProductSupplier() != null
								&& !suppliersSelected.contains(cs.getMgaProductSupplier())) {
							suppliersSelected.add(cs.getMgaProductSupplier());
							address.setMga(cs.getMgaProductSupplier());
						}
						if (cs.getAgaProductSupplier() != null
								&& !suppliersSelected.contains(cs.getAgaProductSupplier())) {
							productSuppliers.add(cs.getAgaProductSupplier());
							address.setAga(cs.getAgaProductSupplier());
						}
						if (cs.getAga2ProductSupplier() != null
								&& !suppliersSelected.contains(cs.getAga2ProductSupplier())) {
							suppliersSelected.add(cs.getAga2ProductSupplier());
							address.setAga2(cs.getAga2ProductSupplier());
						}
						// }
					}
				}
			}
			toSend.add(address);
		} else {
			toSend.remove(address);
		}
		if (toSend.isEmpty()) {
			enableLists = false;
		} else {
			enableLists = true;
		}
		updateLists();
	}

	public void addContactToList(Contact address) {
		if (address.isSelected() && !toSendContacts.contains(address)) {
			toSendContacts.add(address);
		} else {
			toSendContacts.remove(address);
		}
	}

	public boolean isEnableLists() {
		return enableLists;
	}

	public void setEnableLists(boolean enableLists) {
		this.enableLists = enableLists;
	}

	public boolean isHasLicense() {
		return hasLicense;
	}

	public void setHasLicense(boolean hasLicense) {
		this.hasLicense = hasLicense;
	}

	public boolean isHasLiability() {
		return hasLiability;
	}

	public void setHasLiability(boolean hasLiability) {
		this.hasLiability = hasLiability;
	}

	public String getLicenseName(License license) {

		if (license == null) {
			return "N/A";
		}

		if (license.getCompany() == null) {
			return "N/A";
		}

		return license.getCompany().getPrimaryName();
	}

	public String provinceFullName(String provinceCode) {

		if (provinces == null || provinces.isEmpty()) {
			loadCanadaProvinces();
		}

		if (provinceCode.equalsIgnoreCase("XX")) {
			return applicationBean.getLanguage().getString("All");
		}

		for (Province province : provinces) {
			if (province.getProvinceCode().equalsIgnoreCase(provinceCode)) {

				if (applicationBean.getLanguage().isEnglish()) {
					return province.getNameEn();
				} else {
					return province.getNameFr();
				}
			}
		}

		return provinceCode;
	}

	private List<Contact> getContactListMulti(License license) {

		List<Contact> results = new ArrayList<>();
		Company company = license.getCompany();
//        System.out.println("getServicingMGA : " + company);

		if (company == null) {
			return null;
		}

		List<ContractSetup> contractSetups = contractSetupFacade.findAllByLicensee(company);

		for (ContractSetup cs : contractSetups) {
			for (Contact co : cs.getProductSupplier().getOrganization().getContactList()) {
				for (ContactAction ca : co.getContactActionList()) {
					if (ca.getContactActionType() == 3) {
						results.add(co);
					}
				}
			}

			if (cs.getMgaProductSupplier() != null) {
				for (Contact co : cs.getMgaProductSupplier().getOrganization().getContactList()) {
					for (ContactAction ca : co.getContactActionList()) {
						if (ca.getContactActionType() == 3) {
							results.add(co);
						}
					}
				}
			}
			if (cs.getAgaProductSupplier() != null) {
				for (Contact co : cs.getMgaProductSupplier().getOrganization().getContactList()) {
					for (ContactAction ca : co.getContactActionList()) {
						if (ca.getContactActionType() == 3) {
							results.add(co);
						}
					}
				}
			}
			if (cs.getAga2ProductSupplier() != null) {
				for (Contact co : cs.getMgaProductSupplier().getOrganization().getContactList()) {
					for (ContactAction ca : co.getContactActionList()) {
						if (ca.getContactActionType() == 3) {
							results.add(co);
						}
					}
				}
			}
		}

		return results;
	}

	protected void loadCanadaProvinces() {
		provinces = provinceFacade.findByCountry("CAN");
	}

	public void sendLicenseDocument(License license) {
		commonContractingBean.sendLicenseAndLiabilityDocument(license, 1, getContactListMulti(license)); 
	}
	
	public void sendMultipleDocumentsLicense() {
		List<Contact> contactList = new ArrayList<>();
		Set<Integer> seenContactIds = new HashSet<>();  // Set to track unique ContactIntIds
		
		List<License> licenses = filesToSendAdvisor
				.stream().map(StoredFile::getLicense).filter(Objects::nonNull).collect(Collectors
						.toMap(License::getLicenseIntId, license -> license, (existing, replacement) -> existing))
				.values().stream().collect(Collectors.toList()); 
		
		licenses.forEach(license -> {
		    getContactListMulti(license).stream()  // Assuming getContactListMulti returns a list of contacts
		        .filter(contact -> seenContactIds.add(contact.getContactIntId()))  // Add to Set and filter out duplicates
		        .forEach(contactList::add);  // Add unique contacts to the contactList
		});
		commonContractingBean.sendMultipleDocumentsLicense(filesToSendAdvisor , contactList);
	}
	
	public List<StoredFile> getFilesToSendAdvisor() {
		return filesToSendAdvisor;
	}

	public void addDocumentToSelection(StoredFile sf) {
		if (filesToSendAdvisor == null) {
			filesToSendAdvisor = new ArrayList<StoredFile>();
		}
		if (filesToSendAdvisor.contains(sf))
			filesToSendAdvisor.remove(sf);
		else
			filesToSendAdvisor.add(sf);
	}
	
	public boolean isFileSelected(StoredFile sf) {
	    return filesToSendAdvisor != null && filesToSendAdvisor.contains(sf);
	}

	public void sendLiabilityDocument(LicenseLiability liability) {
		// for (Integer ps : selectedProductSupliers) {
		commonContractingBean.sendLiabilityDocumentNoDialog(liability,
				productSupplierFacade.find(selectedProductSuplier).getOrganization().getContactList());
		// }
	}

	public void sendAllDocuments() {
		for (License li : selectedAdvisor.getLicenseList()) {
			if (!li.isExpired()) {
				for (Integer ps : selectedProductSupliers) {
					commonContractingBean.sendLicenseDocumentNoDialog(li,
							productSupplierFacade.find(ps).getOrganization().getContactList());
				}
			}
		}
		for (LicenseLiability li : selectedAdvisor.getLicenseLiabilityList()) {
			if (!li.isExpired()) {
				// for (Integer ps : selectedProductSupliers) {
				commonContractingBean.sendLiabilityDocumentNoDialog(li,
						productSupplierFacade.find(selectedProductSuplier).getOrganization().getContactList());
				// }
			}
		}
	}

	public List<Integer> getSelectedProductSupliers() {
		return selectedProductSupliers;
	}

	public void setSelectedProductSupliers(List<Integer> selectedProductSupliers) {
		this.selectedProductSupliers = selectedProductSupliers;
	}

	public AddressBook getSelectedAddressBook() {
		return selectedAddressBook;
	}

	private StreamedContent foulderDocument;

	public StreamedContent getQuoteFile(StoredFile foulder) throws IOException {

		if (foulder != null) {

			FacesContext context = FacesContext.getCurrentInstance();

			if (context.getCurrentPhaseId() == PhaseId.RENDER_RESPONSE) {
				// So, we're rendering the HTML. Return a stub StreamedContent so that it will
				// generate right URL.
				foulderDocument = new DefaultStreamedContent();
			} else {
				// So, browser is requesting the media. Return a real StreamedContent with the
				// media bytes.
				// String id = context.getExternalContext().getRequestParameterMap().get("id");
				// foulderDocument = new DefaultStreamedContent(new
				// ByteArrayInputStream(foulder.getStoredFileDesc().getFileEn()), "",
				// foulder.getName());
				foulderDocument = DefaultStreamedContent.builder().name(foulder.getName())
						.stream(() -> new ByteArrayInputStream(foulder.getStoredFileDesc().getFileEn())).build();
			}
		}

		return foulderDocument;
	}

	public void setSelectedAddressBook(AddressBook selectedAddressBook) {
		this.selectedAddressBook = selectedAddressBook;
	}

	public void setSelectedAddressBookEvent(SelectEvent<?> event) {
		AddressBook ab = (AddressBook) event.getObject();
		this.selectedAddressBook = ab;
	}

	public void createNewNote() {
		selectedNote = new StoredFileNote();
		selectedNote.setStoredFile(selectedFileNotes);
		selectedNote.setCreationDate(applicationBean.now());
	}

	public void saveNote() {
		if (selectedNote.getStoredFilesNoteIntId() == null) {
			storedFileNoteFacade.create(selectedNote);
			selectedFileNotes.appendNotes(selectedNote);
		} else {
			storedFileNoteFacade.edit(selectedNote);
		}
		selectedNote = null;
	}

	public void removeNote() {
		storedFileNoteFacade.remove(selectedNoteToDelete);
		selectedFile.getFileNotes().remove(selectedNoteToDelete);
		selectedNoteToDelete = null;
	}

	public void createAddressBook() {
		selectedAddressBook = new AddressBook();
	}

	public void createAddressBookBussiness() {
		selectedAddressBook = new AddressBook();
		selectedAddressBook.setAddressBookType(8);
	}

	public void saveAddressBook() {
		if (selectedAddressBook.getAddressBookIntId() == null) {
			selectedAddressBook.setOwner(applicationBean.getUsers());
			selectedAddressBook.setPhoneNumber(selectedAddressBook.getPhoneNumber().replace("-", ""));
			Lead fakeLead = new Lead(selectedAddressBook);
			leadFacade.create(fakeLead);
			// addressBookFacade.create(selectedAddressBook);
			applicationBean.getUsers().getAddressBookList().add(selectedAddressBook);
			allAddressBooksView.add(selectedAddressBook);
			if (searchAct) {
				addressBooks.add(selectedAddressBook);
			}
		} else {
			// System.out.println("selectedAddressBook: " +
			// selectedAddressBook.getPhoneNumber());
			String phone = "";
			if (selectedAddressBook.getPhoneNumber() != null && !selectedAddressBook.getPhoneNumber().isEmpty()) {
				phone = selectedAddressBook.getPhoneNumber().substring(0, 3)
						+ selectedAddressBook.getPhoneNumber().substring(4, 7)
						+ selectedAddressBook.getPhoneNumber().substring(8);
			}
			if (selectedAddressBook.getContact() != null) {
				Contact contact = selectedAddressBook.getContact();
				contact.getDefaultPhone().setAreaCode(selectedAddressBook.getPhoneNumber().substring(0, 3));
				contact.getDefaultPhone().setPhoneNumber(selectedAddressBook.getPhoneNumber().substring(3));

				contact.getDefaultEmail().setEmailAddress(selectedAddressBook.getAddressEmail());

				contact.setFirstname(selectedAddressBook.getFirstname());
				contact.setLastname(selectedAddressBook.getLastname());
				contactFacade.edit(contact);
			}
			// System.out.println("phone: " + phone);
			// System.out.println("com.insurfact.avue.navigation.common.DocumentCentreBean.saveAddressBook()");
			if (selectedAddressBook.getPhoneNumber() != null && !selectedAddressBook.getPhoneNumber().isEmpty()) {
				selectedAddressBook.setPhoneNumber(phone);
				selectedAddressBook.setPhoneNumber(selectedAddressBook.getPhoneNumber().replace("-", ""));
			}
			addressBookFacade.edit(selectedAddressBook);
			refreshAddress();
		}
	}

	public void removeAddressBook(AddressBook ab) {
		addressBookFacade.remove(ab);
		applicationBean.getUsers().getAddressBookList().remove(ab);
	}

	public void removeAddressBookDirect() {
		if (selectedAddressBook != null) {
			if (selectedAddressBook.getLead() != null) {
				leadFacade.remove(selectedAddressBook.getLead());
			} else if (selectedAddressBook.getContact() != null) {
				contactFacade.remove(selectedAddressBook.getContact());
			} else {
				addressBookFacade.remove(selectedAddressBook);
			}
			allAddressBooksView.remove(selectedAddressBook);
			if (searchAct) {
				addressBooks.remove(selectedAddressBook);
			}
			applicationBean.getUsers().getAddressBookList().remove(selectedAddressBook);

			/*
			 * if (toolMaster && selectedAddressBook.getContact() != null &&
			 * selectedAddressBook.getContact().getAdvisor() != null &&
			 * applicationBean.getUsers().getProfileTool().getProductSupplier() != null) {
			 * boolean addIt = false; for (ContractSetup cs :
			 * selectedAddressBook.getContact().getAdvisor().getContractSetupList()) { if
			 * ((cs.getMgaProductSupplier() != null &&
			 * Objects.equals(cs.getMgaProductSupplier().getProductSupplierIntId(),
			 * applicationBean.getUsers().getProfileTool().getProductSupplier().
			 * getProductSupplierIntId())) || (cs.getAgaProductSupplier() != null &&
			 * Objects.equals(cs.getAgaProductSupplier().getProductSupplierIntId(),
			 * applicationBean.getUsers().getProfileTool().getProductSupplier().
			 * getProductSupplierIntId()))) { addIt = true; } } if (addIt &&
			 * !advisorNavigationBean.getAllAdvisorsBook().isEmpty()) {
			 * //advisorNavigationBean.getAllAdvisorsBook().remove(selectedAddressBook);
			 * //advisorNavigationBean.getAllAdvisorsBook().add(new
			 * AddressBook(selectedAddressBook.getContact(), applicationBean.getUsers()));
			 * advisorNavigationBean.getAllAdvisorsBook().clear();
			 * advisorNavigationBean.setIsFiltered(false); }
			 * 
			 * }
			 */
			// advisorNavigationBean.search();
			advisorNavigationBean.reset();
			selectedAddressBook = null;
		}
	}

	public boolean isSendEmailBool() {
		return sendEmailBool;
	}

	public void setSendEmailBool(boolean sendEmailBool) {
		this.sendEmailBool = sendEmailBool;
	}

	public List<AddressBook> getAddressBooks() {
		return addressBooks;
	}

	public void setAddressBooks(List<AddressBook> addressBooks) {
		this.addressBooks = addressBooks;
	}

	public List<AddressBook> getAllAddressBooks() {
		return allAddressBooks;
	}

	public void setAllAddressBooks(List<AddressBook> allAddressBooks) {
		this.allAddressBooks = allAddressBooks;
	}

	public int getToogle() {
		return toogle;
	}

	public void setToogle(int toogle) {
		this.toogle = toogle;
	}

	public List<SelectItem> getToogleList() {

		List<SelectItem> selectItems = new ArrayList<>();

		for (ProductSupplier ps : productSuppliers) {
			if (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier)) {
				if (applicationBean.getLanguage().isEnglish()) {
					selectItems.add(new SelectItem(1, ps.getOrganization().getName()));
				} else {
					selectItems.add(new SelectItem(3, ps.getOrganization().getOtherName()));
				}

				if (ps.getContractAttachedProductSupplierMga() != null) {
					if (applicationBean.getLanguage().isEnglish()) {
						selectItems.add(new SelectItem(1,
								ps.getContractAttachedProductSupplierMga().getOrganization().getName()));
					} else {
						selectItems.add(new SelectItem(1,
								ps.getContractAttachedProductSupplierMga().getOrganization().getOtherName()));
					}
				}
			}
		}
		if (selectItems.size() == 2) {
			selectItems.add(new SelectItem(2, applicationBean.getLanguage().getString("All.Above")));
		}

		return selectItems;
	}

	public List<SelectItem> getTreeContractList() {

		List<SelectItem> selectItems = new ArrayList<>();

		for (ProductSupplier ps : productSuppliers) {
			if (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier)) {

				if (applicationBean.getLanguage().isEnglish()) {
					selectItems.add(new SelectItem(ps.getProductSupplierIntId(), ps.getOrganization().getName()));
				} else {
					selectItems.add(new SelectItem(ps.getProductSupplierIntId(), ps.getOrganization().getOtherName()));
				}

				if (ps.getContractAttachedProductSupplierMga() != null) {
					if (applicationBean.getLanguage().isEnglish()) {
						selectItems.add(
								new SelectItem(ps.getContractAttachedProductSupplierMga().getProductSupplierIntId(),
										ps.getContractAttachedProductSupplierMga().getOrganization().getName()));
					} else {
						selectItems.add(
								new SelectItem(ps.getContractAttachedProductSupplierMga().getProductSupplierIntId(),
										ps.getContractAttachedProductSupplierMga().getOrganization().getOtherName()));
					}
				}
				if (ps.getContractAttachedProductSupplierAga() != null) {
					if (applicationBean.getLanguage().isEnglish()) {
						selectItems.add(
								new SelectItem(ps.getContractAttachedProductSupplierAga().getProductSupplierIntId(),
										ps.getContractAttachedProductSupplierAga().getOrganization().getName()));
					} else {
						selectItems.add(
								new SelectItem(ps.getContractAttachedProductSupplierAga().getProductSupplierIntId(),
										ps.getContractAttachedProductSupplierAga().getOrganization().getOtherName()));
					}
				}

				if (ps.getContractAttachedProductSupplierAga2() != null) {
					if (applicationBean.getLanguage().isEnglish()) {
						selectItems.add(
								new SelectItem(ps.getContractAttachedProductSupplierAga2().getProductSupplierIntId(),
										ps.getContractAttachedProductSupplierAga2().getOrganization().getName()));
					} else {
						selectItems.add(
								new SelectItem(ps.getContractAttachedProductSupplierAga2().getProductSupplierIntId(),
										ps.getContractAttachedProductSupplierAga2().getOrganization().getOtherName()));
					}
				}

				break;
			}
		}
		/*
		 * if (selectItems.size() == 2) { selectItems.add(new SelectItem(2,
		 * applicationBean.getLanguage().getString("All.Above"))); }
		 */

		return selectItems;
	}

	private List<ProductSupplier> treeContractListElement;

	public List<ProductSupplier> getTreeContractListDialogElements() {
		if (masterMode || toolMaster) {
			return getTreeContractListAdvisor();
		} else {
			return treeContractListElement;
		}
	}

	public List<ProductSupplier> getTreeContractListElement() {
		return treeContractListElement;
	}

	public void setTreeContractListElement(List<ProductSupplier> treeContractListElement) {
		this.treeContractListElement = treeContractListElement;
	}

	public List<ProductSupplier> getTreeContractListAdvisor() {
		List<ProductSupplier> elements = new ArrayList<>();
		if (!toSend.isEmpty() && toSend.get(0) != null) {
			AddressBook advisor = toSend.get(0);
			if (advisor.getMga() != null) {
				elements.add(advisor.getMga());
			}
			if (advisor.getAga() != null) {
				elements.add(advisor.getAga());
			}
			if (advisor.getAga2() != null) {
				elements.add(advisor.getAga2());
			}
		}
		return elements;
	}

	public void buildTreeContractListView() {
		treeContractListElement = new ArrayList<>();

		for (ProductSupplier ps : productSuppliers) {
			if (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier)) {

				treeContractListElement.add(ps);

				if (ps.getContractAttachedProductSupplierMga() != null) {
					treeContractListElement.add(ps.getContractAttachedProductSupplierMga());
				}
				if (ps.getContractAttachedProductSupplierAga() != null) {
					treeContractListElement.add(ps.getContractAttachedProductSupplierAga());
				}

				if (ps.getContractAttachedProductSupplierAga2() != null) {
					treeContractListElement.add(ps.getContractAttachedProductSupplierAga2());
				}

				break;
			}
		}

	}

	public void updateMgaRightPart() {
		if (!productSuppliers.isEmpty()) {
			toogle = productSuppliers.get(0).getProductSupplierIntId();
		}
		// renderingMgaName = false;
		/*
		 * for (ProductSupplier ps : productSuppliers) { if
		 * (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier) &&
		 * ps.getContractAttachedProductSupplierMga() != null) { for (Contact co :
		 * ps.getContractAttachedProductSupplierMga().getOrganization().getContactList()
		 * ) { for (ContactAction ca : co.getContactActionList()) { if
		 * (ca.getContactActionType() == 13) { renderingMgaName = true; break; } } if
		 * (renderingMgaName) { break; } } } if (renderingMgaName) { break; } }
		 */
	}

	public void updateContacts(ProductSupplier ps) {
		contactsAgencyList.clear();
		for (Contact cont : ps.getOrganization().getContactList()) {
			if (cont.canRecieveDocuments()) {
				contactsAgencyList.add(cont);
			}
		}
	}

	public void updateContactsForTree() {
		for (ProductSupplier ps : treeContractListElement) {
			if (ps.getDocumentRecievers().isEmpty()) {
				ps.setDocumentRecievers(contactFacade.findContactThatCanRecieveDocuments(ps.getOrganization()));
			}
		}
	}

	public void updateContactsForTreeAdvisor() {
		for (ProductSupplier ps : getTreeContractListAdvisor()) {
			if (ps.getDocumentRecievers().isEmpty()) {
				ps.setDocumentRecievers(contactFacade.findContactThatCanRecieveDocuments(ps.getOrganization()));
			}
		}
	}

	public List<Contact> getContactsAgencyList() {
		return contactsAgencyList;
	}

	public void setContactsAgencyList(List<Contact> contactsAgencyList) {
		this.contactsAgencyList = contactsAgencyList;
	}

	public String getMgaName() {
		String name = "";
		for (ProductSupplier ps : productSuppliers) {
			if (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier)
					&& ps.getContractAttachedProductSupplierMga() != null) {
				if (applicationBean.getLanguage().isEnglish()) {
					name = ps.getContractAttachedProductSupplierMga().getOrganization().getName();
				} else {
					name = ps.getContractAttachedProductSupplierMga().getOrganization().getOtherName();
				}
			}
		}
		if (name != null && !name.isEmpty()) {
			renderingMgaName = true;
		} else {
			renderingMgaName = false;
		}
		return name;
	}

	public String getCompanyName() {
		String name = "";
		for (ProductSupplier ps : productSuppliers) {
			if (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier)) {
				if (applicationBean.getLanguage().isEnglish()) {
					name = ps.getOrganization().getName();
				} else {
					name = ps.getOrganization().getOtherName();
				}
			}
		}
		return name;
	}

	public boolean isRenderingMgaName() {
		return renderingMgaName;
	}

	public void setRenderingMgaName(boolean renderingMgaName) {
		this.renderingMgaName = renderingMgaName;
	}

	public List<ProductSupplier> getSuppliersSelected() {
		return suppliersSelected;
	}

	public void setSuppliersSelected(List<ProductSupplier> suppliersSelected) {
		this.suppliersSelected = suppliersSelected;
	}

	public Integer getSelectedAgency() {
		return selectedAgency;
	}

	public void setSelectedAgency(Integer selectedAgency) {
		this.selectedAgency = selectedAgency;
	}

	public void fillContactsAgency() {
		// contactsAgency.clear();
		// contactsAgencyFilter.clear();
		agenciesFilled.clear();
		ProductSupplier ps;
		for (Integer id : selectedAgencies) {
			ps = productSupplierFacade.find(id);
			ps.setDocumentRecievers(new ArrayList<>());
			for (Contact con : ps.getOrganization().getContactList()) {
				if (con.canRecieveDocuments()) {
					ps.getDocumentRecievers().add(con);
					// contactsAgency.add(con);
				}
			}
			agenciesFilled.add(ps);
		}
	}

	public void clearAgencySelection() {
		selectedAgencies.clear();
	}

	public List<ProductSupplier> getAgenciesFilled() {
		return agenciesFilled;
	}

	public void setAgenciesFilled(List<ProductSupplier> agenciesFilled) {
		this.agenciesFilled = agenciesFilled;
	}

	public String getAdvisorsNames() {
		StringBuilder sb = new StringBuilder();

		for (AddressBook ab : toSend) {
			sb.append(ab.getFirstname());
			sb.append(" ");
			sb.append(ab.getLastname());
			sb.append("; ");
		}
		return sb.toString().substring(0, sb.toString().length() - 2);

	}

	public String getContactsNames() {
		StringBuilder sb = new StringBuilder();

		for (Contact c : toSendContacts) {
			sb.append(c.getFullName());
			sb.append("; ");
		}
		return sb.toString().substring(0, sb.toString().length() - 2);

	}

	public String getContactsNamesAgency() {
		StringBuilder sb = new StringBuilder();

		for (Contact c : toSendContacts) {
			sb.append("(");
			if (applicationBean.getLanguage().isEnglish()) {
				sb.append(c.getOrganization().getOrganizationDescEn());
			} else {
				sb.append(c.getOrganization().getOrganizationDescFr());
			}
			sb.append(")");
			sb.append(c.getFullName());
			sb.append("; ");
		}
		return sb.toString().substring(0, sb.toString().length() - 2);

	}

	public List<Contact> getContactsAgency() {
		return contactsAgency;
	}

	public ProductSupplier getSelectedProductSupplierAdvisor() {
		for (ProductSupplier ps : productSuppliers) {
			if (Objects.equals(ps.getProductSupplierIntId(), selectedProductSuplier)) {
				if (ps.getProductSupplierIntId() == toogle) {
					return ps;
				} else if (ps.getContractAttachedProductSupplierMga().getProductSupplierIntId() == toogle) {
					return ps.getContractAttachedProductSupplierMga();
				} else if (ps.getContractAttachedProductSupplierAga().getProductSupplierIntId() == toogle) {
					return ps.getContractAttachedProductSupplierAga();
				} else {
					return ps.getContractAttachedProductSupplierAga2();
				}
			}
		}
		return null;
	}

	/*
	 * public List<Contact> getContactsAgencyList() { List<Contact> result = new
	 * ArrayList<>();
	 * 
	 * ProductSupplier psSelected = getSelectedProductSupplierAdvisor(); if
	 * (psSelected != null) { for (Contact c :
	 * psSelected.getOrganization().getContactList()) { if (c.canRecieveDocuments())
	 * { result.add(c); } } }
	 * 
	 * return result; }
	 */
	public void setContactsAgency(List<Contact> contactsAgency) {
		this.contactsAgency = contactsAgency;
	}

	public List<Integer> getSelectedAgencies() {
		return selectedAgencies;
	}

	public void setSelectedAgencies(List<Integer> selectedAgencies) {
		this.selectedAgencies = selectedAgencies;
	}

	public void cancelSearchAddress() {
		allAddressBooks = addressBookFacade.findByOwnerContact(applicationBean.getUsers());
		addressBooks.clear();
		toSend.clear();
	}

	public List<AddressBook> getRecipientList() {
		return recipientList;
	}

	public void setRecipientList(List<AddressBook> recipientList) {
		this.recipientList = recipientList;
	}

	public void clearRecipientList() {
		recipientList = new ArrayList<>();
	}

	public void addRecipient(AddressBook ab) {
		if (ab.isSelected()) {
			recipientList.add(ab);
		} else {
			recipientList.remove(ab);
		}
	}

	public int getViewTabId() {
		return viewTabId;
	}

	public void setViewTabId(int viewTabId) {
		this.viewTabId = viewTabId;
	}

	public Activity getLastActivity() {
		return lastActivity;
	}

	public void setLastActivity(Activity lastActivity) {
		this.lastActivity = lastActivity;
	}

	public List<StoredFile> getStoredFileList() {
		return storedFileList;
	}

	public void setStoredFileList(List<StoredFile> storedFileList) {
		this.storedFileList = storedFileList;
	}

	public StoredFile getSelectedFileNotes() {
		return selectedFileNotes;
	}

	public void setSelectedFileNotes(StoredFile selectedFileNotes) {
		this.selectedFileNotes = selectedFileNotes;
		selectedNote = null;
	}

	public StoredFileNote getSelectedNote() {
		return selectedNote;
	}

	public void setSelectedNote(StoredFileNote selectedNote) {
		this.selectedNote = selectedNote;
	}

	public StoredFileNote getSelectedNoteToDelete() {
		return selectedNoteToDelete;
	}

	public void setSelectedNoteToDelete(StoredFileNote selectedNoteToDelete) {
		this.selectedNoteToDelete = selectedNoteToDelete;
	}

	public StoredFile getSelectedFileToDelete() {
		return selectedFileToDelete;
	}

	public void setSelectedFileToDelete(StoredFile selectedFileToDelete) {
		this.selectedFileToDelete = selectedFileToDelete;
	}

	public StoredFile getSelectedFile() {
		return selectedFile;
	}

	public void setSelectedFile(StoredFile selectedFile) {
		this.selectedFile = selectedFile;
	}

	public List<Activity> getSentMessages() {
		return sentMessages;
	}

	public void setSentMessages(List<Activity> sentMessages) {
		this.sentMessages = sentMessages;
	}

	public List<SelectItem> getProductSupplierTypesItems() {

		List<SelectItem> typesSelectItems = new ArrayList<>();

		Locale locale = applicationBean.getLanguage().getLocale();

		typesSelectItems.add(new SelectItem(null, applicationBean.getLanguage().getString("Select.One")));

		for (ProductSupplierType type : productSupplierTypes) {
			if (locale == Locale.CANADA) {
				typesSelectItems
						.add(new SelectItem(type.getProductSupplierTypeIntId(), type.getProductSuppTypeDescEn()));
			}

			if (locale == Locale.CANADA_FRENCH) {
				typesSelectItems
						.add(new SelectItem(type.getProductSupplierTypeIntId(), type.getProductSuppTypeDescFr()));
			}
		}

		return typesSelectItems;
	}

	private List<ProductSupplierType> loadSupplierTypesList() {

		return productSupplierFacade.findAllProductSupplierTypesForGabinet();
	}

	public void onProductSupplierTypeSearchSelected() {

		// get the ProductSupplierType from the list
		ProductSupplierType selected = null;

		productSuppliers.clear();
		selectedProdSuppTypeId = 0;
		selectedCompanyId = 0;

		selectedProductSupplier = null;
		if (searchProdSuppTypeId != null) {
			for (ProductSupplierType type : productSupplierTypes) {

				if (searchProdSuppTypeId.intValue() == type.getProductSupplierTypeIntId().intValue()) {
					selected = type;
				}
			}

			productSuppliers = productSupplierFacade.findProductSupplierByType(selected);
		}
		if (productSuppliers != null && !productSuppliers.isEmpty()) {
			selectedProductSupplier = productSuppliers.get(0);
		}

	}

	public List<SelectItem> getProductSuppliersItems() {

		List<SelectItem> typesSelectItems = new ArrayList<>();

		Locale locale = applicationBean.getLanguage().getLocale();

		typesSelectItems.add(new SelectItem(null, applicationBean.getLanguage().getString("Select.One")));

		for (ProductSupplier ps : productSuppliers) {
			if (locale == Locale.CANADA) {
				typesSelectItems.add(
						new SelectItem(ps.getProductSupplierIntId(), ps.getOrganization().getOrganizationDescEn()));
			}

			if (locale == Locale.CANADA_FRENCH) {
				typesSelectItems.add(
						new SelectItem(ps.getProductSupplierIntId(), ps.getOrganization().getOrganizationDescFr()));
			}
		}
		return typesSelectItems;
	}

	public void onProductSupplierSelected() {
		selectedProductSupplier = productSupplierFacade.find(selectedCompanyId);
		selectedFile.setProductSupplierPer(selectedProductSupplier);
	}

	public void saveUploadFile() {

		if (selectedFile.getStoredFileIntId() == null) {
			if (uploadedFile == null) {
				return;
			}

			String normalizedName = uploadedFile.getFileName();

			StoredFileDesc storedFileDesc = new StoredFileDesc();
			selectedFile.setType(13);
			selectedFile.setTypeId(selectedAddressBook.getLead().getLeadIntId());
			selectedFile.setCreationDate(applicationBean.now());
			selectedFile.setName(normalizedName);
			storedFileDesc.setFileEn(uploadedFile.getContent());

			storedFileFacade.create(selectedFile);
			storedFileDesc.setStoredFileIntId(selectedFile.getStoredFileIntId());
			storedFileDescFacade.create(storedFileDesc);

			selectedFile.setStoredFileDesc(storedFileDesc);

			storedFileList.add(selectedFile);

			selectedFile = null;
			uploadedFile = null;
		} else {
			storedFileFacade.edit(selectedFile);
			selectedFile = null;
		}

	}

	public void setSelectedFileEdit(StoredFile selectedFile) {
		this.selectedFile = selectedFile;
		selectedNote = null;
		if (selectedFile.getProductSupplierPer() != null) {
			searchProdSuppTypeId = selectedFile.getProductSupplierPer().getProductSupplierType()
					.getProductSupplierTypeIntId();
			onProductSupplierTypeSearchSelected();
			selectedProductSupplier = selectedFile.getProductSupplierPer();
			selectedCompanyId = selectedProductSupplier.getProductSupplierIntId();
		}
	}

	public String documentTopicDesc(Integer id) {
		if (id == null) {
			id = 66537;
		}

		if (applicationBean.getLanguage().isEnglish()) {
			return typesFacade.findDocumentTypeAndSub(66417, id).getDescEn();
		} else {
			return typesFacade.findDocumentTypeAndSub(66417, id).getDescFr();
		}
	}

	public String documentTypeByTopicDesc(Integer topic, int type) {
		if (topic == null) {
			topic = 66537;
		}
		if (type <= 0) {
			type = 69637;
		}
		if (applicationBean.getLanguage().isEnglish()) {
			return typesFacade.findDocumentTypeAndId(topic, type).getDescEn();
		} else {
			return typesFacade.findDocumentTypeAndId(topic, type).getDescFr();
		}
	}

	public Integer getSelectedProdSuppTypeId() {
		return selectedProdSuppTypeId;
	}

	public void setSelectedProdSuppTypeId(Integer selectedProdSuppTypeId) {
		this.selectedProdSuppTypeId = selectedProdSuppTypeId;
	}

	public Integer getSelectedCompanyId() {
		return selectedCompanyId;
	}

	public void setSelectedCompanyId(Integer selectedCompanyId) {
		this.selectedCompanyId = selectedCompanyId;
	}

	public Integer getSearchProdSuppTypeId() {
		return searchProdSuppTypeId;
	}

	public void setSearchProdSuppTypeId(Integer searchProdSuppTypeId) {
		this.searchProdSuppTypeId = searchProdSuppTypeId;
	}

}
