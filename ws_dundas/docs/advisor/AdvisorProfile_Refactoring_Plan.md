# AdvisorProfileU.xhtml Refactoring Plan to Spring Boot REST APIs

## 1. Introduction

This document outlines the plan to refactor the `AdvisorProfileU.xhtml` page and its associated backend logic into a modern Spring Boot application exposing RESTful APIs. We will analyze the existing functionalities, data dependencies, and business logic to ensure a comprehensive migration.

## 2. Identified Functionalities of AdvisorProfileU.xhtml

Based on the analysis of `advisor.md` (particularly sections 1 and 3) and a review of `AdvisorProfileU.xhtml`, the following core functionalities have been identified. We will detail the logic, data, and tables for each in subsequent sections.

### 2.1. Advisor Search and Selection
*   **Description:** Allows users (potentially admin/manager roles) to search for and select an advisor to view or manage their profile. This is a prerequisite for displaying any advisor-specific information.
*   **Existing UI Elements (from `AdvisorProfileU.xhtml`):** Search input fields, search button, results display table/list.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   `GET /api/v1/advisors/search`: Searches for advisors based on specified criteria.
            *   Query Parameters: e.g., `name`, `advisorCode`, `email`, `status`, `agaId` (if searching within an AGA's hierarchy), `page`, `size`, `sortBy`, `sortDirection`.
    *   **Service Layer (e.g., `AdvisorSearchService` or methods within `AdvisorService` like `searchAdvisors`):**
        *   `searchAdvisors(AdvisorSearchCriteriaDTO criteria, Pageable pageable, Principal principal)`: Handles the logic for searching advisors, applying filters, pagination, sorting, and access control.
    *   **Data Transfer Objects (DTOs):**
        *   `AdvisorSearchCriteriaDTO`: Carries the search parameters from the client.
            *   Illustrative fields: `String nameOrCode`, `String email`, `String status`, `Long agaId`, etc.
        *   `AdvisorSearchResultDTO`: Represents a single advisor in the search results list (could be a slimmer version of `AdvisorProfileDTO`, e.g., containing only ID, name, code, status, primary company name).
        *   `PagedResultDTO<AdvisorSearchResultDTO>`: A generic DTO to carry paginated search results, including the list of `AdvisorSearchResultDTO` and pagination metadata (total elements, total pages, etc.).
    *   **Repository Layer (Spring Data JPA):**
        *   `AdvisorRepository`: Will require custom query methods or the use of Spring Data JPA Specifications/Criteria API to build dynamic search queries.
        *   Example method: `Page<Advisor> findByCriteria(AdvisorSearchCriteriaDTO criteria, Pageable pageable)`.
    *   **Security (Spring Security):**
        *   The search endpoint will be secured.
        *   Search results may need to be filtered based on the roles and permissions of the `principal` (e.g., an AGA might only see advisors in their downline, a regular advisor might not have access to this search at all or only a very limited version).
*   **Detailed Business Logic (Re-contextualized for Spring Boot):**
    *(The following subsections will detail the logic within the proposed Spring Boot components for searching advisors.)*
    *   **Receiving Search Request (within `AdvisorController` and `AdvisorSearchService`):**
        *   The `AdvisorController` receives the GET request with query parameters. These parameters are mapped to an `AdvisorSearchCriteriaDTO` object.
        *   Pagination and sorting parameters (`page`, `size`, `sortBy`, `sortDirection`) are mapped to a Spring `Pageable` object.
        *   The `AdvisorSearchCriteriaDTO` and `Pageable` object, along with the `Principal`, are passed to the `AdvisorSearchService.searchAdvisors` method.
    *   **Applying Search Logic and Access Control (within `AdvisorSearchService.searchAdvisors`):**
        1.  **Authorization Check:** Verify if the `principal` has the necessary permissions to perform advisor searches.
        2.  **Dynamic Query Construction:**
            *   Based on the fields populated in `AdvisorSearchCriteriaDTO`, the service (or a dedicated query builder component) will construct a dynamic JPA query. This is typically done using:
                *   **Spring Data JPA Specifications:** A powerful way to build queries programmatically using the Criteria API. Each search field can correspond to a `Specification`.
                *   **Querydsl:** Another popular option for type-safe query construction.
                *   Manually constructed JPQL with conditional clauses (less flexible for many optional criteria).
            *   The query will target the `Advisor` entity and potentially join with related entities like `Contact` (for name, email) or `Company` (for business name) if search criteria involve these.
        3.  **Access Control on Results:**
            *   If the logged-in user is an AGA, the search query must be further restricted to only include advisors associated with that AGA. This might involve adding a condition like `advisor.aga.id = :agaId` to the query, where `agaId` is derived from the `principal` or explicitly passed if an admin is searching on behalf of an AGA.
            *   Other role-based filtering might apply (e.g., managers seeing advisors in their region/branch).
        4.  **Executing Search Query:**
            *   The constructed query along with the `Pageable` object is executed via the `AdvisorRepository` (e.g., `advisorRepository.findAll(specification, pageable)`).
            *   This returns a `Page<Advisor>` object containing the list of matching `Advisor` entities for the current page and pagination metadata.
    *   **Mapping Results to DTOs (within `AdvisorSearchService.searchAdvisors`):**
        *   The `Page<Advisor>` is then converted to a `PagedResultDTO<AdvisorSearchResultDTO>`.
        *   Each `Advisor` entity in the page's content is mapped to an `AdvisorSearchResultDTO`. This DTO should contain a summary of information suitable for a search results list (e.g., ID, full name, advisor code, primary business name, status).
    *   **Returning Response:**
        *   The `PagedResultDTO<AdvisorSearchResultDTO>` is returned by the service to the controller, which then sends it as the HTTP response.

### 2.2. Advisor Profile Display (Main View)
*   **Description:** The main container page that displays different aspects of an advisor\'s profile, typically organized into tabs. It serves as the entry point for viewing an existing advisor\'s full profile and is the orchestrator for **creating a new advisor profile** or **modifying an existing one**. While data entry for specific sections (like personal info, address, licenses) happens within their respective tabs/sub-sections, the overall "save" or "create" actions are typically managed at this main view level.
*   **Existing UI Elements (from `AdvisorProfileU.xhtml`):** Overall page structure, tab navigation, global action buttons like "Save", "Create New Advisor" (if applicable directly on this page, or navigation to a creation form). The `<ui:include src="AdvisorInformationU.xhtml" rendered="#{applicationBean.viewId eq 0}" />` and `<ui:include src="BusinessDetailsU.xhtml" rendered="#{applicationBean.viewId eq 1}" />` suggest that different views (which could be tabs or distinct sections for create/edit) are loaded into this main display.
*   **Data & Source Tables (for Create/Modify operations):**
    *   **Primary Entity:** `Advisor` (Table: `ADVISOR`) - This is the root entity being created or modified.
    *   **Core Related Entities (often created/updated simultaneously):**
        *   `Contact` (Table: `CONTACT`) - Personal details, usually created with a new Advisor.
        *   `Address` (Table: `ADDRESS`), `Phone` (Table: `PHONE`), `Email` (Table: `EMAIL`) - Linked to `Contact`.
        *   `Users` (Table: `USERS`) - If a login account is created/linked during advisor setup.
    *   Other related entities (Companies, Licenses, etc.) are generally managed in their specific tabs but are part of the overall Advisor profile that `AdvisorManagementBean` would persist.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   `GET /api/v1/advisors/{advisorId}`: Retrieves the full profile for a given advisor.
        *   `POST /api/v1/advisors`: Creates a new advisor profile.
        *   `PUT /api/v1/advisors/{advisorId}`: Updates an existing advisor profile.
        *   `DELETE /api/v1/advisors/{advisorId}`: Deactivates or deletes an advisor profile.
    *   **Service Layer (e.g., `AdvisorService`):** This service will encapsulate the core business logic for managing advisor profiles.
        *   `getAdvisorProfile(advisorId, principal)`: Handles logic for fetching and constructing the advisor's complete profile. This includes determining access rights based on the `principal` (authenticated user).
        *   `createAdvisor(CreateAdvisorRequestDTO, principal)`: Handles the creation of a new advisor, including associated entities like `Contact` and potentially `Users`.
        *   `updateAdvisor(advisorId, UpdateAdvisorRequestDTO, principal)`: Handles updates to an existing advisor's profile, including managing collections of related entities.
        *   `deleteAdvisor(advisorId, principal)`: Handles the deactivation or deletion of an advisor, including business rule checks.
    *   **Data Transfer Objects (DTOs):**
        *   `AdvisorProfileDTO`: Represents the comprehensive advisor profile for API responses.
        *   `CreateAdvisorRequestDTO`: Carries data for creating a new advisor.
        *   `UpdateAdvisorRequestDTO`: Carries data for updating an advisor.
        *   (And other DTOs for nested structures like `AddressDTO`, `LicenseDTO`, `CompanyDTO`, etc.)
        *   **Key DTO Structures (Illustrative):**
            *   **`AdvisorProfileDTO`**:
                *   `Long advisorId`
                *   `String advisorCode` (e.g., `agentCode`)
                *   `ContactDTO contactInfo`
                *   `List<CompanyDTO> companies`
                *   `List<LicenseDTO> licenses`
                *   `List<ContractSetupDTO> contractSetups`
                *   `ContractEftDTO contractEft` (if singular, or `List<ContractEftDTO>` if multiple)
                *   `UserSummaryDTO userAccountInfo` (e.g., username, status)
                *   `List<OnboardingStatusDTO> onboardingStatuses`
                *   `String primaryBusinessName` (denormalized from primary `Company`)
                *   `String primaryBusinessNumber` (denormalized)
                *   `String currentTier`
                *   `LocalDate initialContractDate`
                *   `String createdBy`
                *   `LocalDateTime createdDate`
                *   `String lastModifiedBy`
                *   `LocalDateTime lastModifiedDate`
                *   (Other relevant fields from `Advisor` entity like `advisorType`, `status`, etc.)
            *   **`ContactDTO`**:
                *   `Long contactId`
                *   `String firstName`
                *   `String lastName`
                *   `String middleName`
                *   `String preferredName`
                *   `LocalDate dateOfBirth`
                *   `String sin` (masked or handled securely)
                *   `String preferredLanguage`
                *   `List<AddressDTO> addresses`
                *   `List<PhoneDTO> phones`
                *   `List<EmailDTO> emails`
                *   (Other relevant fields from `Contact` entity)
            *   **`AddressDTO`**:
                *   `Long addressId`
                *   `String addressType` (e.g., Home, Business, Mailing)
                *   `String streetAddress1`
                *   `String streetAddress2`
                *   `String city`
                *   `String provinceCode` (or `ProvinceDTO`)
                *   `String postalCode`
                *   `String countryCode` (or `CountryDTO`)
                *   `Boolean isPrimary`
            *   **`PhoneDTO`**:
                *   `Long phoneId`
                *   `String phoneType` (e.g., Mobile, Home, Work)
                *   `String phoneNumber`
                *   `String extension`
                *   `Boolean isPrimary`
            *   **`EmailDTO`**:
                *   `Long emailId`
                *   `String emailType` (e.g., Personal, Work)
                *   `String emailAddress`
                *   `Boolean isPrimary`
            *   **`CompanyDTO`**:
                *   `Long companyId`
                *   `String companyName`
                *   `String businessNumber`
                *   `String companyType` (e.g., Incorporated, Sole Proprietorship)
                *   `Boolean isPrimary`
                *   `AddressDTO businessAddress` (or relevant address fields)
                *   (Other relevant fields from `Company` entity)
            *   **`LicenseDTO`**:
                *   `Long licenseId`
                *   `String licenseNumber`
                *   `String provinceCode` (or `ProvinceDTO`)
                *   `String licenseCategoryCode` (or `LicenseCategoryDTO`)
                *   `LocalDate issueDate`
                *   `LocalDate expiryDate`
                *   `List<LicenseLiabilityDTO> liabilities`
            *   **`LicenseLiabilityDTO`**:
                *   `Long liabilityId`
                *   `String carrierName` (or `IMCompanyDTO`)
                *   `String policyNumber`
                *   `BigDecimal amount`
                *   `LocalDate expiryDate`
            *   **`ContractSetupDTO`**:
                *   `Long contractSetupId`
                *   `String productSupplierCode` (or `ProductSupplierDTO`)
                *   `String contractNumber`
                *   `LocalDate effectiveDate`
                *   `String status`
            *   **`ContractEftDTO`**:
                *   `Long eftId`
                *   `String bankInstitutionNumber`
                *   `String bankTransitNumber`
                *   `String accountNumber`
                *   `String accountHolderName`
                *   `String imCompanyCode` (or `IMCompanyDTO` for bank details)
            *   **`UserSummaryDTO`**: (Represents linked `Users` account info)
                *   `Long userId`
                *   `String username`
                *   `String status` (e.g., Active, Inactive)
                *   `List<String> roles`
            *   **`OnboardingStatusDTO`**:
                *   `Long statusId`
                *   `String statusName` (e.g., "Personal Info Completed", "Licenses Verified")
                *   `String statusCode`
                *   `Boolean isCompleted`
                *   `LocalDate completionDate`
            *   **`CreateAdvisorRequestDTO`**:
                *   `ContactDTO contactInfo` (likely with validations for required fields)
                *   `UserCreateRequestDTO userInfo` (if creating user simultaneously, e.g., username, password, roles)
                *   `CompanyDTO initialCompanyInfo` (optional, if a primary company is created alongside)
                *   (Other essential fields for a new Advisor like `advisorType`, `agentCode` if not auto-generated)
            *   **`UpdateAdvisorRequestDTO`**:
                *   (Contains fields from `AdvisorProfileDTO` that are updatable, often mirroring its structure but with all fields optional, or specific DTOs for sub-sections if granular updates are preferred e.g. `UpdateContactInfoDTO`, `UpdateCompanyDTO`)
                *   `ContactDTO contactInfo`
                *   `List<AddressDTO> addresses` (for managing the full list of addresses)
                *   `List<PhoneDTO> phones`
                *   `List<EmailDTO> emails`
                *   `List<CompanyDTO> companies`
                *   `List<LicenseDTO> licenses`
                *   `List<ContractSetupDTO> contractSetups`
                *   `ContractEftDTO contractEft`
                *   (Fields from Advisor entity itself that can be updated)
        *   **Mapping Strategy:** Consider using a library like MapStruct for mapping between JPA Entities and DTOs to reduce boilerplate code.
    *   **Repository Layer (Spring Data JPA):**
        *   `AdvisorRepository extends JpaRepository<Advisor, Long>`
        *   `ContactRepository extends JpaRepository<Contact, Long>`
        *   (Repositories for all other relevant entities: `AddressRepository`, `PhoneRepository`, `CompanyRepository`, `LicenseRepository`, `UsersRepository`, `OnboardingStatusRepository`, etc.)
        *   These repositories will manage database interactions, potentially with custom query methods.
    *   **Security (Spring Security):**
        *   API endpoints will be secured, likely using token-based authentication (e.g., JWT).
        *   Role-based authorization will be implemented (e.g., an advisor can only access/edit their own profile, while an admin/manager might have broader access). This will be enforced using Spring Security mechanisms like `@PreAuthorize` annotations on controller or service methods.
*   **Detailed Business Logic (Re-contextualized for Spring Boot):**
    *(The following subsections will detail the logic within the proposed Spring Boot components, primarily the `AdvisorService` methods, mapping to the functionalities previously handled by JSF beans.)*
    *   **Loading Advisor Profile Data (corresponds to `AdvisorService.getAdvisorProfile`):**
        *   **Step 1: Determine Target Advisor and Verify Access (within `AdvisorService.getAdvisorProfile`):**
            *   The `AdvisorService` receives the `advisorId` (from the path variable) and the `Principal` object (representing the authenticated user, injected by Spring Security).
            *   **Scenario A: Logged-in User is an Advisor (Self-Service View):**
                *   The `AdvisorService` will use the `Principal` to get the logged-in `Users`'s ID or username.
                *   It will then query the `AdvisorRepository` (e.g., `advisorRepository.findByUserId(loggedInUser.getId())`) to find the associated `Advisor` record.
                *   If the requested `advisorId` matches the `advisorId` of the logged-in user, access is permitted. If they don't match, or if the user is an advisor but the `advisorId` path variable indicates a different advisor, an access denied error (e.g., HTTP 403 Forbidden) should be returned, unless the advisor also has broader administrative roles.
            *   **Scenario B: Logged-in User is an Admin/Manager/AGA:**
                *   The `AdvisorService` will check the roles/permissions of the user from the `Principal`.
                *   If the user has appropriate administrative roles (e.g., ROLE_ADMIN, ROLE_MANAGER, ROLE_AGA_ADMIN), they are permitted to view the profile associated with the given `advisorId`.
                *   The `advisorId` for data fetching is directly taken from the request path variable.
                *   If an AGA is viewing an advisor, there might be additional checks to ensure the advisor is within their hierarchy, potentially by querying relationships through `AdvisorRepository` or related repositories based on AGA-Advisor linkage tables/fields.
            *   If access is not permitted based on these checks, the service should throw an appropriate security exception (e.g., `AccessDeniedException`), which a global exception handler can translate into an HTTP 403 response.
        *   **Step 2: Fetch Primary `Advisor` Entity (within `AdvisorService.getAdvisorProfile`):**
            *   Once access is confirmed and the target `advisorId` is validated, the `AdvisorService` calls `advisorRepository.findById(advisorId)`.
            *   If the advisor is not found, a `ResourceNotFoundException` (custom exception) should be thrown, leading to an HTTP 404 response.
            *   **Data Retrieved:** The `Advisor` JPA entity.
        *   **Step 3: Fetching Related Entities (within `AdvisorService.getAdvisorProfile`):**
            *   The `AdvisorService` will then orchestrate the fetching of all related data for the `Advisor` entity. This can be achieved through:
                *   **JPA Relationships:** Accessing properties on the `Advisor` entity that are mapped with appropriate fetch types (e.g., `advisor.getContact()`, `advisor.getCompanies()`). For collections that are LAZY loaded, ensure they are initialized before the DTO mapping (e.g., by calling `size()` or using `Hibernate.initialize()`, or preferably by using JOIN FETCH in repository queries).
                *   **Dedicated Repository Calls:** For more complex queries or to control fetching strategies, the `AdvisorService` might call other repositories directly (e.g., `contactRepository.findByAdvisor_AdvisorId(advisorId)`, `companyRepository.findAllByAdvisor_AdvisorId(advisorId)`).
            *   **Specific Entities:**
                *   **`Contact` Information:** Accessed via `advisor.getContact()`. Its nested collections (`addresses`, `phones`, `emails`) would be loaded based on their JPA mappings or explicit repository calls (e.g., `addressRepository.findAllByContact_ContactId(contact.getContactId())`). `Province` and `Country` for addresses are fetched via their relationships.
                *   **`Company` Information:** Accessed via `advisor.getCompanies()` or `companyRepository.findAllByAdvisor_AdvisorId(advisorId)`.
                *   **`License` Information:** Accessed via `advisor.getLicenses()` or `licenseRepository.findAllByAdvisor_AdvisorId(advisorId)`. `Province` and `LicenseCategory` are fetched via relationships.
                *   **`LicenseLiability` Information:** Accessed via `license.getLicenseLiabilities()` for each license.
                *   **`ContractSetup` Information:** Accessed via `advisor.getContractSetups()` or `contractSetupRepository.findAllByAdvisor_AdvisorId(advisorId)`. `ProductSupplier` is fetched via relationships.
                *   **`ContractEft` Information:** Accessed via `advisor.getContractEft()` or `contractEftRepository.findByAdvisor_AdvisorId(advisorId)` (assuming one-to-one or a specific primary EFT). If `IMCompany` (bank list) is a general list, it might be fetched by a separate call to `imCompanyRepository.findAll()` and provided as context if needed for selection, or the relevant `IMCompany` linked to the `ContractEft` is fetched via its relationship.
                *   **`Users` Information:** Accessed via `advisor.getUser()` or `userRepository.findByAdvisor_AdvisorId(advisorId)`.
                *   **`OnboardingStatus` Information:** Fetched via `onboardingStatusRepository.findAllByContact_ContactId(advisor.getContact().getContactId())`.
        *   **Optimization with JOIN FETCH:**
            *   To avoid N+1 problems, the `AdvisorRepository` could have a custom method `findFullAdvisorProfileById(advisorId)` that uses JPQL `JOIN FETCH` to load the `Advisor` along with its essential related entities (e.g., `Contact`, `Contact.addresses`, `Companies`, `Licenses`) in fewer queries.
            *   Example: `SELECT a FROM Advisor a LEFT JOIN FETCH a.contact c LEFT JOIN FETCH c.addresses LEFT JOIN FETCH a.companies WHERE a.advisorId = :advisorId`.
        *   **Step 4: Map Entities to `AdvisorProfileDTO` (within `AdvisorService.getAdvisorProfile`):**
            *   The `AdvisorService` (or a dedicated mapper component called by the service) will map the fetched `Advisor` JPA entity and all its related entities to the `AdvisorProfileDTO` structure defined earlier. This includes mapping nested collections to their respective DTOs (e.g., `Address` to `AddressDTO`, `Company` to `CompanyDTO`).
            *   This DTO is then returned to the `AdvisorController`.
    *   **Creating a New Advisor Profile (corresponds to `AdvisorService.createAdvisor`):**
        *   **Initiation:** A `POST` request to `/api/v1/advisors` with a `CreateAdvisorRequestDTO` in the request body.
        *   **`AdvisorService.createAdvisor(CreateAdvisorRequestDTO requestDTO, Principal principal)`:**
            1.  **Authorization:** Check if the `principal` (authenticated user) has the permission to create advisors (e.g., Admin, Manager role).
            2.  **Validation:** The `requestDTO` is first validated (e.g., using `@Valid` in the controller, triggering Bean Validation on DTO fields). The service may perform additional business-level validations (e.g., checking for uniqueness of `advisorCode` if provided, or SIN/BN if applicable and not handled by database constraints directly on create).
            3.  **Entity Initialization & Population:**
                *   Create a new `Advisor` JPA entity.
                *   Create a new `Contact` JPA entity from `requestDTO.getContactInfo()`. Map `AddressDTOs`, `PhoneDTOs`, `EmailDTOs` within `contactInfo` to their respective JPA entities and associate them with the `Contact` entity.
                *   Associate the new `Contact` with the new `Advisor`.
                *   If `requestDTO.getUserInfo()` is provided:
                    *   Create a new `Users` entity. Populate its fields (username, hashed password, roles).
                    *   Associate the new `Users` entity with the new `Advisor`.
                    *   The `UsersRepository` will be used to save the `Users` entity.
                *   If `requestDTO.getInitialCompanyInfo()` is provided, create a `Company` entity and associate it with the `Advisor`.
                *   Populate other fields of the `Advisor` entity from the `requestDTO`.
            4.  **Persistence (`@Transactional` method):**
                *   The `AdvisorService.createAdvisor` method should be annotated with `@Transactional`.
                *   Save the `Contact` entity using `contactRepository.save(newContact)` (if not cascaded from Advisor).
                *   Save the `Advisor` entity using `advisorRepository.save(newAdvisor)`. If JPA relationships are configured with `CascadeType.PERSIST` or `CascadeType.ALL` (e.g., Advisor to Contact, Contact to Addresses), related entities will be saved automatically. Otherwise, explicit saves for `Address`, `Phone`, `Email` entities via their repositories would be needed after associating them.
                *   Save the `Users` entity using `userRepository.save(newUser)` (if applicable).
                *   Save the initial `Company` entity using `companyRepository.save(newCompany)` (if applicable).
            5.  **Post-Creation Actions (still within the transaction or in an event listener):**
                *   Initialize and save default `OnboardingStatus` records for the new advisor's contact using `onboardingStatusRepository`.
                *   Potentially trigger an event for sending a welcome email (e.g., using Spring Application Events or a message queue).
                *   Audit logging.
            6.  **Response:** Map the newly created `Advisor` (and its core related data) to an `AdvisorProfileDTO` and return it (typically with HTTP 201 Created status).
    *   **Updating an Existing Advisor Profile (corresponds to `AdvisorService.updateAdvisor`):**
        *   **Initiation:** A `PUT` request to `/api/v1/advisors/{advisorId}` with an `UpdateAdvisorRequestDTO` in the request body.
        *   **`AdvisorService.updateAdvisor(Long advisorId, UpdateAdvisorRequestDTO requestDTO, Principal principal)`:**
            1.  **Authorization:** Check if the `principal` has permission to update the specified `advisorId` (self-update for advisor, or admin/manager role).
            2.  **Fetch Existing Advisor:** Retrieve the `Advisor` entity using `advisorRepository.findById(advisorId)`. Throw `ResourceNotFoundException` if not found.
            3.  **Validation:** Validate the `requestDTO`. Perform business-level validations.
            4.  **Entity Update (`@Transactional` method):
                *   Map fields from `requestDTO` to the fetched `Advisor` entity and its related entities. This is a complex step, especially for collections.
                *   **Updating `Contact` and its collections (`Address`, `Phone`, `Email`):**
                    *   Update scalar properties of the `Contact` entity from `requestDTO.getContactInfo()`.
                    *   For collections like `addresses`, `phones`, `emails`: Reconcile the list from the DTO with the existing list in the database. This involves identifying new items to add (create with `addressRepository.save()`), items to remove (delete with `addressRepository.delete()`, ensuring `orphanRemoval=true` or manual removal), and items to update (fetch existing, modify, and rely on JPA dirty checking or explicit `addressRepository.save()`).
                *   **Updating `Company` collection:** Similar reconciliation logic as for addresses, using `companyRepository`.
                *   **Updating `License` collection:** Similar reconciliation logic, using `licenseRepository`.
                *   **Updating `ContractSetup` collection:** Similar reconciliation logic, using `contractSetupRepository`.
                *   **Updating `ContractEft`:** Update fields or replace the entity if it's a one-to-one, using `contractEftRepository`.
                *   The `advisorRepository.save(existingAdvisor)` will persist changes to the `Advisor` and, due to cascading (if configured, e.g. `CascadeType.MERGE` or `ALL`) or explicit repository calls for related entities, the entire graph.
            5.  **Handling Collections (Detailed Strategy):**
                *   A common strategy for updating collections is to: Clear the existing collection on the JPA entity (e.g., `advisor.getCompanies().clear()`) and then add all items from the DTO as new or re-associated entities. This relies on `orphanRemoval=true` to delete old unreferenced items. Alternatively, iterate through DTO items: if an item has an ID, fetch and update; if no ID, create new. Then, iterate existing DB items and remove any not present in the DTO list.
            6.  **Post-Update Actions:**
                *   Update `OnboardingStatus` if relevant sections were modified, using `onboardingStatusRepository`.
                *   Audit logging.
            7.  **Response:** Map the updated `Advisor` to an `AdvisorProfileDTO` and return it (HTTP 200 OK).
    *   **Deleting an Advisor Profile (Conceptual - corresponds to `AdvisorService.deleteAdvisor`):**
        *   **Initiation:** A `DELETE` request to `/api/v1/advisors/{advisorId}`.
        *   **`AdvisorService.deleteAdvisor(Long advisorId, Principal principal)`:**
            1.  **Authorization:** Check if the `principal` has permission to delete/deactivate the advisor (typically Admin/Manager role).
            2.  **Fetch Advisor:** Retrieve `advisorRepository.findById(advisorId)`.
            3.  **Business Rules Check:** Determine if deletion is allowed (e.g., no active contracts, regulatory compliance). This might involve querying other repositories.
            4.  **Soft Delete (Preferred):**
                *   Set an `isActive` flag (or similar status field) on the `Advisor` entity to `false` or `DEACTIVATED`.
                *   If there's a linked `Users` account, its status might also be set to inactive via `userRepository`.
                *   `advisorRepository.save(advisorToDeactivate)`.
            5.  **Hard Delete (If strictly required and allowed):**
                *   This is more complex due to foreign key constraints. Requires careful ordering of deletions or relying on database cascade-on-delete features (if configured, and if `CascadeType.REMOVE` or `ALL` is used on JPA relationships).
                *   Example order: `ContractEft`, `ContractSetup`, `LicenseLiability`, `License`, `Company`, `OnboardingStatus`, `Address`, `Phone`, `Email` (via `Contact`), then `Contact`, then `Users` (if linked and to be hard-deleted), then finally `Advisor`.
                *   Each would involve `repository.delete()` calls.
                *   This should be a `@Transactional` operation.
            6.  **Post-Deletion Actions:**
                *   Audit logging.
            7.  **Response:** HTTP 204 No Content on successful deletion/deactivation.
*   **Relevant Beans (from `advisor.md`):**
    *   `AdvisorNavigationBean`: Controls tab switching, overall view state, loads the selected `Advisor` object and its related data (e.g., `advisorNavigationBean.advisor`) for display and editing. Prepares the POJOs for the page.
    *   `AdvisorManagementBean`: Handles the logic for creating a new advisor (e.g., initializing new `Advisor` and `Contact` entities) and for saving all changes made to an existing advisor. This involves assembling/validating input from various parts of the profile and calling JPA Facades for persistence.
    *   `ApplicationMainBean`: For general application context, user session information.
    *   `AGANavigationBean`: Potentially involved if the logged-in user is an AGA or if the advisor profile display requires AGA-specific contextual information (e.g., for lists like `IMCompany` used in `ContractEft` section, or for contextual navigation if an AGA is managing the advisor). While its primary role might be more prominent in advisor search/selection (functionality 2.1), it can influence data availability or specific data points presented within the advisor\'s profile.

### 2.3. Advisor Personal Information Tab/Section
*   **Description:** This section focuses on managing the advisor\'s core personal details, including their name, date of birth, SIN, contact methods (addresses, phone numbers, email addresses), and preferred language. This data is primarily associated with the `Contact` entity, which is linked to the `Advisor` entity.
*   **Existing UI Elements (from `AdvisorProfileU.xhtml` via `AdvisorInformationU.xhtml`):** Input fields for first name, last name, middle name, preferred name, DOB, SIN, language. Sections/tables for managing multiple addresses, phone numbers, and email addresses, each with type and primary flag.
*   **Data & Source Tables:**
    *   `Contact` (Table: `CONTACT`): Stores personal details like name, DOB, SIN, language.
    *   `Address` (Table: `ADDRESS`): Stores multiple addresses linked to a `Contact`. Includes type (Home, Business, Mailing), primary flag, and address components.
    *   `Phone` (Table: `PHONE`): Stores multiple phone numbers linked to a `Contact`. Includes type (Mobile, Home, Work), primary flag, number, and extension.
    *   `Email` (Table: `EMAIL`): Stores multiple email addresses linked to a `Contact`. Includes type (Personal, Work), primary flag, and email address.
    *   `Province`, `Country`: Lookup tables for address components.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   The personal information is typically part of the main advisor profile. Thus, updates would go through the main `PUT /api/v1/advisors/{advisorId}` endpoint, with the relevant personal information DTOs nested within the `UpdateAdvisorRequestDTO`.
        *   Retrieval is part of `GET /api/v1/advisors/{advisorId}`.
        *   If highly granular updates are desired (e.g., updating only an address), dedicated sub-resource endpoints could be considered, but often updating the relevant section of the main profile DTO is sufficient:
            *   `PUT /api/v1/advisors/{advisorId}/contact-details` (Optional, if a more focused update is needed)
    *   **Service Layer (e.g., `AdvisorService` and potentially `ContactService`):**
        *   `AdvisorService.updateAdvisor(advisorId, UpdateAdvisorRequestDTO, principal)`: As described in 2.2, this service method will handle updates to the `Contact` entity and its associated collections (`Address`, `Phone`, `Email`) based on the data provided in the `requestDTO.contactInfo` part.
        *   Logic for managing collections (add, update, delete) of addresses, phones, and emails will reside here or be delegated to a `ContactService`.
    *   **Data Transfer Objects (DTOs):**
        *   `ContactDTO`: As defined in 2.2, nested within `AdvisorProfileDTO` and `UpdateAdvisorRequestDTO`.
            *   Includes fields for name, DOB, SIN, language, and lists of `AddressDTO`, `PhoneDTO`, `EmailDTO`.
        *   `AddressDTO`, `PhoneDTO`, `EmailDTO`: As defined in 2.2. Each includes fields for type, primary flag, and specific details.
    *   **Repository Layer (Spring Data JPA):**
        *   `ContactRepository extends JpaRepository<Contact, Long>`
        *   `AddressRepository extends JpaRepository<Address, Long>`
        *   `PhoneRepository extends JpaRepository<Phone, Long>`
        *   `EmailRepository extends JpaRepository<Email, Long>`
    *   **Security (Spring Security):**
        *   Permissions to update personal information are governed by the security rules on `PUT /api/v1/advisors/{advisorId}` (e.g., advisor can update their own, admin/manager can update others).
        *   Sensitive data like SIN should be handled with care (e.g., masking in responses if not essential, encryption at rest, field-level security if needed).
*   **Detailed Business Logic (Re-contextualized for Spring Boot - focusing on updates via `AdvisorService.updateAdvisor`):**
    *   **Receiving Update Request:**
        *   The `AdvisorController` receives a `PUT` request to `/api/v1/advisors/{advisorId}` with an `UpdateAdvisorRequestDTO`. The `contactInfo` field within this DTO will contain the personal information to be updated.
    *   **Authorization & Initial Fetch (within `AdvisorService.updateAdvisor`):**
        *   Verify user's permission to update the advisor.
        *   Fetch the existing `Advisor` entity, which includes fetching its associated `Contact` entity.
    *   **Updating `Contact` Scalar Fields (within `AdvisorService.updateAdvisor`):**
        *   If `requestDTO.getContactInfo()` is not null:
            *   Retrieve the `Contact` entity from `advisor.getContact()`. If it's null (should not happen for an existing advisor but good to check), it might indicate an issue or a new advisor creation flow (which is handled by `createAdvisor`).
            *   Update scalar fields on the `Contact` entity: `firstName`, `lastName`, `middleName`, `preferredName`, `dateOfBirth`, `sin` (handle with care), `preferredLanguage` from the `requestDTO.getContactInfo()`.
            *   The `contactRepository.save(contact)` might be called explicitly if cascading is not configured from `Advisor` to `Contact` for updates, or rely on dirty checking within the transaction.
    *   **Managing `Address` Collection (within `AdvisorService.updateAdvisor`):**
        *   If `requestDTO.getContactInfo().getAddresses()` is provided:
            *   Get the existing `List<Address> currentAddresses = contact.getAddresses();`
            *   Get the `List<AddressDTO> updatedAddressDTOs = requestDTO.getContactInfo().getAddresses();`
            *   **Strategy for Collection Update (example):**
                1.  **Identify Deletions:** Iterate through `currentAddresses`. If an address's ID is not found in `updatedAddressDTOs` (or if an address is explicitly marked for deletion in the DTO), remove it from `currentAddresses` and call `addressRepository.delete(addressToDelete)`. If `orphanRemoval=true` is set on the `Contact` to `Address` relationship, removing from the collection and saving the `Contact` is enough.
                2.  **Identify Updates and Additions:** Iterate through `updatedAddressDTOs`:
                    *   If `AddressDTO.getAddressId()` is present: Find the corresponding `Address` in `currentAddresses`. Update its fields from the DTO. `addressRepository.save(existingAddress)` if not relying solely on dirty checking.
                    *   If `AddressDTO.getAddressId()` is null or not found: This is a new address. Create a new `Address` entity, populate it from the DTO, associate it with the `Contact` (`newAddress.setContact(contact)`), and add it to `currentAddresses` (if managing the collection manually before saving parent) or directly save it `addressRepository.save(newAddress)`. Ensure it's added to `contact.getAddresses()` if the JPA provider requires it for relationship persistence.
                3.  **Primary Flag Logic:** Ensure that only one address is marked as primary. If multiple DTOs have `isPrimary=true`, the service should enforce the rule (e.g., last one wins, or throw a validation error). When a new address is set as primary, any existing primary address must have its flag unset.
                *   The `contactRepository.save(contact)` at the end of the `updateAdvisor` method (or `advisorRepository.save(advisor)` if cascading) will persist these changes.
    *   **Managing `Phone` Collection (within `AdvisorService.updateAdvisor`):**
        *   Similar logic to managing `Address` collection, using `contact.getPhones()`, `requestDTO.getContactInfo().getPhones()`, and `phoneRepository`.
        *   Enforce primary phone flag logic.
    *   **Managing `Email` Collection (within `AdvisorService.updateAdvisor`):**
        *   Similar logic to managing `Address` collection, using `contact.getEmails()`, `requestDTO.getContactInfo().getEmails()`, and `emailRepository`.
        *   Enforce primary email flag logic.
    *   **Validation:**
        *   Bean Validation on DTOs (`@Valid` in controller) for format, required fields.
        *   Service-level validation for business rules (e.g., SIN format/checksum if applicable, valid province/country codes, primary flag uniqueness).
    *   **Persistence:**
        *   All changes are saved within the `@Transactional` `updateAdvisor` method. Saving the `Advisor` entity (which cascades to `Contact`, which in turn can cascade to `Address`, `Phone`, `Email` if configured) or saving the `Contact` entity directly will persist the changes.
    *   **Response:** The updated `AdvisorProfileDTO` (containing the updated `ContactDTO`) is returned.
*   **Relevant Beans (from `advisor.md`):**
    *   `AdvisorNavigationBean`: Loads `Advisor` and `Contact` data for display.
    *   `AdvisorManagementBean`: Handles saving changes to `Contact` and its collections (`Address`, `Phone`, `Email`) by interacting with JPA Facades (`ContactFacade`, `AddressFacade`, etc.).
    *   `ListBean`: Provides lists for dropdowns like address type, phone type, email type, provinces, countries. In Spring Boot, these would be provided by dedicated services or could be part of the DTOs themselves if static, or fetched via separate API endpoints if dynamic.

### 2.4. Advisor Business Information (Companies) Tab/Section
*   **Description:** This section manages the advisor's business affiliations, primarily focusing on the companies they are associated with. It handles the addition, removal, and modification of company details linked to the advisor.
*   **Existing UI Elements (from `AdvisorProfileU.xhtml` via `BusinessDetailsU.xhtml`):** Tables/lists displaying associated companies, with options to add, remove, or edit company details. Fields for company name, business number, type, and address. A primary company flag might also be present.
*   **Data & Source Tables:**
    *   `Company` (Table: `COMPANY`): Stores company details like name, business number, type. Linked to `Advisor` (many-to-many or one-to-many if a company is exclusive to one advisor, but typically many-to-many if a company entity can be shared, or one-to-many from Advisor to Company if companies are advisor-specific instances).
    *   `Address` (Table: `ADDRESS`): A company can have one or more addresses (e.g., business address). This might be a direct relationship from `Company` to `Address` or via a join table if addresses are shared.
    *   `Province`, `Country`: Lookup tables for address components.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   Business information (companies) is typically part of the main advisor profile. Updates would go through `PUT /api/v1/advisors/{advisorId}` with the `List<CompanyDTO>` nested within `UpdateAdvisorRequestDTO`.
        *   Retrieval is part of `GET /api/v1/advisors/{advisorId}`.
        *   For more granular control over individual companies, especially if they are complex entities or if there are many, sub-resource endpoints could be used. However, for typical scenarios, managing them as part of the advisor profile update is common.
            *   `POST /api/v1/advisors/{advisorId}/companies`: (Optional) To add a new company to an advisor.
            *   `PUT /api/v1/advisors/{advisorId}/companies/{companyId}`: (Optional) To update a specific company linked to an advisor.
            *   `DELETE /api/v1/advisors/{advisorId}/companies/{companyId}`: (Optional) To remove a company linkage from an advisor.
        *   If using the main advisor update endpoint, the `List<CompanyDTO>` in `UpdateAdvisorRequestDTO` will represent the desired state of companies for the advisor.
    *   **Service Layer (e.g., `AdvisorService`):**
        *   `AdvisorService.updateAdvisor(advisorId, UpdateAdvisorRequestDTO, principal)`: This service method will handle updates to the `Company` collection associated with the `Advisor` based on `requestDTO.getCompanies()`.
        *   The logic for managing the collection (add, update, delete companies) will reside here.
    *   **Data Transfer Objects (DTOs):**
        *   `CompanyDTO`: As defined in 2.2, nested within `AdvisorProfileDTO` and `UpdateAdvisorRequestDTO` (as a list).
            *   Includes fields like `companyId`, `companyName`, `businessNumber`, `companyType`, `isPrimary`, `AddressDTO businessAddress`.
        *   `AddressDTO`: As defined in 2.2, potentially nested within `CompanyDTO`.
    *   **Repository Layer (Spring Data JPA):**
        *   `CompanyRepository extends JpaRepository<Company, Long>`
        *   `AdvisorRepository extends JpaRepository<Advisor, Long>` (for managing the relationship if needed, or relying on cascading from Advisor).
    *   **Security (Spring Security):**
        *   Permissions to manage company information are governed by the security rules on `PUT /api/v1/advisors/{advisorId}`.
*   **Detailed Business Logic (Re-contextualized for Spring Boot - focusing on updates via `AdvisorService.updateAdvisor`):**
    *   **Receiving Update Request:**
        *   The `AdvisorController` receives a `PUT` request to `/api/v1/advisors/{advisorId}` with an `UpdateAdvisorRequestDTO`. The `companies` field (a `List<CompanyDTO>`) within this DTO will contain the business information.
    *   **Authorization & Initial Fetch (within `AdvisorService.updateAdvisor`):**
        *   Verify user's permission.
        *   Fetch the existing `Advisor` entity.
    *   **Managing `Company` Collection (within `AdvisorService.updateAdvisor`):**
        *   If `requestDTO.getCompanies()` is provided:
            *   Get the existing `List<Company> currentCompanies = advisor.getCompanies();`
            *   Get the `List<CompanyDTO> updatedCompanyDTOs = requestDTO.getCompanies();`
            *   **Strategy for Collection Update (similar to Addresses/Phones):**
                1.  **Identify Deletions:** Iterate through `currentCompanies`. If a company's ID is not in `updatedCompanyDTOs`, remove it from `currentCompanies` and call `companyRepository.delete(companyToDelete)`. If `orphanRemoval=true` is on the `Advisor` to `Company` relationship, removing from the collection and saving `Advisor` is sufficient.
                2.  **Identify Updates and Additions:** Iterate through `updatedCompanyDTOs`:
                    *   If `CompanyDTO.getCompanyId()` is present: Find the corresponding `Company` in `currentCompanies`. Update its scalar fields (name, BN, type) and its `Address` (if it has one and it's part of `CompanyDTO`). `companyRepository.save(existingCompany)`.
                    *   If `CompanyDTO.getCompanyId()` is null: This is a new company. Create a new `Company` entity, populate it from the DTO (including its `Address`), associate it with the `Advisor` (`newCompany.setAdvisor(advisor)` or add to `advisor.getCompanies()`), and save it `companyRepository.save(newCompany)`.
                3.  **Primary Company Flag:** Ensure logic to handle the `isPrimary` flag for companies, allowing only one primary company per advisor. If a new company is marked primary, the previous primary should be unmarked.
    *   **Validation:**
        *   Bean Validation on `CompanyDTO` and nested `AddressDTO`.
        *   Service-level validation (e.g., business number format/uniqueness if required by business rules, primary company uniqueness).
    *   **Persistence:**
        *   Changes are saved within the `@Transactional` `updateAdvisor` method. Saving the `Advisor` entity (which cascades to `Company` if configured with `CascadeType.ALL` or `MERGE` and `orphanRemoval=true`) or explicit `companyRepository.save/delete` calls will persist changes.
    *   **Response:** The updated `AdvisorProfileDTO` (containing the updated `List<CompanyDTO>`) is returned.
*   **Relevant Beans (from `advisor.md`):**
    *   `AdvisorNavigationBean`: Loads `Advisor` and its associated `Company` data.
    *   `AdvisorManagementBean`: Handles saving changes to `Company` entities, including adding new ones, updating existing ones, and removing them. Interacts with `CompanyFacade`.
    *   `ListBean`: For dropdowns like company type, province, country (for company addresses).

### 2.5. Advisor Licenses Tab/Section
*   **Description:** This section manages the licenses held by the advisor, including details like license number, province, category, issue/expiry dates, and any associated E&O (Errors and Omissions) insurance or liability details. It typically involves CRUD operations on the `License` entity and its related `LicenseLiability` entities.
*   **Existing UI Elements (from `AdvisorProfileU.xhtml` via `LicensesU.xhtml` and potentially `LiabilityU.xhtml`):** Tables/lists displaying licenses, with options to add, remove, or edit license details. Fields for license number, province, category, issue date, expiry date. A sub-section or separate modal for managing E&O/liability details (carrier, policy number, amount, expiry) for each license.
*   **Data & Source Tables:**
    *   `License` (Table: `LICENSE`): Stores license details. Linked to `Advisor` (one-to-many).
        *   Key fields: `licenseNumber`, `provinceCode`, `licenseCategoryCode`, `issueDate`, `expiryDate`, `status`.
    *   `LicenseLiability` (Table: `LICENSE_LIABILITY`): Stores E&O insurance details for each license. Linked to `License` (one-to-many).
        *   Key fields: `imCompanyId` (carrier), `policyNumber`, `amount`, `effectiveDate`, `expiryDate`.
    *   `Province`: Lookup table for province codes/names.
    *   `LicenseCategory`: Lookup table for license categories.
    *   `IMCompany`: Lookup table for insurance carriers (for E&O).
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   License information is typically part of the main advisor profile. Updates would go through `PUT /api/v1/advisors/{advisorId}` with the `List<LicenseDTO>` nested within `UpdateAdvisorRequestDTO`.
        *   Retrieval is part of `GET /api/v1/advisors/{advisorId}`.
        *   Granular endpoints are less common for this unless license management is exceptionally complex or decoupled.
    *   **Service Layer (e.g., `AdvisorService`):**
        *   `AdvisorService.updateAdvisor(advisorId, UpdateAdvisorRequestDTO, principal)`: This service method will handle updates to the `License` collection (and their nested `LicenseLiability` collections) associated with the `Advisor`, based on `requestDTO.getLicenses()`.
    *   **Data Transfer Objects (DTOs):**
        *   `LicenseDTO`: As defined in 2.2, nested within `AdvisorProfileDTO` and `UpdateAdvisorRequestDTO` (as a list).
            *   Includes fields like `licenseId`, `licenseNumber`, `provinceCode`, `licenseCategoryCode`, `issueDate`, `expiryDate`, and a `List<LicenseLiabilityDTO>`.
        *   `LicenseLiabilityDTO`: As defined in 2.2, nested within `LicenseDTO`.
            *   Includes fields like `liabilityId`, `carrierName` (or `carrierCode`), `policyNumber`, `amount`, `expiryDate`.
    *   **Repository Layer (Spring Data JPA):**
        *   `LicenseRepository extends JpaRepository<License, Long>`
        *   `LicenseLiabilityRepository extends JpaRepository<LicenseLiability, Long>`
        *   `AdvisorRepository extends JpaRepository<Advisor, Long>`
    *   **Security (Spring Security):**
        *   Permissions to manage licenses are governed by the security rules on `PUT /api/v1/advisors/{advisorId}`.
*   **Detailed Business Logic (Re-contextualized for Spring Boot - focusing on updates via `AdvisorService.updateAdvisor`):**
    *   **Receiving Update Request:**
        *   The `AdvisorController` receives a `PUT` request to `/api/v1/advisors/{advisorId}` with an `UpdateAdvisorRequestDTO`. The `licenses` field (a `List<LicenseDTO>`) within this DTO will contain the license information.
    *   **Authorization & Initial Fetch (within `AdvisorService.updateAdvisor`):**
        *   Verify user's permission.
        *   Fetch the existing `Advisor` entity.
    *   **Managing `License` Collection (within `AdvisorService.updateAdvisor`):**
        *   If `requestDTO.getLicenses()` is provided:
            *   Get the existing `List<License> currentLicenses = advisor.getLicenses();`
            *   Get the `List<LicenseDTO> updatedLicenseDTOs = requestDTO.getLicenses();`
            *   **Strategy for `License` Collection Update:**
                1.  **Deletions:** Iterate `currentLicenses`. If a license ID is not in `updatedLicenseDTOs`, remove it from `currentLicenses` and call `licenseRepository.delete(licenseToDelete)`. (Assumes `orphanRemoval=true` on `Advisor` to `License` relationship, or manual deletion of associated `LicenseLiability` records first if not cascaded).
                2.  **Updates and Additions:** Iterate `updatedLicenseDTOs`:
                    *   **Existing License (ID present):** Find the corresponding `License` in `currentLicenses`. Update its scalar fields (`licenseNumber`, `provinceCode`, etc.). Then, manage its `LicenseLiability` sub-collection (see below). `licenseRepository.save(existingLicense)`.
                    *   **New License (ID null):** Create a new `License` entity, populate its scalar fields. Associate it with the `Advisor`. Manage its `LicenseLiability` sub-collection. `licenseRepository.save(newLicense)`.
            *   **Managing `LicenseLiability` Sub-Collection (for each `License` being added/updated):**
                *   Let `currentLiabilities = license.getLiabilities();` and `updatedLiabilityDTOs = licenseDTO.getLiabilities();`
                1.  **Liability Deletions:** Iterate `currentLiabilities`. If a liability ID is not in `updatedLiabilityDTOs`, remove it from `currentLiabilities` and call `licenseLiabilityRepository.delete(liabilityToDelete)`. (Assumes `orphanRemoval=true` on `License` to `LicenseLiability` relationship).
                2.  **Liability Updates and Additions:** Iterate `updatedLiabilityDTOs`:
                    *   **Existing Liability (ID present):** Find corresponding `LicenseLiability` in `currentLiabilities`. Update its fields. `licenseLiabilityRepository.save(existingLiability)`.
                    *   **New Liability (ID null):** Create new `LicenseLiability`, populate, associate with `License`. `licenseLiabilityRepository.save(newLiability)`.
    *   **Validation:**
        *   Bean Validation on `LicenseDTO`, `LicenseLiabilityDTO`.
        *   Service-level validation (e.g., valid province/category codes, date consistencies, E&O requirements based on license type/status).
    *   **Persistence:**
        *   Changes saved via `@Transactional` `updateAdvisor` method, relying on cascading or explicit repository calls.
    *   **Response:** Updated `AdvisorProfileDTO` (with updated `List<LicenseDTO>`) is returned.
*   **Relevant Beans (from `advisor.md`):**
    *   `AdvisorNavigationBean` / `LicenseBean` / `LiabilityBean`: Load and manage `License` and `LicenseLiability` data.
    *   `AdvisorManagementBean`: Saves changes to `License` and `LicenseLiability` entities via facades.
    *   `ListBean`: Provides dropdowns for provinces, license categories, carriers.

### 2.6. Advisor Contract Setup Tab/Section
*   **Description:** This section manages the contractual agreements between the advisor and various product suppliers (e.g., insurance carriers, investment firms). It includes details such as contract numbers, effective dates, status, product supplier information, and associated commission structures.
*   **Existing UI Elements (from `AdvisorProfileU.xhtml` via `ContractSetupU.xhtml` and potentially a separate or embedded `CommissionU.xhtml` or similar for commission details):** Tables or lists displaying contract setups. Forms for adding/editing contract details including fields for product supplier, contract number, effective date, termination date, status, contract type. A sub-section or linked area for managing commission details (type, rate/amount, effective/expiry dates) for each contract.
*   **Data & Source Tables:**
    *   `ContractSetup` (Table: `CONTRACT_SETUP`): Stores main contract details. Linked to `Advisor` (one-to-many).
        *   Key fields: `advisorId`, `productSupplierCode`, `contractNumber`, `effectiveDate`, `terminationDate`, `status`, `contractType`.
    *   `Commission` (Table: `COMMISSION`): Stores commission rules/tiers associated with a `ContractSetup`. Linked to `ContractSetup` (one-to-many).
        *   Key fields: `contractSetupId`, `commissionType`, `rate`, `amount`, `effectiveDate`, `expiryDate`, `notes`.
    *   `ProductSupplier`: Lookup table for product suppliers.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   Contract setup information is typically part of the main advisor profile. Updates would go through `PUT /api/v1/advisors/{advisorId}` with the `List<ContractSetupDTO>` nested within `UpdateAdvisorRequestDTO`.
        *   Retrieval is part of `GET /api/v1/advisors/{advisorId}`.
    *   **Service Layer (e.g., `AdvisorService`):**
        *   `AdvisorService.updateAdvisor(advisorId, UpdateAdvisorRequestDTO, principal)`: This method will handle updates to the `ContractSetup` collection and their nested `Commission` collections, based on `requestDTO.getContractSetups()`.
    *   **Data Transfer Objects (DTOs):**
        *   `ContractSetupDTO`: As defined in 2.2 (potentially needing augmentation for commission details if not already covered). For clarity in this section, we assume `ContractSetupDTO` includes:
            *   `Long contractSetupId`
            *   `String productSupplierCode` (or `ProductSupplierDTO`)
            *   `String contractNumber`
            *   `LocalDate effectiveDate`
            *   `LocalDate terminationDate`
            *   `String status`
            *   `String contractType`
            *   `List<CommissionDTO> commissions`
        *   `CommissionDTO` (nested within `ContractSetupDTO`):
            *   `Long commissionId`
            *   `String commissionType`
            *   `BigDecimal rate`
            *   `BigDecimal amount`
            *   `LocalDate effectiveDate`
            *   `LocalDate expiryDate`
            *   `String notes`
        *   *(Note: The main `AdvisorProfileDTO` in section 2.2 should list `List<ContractSetupDTO> contractSetups`)*
    *   **Repository Layer (Spring Data JPA):**
        *   `ContractSetupRepository extends JpaRepository<ContractSetup, Long>`
        *   `CommissionRepository extends JpaRepository<Commission, Long>`
        *   `AdvisorRepository extends JpaRepository<Advisor, Long>`
    *   **Security (Spring Security):**
        *   Permissions governed by rules on `PUT /api/v1/advisors/{advisorId}`.
*   **Detailed Business Logic (Re-contextualized for Spring Boot - via `AdvisorService.updateAdvisor`):**
    *   **Receiving Update Request:**
        *   `AdvisorController` receives `PUT /api/v1/advisors/{advisorId}` with `UpdateAdvisorRequestDTO`. The `contractSetups` field (`List<ContractSetupDTO>`) contains the data.
    *   **Authorization & Initial Fetch:** Standard permission checks and `Advisor` entity retrieval.
    *   **Managing `ContractSetup` Collection:**
        *   If `requestDTO.getContractSetups()` is provided:
            *   Get `currentContractSetups = advisor.getContractSetups();`
            *   Get `updatedContractSetupDTOs = requestDTO.getContractSetups();`
            *   **Strategy for `ContractSetup` Collection Update:**
                1.  **Deletions:** Iterate `currentContractSetups`. If ID not in `updatedContractSetupDTOs`, delete using `contractSetupRepository.delete()`. (Handle `Commission` children deletion: cascade or manual before deleting `ContractSetup`).
                2.  **Updates and Additions:** Iterate `updatedContractSetupDTOs`:
                    *   **Existing (ID present):** Find `ContractSetup` in `currentContractSetups`. Update scalar fields. Manage its `Commission` sub-collection (see below).
                    *   **New (ID null):** Create new `ContractSetup`, populate, associate with `Advisor`. Manage `Commission` sub-collection. `contractSetupRepository.save()`.
            *   **Managing `Commission` Sub-Collection (for each `ContractSetup`):**
                *   Let `currentCommissions = contractSetup.getCommissions();` and `updatedCommissionDTOs = contractSetupDTO.getCommissions();`
                1.  **Commission Deletions:** Iterate `currentCommissions`. If ID not in `updatedCommissionDTOs`, delete using `commissionRepository.delete()`.
                2.  **Commission Updates and Additions:** Iterate `updatedCommissionDTOs`:
                    *   **Existing (ID present):** Find `Commission`, update fields. `commissionRepository.save()`.
                    *   **New (ID null):** Create `Commission`, populate, associate with `ContractSetup`. `commissionRepository.save()`.
    *   **Validation:**
        *   Bean Validation on DTOs.
        *   Service-level: valid product supplier codes, date logic (effective < termination), commission rule consistency.
    *   **Persistence:**
        *   Changes are saved within the `@Transactional` `updateAdvisor` method, relying on cascades or explicit repository calls.
    *   **Response:** Updated `AdvisorProfileDTO` (with `List<ContractSetupDTO>`) is returned.
*   **Relevant Beans (from `advisor.md`):**
    *   `AdvisorNavigationBean` / `ContractSetupBean` / `CommissionBean`: Load and manage contract and commission data.
    *   `AdvisorManagementBean`: Saves changes via facades.
    *   `ListBean`: Dropdowns for product suppliers, contract types, commission types.

### 2.7. Advisor EFT Information Tab/Section
*   **Description:** This section manages the Electronic Funds Transfer (EFT) information for the advisor, which is used for direct deposit of commissions and other payments. It includes bank account details and associated IM company information. This typically corresponds to a primary EFT setup for the advisor.
*   **Existing UI Elements (from `AdvisorProfileU.xhtml` via `ContractEftU.xhtml`):** Input fields for bank institution number, transit number, account number, account holder name. Dropdowns or lookup fields for selecting the IM company (e.g., for commission payments from a specific carrier).
*   **Data & Source Tables:**
    *   `ContractEft` (Table: `CONTRACT_EFT`): Stores EFT details for advisors. Typically linked to `Advisor` (one-to-one for primary EFT, or one-to-many if multiple EFT accounts are supported per advisor, though the DTO structure below implies one primary).
        *   Key fields: `advisorId` (FK to Advisor), `bankInstitutionNumber`, `bankTransitNumber`, `accountNumber`, `accountHolderName`, `imCompanyCode` (FK to IMCompany). May also include `eftId` as a separate PK.
    *   `IMCompany`: Lookup table for insurance/money transfer companies.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   EFT information is typically part of the main advisor profile. Updates would go through `PUT /api/v1/advisors/{advisorId}` with the `ContractEftDTO` nested within `UpdateAdvisorRequestDTO`.
        *   Retrieval is part of `GET /api/v1/advisors/{advisorId}`.
    *   **Service Layer (e.g., `AdvisorService`):**
        *   `AdvisorService.updateAdvisor(advisorId, UpdateAdvisorRequestDTO, principal)`: This method will handle updates to the `ContractEft` information based on `requestDTO.getContractEft()`.
    *   **Data Transfer Objects (DTOs):**
        *   `ContractEftDTO`: (Assumed to be defined in section 2.2, or refined here. Nested within `AdvisorProfileDTO` and `UpdateAdvisorRequestDTO`).
            *   `Long eftId` (nullable, for existing records)
            *   `String bankInstitutionNumber`
            *   `String bankTransitNumber`
            *   `String accountNumber`
            *   `String accountHolderName`
            *   `String imCompanyCode` (references an `IMCompany` entity/code)
            *   *(Other fields like `currency`, `accountType` could be added if necessary)*
        *   *(Note: The main `AdvisorProfileDTO` in section 2.2 should include `ContractEftDTO contractEft` and `UpdateAdvisorRequestDTO` should also include `ContractEftDTO contractEft`)*
    *   **Repository Layer (Spring Data JPA):**
        *   `ContractEftRepository extends JpaRepository<ContractEft, Long>`
        *   `AdvisorRepository extends JpaRepository<Advisor, Long>`
    *   **Security (Spring Security):**
        *   Permissions to update EFT information are governed by the security rules on `PUT /api/v1/advisors/{advisorId}`.
        *   Banking information is sensitive. Ensure appropriate measures like encryption at rest for account numbers, and masking in logs or less privileged API responses if applicable. Field-level security might be considered if different roles have different access levels to EFT data.
*   **Detailed Business Logic (Re-contextualized for Spring Boot - via `AdvisorService.updateAdvisor`):**
    *   **Receiving Update Request:**
        *   The `AdvisorController` receives a `PUT` request to `/api/v1/advisors/{advisorId}` with an `UpdateAdvisorRequestDTO`. The `contractEft` field (a `ContractEftDTO`) within this DTO will contain the EFT information.
    *   **Authorization & Initial Fetch (within `AdvisorService.updateAdvisor`):**
        *   Verify user's permission to update the advisor.
        *   Fetch the existing `Advisor` entity. This will also fetch its associated `ContractEft` entity if the mapping is eager or if explicitly joined.
    *   **Managing `ContractEft` Entity (within `AdvisorService.updateAdvisor`):**
        *   Let `updatedEftDto = requestDTO.getContractEft();`
        *   Let `existingAdvisor = advisorRepository.findById(advisorId).orElseThrow(...);`
        *   Let `currentEft = existingAdvisor.getContractEft();` (assuming a one-to-one mapping on the `Advisor` entity)
        *   **If `updatedEftDto` is null:**
            *   If `currentEft` is not null, it implies deletion of existing EFT info.
            *   `existingAdvisor.setContractEft(null);`
            *   `contractEftRepository.delete(currentEft);` (if `orphanRemoval=true` is not set on the relationship).
        *   **If `updatedEftDto` is not null:**
            *   **If `currentEft` is null (no existing EFT):**
                *   Create a new `ContractEft newEft = new ContractEft();`
                *   Map fields from `updatedEftDto` to `newEft` (e.g., using MapStruct or manually).
                *   `newEft.setAdvisor(existingAdvisor);` (if bidirectional, or set FK)
                *   `existingAdvisor.setContractEft(newEft);`
                *   `contractEftRepository.save(newEft);` (or rely on cascade from saving `Advisor`)
            *   **If `currentEft` is not null (existing EFT to update):**
                *   Map fields from `updatedEftDto` to `currentEft`.
                *   `contractEftRepository.save(currentEft);` (or rely on cascade from saving `Advisor`)
    *   **Validation:**
        *   Bean Validation on `ContractEftDTO` for format (e.g., bank numbers, required fields).
        *   Service-level validation:
            *   Verify `imCompanyCode` exists if provided.
            *   Validate bank institution/transit/account number formats according to country-specific rules if possible.
    *   **Persistence:**
        *   All changes are saved within the `@Transactional` `updateAdvisor` method. Saving the `Advisor` entity (which cascades to `ContractEft` if configured with `CascadeType.ALL` and `orphanRemoval=true`) or explicit `contractEftRepository.save/delete` calls will persist changes.
    *   **Response:** Updated `AdvisorProfileDTO` (with updated or null `ContractEftDTO`).
*   **Relevant Beans (from `advisor.md`):**
    *   `AdvisorNavigationBean` or a dedicated `ContractEftBean`: Responsible for loading the `ContractEft` data associated with the advisor for display on the `ContractEftU.xhtml` page.
    *   `AdvisorManagementBean`: Handles the saving (create/update/delete) of `ContractEft` information by interacting with a `ContractEftFacade`.
    *   `ListBean`: Provides lists for dropdowns, such as the list of `IMCompany` entities, which would be used to select the IM company for the EFT setup. In Spring Boot, this data would be fetched via a dedicated service/endpoint or provided as part of a larger reference data API.

### 2.8. Advisor Notes/Comments Tab/Section
*   **Description:** This section allows users (e.g., back-office staff, managers) to add, view, and manage internal notes or comments related to an advisor. These notes can be time-stamped and attributed to the user who created them.
*   **Existing UI Elements (from `AdvisorProfileU.xhtml` likely via a `NotesU.xhtml` or similar include):** A text area for adding new notes. A list or table displaying existing notes, showing the note content, creation date/time, and the user who added it. Options to edit or delete notes might be available depending on permissions.
*   **Data & Source Tables:**
    *   `AdvisorNote` (Table: `ADVISOR_NOTE` or similar): Stores notes related to an advisor. Linked to `Advisor` (one-to-many).
        *   Key fields: `advisorId`, `noteText`, `creationTimestamp`, `userId` (or `userName` of the creator).
    *   `User` (Table: `USER_ACCOUNT` or similar): To link `userId` to a user's name if not stored directly.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   Notes are specific to an advisor and often managed as a sub-resource.
        *   `GET /api/v1/advisors/{advisorId}/notes`: Retrieve all notes for an advisor.
        *   `POST /api/v1/advisors/{advisorId}/notes`: Add a new note for an advisor.
        *   `PUT /api/v1/advisors/{advisorId}/notes/{noteId}`: Update an existing note.
        *   `DELETE /api/v1/advisors/{advisorId}/notes/{noteId}`: Delete a note.
    *   **Service Layer (e.g., `AdvisorNoteService`):**
        *   `getNotesForAdvisor(advisorId, principal)`: Retrieves notes for an advisor.
        *   `addNoteForAdvisor(advisorId, noteDTO, principal)`: Adds a new note.
        *   `updateNote(noteId, noteDTO, principal)`: Updates an existing note.
        *   `deleteNote(noteId, principal)`: Deletes a note.
    *   **Data Transfer Objects (DTOs):**
        *   `AdvisorNoteDTO`:
            *   `Long noteId`
            *   `Long advisorId`
            *   `String noteText`
            *   `OffsetDateTime creationTimestamp`
            *   `Long userId`
            *   `String userName`
    *   **Repository Layer (Spring Data JPA):**
        *   `AdvisorNoteRepository extends JpaRepository<AdvisorNote, Long>`
        *   `UserRepository extends JpaRepository<User, Long>`
    *   **Security (Spring Security):**
        *   Permissions to manage notes are governed by the security rules on the respective endpoints (e.g., `POST /api/v1/advisors/{advisorId}/notes`).
        *   Typically, the user creating the note has full permissions on it, while others might have read-only access or no access depending on the role.
*   **Detailed Business Logic (Re-contextualized for Spring Boot):**
    *   **Adding a Note (`AdvisorNoteService.addNoteForAdvisor`):**
        1.  Receive `advisorId`, `noteDTO`, and `principal`.
        2.  Verify `principal` has permission to add a note for this advisor.
        3.  Create a new `AdvisorNote` entity from `noteDTO`.
        
               
        4.  Set additional fields like `creationTimestamp` from the server side.
        5.  Persist the `AdvisorNote` entity.
        6.  Return the saved `AdvisorNoteDTO`.
    *   **Updating a Note (`AdvisorNoteService.updateNote`):**
        1.  Receive `noteId`, `noteDTO`, and `principal`.
        2.  Verify `principal` has permission to update this note (e.g., is the creator or has admin rights).
        3.  Fetch the existing `AdvisorNote` entity.
        4.  Update the `noteText` and other fields from `noteDTO`.
        5.  Persist the changes.
        6.  Return the updated `AdvisorNoteDTO`.
    *   **Deleting a Note (`AdvisorNoteService.deleteNote`):**
        1.  Receive `noteId` and `principal`.
        2.  Verify `principal` has permission to delete this note.
        3.  Fetch and delete the `AdvisorNote` entity.
    *   **Fetching Notes for an Advisor (`AdvisorNoteService.getNotesForAdvisor`):**
        1.  Receive `advisorId` and `principal`.
        2.  Verify `principal` has permission to view notes for this advisor.
        3.  Retrieve and return the list of `AdvisorNoteDTO` for the advisor.
*   **Relevant Beans (from `advisor.md`):**
    *   `AdvisorNavigationBean`: Likely responsible for loading and displaying the notes section.
    *   `AdvisorManagementBean`: May handle the business logic for adding, updating, and deleting notes.
    *   `ListBean`: Could provide lists for dropdowns or other UI elements related to notes.

### 2.9. Advisor Search and Reporting
*   **Description:** This functionality allows for searching advisors based on various criteria and generating reports on advisor data. It may include filtering, sorting, and exporting data to different formats (e.g., CSV, PDF).
*   **Existing UI Elements (from `AdvisorProfileU.xhtml` and related pages):** Search fields, filter options, sort options, report type selection, export buttons.
*   **Data & Source Tables:**
    *   `Advisor` (Table: `ADVISOR`): The primary table for advisor data.
    *   Related tables as needed for filtering/sorting (e.g., `CONTACT`, `COMPANY`, `LICENSE`, etc.).
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   `GET /api/v1/advisors/report`: Generates a report based on the specified criteria.
        *   `GET /api/v1/advisors/search`: Searches for advisors (as detailed in 2.1).
    *   **Service Layer (e.g., `AdvisorReportService`):**
        *   `generateAdvisorReport(criteria, principal)`: Handles report generation logic.
    *   **Data Transfer Objects (DTOs):**
        *   `AdvisorReportCriteriaDTO`: Criteria for generating reports (e.g., date range, status).
        *   `AdvisorReportDTO`: Represents the generated report data.
    *   **Repository Layer (Spring Data JPA):**
        *   `AdvisorRepository` and other relevant repositories for data access.
    *   **Security (Spring Security):**
        *   Permissions to access report generation features.
*   **Detailed Business Logic (Re-contextualized for Spring Boot):**
    *   **Generating a Report (`AdvisorReportService.generateAdvisorReport`):**
        1.  Receive `criteria` and `principal`.
        2.  Verify permissions.
        3.  Fetch data based on criteria using repositories.
        4.  Generate report in the requested format (e.g., CSV, PDF).
        5.  Return the report file or a download link.
*   **Relevant Beans (from `advisor.md`):**
    *   `AdvisorManagementBean`: Likely handles the business logic for searching and reporting.
    *   `ReportBean`: If exists, this bean would be responsible for managing report generation and export.

### 2.10. Advisor Onboarding & Offboarding Workflows
*   **Description:** This section outlines the processes for managing the lifecycle of an advisor within the organization, specifically the workflows for onboarding a new advisor and offboarding an existing one. These workflows typically involve a series of steps, potentially automated or requiring manual intervention, to ensure all necessary tasks are completed (e.g., account creation, role assignment, document submission, system access changes, notifications).
*   **Existing UI Elements (Conceptual - may not be a single `AdvisorWorkflowU.xhtml` but distributed actions):**
    *   Forms or actions to initiate an onboarding or offboarding process for an advisor.
    *   Displays showing the current status of an ongoing workflow.
    *   Task lists or notifications for users responsible for specific steps in the workflow.
    *   Sections within the advisor profile might reflect the outcome of these workflows (e.g., advisor status, start date, termination date).
*   **Data & Source Tables:**
    *   `AdvisorWorkflow` (New Table: `ADVISOR_WORKFLOW` or similar): To track instances of onboarding or offboarding processes.
        *   Key fields: `workflowId`, `advisorId`, `workflowType` (e.g., 'ONBOARDING', 'OFFBOARDING'), `status` (e.g., 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'), `startDate`, `completionDate`, `initiatedByUserId`.
    *   `AdvisorWorkflowStep` (New Table: `ADVISOR_WORKFLOW_STEP` or similar): To track individual tasks or stages within a workflow instance.
        *   Key fields: `stepId`, `workflowId` (FK to `ADVISOR_WORKFLOW`), `stepName` (or `stepCode`), `status` (e.g., 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'SKIPPED'), `assignedToUserId` (optional), `dueDate` (optional), `completionDate`, `notes`, `outcomeData` (JSON blob for step-specific results).
    *   `Advisor`: Existing table, will be updated by these workflows (e.g., `status`, `onboardingDate`, `terminationDate`).
    *   Other tables like `UserAccount`, `UserRole`, `AdvisorDocument` will be affected as part of workflow steps.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   `POST /api/v1/advisors/{advisorId}/workflows/onboarding`: Initiate the onboarding workflow for a specific advisor.
        *   `POST /api/v1/advisors/{advisorId}/workflows/offboarding`: Initiate the offboarding workflow for a specific advisor.
        *   `GET /api/v1/advisors/{advisorId}/workflows`: Retrieve a list of all workflow instances (active and historical) for an advisor.
        *   `GET /api/v1/workflows/{workflowId}`: Get detailed information about a specific workflow instance, including its steps.
        *   `POST /api/v1/workflows/{workflowId}/steps/{stepId}/complete`: Mark a specific workflow step as complete (potentially with input data).
        *   `POST /api/v1/workflows/{workflowId}/steps/{stepId}/assign`: Assign a step to a user (if manual assignment is needed).
        *   `GET /api/v1/workflows/tasks`: Retrieve a list of workflow steps/tasks assigned to the currently authenticated user.
    *   **Service Layer (e.g., `AdvisorWorkflowService`):**
        *   `initiateOnboardingWorkflow(advisorId, OnboardingInitiationRequestDTO, principal)`: Creates and starts an onboarding workflow.
        *   `initiateOffboardingWorkflow(advisorId, OffboardingInitiationRequestDTO, principal)`: Creates and starts an offboarding workflow.
        *   `getAdvisorWorkflows(advisorId, principal)`: Retrieves workflow instances for an advisor.
        *   `getWorkflowDetails(workflowId, principal)`: Retrieves details of a specific workflow.
        *   `completeWorkflowStep(workflowId, stepId, StepCompletionRequestDTO, principal)`: Processes the completion of a workflow step, triggers next steps.
        *   `assignWorkflowStep(workflowId, stepId, AssignStepRequestDTO, principal)`: Assigns a step.
        *   `getMyWorkflowTasks(principal)`: Gets tasks for the current user.
        *   *(This service might integrate with a workflow engine like Camunda/Flowable, or implement a state machine for managing workflow progression.)*
    *   **Data Transfer Objects (DTOs):**
        *   `AdvisorWorkflowDTO`:
            *   `Long workflowId`
            *   `Long advisorId`
            *   `String workflowType` (ONBOARDING, OFFBOARDING)
            *   `String status`
            *   `OffsetDateTime startDate`
            *   `OffsetDateTime completionDate`
            *   `Long initiatedByUserId`
            *   `List<AdvisorWorkflowStepDTO> steps`
        *   `AdvisorWorkflowStepDTO`:
            *   `Long stepId`
            *   `String stepName`
            *   `String status`
            *   `Long assignedToUserId`
            *   `String assignedToUserName`
            *   `OffsetDateTime dueDate`
            *   `OffsetDateTime completionDate`
            *   `String notes`
        *   `OnboardingInitiationRequestDTO`:
            *   `String comments` (optional)
            *   `Map<String, Object> initialData` (e.g., for pre-filling advisor details if created via workflow)
        *   `OffboardingInitiationRequestDTO`:
            *   `String reason`
            *   `LocalDate effectiveDate`
            *   `String comments` (optional)
        *   `StepCompletionRequestDTO`:
            *   `String notes` (optional)
            *   `Map<String, Object> outcomeData` (data collected or generated by this step)
        *   `AssignStepRequestDTO`:
            *   `Long assignToUserId`
    *   **Repository Layer (Spring Data JPA):**
        *   `AdvisorWorkflowRepository extends JpaRepository<AdvisorWorkflow, Long>`
        *   `AdvisorWorkflowStepRepository extends JpaRepository<AdvisorWorkflowStep, Long>`
        *   `AdvisorRepository`, `UserRepository`, etc. as needed by workflow steps.
    *   **Security (Spring Security):**
        *   `@PreAuthorize` for initiating workflows (e.g., `hasAuthority('INITIATE_ADVISOR_ONBOARDING')`).
        *   Permissions to view workflow details.
        *   Permissions to complete or assign steps might be based on roles or direct assignment of the step to the user.
    *   **Workflow Engine / State Management:**
        *   For complex workflows with branching, parallel tasks, escalations, and long-running processes, integrating a dedicated workflow engine (e.g., Camunda, Flowable) is recommended.
        *   For simpler, linear workflows, a custom state machine implementation within the `AdvisorWorkflowService` might suffice.
        *   The design should define workflow templates (sequences of steps for onboarding/offboarding).
*   **Detailed Business Logic (Re-contextualized for Spring Boot):**
    *   **Initiating an Onboarding Workflow (`AdvisorWorkflowService.initiateOnboardingWorkflow`):**
        1.  Receive `advisorId` and `OnboardingInitiationRequestDTO`.
        2.  Verify permissions of the `principal`.
        3.  Fetch the `Advisor` entity. If the workflow is meant to also create the advisor, this step might be different.
        4.  Create an `AdvisorWorkflow` entity (type: ONBOARDING, status: IN_PROGRESS).
        5.  Based on a predefined onboarding template, create associated `AdvisorWorkflowStep` entities (e.g., "Verify Advisor Data", "Create User Account", "Assign Default Roles", "Submit Compliance Documents", "Background Check Approval", "Activate Advisor Profile").
        6.  Set the advisor's main status to "Pending Onboarding" or similar (via `AdvisorStatusService`).
        7.  Trigger the first step(s) – this might involve assigning a task to a user/group or an automated system action.
        8.  Persist workflow and step entities.
        9.  Return `AdvisorWorkflowDTO`.
    *   **Initiating an Offboarding Workflow (`AdvisorWorkflowService.initiateOffboardingWorkflow`):**
        1.  Receive `advisorId` and `OffboardingInitiationRequestDTO`.
        2.  Verify permissions.
        3.  Fetch `Advisor`.
        4.  Create `AdvisorWorkflow` (type: OFFBOARDING, status: IN_PROGRESS).
        5.  Based on an offboarding template, create `AdvisorWorkflowStep` entities (e.g., "Notify Advisor & Team", "Plan Client Handovers", "Disable System Access", "Process Final Commissions", "Archive Documents", "Terminate Advisor Profile").
        6.  Set advisor's main status to "Pending Offboarding" or similar.
        7.  Trigger the first step(s).
        8.  Persist entities.
        9.  Return `AdvisorWorkflowDTO`.
    *   **Completing a Workflow Step (`AdvisorWorkflowService.completeWorkflowStep`):**
        1.  Receive `workflowId`, `stepId`, `StepCompletionRequestDTO`, and `principal`.
        2.  Verify `principal` is authorized to complete this step (e.g., assigned user or has specific role).
        3.  Fetch `AdvisorWorkflowStep` and its parent `AdvisorWorkflow`.
        4.  Validate current status of step and workflow.
        5.  Update `AdvisorWorkflowStep` status to 'COMPLETED', set `completionDate`, `notes`, `outcomeData`.
        6.  Execute any business logic tied to this step's completion (e.g., call `UserRoleService.assignRoles`, `AdvisorDocumentService.requestDocumentUpload`, `NotificationService.sendEmail`).
        7.  Determine the next step(s) based on the workflow definition. Update their status to 'PENDING' or 'IN_PROGRESS' and assign them if necessary.
        8.  If all mandatory steps for the workflow are completed:
            *   Update `AdvisorWorkflow` status to 'COMPLETED'.
            *   For onboarding: Call `AdvisorStatusService.changeAdvisorStatus` to set advisor to 'ACTIVE'.
            *   For offboarding: Call `AdvisorStatusService.changeAdvisorStatus` to set advisor to 'TERMINATED' or 'INACTIVE'.
        9.  Persist changes.
        10. Return updated `AdvisorWorkflowStepDTO` or `AdvisorWorkflowDTO`.
    *   **Notifications:** Throughout the workflow, integrate with a `NotificationService` to inform assignees of new tasks, stakeholders of progress, and the advisor about key milestones.
    *   **Error Handling & Escalation:** Define how errors in steps are handled (e.g., retry, manual intervention, escalation paths). This is where a workflow engine excels.
*   **Relevant Beans (from `advisor.md` - Conceptual Mapping):**
    *   This functionality is likely a combination of manual processes and actions within various existing beans (`AdvisorManagementBean` for status changes, `UserManagementBean` for user/role setup).
    *   **Spring Boot Mapping:**
        *   **Controller (e.g., `AdvisorWorkflowController`):** Exposes REST endpoints for workflow operations.
        *   **Service (`AdvisorWorkflowService`):** Orchestrates the workflow, manages state, and integrates with other services.
        *   **Repositories (`AdvisorWorkflowRepository`, `AdvisorWorkflowStepRepository`):** Data access for workflow entities.
        *   Potential integration with a **Workflow Engine** (e.g., Camunda, Flowable) for managing complex workflow logic.
        *   `NotificationService` for sending alerts and updates.

### 2.14. Audit Trail & Change History
*   **Description:** This section focuses on maintaining a comprehensive audit trail of all significant changes made to advisor data and related entities. It involves logging who made what change, when it was made, and the old and new values of the data. This is crucial for compliance, security, and troubleshooting.
*   **Existing UI Elements (Conceptual - may not be a single `AuditTrailU.xhtml` but integrated into views):**
    *   A way to view the history of changes for a specific advisor or a specific piece of information (e.g., status history, address change history).
    *   This might be a dedicated audit log viewer or integrated into the relevant sections of the advisor profile.
*   **Data & Source Tables:**
    *   `AuditLog` (New or Existing Table: `AUDIT_LOG`, `ENTITY_AUDIT_TRAIL` or similar):
        *   Key fields: `auditLogId`, `entityName` (e.g., "Advisor", "Contact", "License"), `entityId` (PK of the audited entity), `fieldName` (if field-level auditing), `oldValue`, `newValue`, `actionType` (e.g., 'CREATE', 'UPDATE', 'DELETE'), `changedByUserId`, `changeTimestamp`, `sourceIpAddress` (optional), `changeReason` (optional).
    *   Alternatively, using a dedicated auditing solution like Hibernate Envers, which creates shadow tables (e.g., `ADVISOR_AUD`, `CONTACT_AUD`) to store historical versions of entities.
*   **Proposed Spring Boot REST API Implementation:**
    *   **Key API Endpoints:**
        *   `GET /api/v1/advisors/{advisorId}/audit-trail`: Retrieve audit trail records specifically for an advisor (potentially across multiple related entities).
        *   `GET /api/v1/audit-trail?entityName={entityName}&entityId={entityId}`: A more generic endpoint to retrieve audit logs for any audited entity.
        *   `GET /api/v1/audit-trail?userId={userId}&startDate={startDate}&endDate={endDate}`: Search audit logs by user, date range, etc.
    *   **Service Layer (e.g., `AuditTrailService` or integrated with existing services):**
        *   `getAuditTrailForAdvisor(advisorId, Pageable pageable, AuditLogFilterDTO filter, principal)`: Retrieves audit logs related to a specific advisor.
        *   `getAuditTrailForEntity(entityName, entityId, Pageable pageable, AuditLogFilterDTO filter, principal)`: Retrieves audit logs for a specific entity instance.
        *   `searchAuditLogs(AuditLogSearchCriteriaDTO criteria, Pageable pageable, principal)`: Generic search for audit logs.
        *   *(The actual logging of audit entries is typically handled by an AOP aspect, JPA entity listeners, or a library like Hibernate Envers, rather than explicit service calls for each change.)*
    *   **Data Transfer Objects (DTOs):**
        *   `AuditLogEntryDTO`:
            *   `Long auditLogId`
            *   `String entityName`
            *   `String entityId`
            *   `String fieldName` (optional, if field-level)
            *   `String oldValue` (masked for sensitive data)
            *   `String newValue` (masked for sensitive data)
            *   `String actionType`
            *   `Long changedByUserId`
            *   `String changedByUsername`
            *   `OffsetDateTime changeTimestamp`
            *   `String sourceIpAddress` (optional)
            *   `String changeReason` (optional)
        *   `AuditLogFilterDTO` / `AuditLogSearchCriteriaDTO`:
            *   `String entityName`
            *   `String entityId`
            *   `Long userId`
            *   `LocalDate startDate`
            *   `LocalDate endDate`
            *   `String actionType`
            *   *(Other relevant filter fields)*
    *   **Repository Layer (Spring Data JPA):**
        *   `AuditLogRepository extends JpaRepository<AuditLog, Long>` (if using a custom audit log table).
        *   If using Hibernate Envers, queries are made via `AuditReader` API.
    *   **Security (Spring Security):**
        *   `@PreAuthorize` for accessing audit trail endpoints. Access is typically restricted to administrators or users with specific audit viewing permissions (e.g., `hasAuthority('VIEW_AUDIT_LOG')`).
        *   Sensitive data in `oldValue`/`newValue` fields should be masked or omitted for users without sufficient privileges.
*   **Detailed Business Logic & Implementation Strategy (Re-contextualized for Spring Boot):**
    *   **Auditing Mechanism (Choose one or combine):**
        1.  **Hibernate Envers:**
            *   **Setup:** Add `hibernate-envers` dependency. Annotate entities to be audited with `@Audited`.
            *   **Logging:** Envers automatically tracks changes to `@Audited` entities and stores them in corresponding `_AUD` tables.
            *   **Retrieval:** Use `AuditReaderFactory.get(entityManager).createQuery()` to query historical data.
        2.  **Spring Data JPA Auditing (`@CreatedBy`, `@LastModifiedBy`, `@CreatedDate`, `@LastModifiedDate`):**
            *   **Setup:** Enable JPA auditing (`@EnableJpaAuditing`). Provide an `AuditorAware` bean to resolve the current user.
            *   **Logging:** Automatically populates audit metadata fields on entities.
            *   **Scope:** Primarily for tracking who created/modified an entity and when, not for detailed field-level change history or old/new values.
        3.  **Custom AOP Aspect + JPA Entity Listeners:**
            *   **Setup:** Create an aspect that intercepts calls to repository `save`, `delete` methods or service methods that modify data.
            *   Alternatively, use JPA `@EntityListeners` with `@PostPersist`, `@PostUpdate`, `@PostRemove` methods.
            *   **Logging:** Within the aspect/listener, manually create `AuditLog` entities and persist them. This requires logic to determine old/new values (e.g., by re-fetching the entity before update, or comparing detached/managed states).
            *   **Pros:** Full control over what and how to audit.
            *   **Cons:** More complex to implement and maintain correctly.
        4.  **Database Triggers:**
            *   **Setup:** Create triggers on tables to log changes to a separate audit table.
            *   **Pros:** Database-level, captures all changes regardless of application source.
            *   **Cons:** Less portable, logic is outside the application, can impact database performance.
    *   **Recommended Approach for Advisor Profile:** Hibernate Envers is often a good choice for comprehensive entity versioning and audit trails in a Spring Boot/JPA application due to its ease of integration and powerful querying capabilities for historical data.
    *   **Retrieving Audit Data (Example using `AuditTrailService` with Envers or custom log):**
        1.  Receive request (e.g., `advisorId`, `entityName`, `entityId`, filter criteria, pagination).
        2.  Verify user permissions to view audit data.
        3.  **If using Hibernate Envers:**
            *   Use `AuditReader` to construct queries. For example, to get revisions for an entity: `auditReader.createQuery().forRevisionsOfEntity(YourEntity.class, true, true).add(AuditEntity.id().eq(entityId)).getResultList();`
            *   Map Envers results (which include entity snapshots and revision info) to `AuditLogEntryDTO`.
        4.  **If using Custom `AuditLog` Table:**
            *   Use `AuditLogRepository` with Spring Data specifications or Querydsl to build dynamic queries based on filter criteria.
            *   Fetch `Page<AuditLog>` and map to `Page<AuditLogEntryDTO>`.
        5.  Ensure sensitive data in `oldValue`/`newValue` is masked appropriately in the DTOs based on user permissions or system policy.
        6.  Return the paged list of `AuditLogEntryDTO`.
    *   **What to Audit:**
        *   Changes to `Advisor` entity itself (status, key dates, managerId).
        *   Changes to `Contact`, `Address`, `Phone`, `Email`.
        *   Changes to `Company` associations.
        *   Changes to `License`, `LicenseLiability`.
        *   Changes to `ContractSetup`, `Commission`.
        *   Changes to `ContractEft`.
        *   Creation/deletion/updates of `AdvisorNote` (content changes might be too verbose; consider auditing only metadata or significant updates).
        *   Creation/deletion/metadata updates of `AdvisorDocument`.
        *   Changes to `UserAccount` roles associated with the advisor.
        *   Key events in `AdvisorWorkflow` and `AdvisorWorkflowStep` (status changes, assignments).
*   **Relevant Beans (from `advisor.md`) - Conceptual Mapping:**
    *   Auditing is often a cross-cutting concern. In JSF, it might have been handled by manual logging in `AdvisorManagementBean` or other beans, or via a lower-level persistence interceptor if one was in place.
    *   **Spring Boot Mapping:**
        *   **Auditing Mechanism:** Hibernate Envers, Spring Data JPA Auditing, or custom AOP/Listeners.
        *   **Controller (e.g., `AuditTrailController`):** Exposes REST endpoints for querying audit data.
        *   **Service (`AuditTrailService`):** Provides methods to query and transform audit data into DTOs.
        *   **Repository (`AuditLogRepository` or Envers `AuditReader`):** Data access for audit logs.

## 3. Consolidated Design Considerations (To be detailed later)
*   **3.1. Security and Access Control:**
    *   Consistent use of Spring Security annotations (`@PreAuthorize`, `@Secured`) to protect REST endpoints.
    *   Method-level security in services where necessary, especially for sensitive operations.
    *   Consideration of OAuth2/JWT for secure, token-based authentication and authorization.
*   **3.2. Exception Handling:**
    *   Global exception handling using `@ControllerAdvice` to manage and standardize error responses.
    *   Custom exception classes for domain-specific errors (e.g., `ResourceNotFoundException`, `AccessDeniedException`).
*   **3.3. Logging and Monitoring:**
    *   Use of Spring AOP for logging method entry, exit, and exceptions.
    *   Integration with a monitoring solution (e.g., Spring Boot Actuator, Micrometer) for application metrics and health checks.
*   **3.4. Performance Considerations:**
    *   Caching strategies for frequently accessed data (e.g., advisor search results, dropdown values).
    *   Database indexing and query optimization, especially for complex searches and reporting.
*   **3.5. API Documentation and Testing:**
    *   Use of Swagger/OpenAPI for API documentation.
    *   Comprehensive unit and integration testing, with tools like JUnit, Mockito, and Spring Test.
*   **3.6. Data Migration and Synchronization:**
    *   Strategies for migrating existing data to the new schema (if applicable).
    *   Consideration of data synchronization issues, especially for distributed systems or microservices.
*   **3.7. Future Enhancements and Scalability:**
    *   Design considerations for potential future requirements (e.g., new advisor attributes, additional related entities).
    *   Scalability considerations for handling large volumes of data or high numbers of concurrent users.

*This document will be updated as the design is further refined and implemented.*
