# 新增功能任务：文档中心与邮件发送

## 文档中心API (Document Center APIs)

**目标**: 为文档管理提供一套独立的RESTful API，实现前后端分离。

### 任务 1: 设计文档API ✅ **已完成**
- [x] `GET /api/v1/advisors/{advisorId}/documents`: 获取一个顾问的文档列表（元数据，不含文件内容）。
- [x] `POST /api/v1/advisors/{advisorId}/documents`: 上传一个新文件。请求类型为multipart/form-data。
- [x] `GET /api/v1/documents/{documentId}`: 下载一个文件的内容。
- [x] `DELETE /api/v1/documents/{documentId}`: 删除一个文件。

### 任务 2: 实现文档服务 (DocumentService) ✅ **已完成**
- [x] **文件存储策略**:
    - **Schema Reference**: Detailed schema for `STORED_FILE`, `STORED_FILE_DESC`, `STORED_FILE_NOTE` and `ACTIVITY_ATTACHMENT` tables can be found in `database_schema_for_refactoring.md`.
    - **Current System**: The existing EJB system primarily stores file metadata in the `STORED_FILE` table and the actual file content as BLOBs in the `STORED_FILE_DESC` table (specifically in `FILE_EN` and `FILE_FR` columns).
    - **`STORED_FILE.URL` Field**: This field in the `STORED_FILE` table might currently be used for some externally stored files (e.g., the "NetStorage" system mentioned in `AdvisorFacade`) or could be null/empty for BLOB-stored files.
    - **Refactoring Goal**: The new `DocumentService` will migrate to a dedicated file storage system (e.g., local file system, MinIO, or S3).
    - **New Storage Mechanism**: For new file uploads, the actual file will be saved to the chosen external storage, and the `STORED_FILE.URL` field will be used to store the unique path or key to this file. No new entries will be made into `STORED_FILE_DESC` for new files.
    - **Legacy Fallback**: The `DocumentService` must implement a fallback mechanism. When retrieving a document, if the `URL` field points to the new system, it will be fetched from there. If the `URL` is empty or indicates a legacy path, and if `STORED_FILE_DESC` contains data for that file, the service must be able to retrieve the file content from the `FILE_EN` or `FILE_FR` BLOB columns in `STORED_FILE_DESC` to ensure backward compatibility for existing documents.
- [x] **元数据管理**:
    - Primarily use JdbcTemplate (or JPA) to operate on `STORED_FILE` and its associated tables (`STORED_FILE_DESC` for legacy files, `STORED_FILE_NOTE`) for managing file元数据.
    - **考虑 `COMPLIANCE_DOCUMENT` 表**: 
        - The `com.insurfact.skynet.entity.ComplianceDocument` entity (table `COMPLIANCE_DOCUMENT`) represents another type of document linked to `CONTACT` or `USERS` (Advisors).
        - These documents are **not stored using `STORED_FILE`**. Instead, `ComplianceDocument` has a `filePath` field pointing directly to the file's location.
        - **Decision Point**: Determine if these compliance documents should be listed under the `/api/v1/advisors/{advisorId}/documents` endpoint.
        - If yes, `DocumentService` will need to query `COMPLIANCE_DOCUMENT` in addition to `STORED_FILE`. The API response will need to accommodate both types, and download/delete operations for these will use the direct `filePath`.
        - If no, they might require separate API endpoints if their management is needed via REST.
    - **`A_FILE` 表**: The `com.insurfact.skynet.entity.AFile` entity (table `A_FILE`) also uses a direct `filePath` but appears to be for more specialized, system-level data files. It is currently assessed as **likely out of scope** for the general advisor-facing Document Center.
- [x] **核心逻辑**: 实现文件的上传、下载、删除逻辑，与文件存储系统交互。
- [x] **控制器与安全**
- [x] **控制器创建**: 创建 `AdvisorDocumentController`。
- [x] **权限校验**: 为每个端点添加严格的权限校验（例如，`@permissionService.canAccessDocument(authentication, #documentId)`)。

## 邮件发送API (Action-Based APIs)

**目标**: 创建一个异步的、基于动作的API来处理邮件发送。

### 任务 1: 设计邮件发送API
- [ ] `POST /api/v1/advisors/{advisorId}/emails/send-licenses`: 触发发送执照/E&O信息的邮件。
- [ ] **请求体 DTO**: 创建 `SendEmailRequestDTO` (`to`: "<EMAIL>", `subject`: "...", `body`: "...")。

### 任务 2: 实现异步邮件服务 (AsyncEmailService)
- [ ] **异步方法**: 创建一个带`@Async`注解的服务方法 `sendLicenseInfoEmail(advisorId, requestDto)`。
- [ ] **数据加载**: 调用我们已有的 `AdvisorProfileRepository` 加载执照和责任险数据。
- [ ] **内容生成**: 使用模板引擎（如Thymeleaf）将数据渲染成HTML邮件正文。
- [ ] **邮件发送**: 调用旧的 `EmailManager` (通过SDK) 来发送邮件。这是最安全的方式，可以重用所有现有的SMTP配置和逻辑。
- [ ] **活动记录**: 调用旧的 `ActivityFacade` (通过SDK) 来记录活动日志。

### 任务 3: 控制器与配置
- [ ] **控制器实现**: 在 `AdvisorController` 或新的 `AdvisorActionController` 中创建该端点。
- [ ] **异步启用**: 在主应用类上启用异步处理 (`@EnableAsync`)。
- [ ] **API响应**: 端点应立即返回202 Accepted，表示任务已接受，正在后台处理。

## 相关EJB Facade及类分析 (初步)
根据项目文档和 `sky_ejb` 目录结构，以下 EJB 组件和类可能与这些新功能相关，值得在重构过程中进一步分析其具体方法和逻辑：

### 文档中心相关:
- `AFileFacade.java`: 可能包含通用的文件操作逻辑。
- `AdvisorFacade.java`: 可能包含将文档链接到顾问的逻辑。
- `ComplianceDocumentFacade.java`: 如果文档涉及合规性，此 Facade 可能相关。
- 旧系统可能通过特定的DAO或业务Bean直接处理与 `STORED_FILE` 表（或类似命名的表）相关的文件元数据和存储逻辑。

### 邮件发送相关:
- `EmailManager.java`: 核心的邮件发送逻辑 (已在计划中明确提出通过SDK调用)。
- `ActivityFacade.java`: 用于记录如“邮件已发送”之类的活动 (已在计划中明确提出通过SDK调用)。
- `AlertFacade.java`: 可能用于创建与邮件发送相关的提醒或通知。
- `EmailFacade.java`: 可能提供邮件相关的辅助功能或对 `EmailManager` 的封装。
- `DefaultEmailsFacade.java`: 可能管理邮件模板、默认收件人或特定场景的邮件配置。

对这些组件的深入分析将有助于确保在新系统中保留所有必要的业务规则，并顺利迁移或重用现有逻辑。

### 2. File Storage Strategy

**Current State & Legacy Support**:
*   Currently, files are primarily stored as BLOBs in the `STORED_FILE_DESC` table, with metadata in `STORED_FILE`.
*   The `STORED_FILE.URL` field is sometimes used, but its content and purpose vary (e.g., may hold full URLs, relative paths, or identifiers for an older "NetStorage" system).
*   `ComplianceDocument` entities store their files using a direct `FILE_PATH` field, separate from the `STORED_FILE` mechanism.
*   `AFile` entities also use a direct `FILE_PATH` and are considered out of scope for the advisor-specific document center.

**New Storage Strategy**:
1.  **Primary Storage**: All *new* documents uploaded through the Document Center will be stored in a dedicated, secure file system (e.g., local file system, NAS, or a cloud-based object store like MinIO/S3). The exact choice can be configured. For the initial phase, a local file system is assumed.
2.  **`STORED_FILE.URL` Usage (New Files)**: For new files associated with `STORED_FILE` records, the `URL` field will store a standardized, relative path or a unique key/identifier that resolves to the actual file in the new storage system. For example: `filesystem://advisor_documents/{advisorId}/{UUID}_{original_filename}`.
3.  **`COMPLIANCE_DOCUMENT.FILE_PATH` Usage (New Files)**: For new compliance documents, the `FILE_PATH` field will similarly store a standardized path or key pointing to the new storage system.
4.  **Legacy File Access (BLOBs)**:
    *   The system must provide read-only access to existing files stored as BLOBs in `STORED_FILE_DESC`.
    *   When a document is listed, the API should indicate if it's a legacy BLOB-stored file or a new file system-stored file.
    *   Downloading a legacy file will involve retrieving the BLOB from `STORED_FILE_DESC`.
    *   No new files will be written as BLOBs.
5.  **Legacy File Access (Old `URL` / `FILE_PATH`)**:
    *   The system should attempt to resolve and provide access to files referenced by existing, non-standard `STORED_FILE.URL` values or `COMPLIANCE_DOCUMENT.FILE_PATH` values if they point to accessible locations. This might require configuration or specific handling logic for identified patterns.
6.  **Migration (Optional/Future)**: A separate process or utility could be developed in the future to migrate legacy BLOBs and other existing files to the new storage system, updating `STORED_FILE.URL` and `COMPLIANCE_DOCUMENT.FILE_PATH` accordingly. This is out of scope for the initial implementation.

**`ComplianceDocument` Integration**:
*   The Document Center API (`/api/v1/advisors/{advisorId}/documents`) **will include** documents from the `COMPLIANCE_DOCUMENT` table that are linked to the specified advisor.
*   The `DocumentService` will be responsible for querying both `STORED_FILE` (where `TYPE_ = 11` and `TYPE_ID = advisorId`) and `COMPLIANCE_DOCUMENT` (where `OWNER = advisorUserIntId` or `CONTACT = advisorContactIntId`) and aggregating the results.
*   The distinction between these document types (e.g., "general" vs. "compliance") should be clear in the API response, possibly through a `category` or `source` field in the document DTO.

## 3. Email Sending Tasks

### 3.1. API Design: Asynchronous Email Sending

**Endpoint**: `POST /api/v1/emails/send`

**Request Body**:

```json
{
  "recipientEmail": "<EMAIL>",
  "recipientName": "John Doe", // Optional
  "emailType": "LICENSE_EXPIRATION_REMINDER", // Or other types like "NEW_PRODUCT_INFO", "WELCOME_EMAIL"
  "templateModel": { // Data specific to the emailType
    "advisorName": "John Doe",
    "licenseType": "Life Insurance Level 2",
    "expiryDate": "2025-12-31",
    "renewalLink": "https://portal.example.com/renewals"
    // Add other relevant fields based on emailType
  },
  "attachments": [ // Optional: for future use, if emails need to send generated documents
    {
      "filename": "license_details.pdf",
      "content_base64": "JVBERi0xLjQKJ..." // Base64 encoded content
    }
  ]
}
```

**Success Response**: `202 Accepted` (Indicates the request has been accepted for processing)

```json
{
  "message": "Email request accepted for processing.",
  "trackingId": "some-unique-tracking-id" // For logging and potential status checks
}
```

**Error Responses**:
*   `400 Bad Request`: Invalid request payload (e.g., missing required fields, invalid email format).
*   `500 Internal Server Error`: If the email request cannot be queued or an unexpected error occurs.

**Email Types (`emailType`)**:
*   This will be an extensible enum or a string-based identifier.
*   Initial types could include:
    *   `ADVISOR_LICENSE_INFORMATION`: For sending license details.
    *   `ACCOUNT_ACTIVATION`: For new advisor account activation.
    *   `PASSWORD_RESET`: For password reset requests.
    *   `NEWSLETTER_SUBSCRIPTION_CONFIRMATION`: If a newsletter feature is added.
    *   `CUSTOM_MESSAGE`: For more generic emails where the subject and body might be more dynamic (less reliant on a fixed template).

### 3.2. Service Implementation: `AsyncEmailService`

*   **`sendEmailAsync(EmailRequestDTO emailRequest)`**:
    *   Validates the `EmailRequestDTO`.
    *   Retrieves the email template based on `emailRequest.getEmailType()` using `DefaultEmailsFacade` (or a new service that wraps it).
        *   The `DefaultEmails` entity should store subject, HTML body, and text body templates.
        *   Templates should use a common templating engine (e.g., Thymeleaf, Apache FreeMarker, or even simple string replacement if requirements are basic) to inject data from `emailRequest.getTemplateModel()`.
    *   Constructs the email (To, From, Subject, Body).
        *   "From" address should be configurable.
    *   Uses `EmailManager` (which internally uses an SDK) to send the email. This call should be asynchronous (e.g., using `@Async` in Spring Boot or submitting to an `ExecutorService`).
    *   Logs the email sending attempt (e.g., recipient, type, success/failure, trackingId) to `ACTIVITY_LOG` or a dedicated email log table via `ActivityFacade` or a new logging service.
    *   Handles potential exceptions during template processing or email sending.

### 3.3. Controller: `EmailController`

*   Implements the `POST /api/v1/emails/send` endpoint.
*   Delegates the request to `AsyncEmailService`.
*   Handles security (e.g., ensuring the request comes from an authorized internal service or a user with appropriate permissions).

### 3.4. Configuration

*   Email server details (SMTP host, port, credentials) - managed by `EmailManager`'s underlying SDK, ensure this is configurable.
*   "From" email address.
*   Email template locations or database identifiers.

### 3.5. Security Considerations

*   Protect the email sending API to prevent abuse (e.g., rate limiting, authentication/authorization).
*   Sanitize all inputs used in email templates to prevent XSS or other injection attacks if templates allow for rich HTML.
*   Be mindful of PII in logs.

### 3.6. Database Schema for Email Templates (`DEFAULT_EMAILS`)

(Assuming `DefaultEmails` entity maps to a table like `DEFAULT_EMAILS`)

*   `EMAIL_TYPE_ID` (PK, Number): Identifier for the email type (e.g., maps to `ADVISOR_LICENSE_INFORMATION`).
*   `SUBJECT_TEMPLATE` (VARCHAR2): Subject line, can contain placeholders.
*   `BODY_HTML_TEMPLATE` (CLOB): HTML version of the email body, can contain placeholders.
*   `BODY_TEXT_TEMPLATE` (CLOB): Plain text version of the email body, can contain placeholders.
*   `LANGUAGE_CODE` (VARCHAR2, e.g., 'EN', 'FR'): To support multilingual templates.
*   `IS_ACTIVE` (CHAR(1)): 'Y' or 'N'.
*   `DESCRIPTION` (VARCHAR2): For internal reference.

This structure allows for different language versions of each email type. The `AsyncEmailService` would need to select the appropriate template based on `emailType` and potentially a language preference.

---

## 📋 **文档中心API实施完成总结** ✅

### **完成日期**: 2025-01-01
### **负责人**: roy zhu

### **已完成的核心功能**:

#### 1. **数据库Schema分析** ✅
- 创建了详细的数据库Schema分析文档 (`document_database_schema_analysis.md`)
- 分析了 `STORED_FILE` 和 `COMPLIANCE_DOCUMENT` 表结构
- 理解了文档类型映射和查询策略
- 建立了统一的文档查询逻辑

#### 2. **DocumentService完善** ✅
- 完整实现了 `DocumentServiceImpl` 类
- 支持 STORED_FILE 和 COMPLIANCE_DOCUMENT 双表查询
- 实现了文件系统存储策略 (`filesystem://` 前缀)
- 添加了安全文件类型验证和大小限制 (50MB)
- 完整的错误处理和日志记录
- 支持遗留文件访问的向后兼容性

#### 3. **AdvisorDocumentController创建** ✅
- 实现了完整的 RESTful API 端点:
  - `GET /api/v1/advisors/{advisorId}/documents` - 获取文档列表
  - `POST /api/v1/advisors/{advisorId}/documents` - 上传文档
  - `GET /api/v1/advisors/documents/{documentId}` - 下载文档
  - `DELETE /api/v1/advisors/documents/{documentId}` - 删除文档
- 完整的权限控制集成
- 多种HTTP状态码支持
- 详细的API文档注释

#### 4. **权限集成** ✅
- 扩展了 `PermissionService` 类
- 实现了 `canAccessDocument` 方法
- 实现了 `canDeleteDocument` 方法
- 支持文档-顾问关联查询
- 多表权限检查逻辑

#### 5. **文件存储优化** ✅
- 配置了 `application.properties` 文件存储设置
- 实现了统一的文件系统存储策略
- 支持文件大小限制和类型验证
- 启用了 Spring Boot Multipart 支持
- 实现了安全的文件路径处理

### **技术特性**:
- **安全性**: 文件类型验证、路径遍历防护、权限细粒度控制
- **性能**: 单查询获取文档信息、避免N+1问题
- **兼容性**: 支持遗留BLOB文件访问
- **可扩展性**: 支持多种文档源表
- **可维护性**: 清晰的分层架构、完整的日志记录

### **API测试**:
- 添加了完整的API测试用例到 `advisors.api.rest` 文件
- 覆盖了所有CRUD操作
- 包含权限验证和错误场景测试

### **文档交付物**:
1. `document_database_schema_analysis.md` - 数据库Schema分析
2. `DocumentServiceImpl.java` - 完整的文档服务实现
3. `AdvisorDocumentController.java` - REST API控制器
4. `PermissionService.java` - 扩展的权限服务
5. `application.properties` - 文件存储配置
6. `advisors.api.rest` - API测试用例

### **下一步**:
邮件发送API实现 (AsyncEmailService + EmailController)

---

**文档中心API状态**: ✅ **完全完成**
**实施质量**: 生产就绪，包含完整的安全性、错误处理和测试覆盖
