# 文档中心数据库Schema分析

## 概述

本文档分析了文档中心功能涉及的两个核心数据库表：`STORED_FILE` 和 `COMPLIANCE_DOCUMENT`。这两个表用于存储不同类型的文档信息，支持文档管理系统的实现。

## 1. STORED_FILE 表分析

### 1.1 表结构

```sql
create table SKYTEST.STORED_FILE
(
    STORED_FILE_INT_ID NUMBER(8) not null primary key,
    TYPE_              NUMBER(4),
    TYPE_ID            NUMBER(8),
    CREATION_DATE      DATE,
    NAME               VARCHAR2(255),
    CREATED_FROM       VARCHAR2(255),
    FILE_TYPE          NUMBER(8),
    AMOUNT             NUMBER(10),
    AVAILABLE_DATE     DATE,
    NOTE               VARCHAR2(500),
    NOTE_FR            VARCHAR2(500),
    CREATED_BY         NUMBER(8),
    EXTENSION          VARCHAR2(8),
    ULR                VARCHAR2(255),
    VIP                NUMBER(2),
    NAME_FR            VARCHAR2(255),
    ULR_FR             VARCHAR2(255),
    UNAVAILABLE_DATE   DATE,
    PRODUCT_SUPPLIER   NUMBER(8) FK -> PRODUCT_SUPPLIER,
    POLICY             NUMBER(8) FK -> POLICY,
    FILE_TYPE_CLASS    NUMBER(8),
    POLICY_NUMBER      VARCHAR2(16),
    ONBOARDING         NUMBER(10) FK -> ONBOARDING_STATUS,
    TYPE_CODE_HIGHER   NUMBER(8)
)
```

### 1.2 关键字段说明

#### TYPE_ 字段 (文档类型)
- `1` - opportunity (机会)
- `2` - report (报告)
- `3` - Company (公司)
- `4` - License (许可证)
- `5` - E&O Policy (责任保险)
- `6` - Banking (银行)
- `7` - Contracts (合同)
- `8` - Activity (活动)
- `9` - DOCUMENTS (Product Supplier)
- `10` - DASHBOARD (Product Supplier)
- **`11` - Advisor (顾问文档)** ⭐ **重点关注**
- `12` - Contact (联系人)
- `13` - Lead (潜在客户)
- `14` - product supplier for onboarding
- `15` - onboarding
- `16` - onboarding_status

#### VIP 字段 (访问权限)
- `0` - private (私有)
- `1` - public (公开)

#### 文件存储字段
- `ULR` - 文件URL/路径 (英文)
- `ULR_FR` - 文件URL/路径 (法文)
- `NAME` - 文件名 (英文)
- `NAME_FR` - 文件名 (法文)
- `EXTENSION` - 文件扩展名

### 1.3 顾问文档查询逻辑

对于顾问文档，查询条件为：
```sql
WHERE TYPE_ = 11 AND TYPE_ID = {advisorId}
```

## 2. COMPLIANCE_DOCUMENT 表分析

### 2.1 表结构

```sql
create table SKYTEST.COMPLIANCE_DOCUMENT
(
    COMPLIANCE_DOC_INT_ID  NUMBER(8) not null primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    OWNER                  NUMBER(8) FK -> USERS,
    CONTACT                NUMBER(8) FK -> CONTACT,
    PRINT_DATE             DATE,
    SIGNATURE_DATE         DATE,
    TYPE                   NUMBER(4),
    FILE_PATH              VARCHAR2(512),
    TITLE_EN               VARCHAR2(256),
    TITLE_FR               VARCHAR2(256)
)
```

### 2.2 关键字段说明

#### TYPE 字段 (合规文档类型)
- `1` - Life (人寿保险)
- `2` - Crit (重大疾病保险)
- `3` - Invest (投资)
- `4` - DI (伤残保险)

#### 关联字段
- `OWNER` - 文档所有者 (FK to USERS)
- `CONTACT` - 关联联系人 (FK to CONTACT)

#### 文件存储
- `FILE_PATH` - 文件路径 (VARCHAR2(512))
- `TITLE_EN` - 英文标题
- `TITLE_FR` - 法文标题

### 2.3 顾问合规文档查询逻辑

需要通过关联查询获取顾问的合规文档：
```sql
-- 通过OWNER字段查询 (顾问作为用户)
WHERE OWNER = {advisorUserIntId}

-- 通过CONTACT字段查询 (顾问作为联系人)
WHERE CONTACT = {advisorContactIntId}
```

## 3. 文档查询策略

### 3.1 统一文档查询

为了获取顾问的所有文档，需要合并两个表的查询结果：

```sql
-- STORED_FILE 中的顾问文档
SELECT 
    sf.STORED_FILE_INT_ID as documentId,
    sf.NAME as fileName,
    sf.EXTENSION as fileExtension,
    sf.ULR as filePath,
    sf.CREATION_DATE as uploadDate,
    sf.NOTE as description,
    'STORED_FILE' as sourceTable,
    'General' as category
FROM STORED_FILE sf
WHERE sf.TYPE_ = 11 AND sf.TYPE_ID = ?

UNION ALL

-- COMPLIANCE_DOCUMENT 中的合规文档
SELECT 
    cd.COMPLIANCE_DOC_INT_ID as documentId,
    cd.TITLE_EN as fileName,
    NULL as fileExtension,
    cd.FILE_PATH as filePath,
    cd.CREATION_DATE as uploadDate,
    cd.TITLE_EN as description,
    'COMPLIANCE_DOCUMENT' as sourceTable,
    'Compliance' as category
FROM COMPLIANCE_DOCUMENT cd
WHERE cd.OWNER = ? OR cd.CONTACT = ?
```

### 3.2 文件存储策略

#### 现有存储方式
1. **STORED_FILE**: 使用 `ULR` 字段存储文件路径
2. **COMPLIANCE_DOCUMENT**: 使用 `FILE_PATH` 字段存储文件路径

#### 新的存储策略
采用 `filesystem://` 前缀的统一存储策略：
- 格式: `filesystem://{uniqueFilename}`
- 实际存储: 配置的文件系统目录
- 优势: 统一管理，性能更好

## 4. 实体映射

### 4.1 现有实体类
- `com.insurfact.skynet.entity.StoredFile`
- `com.insurfact.skynet.entity.ComplianceDocument`

### 4.2 DTO设计
```java
public class DocumentDTO {
    private Long documentId;
    private String fileName;
    private String fileType;
    private Long fileSize;
    private LocalDateTime uploadDate;
    private String description;
    private String category; // "General" or "Compliance"
    private String uploaderName;
    private String accessUrl;
    private String sourceTable; // "STORED_FILE" or "COMPLIANCE_DOCUMENT"
}
```

## 5. 权限控制策略

### 5.1 访问权限规则
1. **管理员**: 可以访问所有文档
2. **AGA**: 可以访问下属顾问的文档
3. **顾问**: 只能访问自己的文档

### 5.2 权限检查逻辑
```java
@PreAuthorize("@permissionService.canAccessDocument(authentication, #documentId, #sourceTable)")
```

## 6. 技术实现要点

### 6.1 查询优化
- 使用单一查询获取所有文档信息
- 避免N+1查询问题
- 合理使用索引

### 6.2 文件安全
- 路径遍历攻击防护
- 文件类型验证
- 文件大小限制

### 6.3 错误处理
- 文件不存在处理
- 权限不足处理
- 存储空间不足处理

## 7. 下一步实施计划

1. **完善DocumentService** - 基于此Schema分析实现完整的业务逻辑
2. **创建Repository层** - 实现高效的数据访问
3. **实现权限控制** - 集成现有的PermissionService
4. **文件存储优化** - 实现统一的文件系统存储策略
5. **API端点实现** - 创建完整的REST API

---

*文档版本: 1.0*  
*创建日期: 2025-01-01*  
*最后更新: 2025-01-01*
