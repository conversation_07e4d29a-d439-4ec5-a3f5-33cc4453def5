spring.application.name=ws_dundas
spring.jpa.properties.eclipselink.weaving = false
spring.jpa.hibernate.ddl-auto = update
spring.jpa.database-platform = org.eclipse.persistence.platform.database.MySQLPlatform
spring.jpa.properties.eclipselink.logging.level = ALL


# JWT Settings
jwt.secret="laohuangyaoqiushixianjwtsecret20250604"
jwt.expirationMs=3600000
jwt.issuer=com.insurfact
session.maxInactiveMs=1800000

# File Storage Settings
file.upload-dir=./uploads/documents
file.max-size=52428800
file.max-request-size=52428800

# Multipart Settings
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
spring.servlet.multipart.enabled=true
