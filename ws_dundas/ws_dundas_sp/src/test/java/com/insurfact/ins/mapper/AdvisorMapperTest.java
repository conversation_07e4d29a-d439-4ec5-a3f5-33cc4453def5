package com.insurfact.ins.mapper;

import com.insurfact.ins.dto.CompanyDTO;
import com.insurfact.skynet.entity.Company;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Address;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Date;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for AdvisorMapper class.
 * Tests the conversion methods between skynet entities and DTOs.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
class AdvisorMapperTest {

    private AdvisorMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new AdvisorMapper();
    }

    @Test
    @DisplayName("Should convert Company entity to CompanyDTO successfully")
    void testToCompanyDTO_Success() {
        // Given
        Company company = createTestCompany();

        // When
        CompanyDTO result = mapper.toCompanyDTO(company);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getCompanyId()).isEqualTo(123L);
        assertThat(result.getCompanyIntId()).isEqualTo(456L);
        assertThat(result.getNameEn()).isEqualTo("Test Company Inc.");
        assertThat(result.getNameFr()).isEqualTo("Compagnie Test Inc.");
        assertThat(result.getPrimaryName()).isEqualTo("Test Company");
        assertThat(result.getOtherName()).isEqualTo("TC Inc");
        assertThat(result.getProvincialBusinessNumber()).isEqualTo("123456789BC0001");
        assertThat(result.getCompanyType()).isEqualTo(1);
        assertThat(result.getActive()).isEqualTo("Y");
        assertThat(result.getAssignableCommissions()).isEqualTo("Y");
        assertThat(result.getBuildingType()).isEqualTo(2);
        assertThat(result.getIsPrimary()).isFalse(); // Default value
    }

    @Test
    @DisplayName("Should handle null Company entity gracefully")
    void testToCompanyDTO_NullInput() {
        // When
        CompanyDTO result = mapper.toCompanyDTO(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("Should handle Company entity with minimal data")
    void testToCompanyDTO_MinimalData() {
        // Given
        Company company = new Company();
        // Only set required fields that exist in entity

        // When
        CompanyDTO result = mapper.toCompanyDTO(company);

        // Then
        assertThat(result).isNotNull();
        // All fields should be null or default values
        assertThat(result.getCompanyId()).isNull();
        assertThat(result.getCompanyIntId()).isNull();
        assertThat(result.getNameEn()).isNull();
        assertThat(result.getNameFr()).isNull();
        assertThat(result.getIsPrimary()).isFalse(); // Default value
    }

    @Test
    @DisplayName("Should handle Company entity with business address")
    void testToCompanyDTO_WithBusinessAddress() {
        // Given
        Company company = createTestCompany();
        Contact contact = createTestContact();
        Address address = createTestAddress();
        
        List<Address> addresses = new ArrayList<>();
        addresses.add(address);
        
        try {
            contact.setAddressList(addresses);
            company.setContact(contact);
        } catch (Exception e) {
            // Methods may not exist, skip for this test
        }

        // When
        CompanyDTO result = mapper.toCompanyDTO(company);

        // Then
        assertThat(result).isNotNull();
        // Business address may or may not be set depending on entity implementation
        // This test verifies the method doesn't throw exceptions
    }

    /**
     * Creates a test Company entity with sample data.
     * Uses defensive programming to handle potential missing setter methods.
     */
    private Company createTestCompany() {
        Company company = new Company();
        
        try {
            company.setCompanyId(123);
        } catch (Exception e) {
            // setCompanyId() method may not exist, skip
        }
        
        try {
            company.setCompanyIntId(456);
        } catch (Exception e) {
            // setCompanyIntId() method may not exist, skip
        }
        
        try {
            company.setNameEn("Test Company Inc.");
        } catch (Exception e) {
            // setNameEn() method may not exist, skip
        }
        
        try {
            company.setNameFr("Compagnie Test Inc.");
        } catch (Exception e) {
            // setNameFr() method may not exist, skip
        }
        
        try {
            company.setPrimaryName("Test Company");
        } catch (Exception e) {
            // setPrimaryName() method may not exist, skip
        }
        
        try {
            company.setOtherName("TC Inc");
        } catch (Exception e) {
            // setOtherName() method may not exist, skip
        }
        
        try {
            company.setProvBusinessNumber("123456789BC0001");
        } catch (Exception e) {
            // setProvBusinessNumber() method may not exist, skip
        }
        
        try {
            company.setCompanyType(1);
        } catch (Exception e) {
            // setCompanyType() method may not exist, skip
        }
        
        try {
            company.setActive("Y");
        } catch (Exception e) {
            // setActive() method may not exist, skip
        }
        
        try {
            company.setAssignableCommissions("Y");
        } catch (Exception e) {
            // setAssignableCommissions() method may not exist, skip
        }
        
        try {
            company.setBuildingType(2);
        } catch (Exception e) {
            // setBuildingType() method may not exist, skip
        }
        
        try {
            company.setBusinessStartDate(new Date());
        } catch (Exception e) {
            // setBusinessStartDate() method may not exist, skip
        }
        
        try {
            company.setCreationDate(new Date());
        } catch (Exception e) {
            // setCreationDate() method may not exist, skip
        }
        
        try {
            company.setLastModificationDate(new Date());
        } catch (Exception e) {
            // setLastModificationDate() method may not exist, skip
        }
        
        return company;
    }

    /**
     * Creates a test Contact entity.
     */
    private Contact createTestContact() {
        Contact contact = new Contact();
        
        try {
            contact.setContactIntId(789);
        } catch (Exception e) {
            // setContactIntId() method may not exist, skip
        }
        
        return contact;
    }

    /**
     * Creates a test Address entity.
     */
    private Address createTestAddress() {
        Address address = new Address();
        
        try {
            address.setAddressIntId(101);
        } catch (Exception e) {
            // setAddressIntId() method may not exist, skip
        }
        
        try {
            address.setAddressLine1("123 Test Street");
        } catch (Exception e) {
            // setAddressLine1() method may not exist, skip
        }
        
        try {
            address.setCity("Test City");
        } catch (Exception e) {
            // setCity() method may not exist, skip
        }
        
        return address;
    }
}
