package com.insurfact.ins.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

/**
 * Data Transfer Object for License information.
 * This DTO represents individual license details for advisors.
 * 
 * Licenses are regulatory permissions that allow advisors to sell specific
 * types of financial products in specific provinces.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "License information including regulatory details and associated liability")
public class LicenseDTO {

    @Schema(description = "Unique license identifier", example = "12345")
    private Long licenseId;

    @Schema(description = "License number", example = "LIC-ON-2024-001")
    private String licenseNumber;

    @Schema(description = "Province where license is valid", example = "ON")
    private String province;

    @Schema(description = "Start date of license validity")
    private LocalDate startDate;

    @Schema(description = "End date of license validity")
    private LocalDate endDate;

    @Schema(description = "License description or type", example = "Life Insurance License")
    private String description;

    @Schema(description = "Client number associated with the license", example = "123456789")
    private Long clientNumber;

    @Schema(description = "Status of the license", example = "1", allowableValues = {"1", "2", "3"})
    private Integer status;

    @Schema(description = "Date when license was requested")
    private LocalDate requestDate;

    @Schema(description = "Date when license was sent/issued")
    private LocalDate sentDate;

    @Schema(description = "Associated license liability information")
    private LicenseLiabilityDTO licenseLiability;

    @Schema(description = "Company name associated with the license")
    private String companyName;

    @Schema(description = "Agency name associated with the license")
    private String agencyName;

    @Schema(description = "License category description")
    private String categoryDescription;

    @Schema(description = "Human-readable status description", example = "Active")
    private String statusDescription;

    /**
     * Get status description based on status code.
     * This is a convenience method for UI display.
     * 
     * @return Human-readable status description
     */
    public String getStatusDescription() {
        if (status == null) {
            return "Unknown";
        }
        
        switch (status) {
            case 1:
                return "Active";
            case 2:
                return "Inactive";
            case 3:
                return "Expired";
            case 4:
                return "Pending";
            case 5:
                return "Suspended";
            default:
                return "Unknown";
        }
    }

    /**
     * Check if the license is currently active.
     * 
     * @return true if status is 1 (Active), false otherwise
     */
    public boolean isActive() {
        return status != null && status == 1;
    }

    /**
     * Check if the license is expired based on end date.
     * 
     * @return true if end date is in the past, false otherwise
     */
    public boolean isExpired() {
        return endDate != null && endDate.isBefore(LocalDate.now());
    }

    /**
     * Check if the license is about to expire within the specified days.
     * 
     * @param days Number of days to check ahead
     * @return true if license expires within the specified days, false otherwise
     */
    public boolean isExpiringWithin(int days) {
        if (endDate == null) {
            return false;
        }
        LocalDate checkDate = LocalDate.now().plusDays(days);
        return endDate.isBefore(checkDate) && !isExpired();
    }
}
