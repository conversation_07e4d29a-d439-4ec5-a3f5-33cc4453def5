package com.insurfact.ins.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

/**
 * Data Transfer Object for License Liability information.
 * This DTO represents license liability details associated with advisor licenses.
 * 
 * License liabilities are insurance coverage requirements that advisors must maintain
 * to operate legally in their respective provinces.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "License liability information including insurance coverage details")
public class LicenseLiabilityDTO {

    @Schema(description = "Unique license liability identifier", example = "12345")
    private Long liabilityId;

    @Schema(description = "License liability number", example = "LIA-2024-001")
    private String liabilityNumber;

    @Schema(description = "Name or description of the liability", example = "Professional Liability Insurance")
    private String name;

    @Schema(description = "Insurance company providing the coverage", example = "ABC Insurance Corp")
    private String insuranceCompany;

    @Schema(description = "Start date of liability coverage")
    private LocalDate startDate;

    @Schema(description = "End date of liability coverage")
    private LocalDate endDate;

    @Schema(description = "Province where liability is applicable", example = "ON")
    private String province;

    @Schema(description = "Additional notes about the liability")
    private String note;

    @Schema(description = "Status of the liability", example = "1", allowableValues = {"1", "2", "3"})
    private Integer status;

    @Schema(description = "Whether this liability can be shared across multiple advisors")
    private Boolean shareable;

    @Schema(description = "Human-readable status description", example = "Active")
    private String statusDescription;

    /**
     * Get status description based on status code.
     * This is a convenience method for UI display.
     * 
     * @return Human-readable status description
     */
    public String getStatusDescription() {
        if (status == null) {
            return "Unknown";
        }
        
        switch (status) {
            case 1:
                return "Active";
            case 2:
                return "Inactive";
            case 3:
                return "Expired";
            default:
                return "Unknown";
        }
    }

    /**
     * Check if the liability is currently active.
     * 
     * @return true if status is 1 (Active), false otherwise
     */
    public boolean isActive() {
        return status != null && status == 1;
    }

    /**
     * Check if the liability is expired based on end date.
     * 
     * @return true if end date is in the past, false otherwise
     */
    public boolean isExpired() {
        return endDate != null && endDate.isBefore(LocalDate.now());
    }
}
