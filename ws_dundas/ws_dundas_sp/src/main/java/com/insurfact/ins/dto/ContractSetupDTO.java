package com.insurfact.ins.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * DTO for Contract Setup information in Advisor Profile
 * 
 * <AUTHOR> zhu
 * @since 2025-01-01
 */
@Setter
@Getter
@Schema(description = "Contract setup information for an advisor")
public class ContractSetupDTO {

    // Getters and Setters
    @Schema(description = "Contract setup internal ID", example = "12345")
    @JsonProperty("contractSetupId")
    private Long contractSetupId;

    @Schema(description = "Product supplier ID", example = "100")
    @JsonProperty("productSupplierId")
    private Long productSupplierId;

    @Schema(description = "Product supplier name", example = "ABC Insurance")
    @JsonProperty("productSupplierName")
    private String productSupplierName;

    @Schema(description = "Agency ID", example = "200")
    @JsonProperty("agencyId")
    private Long agencyId;

    @Schema(description = "Agency name", example = "XYZ Agency")
    @JsonProperty("agencyName")
    private String agencyName;

    @Schema(description = "Company ID", example = "300")
    @JsonProperty("companyId")
    private Long companyId;

    @Schema(description = "Company name", example = "DEF Company")
    @JsonProperty("companyName")
    private String companyName;

    @Schema(description = "Servicing agency ID", example = "250")
    @JsonProperty("servicingAgencyId")
    private Long servicingAgencyId;

    @Schema(description = "Servicing agency name", example = "Service Agency")
    @JsonProperty("servicingAgencyName")
    private String servicingAgencyName;

    @Schema(description = "AGA advisor ID", example = "400")
    @JsonProperty("agaAdvisorId")
    private Long agaAdvisorId;

    @Schema(description = "AGA advisor name", example = "John Smith")
    @JsonProperty("agaAdvisorName")
    private String agaAdvisorName;

    @Schema(description = "MGA product supplier ID", example = "150")
    @JsonProperty("mgaProductSupplierId")
    private Long mgaProductSupplierId;

    @Schema(description = "MGA product supplier name", example = "MGA Insurance")
    @JsonProperty("mgaProductSupplierName")
    private String mgaProductSupplierName;

    @Schema(description = "AGA product supplier ID", example = "160")
    @JsonProperty("agaProductSupplierId")
    private Long agaProductSupplierId;

    @Schema(description = "AGA product supplier name", example = "AGA Insurance")
    @JsonProperty("agaProductSupplierName")
    private String agaProductSupplierName;

    @Schema(description = "AGA2 product supplier ID", example = "170")
    @JsonProperty("aga2ProductSupplierId")
    private Long aga2ProductSupplierId;

    @Schema(description = "AGA2 product supplier name", example = "AGA2 Insurance")
    @JsonProperty("aga2ProductSupplierName")
    private String aga2ProductSupplierName;

    // Default constructor
    public ContractSetupDTO() {}




}
