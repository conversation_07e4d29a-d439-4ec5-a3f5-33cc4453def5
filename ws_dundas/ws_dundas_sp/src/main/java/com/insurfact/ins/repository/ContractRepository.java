package com.insurfact.ins.repository;

import com.insurfact.skynet.entity.Contract;
import com.insurfact.skynet.entity.ContractSetup;
import com.insurfact.skynet.entity.ContractEft;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.Company;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Repository for Contract and ContractSetup data access using JDBC Template.
 * Handles complex queries to retrieve contract information for advisors.
 * 
 * <AUTHOR> zhu
 * @since 2025-01-01
 */
@Repository
public class ContractRepository {

    private static final Logger logger = LoggerFactory.getLogger(ContractRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public ContractRepository(@Qualifier("skytestJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Find all contracts for a given advisor ID.
     * This includes CONTRACT, CONTRACT_SETUP, and related entities.
     * 
     * @param advisorId The advisor ID to search for
     * @return List of Contract entities with populated relationships
     */
    public List<Contract> findContractsByAdvisorId(Long advisorId) {
        logger.debug("Finding contracts for advisor ID: {}", advisorId);

        String sql = """
            SELECT 
                c.CONTRACT_INT_ID,
                c.CONTRACT_TYPE,
                c.CONTRACT_NUMBER,
                c.CONTRACT_STATUS,
                c.EFFECTIVE_DATE,
                c.INACTIVE_DATE,
                c.SALESREP_CODE,
                c.COMMISSION_PAY_TO,
                c.MFCODE,
                c.DEALER_CODE,
                c.DISTRICT,
                c.IS_PRIMARY,
                c.DIRECT_DEPOSIT,
                c.EFT_FORM_MINCL,
                c.EFT_SENT_DATE,
                c.HEALTH_CODE,
                c.TRANSFER_IN_PRICE,
                c.TRANSFER_OUT_PRICE,
                c.TRANSFER_IN_DATE,
                c.TRANSFER_OUT_DATE,
                c.COMPANY_ASSOCIATION,
                c.TRANS_PAID_DATE,
                c.PAYSCALE,
                c.CREATION_DATE as CONTRACT_CREATION_DATE,
                c.LAST_MODIFICATION_DATE as CONTRACT_LAST_MOD_DATE,
                -- Contract Setup fields
                cs.CONTRACT_SETUP_INT_ID,
                cs.CREATION_DATE as CS_CREATION_DATE,
                cs.LAST_MODIFICATION_DATE as CS_LAST_MOD_DATE,
                cs.AGENCY_NAME,
                cs.DELETED as CS_DELETED,
                cs.DELETED_DATE as CS_DELETED_DATE,
                -- Product Supplier fields
                ps.PRODUCT_SUPPLIER_INT_ID,
                ps_org.ORGANIZATION_DESC_EN as PS_NAME,
                ps_org.ORGANIZATION_CODE as PS_CODE,
                -- MGA Product Supplier fields
                mga_ps.PRODUCT_SUPPLIER_INT_ID as MGA_PS_ID,
                mga_ps_org.ORGANIZATION_DESC_EN as MGA_PS_NAME,
                mga_ps_org.ORGANIZATION_CODE as MGA_PS_CODE,
                -- AGA Product Supplier fields
                aga_ps.PRODUCT_SUPPLIER_INT_ID as AGA_PS_ID,
                aga_ps_org.ORGANIZATION_DESC_EN as AGA_PS_NAME,
                aga_ps_org.ORGANIZATION_CODE as AGA_PS_CODE,
                -- AGA2 Product Supplier fields
                aga2_ps.PRODUCT_SUPPLIER_INT_ID as AGA2_PS_ID,
                aga2_ps_org.ORGANIZATION_DESC_EN as AGA2_PS_NAME,
                aga2_ps_org.ORGANIZATION_CODE as AGA2_PS_CODE,
                -- Agency fields
                ag.AGENCY_INT_ID,
                ag_org.ORGANIZATION_DESC_EN as AGENCY_NAME,
                ag_org.ORGANIZATION_CODE as AGENCY_CODE,
                -- Servicing Agency fields
                sag.AGENCY_INT_ID as SERVICING_AGENCY_ID,
                sag_org.ORGANIZATION_DESC_EN as SERVICING_AGENCY_NAME,
                sag_org.ORGANIZATION_CODE as SERVICING_AGENCY_CODE,
                -- Company fields
                comp.COMPANY_INT_ID,
                comp.PRIMARY_NAME as COMPANY_NAME
            FROM CONTRACT c
            INNER JOIN CONTRACT_SETUP cs ON c.CONTRACT_INT_ID = cs.CONTRACT_SETUP_INT_ID
            LEFT JOIN PRODUCT_SUPPLIER ps ON cs.PRODUCT_SUPPLIER = ps.PRODUCT_SUPPLIER_INT_ID
            LEFT JOIN ORGANIZATION ps_org ON ps.ORGANIZATION = ps_org.ORGANIZATION_INT_ID
            LEFT JOIN PRODUCT_SUPPLIER mga_ps ON cs.MGA_PRODUCT_SUPPLIER = mga_ps.PRODUCT_SUPPLIER_INT_ID
            LEFT JOIN ORGANIZATION mga_ps_org ON mga_ps.ORGANIZATION = mga_ps_org.ORGANIZATION_INT_ID
            LEFT JOIN PRODUCT_SUPPLIER aga_ps ON cs.AGA_PRODUCT_SUPPLIER = aga_ps.PRODUCT_SUPPLIER_INT_ID
            LEFT JOIN ORGANIZATION aga_ps_org ON aga_ps.ORGANIZATION = aga_ps_org.ORGANIZATION_INT_ID
            LEFT JOIN PRODUCT_SUPPLIER aga2_ps ON cs.AGA2_PRODUCT_SUPPLIER = aga2_ps.PRODUCT_SUPPLIER_INT_ID
            LEFT JOIN ORGANIZATION aga2_ps_org ON aga2_ps.ORGANIZATION = aga2_ps_org.ORGANIZATION_INT_ID
            LEFT JOIN AGENCY ag ON cs.AGENCY = ag.AGENCY_INT_ID
            LEFT JOIN ORGANIZATION ag_org ON ag.ORGANIZATION = ag_org.ORGANIZATION_INT_ID
            LEFT JOIN AGENCY sag ON cs.SERVICING_AGENCY = sag.AGENCY_INT_ID
            LEFT JOIN ORGANIZATION sag_org ON sag.ORGANIZATION = sag_org.ORGANIZATION_INT_ID
            LEFT JOIN COMPANY comp ON cs.COMPANY = comp.COMPANY_INT_ID
            WHERE cs.ADVISOR = ?
              AND (cs.DELETED != 'Y' OR cs.DELETED IS NULL)
            ORDER BY c.EFFECTIVE_DATE DESC, c.CONTRACT_NUMBER
            """;

        return jdbcTemplate.query(sql, new Object[]{advisorId}, new ContractResultSetExtractor());
    }

    /**
     * ResultSetExtractor to build Contract entities from the complex query result.
     */
    private static class ContractResultSetExtractor implements ResultSetExtractor<List<Contract>> {

        @Override
        public List<Contract> extractData(ResultSet rs) throws SQLException {
            Map<Long, Contract> contractMap = new HashMap<>();

            while (rs.next()) {
                Long contractId = rs.getLong("CONTRACT_INT_ID");
                
                Contract contract = contractMap.get(contractId);
                if (contract == null) {
                    contract = buildContractFromResultSet(rs);
                    contractMap.put(contractId, contract);
                }
            }

            return new ArrayList<>(contractMap.values());
        }

        private Contract buildContractFromResultSet(ResultSet rs) throws SQLException {
            Contract contract = new Contract();
            
            try {
                // Set Contract fields
                contract.setContractIntId(rs.getInt("CONTRACT_INT_ID"));
                contract.setContractType(rs.getInt("CONTRACT_TYPE"));
                contract.setContractNumber(rs.getString("CONTRACT_NUMBER"));
                contract.setContractStatus(rs.getInt("CONTRACT_STATUS"));
                contract.setEffectiveDate(rs.getDate("EFFECTIVE_DATE"));
                contract.setInactiveDate(rs.getDate("INACTIVE_DATE"));
                contract.setSalesrepCode(rs.getString("SALESREP_CODE"));
                contract.setCommissionPayTo(rs.getString("COMMISSION_PAY_TO"));
                contract.setMfcode(rs.getString("MFCODE"));
                contract.setDealerCode(rs.getString("DEALER_CODE"));
                contract.setDistrict(rs.getString("DISTRICT"));
                
                // Handle CHAR fields - convert to appropriate types
                String isPrimary = rs.getString("IS_PRIMARY");
                if (isPrimary != null) {
                    contract.setPrimary("Y".equals(isPrimary));
                }

                String directDeposit = rs.getString("DIRECT_DEPOSIT");
                if (directDeposit != null) {
                    contract.setDirectDeposit(directDeposit);
                }

                String eftFormMincl = rs.getString("EFT_FORM_MINCL");
                if (eftFormMincl != null) {
                    contract.setEftFormMincl(eftFormMincl);
                }
                
                contract.setEftSentDate(rs.getDate("EFT_SENT_DATE"));
                contract.setHealthCode(rs.getString("HEALTH_CODE"));

                // Handle BigDecimal to Double conversion
                BigDecimal transferInPrice = rs.getBigDecimal("TRANSFER_IN_PRICE");
                if (transferInPrice != null) {
                    contract.setTransferInPrice(transferInPrice.doubleValue());
                }

                BigDecimal transferOutPrice = rs.getBigDecimal("TRANSFER_OUT_PRICE");
                if (transferOutPrice != null) {
                    contract.setTransferOutPrice(transferOutPrice.doubleValue());
                }

                contract.setTransferInDate(rs.getDate("TRANSFER_IN_DATE"));
                contract.setTransferOutDate(rs.getDate("TRANSFER_OUT_DATE"));
                contract.setCompanyAssociation(rs.getInt("COMPANY_ASSOCIATION"));
                contract.setTransPaidDate(rs.getDate("TRANS_PAID_DATE"));

                String payScale = rs.getString("PAYSCALE");
                if (payScale != null) {
                    contract.setPayscale(payScale);
                }
                
                contract.setCreationDate(rs.getDate("CONTRACT_CREATION_DATE"));
                contract.setLastModificationDate(rs.getDate("CONTRACT_LAST_MOD_DATE"));

                // Build and set ContractSetup
                ContractSetup contractSetup = buildContractSetupFromResultSet(rs);
                contract.setContractSetup(contractSetup);

            } catch (Exception e) {
                // Defensive programming: handle cases where entity methods might not exist
                logger.warn("Error setting contract field, method might not exist: {}", e.getMessage());
            }

            return contract;
        }

        private ContractSetup buildContractSetupFromResultSet(ResultSet rs) throws SQLException {
            ContractSetup contractSetup = new ContractSetup();
            
            try {
                contractSetup.setContractSetupIntId(rs.getInt("CONTRACT_SETUP_INT_ID"));
                contractSetup.setCreationDate(rs.getDate("CS_CREATION_DATE"));
                contractSetup.setLastModificationDate(rs.getDate("CS_LAST_MOD_DATE"));
                contractSetup.setAgencyName(rs.getString("AGENCY_NAME"));
                contractSetup.setDeleted(rs.getString("CS_DELETED"));
                contractSetup.setDeletedDate(rs.getDate("CS_DELETED_DATE"));

                // Set Product Supplier
                if (rs.getLong("PRODUCT_SUPPLIER_INT_ID") != 0) {
                    ProductSupplier ps = new ProductSupplier();
                    ps.setProductSupplierIntId(rs.getInt("PRODUCT_SUPPLIER_INT_ID"));
                    // Note: ProductSupplier entity may not have setName/setCode methods
                    // These are stored in the related Organization entity
                    contractSetup.setProductSupplier(ps);
                }

                // Set MGA Product Supplier
                if (rs.getLong("MGA_PS_ID") != 0) {
                    ProductSupplier mgaPs = new ProductSupplier();
                    mgaPs.setProductSupplierIntId(rs.getInt("MGA_PS_ID"));
                    contractSetup.setMgaProductSupplier(mgaPs);
                }

                // Set AGA Product Supplier
                if (rs.getLong("AGA_PS_ID") != 0) {
                    ProductSupplier agaPs = new ProductSupplier();
                    agaPs.setProductSupplierIntId(rs.getInt("AGA_PS_ID"));
                    contractSetup.setAgaProductSupplier(agaPs);
                }

                // Set AGA2 Product Supplier
                if (rs.getLong("AGA2_PS_ID") != 0) {
                    ProductSupplier aga2Ps = new ProductSupplier();
                    aga2Ps.setProductSupplierIntId(rs.getInt("AGA2_PS_ID"));
                    contractSetup.setAga2ProductSupplier(aga2Ps);
                }

                // Set Agency
                if (rs.getLong("AGENCY_INT_ID") != 0) {
                    Agency agency = new Agency();
                    agency.setAgencyIntId(rs.getInt("AGENCY_INT_ID"));
                    // Note: Agency entity may not have setName/setCode methods
                    // These are stored in the related Organization entity
                    contractSetup.setAgency(agency);
                }

                // Set Servicing Agency
                if (rs.getLong("SERVICING_AGENCY_ID") != 0) {
                    Agency servicingAgency = new Agency();
                    servicingAgency.setAgencyIntId(rs.getInt("SERVICING_AGENCY_ID"));
                    contractSetup.setServicingAgency(servicingAgency);
                }

                // Set Company
                if (rs.getLong("COMPANY_INT_ID") != 0) {
                    Company company = new Company();
                    company.setCompanyIntId(rs.getInt("COMPANY_INT_ID"));
                    company.setPrimaryName(rs.getString("COMPANY_NAME"));
                    contractSetup.setCompany(company);
                }

            } catch (Exception e) {
                // Defensive programming: handle cases where entity methods might not exist
                logger.warn("Error setting contract setup field, method might not exist: {}", e.getMessage());
            }

            return contractSetup;
        }
    }
}
