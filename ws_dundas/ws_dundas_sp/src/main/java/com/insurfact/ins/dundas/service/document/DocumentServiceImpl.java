package com.insurfact.ins.dundas.service.document;

import com.insurfact.ins.dundas.dto.document.DocumentDTO;
import com.insurfact.skynet.entity.StoredFile;
import com.insurfact.skynet.entity.ComplianceDocument;
import com.insurfact.skynet.entity.Users;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Advisor;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.NoResultException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class DocumentServiceImpl implements DocumentService {

    private static final Logger log = LoggerFactory.getLogger(DocumentServiceImpl.class);

    @PersistenceContext
    private EntityManager entityManager;

    private final Path fileStorageLocation;
    private final JdbcTemplate jdbcTemplate;

    // Constructor injection
    public DocumentServiceImpl(
            @Value("${file.upload-dir:./uploads}") String uploadDir,
            @Qualifier("skytestJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
        this.jdbcTemplate = jdbcTemplate;
        try {
            Files.createDirectories(this.fileStorageLocation);
            log.info("Document storage directory created/verified: {}", this.fileStorageLocation);
        } catch (Exception ex) {
            log.error("Could not create document storage directory: {}", this.fileStorageLocation, ex);
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<DocumentDTO> getDocumentsForAdvisor(Long advisorId) {
        // This implementation needs to convert advisorId (Long) to the appropriate ID type 
        // used in StoredFile (TYPE_ID which is Integer) and ComplianceDocument (OWNER which is Users object or CONTACT which is Contact object).
        // This requires fetching the Users/Contact entity for the advisor first.

        // Fetch User entity for the advisor (assuming advisorId is userIntId)
        Users advisorUser = entityManager.find(Users.class, advisorId.intValue()); // Or however you fetch Users
        Contact advisorContact = null; // Fetch contact if needed, e.g., based on user or a different ID

        List<DocumentDTO> documents = new ArrayList<>();

        // 1. Fetch from STORED_FILE
        if (advisorUser != null) {
            TypedQuery<StoredFile> sfQuery = entityManager.createQuery(
                "SELECT sf FROM StoredFile sf WHERE sf.type = :type AND sf.typeId = :typeId ORDER BY sf.creationDate DESC", StoredFile.class);
            sfQuery.setParameter("type", 11); // TYPE_ = 11 for Advisors
            sfQuery.setParameter("typeId", advisorUser.getUserIntId()); 
            List<StoredFile> storedFiles = sfQuery.getResultList();
            documents.addAll(storedFiles.stream().map(this::mapStoredFileToDTO).collect(Collectors.toList()));
        }

        // 2. Fetch from COMPLIANCE_DOCUMENT

        if (advisorUser != null) {
            TypedQuery<ComplianceDocument> cdQuery = entityManager.createQuery(
                "SELECT cd FROM ComplianceDocument cd WHERE cd.users = :users ORDER BY cd.creationDate DESC", ComplianceDocument.class);
            cdQuery.setParameter("users", advisorUser);
            List<ComplianceDocument> complianceDocuments = cdQuery.getResultList();
            documents.addAll(complianceDocuments.stream().map(this::mapComplianceDocumentToDTO).collect(Collectors.toList()));
        }

        documents.sort((d1, d2) -> d2.getUploadDate().compareTo(d1.getUploadDate()));

        return documents;
    }

    @Override
    @Transactional
    public DocumentDTO uploadDocument(Long advisorId, String description, String category, MultipartFile file) {
        // Determine userIntId for storing in StoredFile or linking ComplianceDocument
        // For simplicity, assuming advisorId is userIntId for StoredFile.TYPE_ID
        // And for ComplianceDocument, we'd need the Users entity.
        Users uploader = entityManager.find(Users.class, advisorId.intValue()); // Fetch the user
        if (uploader == null) {
            throw new RuntimeException("Uploader (advisor) not found with ID: " + advisorId);
        }

        String originalFilename = file.getOriginalFilename();
        String uniqueFilename = UUID.randomUUID().toString() + "_" + originalFilename;
        Path targetLocation = this.fileStorageLocation.resolve(uniqueFilename);

        try {
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException ex) {
            throw new RuntimeException("Could not store file " + originalFilename + ". Please try again!", ex);
        }

        // For this example, we'll create a StoredFile record.
        // If 'category' dictates it's a ComplianceDocument, logic would differ.
        StoredFile storedFile = new StoredFile();
        storedFile.setName(originalFilename);
        int fileType = 0;

        storedFile.setFileType(fileType);
        storedFile.setAmount( ((double) file.getSize() / 1024));
        storedFile.setCreationDate(new Date()); // Current time
        storedFile.setNote(description);
        storedFile.setUrl("filesystem://" + uniqueFilename); // New storage strategy
        storedFile.setType(11); // Advisor documents
        storedFile.setTypeId(advisorId.intValue()); // Link to advisor
        storedFile.setCreatedBy(uploader); // Link to uploader User entity

        entityManager.persist(storedFile);

        return mapStoredFileToDTO(storedFile);
    }

    @Override
    public Resource downloadDocument(Long documentId, String sourceTable) {
        try {
            Path filePath = null;
            if ("COMPLIANCE_DOCUMENT".equalsIgnoreCase(sourceTable)) {
                ComplianceDocument cd = entityManager.find(ComplianceDocument.class, documentId.intValue());
                if (cd == null || cd.getFilePath() == null) {
                    throw new RuntimeException("ComplianceDocument not found or filePath is null for ID: " + documentId);
                }
                // Assuming filePath is an absolute path or resolvable relative to a base
                // For new files, it would be like "filesystem://..."
                if (cd.getFilePath().startsWith("filesystem://")) {
                    filePath = this.fileStorageLocation.resolve(cd.getFilePath().substring("filesystem://".length())).normalize();
                } else {
                    // Legacy: direct path, potentially needs careful handling for security
                    filePath = Paths.get(cd.getFilePath()).normalize(); 
                }
            } else { // Default to STORED_FILE
                StoredFile sf = entityManager.find(StoredFile.class, documentId.intValue());
                if (sf == null) {
                    throw new RuntimeException("StoredFile not found with ID: " + documentId);
                }
                if (sf.getUrl() != null && sf.getUrl().startsWith("filesystem://")) {
                    filePath = this.fileStorageLocation.resolve(sf.getUrl().substring("filesystem://".length())).normalize();
                }
            }

            Resource resource = new UrlResource(filePath.toUri());
            if (resource.exists() && resource.isReadable()) {
                return resource;
            } else {
                throw new RuntimeException("Could not read file: " + filePath.getFileName());
            }
        } catch (MalformedURLException ex) {
            throw new RuntimeException("File not found or path is invalid", ex);
        }
    }
    


    @Override
    @Transactional
    public void deleteDocument(Long documentId, String sourceTable) {
        log.debug("Deleting document ID: {} from table: {}", documentId, sourceTable);

        if (documentId == null || documentId <= 0) {
            throw new IllegalArgumentException("Document ID must be a positive number");
        }

        try {
            if ("COMPLIANCE_DOCUMENT".equalsIgnoreCase(sourceTable)) {
                ComplianceDocument cd = entityManager.find(ComplianceDocument.class, documentId.intValue());
                if (cd == null) {
                    throw new RuntimeException("ComplianceDocument not found with ID: " + documentId);
                }

                // Delete physical file if it exists
                if (cd.getFilePath() != null && !cd.getFilePath().trim().isEmpty()) {
                    deletePhysicalFile(cd.getFilePath(), "ComplianceDocument ID: " + documentId);
                }

                entityManager.remove(cd);
                log.info("Successfully deleted ComplianceDocument ID: {}", documentId);

            } else { // Default to STORED_FILE
                StoredFile sf = entityManager.find(StoredFile.class, documentId.intValue());
                if (sf == null) {
                    throw new RuntimeException("StoredFile not found with ID: " + documentId);
                }

                // Delete physical file if it exists
                if (sf.getUrl() != null && !sf.getUrl().trim().isEmpty()) {
                    deletePhysicalFile(sf.getUrl(), "StoredFile ID: " + documentId);
                }

                entityManager.remove(sf);
                log.info("Successfully deleted StoredFile ID: {}", documentId);
            }

        } catch (Exception ex) {
            log.error("Error deleting document ID {}: {}", documentId, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete document", ex);
        }
    }

    private void deletePhysicalFile(String filePath, String context) {
        try {
            if (filePath.startsWith("filesystem://")) {
                String relativePath = filePath.substring("filesystem://".length());
                Path physicalPath = this.fileStorageLocation.resolve(relativePath).normalize();

                // Security check
                if (!physicalPath.startsWith(this.fileStorageLocation)) {
                    log.warn("Attempted to delete file outside storage directory: {} ({})", physicalPath, context);
                    return;
                }

                if (Files.exists(physicalPath)) {
                    Files.delete(physicalPath);
                    log.debug("Deleted physical file: {} ({})", physicalPath, context);
                } else {
                    log.debug("Physical file not found, skipping deletion: {} ({})", physicalPath, context);
                }
            } else {
                // Legacy file path - be more cautious
                log.warn("Legacy file path deletion requested: {} ({})", filePath, context);
                // Could implement legacy file deletion here if needed
            }
        } catch (IOException ex) {
            // Log error but don't fail the transaction - database cleanup is more important
            log.error("Error deleting physical file: {} ({}): {}", filePath, context, ex.getMessage(), ex);
        }
    }

    // --- Helper Mapper Methods ---
    private DocumentDTO mapStoredFileToDTO(StoredFile sf) {
        if (sf == null) return null;

        try {
            // Determine file size
            Long fileSize = null;
            if (sf.getUrl() != null && sf.getUrl().startsWith("filesystem://")) {
                try {
                    String relativePath = sf.getUrl().substring("filesystem://".length());
                    Path filePath = this.fileStorageLocation.resolve(relativePath).normalize();
                    if (Files.exists(filePath)) {
                        fileSize = Files.size(filePath);
                    }
                } catch (Exception e) {
                    log.debug("Could not determine file size for StoredFile {}: {}", sf.getStoredFileIntId(), e.getMessage());
                }
            }

            // Determine uploader name
            String uploaderName = "Unknown";
            try {
                if (sf.getCreatedBy() != null) {
                    Users user = entityManager.find(Users.class, sf.getCreatedBy());
                    if (user != null && user.getUsername() != null) {
                        uploaderName = user.getUsername();
                    }
                }
            } catch (Exception e) {
                log.debug("Could not determine uploader name for StoredFile {}: {}", sf.getStoredFileIntId(), e.getMessage());
            }

            return DocumentDTO.builder()
                .documentId(sf.getStoredFileIntId().longValue())
                .fileName(sf.getName() != null ? sf.getName() : "Unknown File")
                .fileType(determineFileTypeFromStoredFile(sf))
                .fileSize(fileSize)
                .uploadDate(sf.getCreationDate() != null ?
                    sf.getCreationDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null)
                .description(sf.getNote() != null ? sf.getNote() : "")
                .category(determineCategoryFromStoredFile(sf))
                .uploaderName(uploaderName)
                .accessUrl("/api/v1/documents/" + sf.getStoredFileIntId() + "?sourceTable=STORED_FILE")
                .sourceTable("STORED_FILE")
                .build();

        } catch (Exception e) {
            log.error("Error mapping StoredFile to DTO: {}", e.getMessage(), e);
            // Return a basic DTO with minimal information
            return DocumentDTO.builder()
                .documentId(sf.getStoredFileIntId().longValue())
                .fileName("Error loading file")
                .fileType("application/octet-stream")
                .description("Error occurred while loading file information")
                .category("General")
                .uploaderName("Unknown")
                .accessUrl("/api/v1/documents/" + sf.getStoredFileIntId() + "?sourceTable=STORED_FILE")
                .sourceTable("STORED_FILE")
                .build();
        }
    }

    private DocumentDTO mapComplianceDocumentToDTO(ComplianceDocument cd) {
        if (cd == null) return null;

        try {
            String fileName = "Unknown File";
            Long fileSize = null;
            String fileType = "application/octet-stream";

            // Determine file information
            if (cd.getFilePath() != null && !cd.getFilePath().trim().isEmpty()) {
                try {
                    Path path = null;
                    if (cd.getFilePath().startsWith("filesystem://")) {
                        String relativePath = cd.getFilePath().substring("filesystem://".length());
                        path = this.fileStorageLocation.resolve(relativePath).normalize();
                    } else {
                        // Legacy path - be careful with security
                        if (isPathSecure(Paths.get(cd.getFilePath()))) {
                            path = Paths.get(cd.getFilePath());
                        }
                    }

                    if (path != null && Files.exists(path) && Files.isReadable(path)) {
                        fileName = path.getFileName().toString();
                        fileSize = Files.size(path);
                        fileType = determineMimeTypeFromPath(path);
                    }
                } catch (Exception e) {
                    log.debug("Error accessing file for ComplianceDocument {}: {}", cd.getComplianceDocumentIntId(), e.getMessage());
                }
            }

            // Use title if available, otherwise use filename
            if (cd.getTitleEn() != null && !cd.getTitleEn().trim().isEmpty()) {
                fileName = cd.getTitleEn();
            }

            // Determine uploader name
            String uploaderName = "Unknown";
            try {
                if (cd.getOwner() != null && cd.getOwner().getDisplayName() != null) {
                    uploaderName = cd.getOwner().getDisplayName();
                } else if (cd.getContact() != null && cd.getContact().getDisplayName() != null) {
                    uploaderName = cd.getContact().getDisplayName();
                }
            } catch (Exception e) {
                log.debug("Could not determine uploader name for ComplianceDocument {}: {}", cd.getComplianceDocumentIntId(), e.getMessage());
            }

            // Determine description
            String description = "";
            if (cd.getTitleEn() != null && !cd.getTitleEn().trim().isEmpty()) {
                description = cd.getTitleEn();
            }
            // Add type information to description
            String typeDescription = getComplianceDocumentTypeDescription(cd.getType());
            if (typeDescription != null) {
                description = description.isEmpty() ? typeDescription : description + " (" + typeDescription + ")";
            }

            return DocumentDTO.builder()
                .documentId(cd.getComplianceDocumentIntId().longValue())
                .fileName(fileName)
                .fileType(fileType)
                .fileSize(fileSize)
                .uploadDate(cd.getCreationDate() != null ?
                    cd.getCreationDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null)
                .description(description)
                .category("Compliance")
                .uploaderName(uploaderName)
                .accessUrl("/api/v1/documents/" + cd.getComplianceDocumentIntId() + "?sourceTable=COMPLIANCE_DOCUMENT")
                .sourceTable("COMPLIANCE_DOCUMENT")
                .build();

        } catch (Exception e) {
            log.error("Error mapping ComplianceDocument to DTO: {}", e.getMessage(), e);
            // Return a basic DTO with minimal information
            return DocumentDTO.builder()
                .documentId(cd.getComplianceDocumentIntId().longValue())
                .fileName("Error loading file")
                .fileType("application/octet-stream")
                .description("Error occurred while loading file information")
                .category("Compliance")
                .uploaderName("Unknown")
                .accessUrl("/api/v1/documents/" + cd.getComplianceDocumentIntId() + "?sourceTable=COMPLIANCE_DOCUMENT")
                .sourceTable("COMPLIANCE_DOCUMENT")
                .build();
        }
    }

    private String determineCategoryFromStoredFile(StoredFile sf) {
        if (sf == null || sf.getType() == null) {
            return "General";
        }

        // Based on TYPE_ field values from schema analysis
        switch (sf.getType()) {
            case 1: return "Opportunity";
            case 2: return "Report";
            case 3: return "Company";
            case 4: return "License";
            case 5: return "E&O Policy";
            case 6: return "Banking";
            case 7: return "Contracts";
            case 8: return "Activity";
            case 9: return "Product Supplier Documents";
            case 10: return "Dashboard";
            case 11: return "Advisor Documents";
            case 12: return "Contact";
            case 13: return "Lead";
            default: return "General";
        }
    }

    private String determineFileTypeFromStoredFile(StoredFile sf) {
        // Try to determine MIME type from file extension or URL
        if (sf.getUrl() != null && sf.getUrl().startsWith("filesystem://")) {
            try {
                String relativePath = sf.getUrl().substring("filesystem://".length());
                Path filePath = this.fileStorageLocation.resolve(relativePath).normalize();
                if (Files.exists(filePath)) {
                    String mimeType = Files.probeContentType(filePath);
                    if (mimeType != null) {
                        return mimeType;
                    }
                }
            } catch (Exception e) {
                log.debug("Could not determine MIME type from file path: {}", e.getMessage());
            }
        }

        // Fallback to extension-based detection
        if (sf.getExtension() != null) {
            return getMimeTypeFromExtension(sf.getExtension());
        }

        return "application/octet-stream"; // Default
    }

    private boolean isBlockedFileType(String contentType) {
        // Block potentially dangerous file types
        String[] blockedTypes = {
            "application/x-executable",
            "application/x-msdownload",
            "application/x-msdos-program",
            "application/x-msi",
            "application/x-bat",
            "application/x-sh",
            "text/x-script"
        };

        for (String blockedType : blockedTypes) {
            if (contentType.toLowerCase().contains(blockedType)) {
                return true;
            }
        }
        return false;
    }

    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    private boolean isPathSecure(Path path) {
        try {
            // Basic security check - ensure path doesn't contain directory traversal attempts
            String pathStr = path.toString();
            return !pathStr.contains("..") &&
                   !pathStr.contains("~") &&
                   path.isAbsolute(); // For legacy paths, expect absolute paths
        } catch (Exception e) {
            return false;
        }
    }

    private Resource downloadLegacyFile(StoredFile sf) {
        // Handle legacy file access (BLOB or other formats)
        log.debug("Attempting legacy file download for StoredFile ID: {}", sf.getStoredFileIntId());

        // If URL is a direct file path (legacy)
        if (sf.getUrl() != null && !sf.getUrl().startsWith("filesystem://")) {
            try {
                Path legacyPath = Paths.get(sf.getUrl()).normalize();
                if (isPathSecure(legacyPath) && Files.exists(legacyPath) && Files.isReadable(legacyPath)) {
                    return new UrlResource(legacyPath.toUri());
                }
            } catch (Exception e) {
                log.warn("Could not access legacy file path: {}", sf.getUrl(), e);
            }
        }

        // If no valid URL, try BLOB access (placeholder for now)
        throw new RuntimeException("Legacy BLOB download not implemented. StoredFile ID: " + sf.getStoredFileIntId());
    }

    private String determineMimeTypeFromPath(Path path) {
        if (path == null) return "application/octet-stream";

        try {
            String mimeType = Files.probeContentType(path);
            return mimeType != null ? mimeType : "application/octet-stream";
        } catch (IOException e) {
            log.debug("Could not determine MIME type for path: {}: {}", path, e.getMessage());
            return "application/octet-stream";
        }
    }

    private String getMimeTypeFromExtension(String extension) {
        if (extension == null || extension.isEmpty()) {
            return "application/octet-stream";
        }

        String ext = extension.toLowerCase();
        switch (ext) {
            case "pdf": return "application/pdf";
            case "doc": return "application/msword";
            case "docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls": return "application/vnd.ms-excel";
            case "xlsx": return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt": return "application/vnd.ms-powerpoint";
            case "pptx": return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "txt": return "text/plain";
            case "csv": return "text/csv";
            case "jpg":
            case "jpeg": return "image/jpeg";
            case "png": return "image/png";
            case "gif": return "image/gif";
            case "zip": return "application/zip";
            case "rar": return "application/x-rar-compressed";
            case "7z": return "application/x-7z-compressed";
            default: return "application/octet-stream";
        }
    }

    private String getComplianceDocumentTypeDescription(Integer type) {
        if (type == null) return null;

        switch (type) {
            case 1: return "Life Insurance";
            case 2: return "Critical Illness";
            case 3: return "Investment";
            case 4: return "Disability Insurance";
            default: return "Compliance Document";
        }
    }
}
