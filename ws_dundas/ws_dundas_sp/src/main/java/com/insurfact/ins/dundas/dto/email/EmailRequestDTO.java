package com.insurfact.ins.dundas.dto.email;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRequestDTO {
    private String recipientEmail;
    private String recipientName; // Optional
    private String emailType;
    private Map<String, Object> templateModel;
    private List<AttachmentDTO> attachments; // Optional

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentDTO {
        private String filename;
        private String contentBase64; // Base64 encoded content
        private String contentType; // Optional: MIME type, e.g., "application/pdf"
    }
}
