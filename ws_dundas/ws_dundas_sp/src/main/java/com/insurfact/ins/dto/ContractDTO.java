package com.insurfact.ins.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO for Contract information in Advisor Profile
 * 
 * <AUTHOR> zhu
 * @since 2025-01-01
 */
@Setter
@Getter
@Schema(description = "Contract information for an advisor")
public class ContractDTO {

    // Getters and Setters
    @Schema(description = "Contract internal ID", example = "12345")
    @JsonProperty("contractId")
    private Long contractId;

    @Schema(description = "Contract type", example = "1")
    @JsonProperty("contractType")
    private Integer contractType;

    @Schema(description = "Contract number", example = "CON-2024-001")
    @JsonProperty("contractNumber")
    private String contractNumber;

    @Schema(description = "Contract status", example = "1")
    @JsonProperty("contractStatus")
    private Integer contractStatus;

    @Schema(description = "Effective date", example = "2024-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("effectiveDate")
    private LocalDate effectiveDate;

    @Schema(description = "Inactive date", example = "2024-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("inactiveDate")
    private LocalDate inactiveDate;

    @Schema(description = "Sales representative code", example = "SR001")
    @JsonProperty("salesrepCode")
    private String salesrepCode;

    @Schema(description = "Commission pay to", example = "John Doe")
    @JsonProperty("commissionPayTo")
    private String commissionPayTo;

    @Schema(description = "MF code", example = "MF001")
    @JsonProperty("mfCode")
    private String mfCode;

    @Schema(description = "Dealer code", example = "D001")
    @JsonProperty("dealerCode")
    private String dealerCode;

    @Schema(description = "District", example = "WEST")
    @JsonProperty("district")
    private String district;

    @Schema(description = "Is primary contract", example = "true")
    @JsonProperty("isPrimary")
    private Boolean isPrimary;

    @Schema(description = "Direct deposit enabled", example = "true")
    @JsonProperty("directDeposit")
    private Boolean directDeposit;

    @Schema(description = "EFT form included", example = "true")
    @JsonProperty("eftFormIncluded")
    private Boolean eftFormIncluded;

    @Schema(description = "EFT sent date", example = "2024-01-15")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("eftSentDate")
    private LocalDate eftSentDate;

    @Schema(description = "Health code", example = "HC001")
    @JsonProperty("healthCode")
    private String healthCode;

    @Schema(description = "Transfer in price", example = "1000.00")
    @JsonProperty("transferInPrice")
    private BigDecimal transferInPrice;

    @Schema(description = "Transfer out price", example = "950.00")
    @JsonProperty("transferOutPrice")
    private BigDecimal transferOutPrice;

    @Schema(description = "Transfer in date", example = "2024-02-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("transferInDate")
    private LocalDate transferInDate;

    @Schema(description = "Transfer out date", example = "2024-02-15")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("transferOutDate")
    private LocalDate transferOutDate;

    @Schema(description = "Company association", example = "123")
    @JsonProperty("companyAssociation")
    private Long companyAssociation;

    @Schema(description = "Transfer paid date", example = "2024-02-20")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("transferPaidDate")
    private LocalDate transferPaidDate;

    @Schema(description = "Pay scale", example = "A")
    @JsonProperty("payScale")
    private String payScale;

    @Schema(description = "Associated EFT information")
    @JsonProperty("eftInfo")
    private EftDTO eftInfo;

    @Schema(description = "Contract setup information")
    @JsonProperty("contractSetup")
    private ContractSetupDTO contractSetup;

    // Default constructor
    public ContractDTO() {}

    /**
     * Convenience method to check if contract is active
     */
    public boolean isActive() {
        return contractStatus != null && contractStatus == 1 && 
               (inactiveDate == null || inactiveDate.isAfter(LocalDate.now()));
    }

    /**
     * Convenience method to get display name for contract
     */
    public String getDisplayName() {
        if (contractNumber != null && !contractNumber.trim().isEmpty()) {
            return contractNumber;
        }
        return "Contract #" + (contractId != null ? contractId : "Unknown");
    }
}
