package com.insurfact.ins.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;

/**
 * Service for handling permission checks across the application.
 * 
 * This service implements the authorization logic for advisor profile access,
 * supporting different user roles and their corresponding access permissions:
 * 
 * - ROLE_ADMIN/ROLE_MANAGER: Can view any advisor profile
 * - ROLE_AGA: Can view advisors within their management hierarchy
 * - ROLE_ADVISOR: Can only view their own profile
 * 
 * The service integrates with Spring Security's method-level security
 * through @PreAuthorize annotations.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Service
public class PermissionService {

    private static final Logger log = LoggerFactory.getLogger(PermissionService.class);

    // Role constants
    private static final String ROLE_ADMIN = "ROLE_ADMIN";
    private static final String ROLE_MANAGER = "ROLE_MANAGER";
    private static final String ROLE_AGA = "ROLE_AGA";
    private static final String ROLE_ADVISOR = "ROLE_ADVISOR";

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public PermissionService(@Qualifier("skytestJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Checks if the authenticated user can view the specified advisor profile.
     * 
     * This method implements the core authorization logic for advisor profile access:
     * 
     * 1. Admin/Manager users: Full access to all advisor profiles
     * 2. AGA users: Access to advisors within their management hierarchy
     * 3. Advisor users: Access only to their own profile
     * 4. Other roles: No access
     * 
     * @param authentication the Spring Security authentication object containing user details
     * @param requestedAdvisorId the ID of the advisor profile being requested
     * @return true if access is granted, false otherwise
     */
    public boolean canViewAdvisor(Authentication authentication, Long requestedAdvisorId) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("Access denied: User not authenticated");
            return false;
        }

        if (requestedAdvisorId == null || requestedAdvisorId <= 0) {
            log.warn("Access denied: Invalid advisor ID: {}", requestedAdvisorId);
            return false;
        }

        try {
            // Get user details from authentication
            String username = authentication.getName();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            
            log.debug("Checking advisor access for user: {} requesting advisor ID: {}", username, requestedAdvisorId);

            // Check for admin/manager roles - they have full access
            if (hasRole(authorities, ROLE_ADMIN) || hasRole(authorities, ROLE_MANAGER)) {
                log.debug("Access granted: User {} has admin/manager privileges", username);
                return true;
            }

            // Check for advisor role - can only view own profile
            if (hasRole(authorities, ROLE_ADVISOR)) {
                Long userAdvisorId = getUserAdvisorId(username);
                if (userAdvisorId != null && userAdvisorId.equals(requestedAdvisorId)) {
                    log.debug("Access granted: Advisor {} viewing own profile", username);
                    return true;
                } else {
                    log.warn("Access denied: Advisor {} attempted to view profile {}, but own ID is {}", 
                        username, requestedAdvisorId, userAdvisorId);
                    return false;
                }
            }

            // Check for AGA role - can view advisors in their hierarchy
            if (hasRole(authorities, ROLE_AGA)) {
                Long userAdvisorId = getUserAdvisorId(username);
                if (userAdvisorId != null && isAdvisorInAgaHierarchy(requestedAdvisorId, userAdvisorId)) {
                    log.debug("Access granted: AGA {} has access to advisor {} in their hierarchy", username, requestedAdvisorId);
                    return true;
                } else {
                    log.warn("Access denied: AGA {} does not have access to advisor {}", username, requestedAdvisorId);
                    return false;
                }
            }

            // No matching role found
            log.warn("Access denied: User {} does not have appropriate role for advisor access", username);
            return false;

        } catch (Exception e) {
            log.error("Error checking advisor access for user {} and advisor {}: {}", 
                authentication.getName(), requestedAdvisorId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Checks if the user has a specific role.
     */
    private boolean hasRole(Collection<? extends GrantedAuthority> authorities, String role) {
        return authorities.stream()
            .anyMatch(authority -> role.equals(authority.getAuthority()));
    }

    /**
     * Gets the advisor ID associated with a user account.
     * This method looks up the advisor ID for users who have advisor accounts.
     *
     * @param userIdOrUsername the user ID (as string) or username to look up
     * @return the advisor ID if found, null otherwise
     */
    private Long getUserAdvisorId(String userIdOrUsername) {
        try {
            // First try to parse as user ID (from our authentication system)
            try {
                Long userId = Long.parseLong(userIdOrUsername);
                // Note: Based on schema analysis, USERS table may not have ADVISOR_INT_ID column
                // We need to join with ADVISOR table through CONTACT relationship
                String sql = """
                    SELECT a.ADVISOR_INT_ID
                    FROM USERS u
                    INNER JOIN ADVISOR a ON u.USER_INT_ID = a.ADVISOR_INT_ID
                    WHERE u.USER_INT_ID = ?
                    """;
                return jdbcTemplate.queryForObject(sql, Long.class, userId);
            } catch (NumberFormatException e) {
                // If not a number, treat as username
                String sql = """
                    SELECT a.ADVISOR_INT_ID
                    FROM USERS u
                    INNER JOIN ADVISOR a ON u.USER_INT_ID = a.ADVISOR_INT_ID
                    WHERE u.USERNAME = ?
                    """;
                return jdbcTemplate.queryForObject(sql, Long.class, userIdOrUsername);
            }

        } catch (Exception e) {
            log.debug("No advisor ID found for user: {} (this is normal for non-advisor users)", userIdOrUsername);
            return null;
        }
    }

    /**
     * Checks if a requested advisor is within an AGA's management hierarchy.
     *
     * This method implements the business logic for AGA (Agency General Agent)
     * hierarchy checking. AGAs can manage multiple advisors and should only
     * have access to advisors within their organizational structure.
     *
     * Note: The exact hierarchy logic will depend on the business rules and
     * database schema. This is a placeholder implementation that should be
     * refined based on the actual AGA-advisor relationship structure.
     *
     * @param requestedAdvisorId the advisor being requested
     * @param agaAdvisorId the AGA's advisor ID
     * @return true if the advisor is in the AGA's hierarchy, false otherwise
     */
    private boolean isAdvisorInAgaHierarchy(Long requestedAdvisorId, Long agaAdvisorId) {
        try {
            // TODO: Implement actual AGA hierarchy checking logic
            // Based on schema analysis, ADVISOR table doesn't have PARENT_AGA_ID column
            // We need to use the actual relationship tables or fields that exist
            // For now, implement a basic check using MASTER_GROUP or other available fields

            String sql = """
                SELECT COUNT(*)
                FROM ADVISOR a1
                INNER JOIN ADVISOR a2 ON a1.MASTER_GROUP = a2.MASTER_GROUP
                WHERE a1.ADVISOR_INT_ID = ?
                AND a2.ADVISOR_INT_ID = ?
                AND a1.ADVISOR_INT_ID != a2.ADVISOR_INT_ID
                """;

            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, requestedAdvisorId, agaAdvisorId);
            return count > 0;

        } catch (Exception e) {
            log.error("Error checking AGA hierarchy for advisor {} and AGA {}: {}",
                requestedAdvisorId, agaAdvisorId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Checks if a user can access a specific advisor.
     * Alias for canViewAdvisor for consistency with document permissions.
     *
     * @param authentication the Spring Security authentication object
     * @param advisorId the advisor ID being accessed
     * @return true if access is allowed, false otherwise
     */
    public boolean canAccessAdvisor(Authentication authentication, Long advisorId) {
        return canViewAdvisor(authentication, advisorId);
    }

    /**
     * Checks if a user can modify advisor information.
     * This method will be used for future write operations (POST, PUT, DELETE).
     *
     * @param authentication the Spring Security authentication object
     * @param advisorId the advisor ID being modified
     * @return true if modification is allowed, false otherwise
     */
    public boolean canModifyAdvisor(Authentication authentication, Long advisorId) {
        return canViewAdvisor(authentication, advisorId);
    }

    /**
     * Checks if a user can access a specific document.
     *
     * Document access follows the same rules as advisor access:
     * - Admin/Manager: Can access any document
     * - AGA: Can access documents of advisors in their hierarchy
     * - Advisor: Can only access their own documents
     *
     * @param authentication the Spring Security authentication object
     * @param documentId the document ID being accessed
     * @param sourceTable the source table (STORED_FILE or COMPLIANCE_DOCUMENT)
     * @return true if access is allowed, false otherwise
     */
    public boolean canAccessDocument(Authentication authentication, Long documentId, String sourceTable) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("Document access denied: User not authenticated");
            return false;
        }

        if (documentId == null || documentId <= 0) {
            log.warn("Document access denied: Invalid document ID: {}", documentId);
            return false;
        }

        try {
            String username = authentication.getName();
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();

            log.debug("Checking document access for user: {} requesting document ID: {} from table: {}",
                username, documentId, sourceTable);

            // Admin/Manager have full access
            if (hasRole(authorities, ROLE_ADMIN) || hasRole(authorities, ROLE_MANAGER)) {
                log.debug("Document access granted: User {} has admin/manager privileges", username);
                return true;
            }

            // Find the advisor associated with this document
            Long documentAdvisorId = getDocumentAdvisorId(documentId, sourceTable);
            if (documentAdvisorId == null) {
                log.warn("Document access denied: Could not determine advisor for document {} from table {}",
                    documentId, sourceTable);
                return false;
            }

            // Use existing advisor access logic
            boolean hasAccess = canViewAdvisor(authentication, documentAdvisorId);

            if (hasAccess) {
                log.debug("Document access granted: User {} has access to advisor {} for document {}",
                    username, documentAdvisorId, documentId);
            } else {
                log.warn("Document access denied: User {} does not have access to advisor {} for document {}",
                    username, documentAdvisorId, documentId);
            }

            return hasAccess;

        } catch (Exception e) {
            log.error("Error checking document access for user {} and document {}: {}",
                authentication.getName(), documentId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Checks if a user can delete a specific document.
     * Currently uses the same logic as document access.
     *
     * @param authentication the Spring Security authentication object
     * @param documentId the document ID being deleted
     * @param sourceTable the source table (STORED_FILE or COMPLIANCE_DOCUMENT)
     * @return true if deletion is allowed, false otherwise
     */
    public boolean canDeleteDocument(Authentication authentication, Long documentId, String sourceTable) {
        return canAccessDocument(authentication, documentId, sourceTable);
    }

    /**
     * Gets the advisor ID associated with a document.
     *
     * @param documentId the document ID
     * @param sourceTable the source table (STORED_FILE or COMPLIANCE_DOCUMENT)
     * @return the advisor ID if found, null otherwise
     */
    private Long getDocumentAdvisorId(Long documentId, String sourceTable) {
        try {
            if ("COMPLIANCE_DOCUMENT".equalsIgnoreCase(sourceTable)) {
                // For COMPLIANCE_DOCUMENT, check both OWNER and CONTACT fields
                String sql = """
                    SELECT COALESCE(
                        (SELECT a.ADVISOR_INT_ID
                         FROM USERS u
                         INNER JOIN ADVISOR a ON u.USER_INT_ID = a.ADVISOR_INT_ID
                         WHERE u.USER_INT_ID = cd.OWNER),
                        (SELECT a.ADVISOR_INT_ID
                         FROM ADVISOR a
                         WHERE a.CONTACT = cd.CONTACT)
                    ) as advisor_id
                    FROM COMPLIANCE_DOCUMENT cd
                    WHERE cd.COMPLIANCE_DOC_INT_ID = ?
                    """;
                return jdbcTemplate.queryForObject(sql, Long.class, documentId);

            } else { // Default to STORED_FILE
                // For STORED_FILE with TYPE_ = 11, TYPE_ID is the advisor ID
                String sql = """
                    SELECT sf.TYPE_ID
                    FROM STORED_FILE sf
                    WHERE sf.STORED_FILE_INT_ID = ?
                    AND sf.TYPE_ = 11
                    """;
                Integer advisorId = jdbcTemplate.queryForObject(sql, Integer.class, documentId);
                return advisorId != null ? advisorId.longValue() : null;
            }

        } catch (Exception e) {
            log.debug("Could not determine advisor ID for document {} from table {}: {}",
                documentId, sourceTable, e.getMessage());
            return null;
        }
    }
}
