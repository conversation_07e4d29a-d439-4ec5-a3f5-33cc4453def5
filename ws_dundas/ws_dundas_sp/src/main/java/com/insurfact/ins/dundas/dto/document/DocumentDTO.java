package com.insurfact.ins.dundas.dto.document;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentDTO {
    private Long documentId; // Corresponds to STORED_FILE.STORED_FILE_INT_ID or COMPLIANCE_DOCUMENT.COMPLIANCE_DOCUMENT_INT_ID
    private String fileName; // STORED_FILE.FILE_NAME or generated from COMPLIANCE_DOCUMENT.FILE_PATH
    private String fileType; // STORED_FILE.FILE_TYPE (MIME type) or derived from COMPLIANCE_DOCUMENT.FILE_PATH
    private Long fileSize; // STORED_FILE.FILE_SIZE_KB * 1024 or actual file size for compliance docs
    private LocalDateTime uploadDate; // STORED_FILE.CREATION_DATE or COMPLIANCE_DOCUMENT.CREATION_DATE
    private String description; // STORED_FILE.DESCRIPTION or COMPLIANCE_DOCUMENT.DESCRIPTION
    private String category; // e.g., "General", "Compliance", "ActivityAttachment"
    private String uploaderName; // User who uploaded, might need to fetch from USERS table based on STORED_FILE.USER_INT_ID
    private String accessUrl; // URL to download the file, constructed by the backend
    private String sourceTable; // "STORED_FILE" or "COMPLIANCE_DOCUMENT" to indicate origin
}
