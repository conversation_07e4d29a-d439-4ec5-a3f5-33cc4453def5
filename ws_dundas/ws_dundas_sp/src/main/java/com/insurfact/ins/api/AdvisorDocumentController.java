package com.insurfact.ins.api;

import com.insurfact.ins.dundas.dto.document.DocumentDTO;
import com.insurfact.ins.dundas.service.document.DocumentService;
import com.insurfact.ins.security.PermissionService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * REST Controller for Advisor Document Management
 * 
 * Provides endpoints for:
 * - Listing advisor documents
 * - Uploading new documents
 * - Downloading documents
 * - Deleting documents
 * 
 * Security: All endpoints require authentication and appropriate permissions
 */
@RestController
@RequestMapping("/api/v1/advisors")
public class AdvisorDocumentController {

    private static final Logger log = LoggerFactory.getLogger(AdvisorDocumentController.class);

    @Autowired
    private DocumentService documentService;

    @Autowired
    private PermissionService permissionService;

    /**
     * Get all documents for a specific advisor
     * 
     * @param advisorId The advisor ID
     * @param authentication Current user authentication
     * @return List of documents
     */
    @GetMapping("/{advisorId}/documents")
    @PreAuthorize("@permissionService.canAccessAdvisor(authentication, #advisorId)")
    public ResponseEntity<List<DocumentDTO>> getAdvisorDocuments(
            @PathVariable Long advisorId,
            Authentication authentication) {
        
        log.debug("Getting documents for advisor ID: {} by user: {}", advisorId, authentication.getName());

        try {
            List<DocumentDTO> documents = documentService.getDocumentsForAdvisor(advisorId);
            
            log.info("Successfully retrieved {} documents for advisor {}", documents.size(), advisorId);
            return ResponseEntity.ok(documents);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for advisor documents: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Error retrieving documents for advisor {}: {}", advisorId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Upload a new document for an advisor
     * 
     * @param advisorId The advisor ID
     * @param file The file to upload
     * @param description Document description
     * @param category Document category (optional, defaults to "General")
     * @param authentication Current user authentication
     * @return The created document information
     */
    @PostMapping("/{advisorId}/documents")
    @PreAuthorize("@permissionService.canModifyAdvisor(authentication, #advisorId)")
    public ResponseEntity<DocumentDTO> uploadDocument(
            @PathVariable Long advisorId,
            @RequestParam("file") MultipartFile file,
            @RequestParam("description") String description,
            @RequestParam(value = "category", defaultValue = "General") String category,
            Authentication authentication) {

        log.debug("Uploading document for advisor ID: {} by user: {}, filename: {}", 
            advisorId, authentication.getName(), file.getOriginalFilename());

        try {
            // Validate inputs
            if (file.isEmpty()) {
                log.warn("Empty file upload attempted for advisor {}", advisorId);
                return ResponseEntity.badRequest().build();
            }

            if (description == null || description.trim().isEmpty()) {
                log.warn("Upload attempted without description for advisor {}", advisorId);
                return ResponseEntity.badRequest().build();
            }

            DocumentDTO uploadedDocument = documentService.uploadDocument(advisorId, description, category, file);
            
            log.info("Successfully uploaded document {} for advisor {}", 
                uploadedDocument.getFileName(), advisorId);
            return ResponseEntity.status(HttpStatus.CREATED).body(uploadedDocument);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid upload request for advisor {}: {}", advisorId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Error uploading document for advisor {}: {}", advisorId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Download a specific document
     * 
     * @param documentId The document ID
     * @param sourceTable The source table (STORED_FILE or COMPLIANCE_DOCUMENT)
     * @param authentication Current user authentication
     * @param request HTTP request for content type detection
     * @return The file content as a downloadable resource
     */
    @GetMapping("/documents/{documentId}")
    @PreAuthorize("@permissionService.canAccessDocument(authentication, #documentId, #sourceTable)")
    public ResponseEntity<Resource> downloadDocument(
            @PathVariable Long documentId,
            @RequestParam(value = "sourceTable", defaultValue = "STORED_FILE") String sourceTable,
            Authentication authentication,
            HttpServletRequest request) {

        log.debug("Downloading document ID: {} from table: {} by user: {}", 
            documentId, sourceTable, authentication.getName());

        try {
            Resource resource = documentService.downloadDocument(documentId, sourceTable);

            // Determine content type
            String contentType = null;
            try {
                contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
            } catch (IOException ex) {
                log.debug("Could not determine file type for document {}", documentId);
            }

            // Fallback to default content type
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // Determine filename for download
            String filename = resource.getFilename();
            if (filename == null) {
                filename = "document_" + documentId;
            }

            log.info("Successfully prepared download for document ID: {}, filename: {}", documentId, filename);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .body(resource);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid download request for document {}: {}", documentId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                log.warn("Document not found: {}", documentId);
                return ResponseEntity.notFound().build();
            }
            log.error("Error downloading document {}: {}", documentId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("Unexpected error downloading document {}: {}", documentId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Delete a specific document
     * 
     * @param documentId The document ID
     * @param sourceTable The source table (STORED_FILE or COMPLIANCE_DOCUMENT)
     * @param authentication Current user authentication
     * @return Success or error response
     */
    @DeleteMapping("/documents/{documentId}")
    @PreAuthorize("@permissionService.canDeleteDocument(authentication, #documentId, #sourceTable)")
    public ResponseEntity<Void> deleteDocument(
            @PathVariable Long documentId,
            @RequestParam(value = "sourceTable", defaultValue = "STORED_FILE") String sourceTable,
            Authentication authentication) {

        log.debug("Deleting document ID: {} from table: {} by user: {}", 
            documentId, sourceTable, authentication.getName());

        try {
            documentService.deleteDocument(documentId, sourceTable);
            
            log.info("Successfully deleted document ID: {} from table: {}", documentId, sourceTable);
            return ResponseEntity.noContent().build();

        } catch (IllegalArgumentException e) {
            log.warn("Invalid delete request for document {}: {}", documentId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                log.warn("Document not found for deletion: {}", documentId);
                return ResponseEntity.notFound().build();
            }
            log.error("Error deleting document {}: {}", documentId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("Unexpected error deleting document {}: {}", documentId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
