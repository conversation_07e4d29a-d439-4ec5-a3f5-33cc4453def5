package com.insurfact.ins.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;

/**
 * DTO for EFT (Electronic Funds Transfer) information in Advisor Profile
 * 
 * <AUTHOR> zhu
 * @since 2025-01-01
 */
@Schema(description = "EFT (Electronic Funds Transfer) information for an advisor")
public class EftDTO {

    @Schema(description = "EFT internal ID", example = "12345")
    @JsonProperty("eftId")
    private Long eftId;

    @Schema(description = "Date when EFT was sent", example = "2024-01-15")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("sentDate")
    private LocalDate sentDate;

    @Schema(description = "Bank account holder name", example = "John Doe")
    @JsonProperty("bankHolder")
    private String bankHolder;

    @Schema(description = "Bank address", example = "123 Bank Street, Toronto, ON")
    @JsonProperty("bankAddress")
    private String bankAddress;

    @Schema(description = "Bank transit number", example = "12345")
    @JsonProperty("transit")
    private String transit;

    @Schema(description = "Bank account number", example = "**********")
    @JsonProperty("accountNumber")
    private String accountNumber;

    @Schema(description = "COPO EFT flag", example = "Y")
    @JsonProperty("copoEft")
    private String copoEft;

    @Schema(description = "Statements by email flag", example = "Y")
    @JsonProperty("statementsByEmail")
    private String statementsByEmail;

    @Schema(description = "Bank code", example = "001")
    @JsonProperty("bank")
    private String bank;

    @Schema(description = "EFT info ID", example = "98765")
    @JsonProperty("eftInfoId")
    private Long eftInfoId;

    @Schema(description = "Associated advisor ID", example = "44444")
    @JsonProperty("advisorId")
    private Long advisorId;

    @Schema(description = "Associated company ID", example = "55555")
    @JsonProperty("companyId")
    private Long companyId;

    @Schema(description = "Associated agency ID", example = "66666")
    @JsonProperty("agencyId")
    private Long agencyId;

    @Schema(description = "Deleted flag", example = "N")
    @JsonProperty("deleted")
    private String deleted;

    @Schema(description = "Date when EFT was deleted", example = "2024-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("deletedDate")
    private LocalDate deletedDate;

    @Schema(description = "Associated file name", example = "eft_form_12345.pdf")
    @JsonProperty("fileName")
    private String fileName;

    // Default constructor
    public EftDTO() {}

    // Getters and Setters
    public Long getEftId() { return eftId; }
    public void setEftId(Long eftId) { this.eftId = eftId; }

    public LocalDate getSentDate() { return sentDate; }
    public void setSentDate(LocalDate sentDate) { this.sentDate = sentDate; }

    public String getBankHolder() { return bankHolder; }
    public void setBankHolder(String bankHolder) { this.bankHolder = bankHolder; }

    public String getBankAddress() { return bankAddress; }
    public void setBankAddress(String bankAddress) { this.bankAddress = bankAddress; }

    public String getTransit() { return transit; }
    public void setTransit(String transit) { this.transit = transit; }

    public String getAccountNumber() { return accountNumber; }
    public void setAccountNumber(String accountNumber) { this.accountNumber = accountNumber; }

    public String getCopoEft() { return copoEft; }
    public void setCopoEft(String copoEft) { this.copoEft = copoEft; }

    public String getStatementsByEmail() { return statementsByEmail; }
    public void setStatementsByEmail(String statementsByEmail) { this.statementsByEmail = statementsByEmail; }

    public String getBank() { return bank; }
    public void setBank(String bank) { this.bank = bank; }

    public Long getEftInfoId() { return eftInfoId; }
    public void setEftInfoId(Long eftInfoId) { this.eftInfoId = eftInfoId; }

    public Long getAdvisorId() { return advisorId; }
    public void setAdvisorId(Long advisorId) { this.advisorId = advisorId; }

    public Long getCompanyId() { return companyId; }
    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public Long getAgencyId() { return agencyId; }
    public void setAgencyId(Long agencyId) { this.agencyId = agencyId; }

    public String getDeleted() { return deleted; }
    public void setDeleted(String deleted) { this.deleted = deleted; }

    public LocalDate getDeletedDate() { return deletedDate; }
    public void setDeletedDate(LocalDate deletedDate) { this.deletedDate = deletedDate; }

    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }

    /**
     * Convenience method to check if EFT is active (not deleted)
     */
    public boolean isActive() {
        return deleted == null || !"Y".equalsIgnoreCase(deleted);
    }

    /**
     * Convenience method to check if COPO EFT is enabled
     */
    public boolean isCopoEftEnabled() {
        return "Y".equalsIgnoreCase(copoEft);
    }

    /**
     * Convenience method to check if statements by email is enabled
     */
    public boolean isStatementsByEmailEnabled() {
        return "Y".equalsIgnoreCase(statementsByEmail);
    }

    /**
     * Convenience method to get masked account number for display
     */
    public String getMaskedAccountNumber() {
        if (accountNumber == null || accountNumber.length() <= 4) {
            return accountNumber;
        }
        
        String lastFour = accountNumber.substring(accountNumber.length() - 4);
        String masked = "*".repeat(accountNumber.length() - 4);
        return masked + lastFour;
    }

    /**
     * Convenience method to get display name for EFT
     */
    public String getDisplayName() {
        if (bankHolder != null && !bankHolder.trim().isEmpty()) {
            return bankHolder + " (" + getMaskedAccountNumber() + ")";
        }
        return "EFT #" + (eftId != null ? eftId : "Unknown");
    }
}
