package com.insurfact.ins.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Data Transfer Object for Company information associated with an advisor.
 * This DTO represents company details including business information,
 * contact details, and operational metadata.
 * 
 * Used by the GET /api/v1/advisors/{advisorId} endpoint to return comprehensive
 * company information for the Business Details tab in the UI.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Company information associated with an advisor")
public class CompanyDTO {

    @Schema(description = "Unique company identifier", example = "12345")
    private Long companyId;

    @Schema(description = "Company internal ID", example = "67890")
    private Long companyIntId;

    @Schema(description = "Company name in English", example = "ABC Financial Services Inc.")
    private String nameEn;

    @Schema(description = "Company name in French", example = "Services Financiers ABC Inc.")
    private String nameFr;

    @Schema(description = "Primary company name", example = "ABC Financial")
    private String primaryName;

    @Schema(description = "Other company name", example = "ABC Corp")
    private String otherName;

    @Schema(description = "Provincial business number", example = "123456789BC0001")
    private String provincialBusinessNumber;

    @Schema(description = "Company type code", example = "1")
    private Integer companyType;

    @Schema(description = "Business start date")
    private LocalDate businessStartDate;

    @Schema(description = "Whether company is active", example = "Y")
    private String active;

    @Schema(description = "Whether commissions are assignable", example = "Y")
    private String assignableCommissions;

    @Schema(description = "Building type code", example = "1")
    private Integer buildingType;

    @Schema(description = "Company creation date")
    private LocalDateTime creationDate;

    @Schema(description = "Last modification date")
    private LocalDateTime lastModificationDate;

    @Schema(description = "Whether this is the primary company for the advisor")
    private Boolean isPrimary;

    @Schema(description = "Company contact address information")
    private AddressDTO businessAddress;

    // Manual builder in case Lombok doesn't work
    public static CompanyDTOBuilder builder() {
        return new CompanyDTOBuilder();
    }

    public static class CompanyDTOBuilder {
        private Long companyId;
        private Long companyIntId;
        private String nameEn;
        private String nameFr;
        private String primaryName;
        private String otherName;
        private String provincialBusinessNumber;
        private Integer companyType;
        private LocalDate businessStartDate;
        private String active;
        private String assignableCommissions;
        private Integer buildingType;
        private LocalDateTime creationDate;
        private LocalDateTime lastModificationDate;
        private Boolean isPrimary;
        private AddressDTO businessAddress;

        public CompanyDTOBuilder companyId(Long companyId) {
            this.companyId = companyId;
            return this;
        }

        public CompanyDTOBuilder companyIntId(Long companyIntId) {
            this.companyIntId = companyIntId;
            return this;
        }

        public CompanyDTOBuilder nameEn(String nameEn) {
            this.nameEn = nameEn;
            return this;
        }

        public CompanyDTOBuilder nameFr(String nameFr) {
            this.nameFr = nameFr;
            return this;
        }

        public CompanyDTOBuilder primaryName(String primaryName) {
            this.primaryName = primaryName;
            return this;
        }

        public CompanyDTOBuilder otherName(String otherName) {
            this.otherName = otherName;
            return this;
        }

        public CompanyDTOBuilder provincialBusinessNumber(String provincialBusinessNumber) {
            this.provincialBusinessNumber = provincialBusinessNumber;
            return this;
        }

        public CompanyDTOBuilder companyType(Integer companyType) {
            this.companyType = companyType;
            return this;
        }

        public CompanyDTOBuilder businessStartDate(LocalDate businessStartDate) {
            this.businessStartDate = businessStartDate;
            return this;
        }

        public CompanyDTOBuilder active(String active) {
            this.active = active;
            return this;
        }

        public CompanyDTOBuilder assignableCommissions(String assignableCommissions) {
            this.assignableCommissions = assignableCommissions;
            return this;
        }

        public CompanyDTOBuilder buildingType(Integer buildingType) {
            this.buildingType = buildingType;
            return this;
        }

        public CompanyDTOBuilder creationDate(LocalDateTime creationDate) {
            this.creationDate = creationDate;
            return this;
        }

        public CompanyDTOBuilder lastModificationDate(LocalDateTime lastModificationDate) {
            this.lastModificationDate = lastModificationDate;
            return this;
        }

        public CompanyDTOBuilder isPrimary(Boolean isPrimary) {
            this.isPrimary = isPrimary;
            return this;
        }

        public CompanyDTOBuilder businessAddress(AddressDTO businessAddress) {
            this.businessAddress = businessAddress;
            return this;
        }

        public CompanyDTO build() {
            CompanyDTO dto = new CompanyDTO();
            dto.companyId = this.companyId;
            dto.companyIntId = this.companyIntId;
            dto.nameEn = this.nameEn;
            dto.nameFr = this.nameFr;
            dto.primaryName = this.primaryName;
            dto.otherName = this.otherName;
            dto.provincialBusinessNumber = this.provincialBusinessNumber;
            dto.companyType = this.companyType;
            dto.businessStartDate = this.businessStartDate;
            dto.active = this.active;
            dto.assignableCommissions = this.assignableCommissions;
            dto.buildingType = this.buildingType;
            dto.creationDate = this.creationDate;
            dto.lastModificationDate = this.lastModificationDate;
            dto.isPrimary = this.isPrimary;
            dto.businessAddress = this.businessAddress;
            return dto;
        }
    }
}
