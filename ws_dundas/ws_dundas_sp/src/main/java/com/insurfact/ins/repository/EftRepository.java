package com.insurfact.ins.repository;

import com.insurfact.skynet.entity.ContractEft;
import com.insurfact.skynet.entity.Company;
import com.insurfact.skynet.entity.Agency;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Repository for ContractEft data access using JDBC Template.
 * Handles queries to retrieve EFT information for advisors.
 * 
 * <AUTHOR> zhu
 * @since 2025-01-01
 */
@Repository
public class EftRepository {

    private static final Logger logger = LoggerFactory.getLogger(EftRepository.class);

    private final JdbcTemplate jdbcTemplate;

    public EftRepository(@Qualifier("skytestJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Find all EFT records for a given advisor ID.
     * This includes CONTRACT_EFT and related entities.
     * 
     * @param advisorId The advisor ID to search for
     * @return List of ContractEft entities with populated relationships
     */
    public List<ContractEft> findEftsByAdvisorId(Long advisorId) {
        logger.debug("Finding EFT records for advisor ID: {}", advisorId);

        String sql = """
            SELECT 
                ce.CONTRACT_EFT_INT_ID,
                ce.SENT_DATE,
                ce.BANK_HOLDER,
                ce.BANK_ADDRESS,
                ce.TRANSIT,
                ce.ACCOUNT_NUMBER,
                ce.COPO_EFT,
                ce.STMTS_BY_EMAIL,
                ce.BANK,
                ce.EFT_INFO_ID,
                ce.ADVISOR,
                ce.DELETED,
                ce.DELETED_DATE,
                ce.FILE_NAME,
                ce.CREATION_DATE,
                ce.LAST_MODIFICATION_DATE,
                -- Company fields
                c.COMPANY_INT_ID,
                c.PRIMARY_NAME as COMPANY_NAME,
                c.NAME_EN as COMPANY_NAME_EN,
                c.NAME_FR as COMPANY_NAME_FR,
                -- Agency fields
                a.AGENCY_INT_ID

            FROM CONTRACT_EFT ce
            LEFT JOIN COMPANY c ON ce.COMPANY = c.COMPANY_INT_ID
            LEFT JOIN AGENCY a ON ce.AGENCY = a.AGENCY_INT_ID
            WHERE ce.ADVISOR = ?
              AND (ce.DELETED != 'Y' OR ce.DELETED IS NULL)
            ORDER BY ce.CREATION_DATE DESC, ce.CONTRACT_EFT_INT_ID
            """;

        return jdbcTemplate.query(sql, new Object[]{advisorId}, new EftResultSetExtractor());
    }

    /**
     * ResultSetExtractor to build ContractEft entities from the query result.
     */
    private static class EftResultSetExtractor implements ResultSetExtractor<List<ContractEft>> {

        @Override
        public List<ContractEft> extractData(ResultSet rs) throws SQLException {
            Map<Long, ContractEft> eftMap = new HashMap<>();

            while (rs.next()) {
                Long eftId = rs.getLong("CONTRACT_EFT_INT_ID");
                
                ContractEft eft = eftMap.get(eftId);
                if (eft == null) {
                    eft = buildEftFromResultSet(rs);
                    eftMap.put(eftId, eft);
                }
            }

            return new ArrayList<>(eftMap.values());
        }

        private ContractEft buildEftFromResultSet(ResultSet rs) throws SQLException {
            ContractEft eft = new ContractEft();
            
            try {
                // Set ContractEft fields
                eft.setContractEftIntId(rs.getInt("CONTRACT_EFT_INT_ID"));
                eft.setSentDate(rs.getDate("SENT_DATE"));
                eft.setBankHolder(rs.getString("BANK_HOLDER"));
                eft.setBankAddress(rs.getString("BANK_ADDRESS"));
                eft.setTransit(rs.getString("TRANSIT"));
                eft.setAccountNumber(rs.getString("ACCOUNT_NUMBER"));
                eft.setCopoEft(rs.getString("COPO_EFT"));
                eft.setStmtsByEmail(rs.getString("STMTS_BY_EMAIL"));
                eft.setBank(rs.getString("BANK"));
                eft.setEftInfoId(rs.getInt("EFT_INFO_ID"));
                eft.setDeleted(rs.getString("DELETED"));
                eft.setDeletedDate(rs.getDate("DELETED_DATE"));
                // Note: ContractEft entity may not have setFileName method
                // eft.setFileName(rs.getString("FILE_NAME"));
                eft.setCreationDate(rs.getDate("CREATION_DATE"));
                eft.setLastModificationDate(rs.getDate("LAST_MODIFICATION_DATE"));

                // Set Company if present
                if (rs.getLong("COMPANY_INT_ID") != 0) {
                    Company company = new Company();
                    company.setCompanyIntId(rs.getInt("COMPANY_INT_ID"));
                    company.setPrimaryName(rs.getString("COMPANY_NAME"));
                    company.setNameEn(rs.getString("COMPANY_NAME_EN"));
                    company.setNameFr(rs.getString("COMPANY_NAME_FR"));
                    eft.setCompany(company);
                }

                // Set Agency if present
                if (rs.getLong("AGENCY_INT_ID") != 0) {
                    Agency agency = new Agency();
                    agency.setAgencyIntId(rs.getInt("AGENCY_INT_ID"));
                    // Note: Agency entity may not have setName/setCode methods
                    eft.setAgency(agency);
                }

            } catch (Exception e) {
                // Defensive programming: handle cases where entity methods might not exist
                logger.warn("Error setting EFT field, method might not exist: {}", e.getMessage());
            }

            return eft;
        }
    }

    /**
     * Find EFT records that are referenced by contracts for a given advisor.
     * This method finds EFTs that are linked through CONTRACT.CONTRACT_EFT.
     * 
     * @param advisorId The advisor ID to search for
     * @return List of ContractEft entities referenced by contracts
     */
    public List<ContractEft> findEftsReferencedByContracts(Long advisorId) {
        logger.debug("Finding EFT records referenced by contracts for advisor ID: {}", advisorId);

        String sql = """
            SELECT DISTINCT
                ce.CONTRACT_EFT_INT_ID,
                ce.SENT_DATE,
                ce.BANK_HOLDER,
                ce.BANK_ADDRESS,
                ce.TRANSIT,
                ce.ACCOUNT_NUMBER,
                ce.COPO_EFT,
                ce.STMTS_BY_EMAIL,
                ce.BANK,
                ce.EFT_INFO_ID,
                ce.ADVISOR,
                ce.DELETED,
                ce.DELETED_DATE,
                ce.FILE_NAME,
                ce.CREATION_DATE,
                ce.LAST_MODIFICATION_DATE,
                -- Company fields
                c.COMPANY_INT_ID,
                c.PRIMARY_NAME as COMPANY_NAME,
                c.NAME_EN as COMPANY_NAME_EN,
                c.NAME_FR as COMPANY_NAME_FR,
                -- Agency fields
                a.AGENCY_INT_ID,
                a_org.ORGANIZATION_DESC_EN as AGENCY_NAME,
                a_org.ORGANIZATION_CODE as AGENCY_CODE
            FROM CONTRACT_EFT ce
            INNER JOIN CONTRACT con ON con.CONTRACT_EFT = ce.CONTRACT_EFT_INT_ID
            INNER JOIN CONTRACT_SETUP cs ON con.CONTRACT_INT_ID = cs.CONTRACT_SETUP_INT_ID
            LEFT JOIN COMPANY c ON ce.COMPANY = c.COMPANY_INT_ID
            LEFT JOIN AGENCY a ON ce.AGENCY = a.AGENCY_INT_ID
            LEFT JOIN ORGANIZATION a_org ON a.ORGANIZATION = a_org.ORGANIZATION_INT_ID
            WHERE cs.ADVISOR = ?
              AND (ce.DELETED != 'Y' OR ce.DELETED IS NULL)
              AND (cs.DELETED != 'Y' OR cs.DELETED IS NULL)
            ORDER BY ce.CREATION_DATE DESC, ce.CONTRACT_EFT_INT_ID
            """;

        return jdbcTemplate.query(sql, new Object[]{advisorId}, new EftResultSetExtractor());
    }

    /**
     * Check if an EFT record exists for the given advisor.
     * 
     * @param advisorId The advisor ID to check
     * @return true if at least one EFT record exists, false otherwise
     */
    public boolean hasEftRecords(Long advisorId) {
        logger.debug("Checking if EFT records exist for advisor ID: {}", advisorId);

        String sql = """
            SELECT COUNT(*)
            FROM CONTRACT_EFT ce
            WHERE ce.ADVISOR = ?
              AND (ce.DELETED != 'Y' OR ce.DELETED IS NULL)
            """;

        Integer count = jdbcTemplate.queryForObject(sql, new Object[]{advisorId}, Integer.class);
        return count != null && count > 0;
    }
}
