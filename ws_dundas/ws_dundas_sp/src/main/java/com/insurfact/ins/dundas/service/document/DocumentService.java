package com.insurfact.ins.dundas.service.document;

import com.insurfact.ins.dundas.dto.document.DocumentDTO;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface DocumentService {
    List<DocumentDTO> getDocumentsForAdvisor(Long advisorId);
    DocumentDTO uploadDocument(Long advisorId, String description, String category, MultipartFile file);
    Resource downloadDocument(Long documentId, String sourceTable);
    void deleteDocument(Long documentId, String sourceTable);
    // Potentially add a method to get a specific document's metadata by ID if needed
    // DocumentDTO getDocumentMetadata(Long documentId, String sourceTable);
}
