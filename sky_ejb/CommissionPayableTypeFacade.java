/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.CommissionPayableType;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CommissionPayableTypeFacade extends AbstractFacade<CommissionPayableType> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public CommissionPayableTypeFacade() {
		super(CommissionPayableType.class);
	}

	public List<CommissionPayableType> findAllCommissionTypes() {

		List<CommissionPayableType> commTypes = null;

		try {
			TypedQuery<CommissionPayableType> nq = getEntityManager().createNamedQuery("CommissionPayableType.findAll",
					CommissionPayableType.class);

			commTypes = nq.getResultList();

			return commTypes;

		} catch (NoResultException e) {
			e.printStackTrace();
			return null;
		}
	}

}
