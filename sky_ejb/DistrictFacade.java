/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.District;
import com.insurfact.skynet.entity.MarketingRegion;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class DistrictFacade extends AbstractFacade<District> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public DistrictFacade() {
		super(District.class);
	}

	public void saveDisctrict(District district) {
		Query q = em.createNativeQuery("update DISTRICT set NAME_EN = '" + district.getNameEn() + "', NAME_FR = '"
				+ district.getNameFr() + "', MARKETING_REGION = " + district.getMarketingRegion() + ", NUMBER = "
				+ district.getNumber() + " where DISTRICT_INT_ID = " + district.getDistrictIntId());
		q.executeUpdate();
	}

	public List<District> allDistrictByRegion(MarketingRegion mr) {
		Query q = em.createNativeQuery(
				"SELECT * from DISTRICT where MARKETING_REGION = " + mr.getMarketingRegionIntId(), District.class);
		@SuppressWarnings("unchecked")
		List<District> result = q.getResultList();
		return result;
	}
}
