/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.im.IMCompany;
import com.insurfact.skynet.entity.Organization;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class OrganizationFacade extends AbstractFacade<Organization> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public OrganizationFacade() {
		super(Organization.class);
	}

	public Organization getByID(Integer orgID) {

		Query nq = (Query) em.createNamedQuery("Organization.findByOrganizationIntId", Organization.class);
		nq.setParameter("organizationIntId", orgID);

		Organization org = null;

		try {
			org = (Organization) nq.getSingleResult();
		} catch (Exception e) {
		}

		return org;
	}

	public List<Organization> findByName(String name) {

		name = "%" + name.toUpperCase() + "%";

		TypedQuery<Organization> nq = em.createQuery(
				"SELECT a FROM Organization a WHERE   UPPER(a.organizationDescEn) like :name or UPPER(a.organizationDescFr) like :name",
				Organization.class);
		nq.setParameter("name", name);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * CriteriaQuery<Organization> criteriaQuery =
		 * cBuilder.createQuery(Organization.class); Root<Organization> p =
		 * criteriaQuery.from(Organization.class);
		 * 
		 * Predicate englishName =
		 * cBuilder.like(cBuilder.upper(p.get(Organization_.organizationDescEn)), name);
		 * 
		 * Predicate frenchName =
		 * cBuilder.like(cBuilder.upper(p.get(Organization_.organizationDescFr)), name);
		 * 
		 * criteriaQuery.where(cBuilder.or(englishName, frenchName));
		 * 
		 * TypedQuery<Organization> query = em.createQuery(criteriaQuery); return
		 * query.getResultList();
		 */
	}

	@Resource(lookup = "jdbc/Skytest")
	private DataSource ds;

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (SQLException e) {

			e.printStackTrace();
		}
		return null;
	}

	public List<IMCompany> findAllIMCompanies() {

//        String type = "B";
		List<IMCompany> companies = new ArrayList<>();

//        StringBuilder buffer = new StringBuilder();
//        buffer.append("select * from IM_COMPANY WHERE COMPANYTYPE=\'").append(type).append('\'');
		String sqlReq = " SELECT ORGANIZATION_DESC_EN, ORGANIZATION_DESC_FR, COMPANY_ID FROM ORGANIZATION o INNER JOIN PRODUCT_SUPPLIER p "
				+ " ON o.ORGANIZATION_INT_ID = p.ORGANIZATION where p.PRODUCT_SUPPLIER_TYPE = 1001 and p.ACTIVE = 'Y' ORDER BY 1";

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return companies;
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(sqlReq);

			// iterate through the resultset
			while (rs.next()) {
				IMCompany cie = new IMCompany();
				cie.setCompanyId(rs.getInt("COMPANY_ID"));
				cie.setDescriptionEN(rs.getString("ORGANIZATION_DESC_EN"));
				cie.setDescriptionFR(rs.getString("ORGANIZATION_DESC_FR"));

				// adding to the list
				companies.add(cie);

			}

			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(OrganizationFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return companies;
		}

		return companies;
	}

	public List<IMCompany> findAllIMCompaniesByProductSuplier(int type) {

		List<IMCompany> companies = new ArrayList<>();

		String sqlReq = " SELECT ORGANIZATION_DESC_EN, ORGANIZATION_DESC_FR, COMPANY_ID FROM ORGANIZATION o INNER JOIN PRODUCT_SUPPLIER p "
				+ " ON o.ORGANIZATION_INT_ID = p.ORGANIZATION where p.PRODUCT_SUPPLIER_TYPE = " + type
				+ " and p.ACTIVE = 'Y' ORDER BY 1";

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {

			return companies;
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(sqlReq);

			// iterate through the resultset
			while (rs.next()) {
				IMCompany cie = new IMCompany();
				cie.setCompanyId(rs.getInt("COMPANY_ID"));
				cie.setDescriptionEN(rs.getString("ORGANIZATION_DESC_EN"));
				cie.setDescriptionFR(rs.getString("ORGANIZATION_DESC_FR"));

				// adding to the list
				companies.add(cie);

			}

			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(OrganizationFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return companies;
		}

		return companies;
	}

	public int findProductSuplierTypeByCompany(int companyId) {

		int result = 0;

		String sqlReq = " SELECT p.PRODUCT_SUPPLIER_TYPE FROM ORGANIZATION o INNER JOIN PRODUCT_SUPPLIER p "
				+ " ON o.ORGANIZATION_INT_ID = p.ORGANIZATION where COMPANY_ID = " + companyId;

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return 0;
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(sqlReq);

			// iterate through the resultset
			if (rs.next()) {
				result = rs.getInt("PRODUCT_SUPPLIER_TYPE");

			}
			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(OrganizationFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return 0;
		}

		return result;
	}

}
