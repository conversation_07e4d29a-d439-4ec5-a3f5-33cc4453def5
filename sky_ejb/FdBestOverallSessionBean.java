/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdBestOverall;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdBestOverallSessionBean extends AbstractFacade<FdBestOverall> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdBestOverallSessionBean() {
        super(FdBestOverall.class);
    }
    
    public FdBestOverall getBestOverall(){
        
        List<FdBestOverall> best = findAll();

        if(best != null && !best.isEmpty())
            return best.get(0);
        
        return null;
    }
    
}
