/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.License;
import com.insurfact.skynet.entity.Province;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

import org.eclipse.persistence.config.QueryHints;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class LicenseFacade extends AbstractFacade<License> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public LicenseFacade() {
		super(License.class);
	}

	public void editLicenseLastRequestdate(Integer id) {
		Date now = Calendar.getInstance().getTime();

		License license = em.find(License.class, id);

		if (license != null) {

			license.setRequestDate(now);
			em.merge(license);
		}
	}

	public List<License> findByLicenseNumber(String number) {

		number = "%" + number + "%";

		TypedQuery<License> query = em.createQuery(
				"SELECT l from License L where UPPER(l.licenseNumber) LIKE :number ORDER BY l.company.primaryName, l.province",
				License.class);
		query.setParameter("number", number);
		query.setHint(QueryHints.READ_ONLY, true);

		List<License> list = query.getResultList();

		return list;
	}

	public List<License> findByStatus(Integer status) {

		TypedQuery<License> query = em.createQuery(
				"SELECT l from License L where l.status = :status AND l.endDate IS NOT NULL ORDER BY l.company.primaryName, l.province",
				License.class);
		query.setParameter("status", status);
		query.setHint(QueryHints.READ_ONLY, true);

		List<License> list = query.getResultList();

		return list;
	}

	public List<License> findByStatusProvince(Integer status, Province province) {

		TypedQuery<License> query;

		query = em.createQuery(
				"SELECT l from License L where l.status = :status AND l.province = :province ORDER BY l.company.primaryName",
				License.class);
		query.setParameter("province", province.getProvinceCode());

		query.setParameter("status", status);
		query.setHint(QueryHints.READ_ONLY, true);

		List<License> list = query.getResultList();

		return list;
	}

	public List<License> findAllAboutToExpire(Date date) {
		TypedQuery<License> query = em.createQuery(
				"SELECT l from License L where  l.endDate IS NOT NULL AND l.endDate >= CURRENT_DATE AND l.endDate <=:date ORDER BY l.endDate, l.company.primaryName, l.province",
				License.class);
		query.setParameter("date", date);
//        query.setParameter("status", status);
		query.setHint(QueryHints.READ_ONLY, true);

		List<License> list = query.getResultList();

		return list;
	}

	public List<License> findActiveByDateProvince(Province province) {
		TypedQuery<License> query;

		query = em.createQuery(
				"SELECT l from License L where l.status = 1 AND l.province = :province ORDER BY l.endDate, l.company.primaryName",
				License.class);
		query.setParameter("province", province.getProvinceCode());

		query.setHint(QueryHints.READ_ONLY, true);

		List<License> list = query.getResultList();

		return list;
	}

	public List<License> findExpiredByDateProvince(Date date, Province province) {
		TypedQuery<License> query;

		query = em.createQuery(
				"SELECT l from License L where l.province = :province AND (l.endDate <= CURRENT_DATE AND l.endDate >=:date  OR l.endDate IS NULL) ORDER BY l.endDate, l.company.primaryName",
				License.class);
		query.setParameter("province", province.getProvinceCode());

		query.setParameter("date", date);
		query.setHint(QueryHints.READ_ONLY, true);

		List<License> list = query.getResultList();

		return list;
	}

	public List<License> findAboutExpireByDateProvince(Date date, Province province) {
		TypedQuery<License> query;

		query = em.createQuery(
				"SELECT l from License L where  l.endDate >= CURRENT_DATE AND l.endDate <=:date AND l.province = :province ORDER BY l.endDate, l.company.primaryName",
				License.class);
		query.setParameter("province", province.getProvinceCode());

		query.setParameter("date", date);
		query.setHint(QueryHints.READ_ONLY, true);

		List<License> list = query.getResultList();

		return list;
	}

	public List<License> findByAgency(List<Advisor> advisors) {
		TypedQuery<License> query = em.createQuery("SELECT l from License L where l.advisor IN :advisors",
				License.class);
		query.setParameter("advisors", advisors);

		List<License> list = query.getResultList();

		return list;
	}

}
