/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.cannex.entity.TermProduct;
import com.insurfact.cannex.entity.TermsInterestRate;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class TermsInterestRateFacade extends AbstractFacade<TermsInterestRate> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public TermsInterestRateFacade() {
        super(TermsInterestRate.class);
    }
    
    public List<TermsInterestRate> findTermRates(TermProduct product, Integer termTypeId){
        
        TypedQuery<TermsInterestRate> query = em.createQuery("SELECT r FROM TermsInterestRate r WHERE r.termProduct = :product AND r.termProduct.termTypeId = :duration  ORDER BY r.interestRateValue ASC",TermsInterestRate.class);
        query.setParameter("product", product);
        query.setParameter("duration", termTypeId);
        
        return query.getResultList();
    }
    
    
    public void deleteRates(TermProduct termProduct,Integer termTypeId){
         List<TermsInterestRate> rates = findTermRates(termProduct, termTypeId);
         
         if(rates != null && !rates.isEmpty()) {
             
             for(TermsInterestRate rate : rates){
                 em.remove(rate);
             }
         }
        
    }
}
