/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.ParkingSpace;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager; 
import jakarta.persistence.PersistenceContext; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ParkingsFacade extends AbstractFacade<ParkingSpace> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public ParkingsFacade() {
        super(ParkingSpace.class);
    }

    public void editFile(ParkingSpace p) {
        em.merge(p);
    }

}
