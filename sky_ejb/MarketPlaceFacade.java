/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2012 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.MarketPlace;
import com.insurfact.skynet.entity.Users;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class MarketPlaceFacade extends AbstractFacade<MarketPlace> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public MarketPlaceFacade() {
		super(MarketPlace.class);
	}

	public List<MarketPlace> findAllForAdvisor(Advisor advisor) {

		try {
			TypedQuery<MarketPlace> nq = getEntityManager().createNamedQuery("MarketPlace.findByAdvisor",
					MarketPlace.class);
			nq.setParameter("advisor", advisor);

			List<MarketPlace> list = nq.getResultList();

			return list;

		} catch (Exception e) {

			return null;
		}
	}

	public List<MarketPlace> findAllForUsers(Users users) {

		try {
			TypedQuery<MarketPlace> nq = getEntityManager().createNamedQuery("MarketPlace.findByUsers",
					MarketPlace.class);
			nq.setParameter("users", users);

			List<MarketPlace> list = nq.getResultList();

			return list;

		} catch (Exception e) {

			return null;
		}
	}

	public List<MarketPlace> findAllForAdvisorAndCategory(Advisor advisor, String category) {

		try {
			TypedQuery<MarketPlace> nq = getEntityManager().createNamedQuery("MarketPlace.findByCategoryAndAdvisor",
					MarketPlace.class);
			nq.setParameter("advisor", advisor);
			nq.setParameter("category", category);

			List<MarketPlace> list = nq.getResultList();

			return list;

		} catch (Exception e) {

			return null;
		}
	}

	public List<MarketPlace> findAllForUsersAndCategory(Users users, String category) {

		try {
			TypedQuery<MarketPlace> nq = getEntityManager().createNamedQuery("MarketPlace.findByCategoryAndUsers",
					MarketPlace.class);
			nq.setParameter("users", users);
			nq.setParameter("category", category);

			List<MarketPlace> list = nq.getResultList();

			return list;

		} catch (Exception e) {

			return null;
		}
	}

	public List<MarketPlace> findAllForAdvisorAndClassName(Advisor advisor, String className) {

		try {
			TypedQuery<MarketPlace> nq = getEntityManager().createNamedQuery("MarketPlace.findByClassNameAndAdvisor",
					MarketPlace.class);
			nq.setParameter("advisor", advisor);
			nq.setParameter("className", className);

			List<MarketPlace> list = nq.getResultList();

			return list;

		} catch (Exception e) {

			return null;
		}
	}

	public List<MarketPlace> findAllForUsersAndClassName(Users users, String className) {

		try {
			TypedQuery<MarketPlace> nq = getEntityManager().createNamedQuery("MarketPlace.findByClassNameAndUsers",
					MarketPlace.class);
			nq.setParameter("users", users);
			nq.setParameter("className", className);

			List<MarketPlace> list = nq.getResultList();

			return list;

		} catch (Exception e) {

			return null;
		}
	}

	public void deleteAllAdvisorSelection(Advisor advisor) {

		Query query = em.createQuery("DELETE FROM MarketPlace c WHERE c.advisor = :advisor");
		query.setParameter("advisor", advisor);

		int result = query.executeUpdate();

		System.out.println("deleted [" + result + "] for MarketPlace  ");
	}

	public void deleteAllUsersSelection(Users users) {

		Query query = em.createQuery("DELETE FROM MarketPlace c WHERE c.users = :users");
		query.setParameter("users", users);

		int result = query.executeUpdate();

		System.out.println("deleted [" + result + "] for MarketPlace  ");

	}

}
