/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.Statement;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class StatementFacade extends AbstractFacade<Statement> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public StatementFacade() {
		super(Statement.class);
	}

	public int getNextStatementNumber() {
		Query q = em.createNativeQuery("SELECT STATEMENT_NUMBER_SEQ.nextval from DUAL");
		BigDecimal result = (BigDecimal) q.getSingleResult();
		return result.intValue();
	}

	public List<Statement> findByPayableAndTypeAndLevel(String type, int level) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.printCount = 0 and s.type = :type AND s.payeeType = :level ";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("type", type);
			nq.setParameter("level", level);
			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;
	}

	public List<Statement> findByPrintCountAndTypeAndLevel(String type, int level) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.printCount > 0 and s.type = :type AND s.payeeType = :level ";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("type", type);
			nq.setParameter("level", level);
			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;
	}

	public List<Statement> findByStartAndEndDates(Date start, Date end) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.paymentDueDate >= :startDate and s.paymentDueDate <= :endDate";

		List<Statement> statements = new ArrayList<Statement>();

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("startDate", start);
			nq.setParameter("endDate", end);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;

	}

	public List<Statement> findByProductSupplierStartAndEndDates(ProductSupplier productSupplier, Date start,
			Date end) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.productSupplier = :productSupplier and s.paymentDueDate >= :startDate and s.paymentDueDate <= :endDate";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("startDate", start);
			nq.setParameter("endDate", end);
			nq.setParameter("productSupplier", productSupplier);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;
	}

	public List<Statement> findByPayableProductSupplierTypeAndCount(ProductSupplier productSupplier, String type,
			int level) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.productSupplier = :productSupplier AND s.type = :type AND s.payeeType = :level AND s.printCount = 0 ORDER by s.paymentDueDate Desc";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("productSupplier", productSupplier);
			nq.setParameter("type", type);
			nq.setParameter("level", level);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;

	}

	public List<Statement> findByPrintoutProductSupplierTypeAndCount(ProductSupplier productSupplier, String type,
			int level) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.productSupplier = :productSupplier AND s.type = :type AND s.payeeType = :level AND s.printCount > 0 ORDER by s.paymentDueDate Desc";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("productSupplier", productSupplier);
			nq.setParameter("type", type);
			nq.setParameter("level", level);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;

	}

	public List<Statement> findByProductSupplierAndDueDate(ProductSupplier productSupplier, Date date) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.productSupplier = :productSupplier AND s.paymentDueDate = :paymentDueDate  ORDER by s.paymentDueDate Desc";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("paymentDueDate", date);
			nq.setParameter("productSupplier", productSupplier);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;

	}

	public List<Statement> findSubAgencyAndType(String type) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.payee.agency IS NOT NULL AND s.type = :type ORDER by s.paymentDueDate Desc";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("type", type);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;
	}

	public List<Statement> findBySubAgencyAndType(Agency subAgency, String type) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.payee.agency = :agency AND s.type = :type ORDER by s.paymentDueDate Desc";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("agency", subAgency);
			nq.setParameter("type", type);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;
	}

	public List<Statement> findBySubAgencyAndProductSupplierAndType(Agency subAgency, ProductSupplier productSupplier,
			String type) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.payee.agency = :agency AND s.productSupplier = :productSupplier AND s.type = :type ORDER by s.paymentDueDate Desc";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("agency", subAgency);
			nq.setParameter("productSupplier", productSupplier);
			nq.setParameter("type", type);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;
	}

	public List<Statement> findSubAgencyAndProductSupplierAndTypeAndDate(Agency subAgency,
			ProductSupplier productSupplier, String type, Date date) {

		String query = null;

		query = "SELECT s FROM Statement s WHERE s.payee.agency = :agency AND s.productSupplier = :productSupplier AND s.type = :type AND s.paymentDueDate = :paymentDueDate ORDER by s.paymentDueDate Desc";

		List<Statement> statements = null;

		try {
			TypedQuery<Statement> nq = getEntityManager().createQuery(query, Statement.class);
			nq.setParameter("agency", subAgency);
			nq.setParameter("productSupplier", productSupplier);
			nq.setParameter("type", type);
			nq.setParameter("paymentDueDate", date);

			statements = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return statements;
	}
}
