/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.ProductNotes;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ProductNoteFacade extends AbstractFacade<ProductNotes> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public ProductNoteFacade() {
        super(ProductNotes.class);
    }

    public ProductNotes editNote(ProductNotes p) {
        try {
            em.persist(p);

        } catch (Exception ex) {
            String msg = ex.getLocalizedMessage();
            System.err.println(msg);
        }
        return refresh(p);
    }

    public ProductNotes refresh(ProductNotes note) {
        return find(note.getProductNoteIntId());
    }

}
