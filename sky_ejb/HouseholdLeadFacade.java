/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.HouseholdLead;
import com.insurfact.skynet.entity.Lead; 
import java.util.ArrayList;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class HouseholdLeadFacade extends AbstractFacade<HouseholdLead> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	@Resource(lookup = "jdbc/Orains")
	private DataSource ds;

	public HouseholdLeadFacade() {
		super(HouseholdLead.class);
	}

	@SuppressWarnings("unchecked")
	public List<Lead> findHouseholdByLead(Lead lead) {

		List<Lead> list = new ArrayList<>();
		List<HouseholdLead> listDirty = new ArrayList<>();

		try {
			Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_LEAD where LEAD1 = " + lead.getLeadIntId()
					+ " or LEAD2 = " + lead.getLeadIntId(), HouseholdLead.class);
			System.out.println("SELECT * FROM HOUSEHOLD_LEAD where LEAD1 = " + lead.getLeadIntId() + " or LEAD2 = "
					+ lead.getLeadIntId());

			listDirty = nq.getResultList();

			list.add(lead);
			for (HouseholdLead h : listDirty) {
				if (!list.contains(h.getLead1())) {
					h.getLead1().setRelationship(h.getRelationship());
					findHouseholdByLead(list, h.getLead1());
					// list.add(h.getLead1());
				}
				if (!list.contains(h.getLead2())) {
					h.getLead2().setRelationship(h.getRelationship());
					findHouseholdByLead(list, h.getLead2());
					// list.add(h.getLead2());
				}
			}
			list.remove(0);

		} catch (NoResultException | ArrayIndexOutOfBoundsException e) {
			return list;
		}

		return list;
	}

	public HouseholdLead findHousehold(Lead lead1, Lead lead2) {

		try {
			Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_LEAD where ( LEAD1 = " + lead1.getLeadIntId()
					+ " and LEAD2 = " + lead2.getLeadIntId() + ") or (LEAD1 = " + lead2.getLeadIntId() + " and LEAD2 = "
					+ lead1.getLeadIntId() + " )", HouseholdLead.class);
			System.out.println("SELECT * FROM HOUSEHOLD_LEAD where ( LEAD1 = " + lead1.getLeadIntId() + " and LEAD2 = "
					+ lead2.getLeadIntId() + ") or (LEAD1 = " + lead2.getLeadIntId() + " and LEAD2 = "
					+ lead1.getLeadIntId() + " )");

			if (nq.getMaxResults() > 0) {
				return (HouseholdLead) nq.getResultList().get(0);
			} else {
				return null;
			}

		} catch (NoResultException | ArrayIndexOutOfBoundsException e) {
		}

		return null;
	}

	@SuppressWarnings("unchecked")
	public void findHouseholdByLead(List<Lead> toFill, Lead lead) {

		// List<Lead> list = new ArrayList<>();
		List<HouseholdLead> listDirty = new ArrayList<>();

		try {
			Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_LEAD where LEAD1 = " + lead.getLeadIntId()
					+ " or LEAD2 = " + lead.getLeadIntId(), HouseholdLead.class);
			System.out.println("SELECT * FROM HOUSEHOLD_LEAD where LEAD1 = " + lead.getLeadIntId() + " or LEAD2 = "
					+ lead.getLeadIntId());

			listDirty = nq.getResultList();

			toFill.add(lead);
			System.out.println(listDirty.get(0));

			if (listDirty.get(0).getHouseholdIntId() == null) {
				System.out.println("*******exiting*******");
				return;
			} else {
				for (HouseholdLead h : listDirty) {
					if (!toFill.contains(h.getLead1())) {
						findHouseholdByLead(toFill, h.getLead1());
						// toFill.add(h.getLead1());
					}
					if (!toFill.contains(h.getLead2())) {
						findHouseholdByLead(toFill, h.getLead2());
						// toFill.add(h.getLead2());
					}
				}
			}

		} catch (NoResultException | ArrayIndexOutOfBoundsException e) {
			return;
		}

		return;
	}

	/*
	 * public List<Integer> findHouseholdByLead(Lead lead) {
	 * 
	 * List<Integer> list = new ArrayList<>(); ResultSet rst = null; Statement
	 * ptStmt; Connection connection = null;
	 * 
	 * try {
	 * 
	 * connection = getConnection();
	 * 
	 * ptStmt = connection.createStatement();
	 * 
	 * String sql =
	 * "SELECT m.ROW_KEY, m.AGE, m.RATE_1, m.POLICY_FEE_1 FROM IM_LIFE_PREMIUM_MATRIX m WHERE m.ROW_KEY ="
	 * +rowkey + " ORDER BY  m.age ASC";
	 * 
	 * rst = ptStmt.executeQuery(sql);
	 * 
	 * 
	 * if(rst == null){ System.err.
	 * println("***** Some error occured - unable to find any BandRates with rowKey="
	 * +rowkey ); ptStmt.close(); connection.close(); return null; } while
	 * (rst.next() ) {
	 * 
	 * } rst.close(); ptStmt.close();
	 * 
	 * } catch (SQLException e) { e.printStackTrace(); return null; } // return the
	 * connection to the pool finally { if (connection != null) { try {
	 * connection.close(); } catch (Exception exp) { exp.printStackTrace(); } } }
	 * 
	 * return list; }
	 */
}
