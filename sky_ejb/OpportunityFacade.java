/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Lead;
import com.insurfact.skynet.entity.Opportunity;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class OpportunityFacade extends AbstractFacade<Opportunity> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public OpportunityFacade() {
		super(Opportunity.class);
	}

	public List<Opportunity> findByUsers(Users users) {

		List<Opportunity> list = new ArrayList<>();

//        System.out.println("**** findByUsers() : users="+users.getUserIntId() );

		try {
			TypedQuery<Opportunity> nq = em.createNamedQuery("Opportunity.findByUsers", Opportunity.class);

			nq.setParameter("users", users);

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;

	}

	public List<Opportunity> findByAdvisor(Advisor advisor) {

		List<Opportunity> list = new ArrayList<>();

//        System.out.println("**** findByAdvisor() : advisor="+advisor.getAdvisorIntId() );

		if (advisor == null)
			return list;

		try {
			TypedQuery<Opportunity> nq = em.createNamedQuery("Opportunity.findByAdvisor", Opportunity.class);

			nq.setParameter("advisor", advisor);

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;

	}

	/*
	 * public List<Opportunity> findByContactAndUsers(Contact contact, Users users){
	 * 
	 * List<Opportunity> list = new ArrayList<>();
	 * 
	 * try{ Query nq =
	 * em.createNamedQuery("Opportunity.findByContactAndUsers",Opportunity.class);
	 * 
	 * nq.setParameter("contact", contact); nq.setParameter("users", users);
	 * 
	 * list = nq.getResultList();
	 * 
	 * } catch (NoResultException e) { return list; }
	 * 
	 * return list;
	 * 
	 * }
	 */

	@SuppressWarnings("unchecked")
	public List<Opportunity> findByLead(Lead lead) {

		List<Opportunity> list = new ArrayList<>();

		try {
			Query nq = em.createNativeQuery(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN LEADS_RELATIONSHIP lr ON o.LEADS_RELATIONSHIP = lr.LEADS_RELATIONSHIP_INT_ID where lr.FIRST_LEAD_INT_ID  = "
							+ lead.getLeadIntId(),
					Opportunity.class);

			System.out.println(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN LEADS_RELATIONSHIP lr ON o.LEADS_RELATIONSHIP = lr.LEADS_RELATIONSHIP_INT_ID where lr.FIRST_LEAD_INT_ID  = "
							+ lead.getLeadIntId());

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;

	}

	@SuppressWarnings("unchecked")
	public List<Opportunity> findByContact(Contact contact) {

		List<Opportunity> list = new ArrayList<>();

		try {
			Query nq = em.createNativeQuery(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN CONTACTS_RELATIONSHIP cr ON o.CONTACTS_RELATIONSHIP = cr.CONTACTS_RELATIONSHIP_INT_ID where cr.FIRST_CONTACT_INT_ID  = "
							+ contact.getContactIntId(),
					Opportunity.class);

			System.out.println(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN CONTACTS_RELATIONSHIP cr ON o.CONTACTS_RELATIONSHIP = cr.CONTACTS_RELATIONSHIP_INT_ID where cr.FIRST_CONTACT_INT_ID  = "
							+ contact.getContactIntId());

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;

	}

	@SuppressWarnings("unchecked")
	public List<Opportunity> findByLeadHouse(Lead lead) {

		List<Opportunity> list = new ArrayList<>();

		try {
			Query nq = em.createNativeQuery(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN LEADS_RELATIONSHIP lr ON o.LEADS_RELATIONSHIP = lr.LEADS_RELATIONSHIP_INT_ID where lr.SECOND_LEAD_INT_ID  = "
							+ lead.getLeadIntId(),
					Opportunity.class);

			System.out.println(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN LEADS_RELATIONSHIP lr ON o.LEADS_RELATIONSHIP = lr.LEADS_RELATIONSHIP_INT_ID where lr.SECOND_LEAD_INT_ID  = "
							+ lead.getLeadIntId());

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;

	}

	@SuppressWarnings("unchecked")
	public List<Opportunity> findByContactHouse(Contact contact) {

		List<Opportunity> list = new ArrayList<>();

		try {
			Query nq = em.createNativeQuery(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN CONTACTS_RELATIONSHIP cr ON o.CONTACTS_RELATIONSHIP = cr.CONTACTS_RELATIONSHIP_INT_ID where cr.SECOND_CONTACT_INT_ID  = "
							+ contact.getContactIntId(),
					Opportunity.class);

			System.out.println(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN CONTACTS_RELATIONSHIP cr ON o.CONTACTS_RELATIONSHIP = cr.CONTACTS_RELATIONSHIP_INT_ID where cr.SECOND_CONTACT_INT_ID  = "
							+ contact.getContactIntId());

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;

	}

	public void editOpportunity(Opportunity opportunity) {
		em.merge(opportunity);
	}

}
