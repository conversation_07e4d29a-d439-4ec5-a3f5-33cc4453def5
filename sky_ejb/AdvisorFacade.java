/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.AddressBook;
import com.insurfact.skynet.entity.Advisor;
//import com.insurfact.skynet.entity.Advisor_;
import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.Branch;
import com.insurfact.skynet.entity.Company;
import com.insurfact.skynet.entity.Contact;
//import com.insurfact.skynet.entity.Contact_;
import com.insurfact.skynet.entity.Contract;
import com.insurfact.skynet.entity.ContractEft;
import com.insurfact.skynet.entity.ContractSetup;
import com.insurfact.skynet.entity.Email;
import com.insurfact.skynet.entity.License;
import com.insurfact.skynet.entity.LicenseLiability;
import com.insurfact.skynet.entity.Phone;

import com.insurfact.skynet.ReplaceAdvisorSun;
import com.insurfact.skynet.entity.AgaAdvisors;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.Profile;
import com.insurfact.skynet.entity.Users;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TemporalType;
import jakarta.persistence.TypedQuery;
import javax.sql.DataSource;
import org.eclipse.persistence.config.HintValues;
import org.eclipse.persistence.config.QueryHints;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AdvisorFacade extends AbstractFacade<Advisor> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Resource(lookup = "jdbc/Skytest")
	private DataSource ds;

//    @EJB
//    private ContractRouteFacade contractRouteFacade;
//    
	@EJB
	private LicenseFacade licenseFacade;

	@EJB
	private LicenseLiabilityFacade liabilityFacade;

	@EJB
	private ContractFacade contractFacade;

	@EJB
	private ContractSetupFacade contractSetupFacade;

	@EJB
	private ContractEftFacade contractEftFacade;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public AdvisorFacade() {
		super(Advisor.class);
	}

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (Exception e) {

			e.printStackTrace();
		}
		return null;
	}

	/**
	 * get next valid Advisor Number
	 *
	 * @return
	 */
	public int getNextAdvisorId() {
		Query q = em.createNativeQuery("SELECT ADVISOR_ID_SEQ.nextval from DUAL");
		BigDecimal result = (BigDecimal) q.getSingleResult();
		return result.intValue();
	}

	/**
	 * fetch SunLife Advisor
	 *
	 * @param advisorId
	 * @return
	 */
	public Advisor getAdvisorBySunlifeAdvisorId(String advisorId) {

		try {
			Query nq = em.createNamedQuery("Advisor.findBySunlifeAdvisorId", Advisor.class);

			nq.setParameter("sunlifeAdvisor", advisorId);
			System.out.println("AdvisorFacade 129 advisorId=" + advisorId + " val:" + nq.getSingleResult().toString());
			return (Advisor) nq.getSingleResult();

		} catch (NoResultException | NonUniqueResultException e) {
			System.out.println("AdvisorFacade 134 NoResultException or NonUniqueResultException " + e.getMessage());
			return null;
		}

	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findAdvisorByContractProductSupplier(ProductSupplier productSupplier) {

		try {
			Query nq = em.createNativeQuery(
					"select DISTINCT adv.* from advisor adv inner JOIN CONTRACT_SETUP cs on adv.ADVISOR_INT_ID = cs.advisor where cs.PRODUCT_SUPPLIER = "
							+ productSupplier.getProductSupplierIntId() + " or cs.MGA_PRODUCT_SUPPLIER =  "
							+ productSupplier.getProductSupplierIntId() + "  or cs.AGA_PRODUCT_SUPPLIER =  "
							+ productSupplier.getProductSupplierIntId() + "  or cs.AGA2_PRODUCT_SUPPLIER =  "
							+ productSupplier.getProductSupplierIntId(),
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException | NonUniqueResultException e) {
			return new ArrayList<>();
		}

	}

	@SuppressWarnings("unchecked")
	public List<Advisor> getAdvisorBySunlifeAdvisorNumber(String sunlifeAdvNumber) {

		try {
			Query nq = em.createNativeQuery(
					"select * from advisor where SUNLIFE_ADVISOR LIKE '%" + sunlifeAdvNumber + "%'", Advisor.class);
			return nq.getResultList();

		} catch (NoResultException | NonUniqueResultException e) {
			return new ArrayList<>();
		}

	}

	@SuppressWarnings("unchecked")
	public List<Advisor> getAdvisorBySunlifeAdvisorNumber(String sunlifeAdvNumber, String financialCenters) {

		try {
			Query nq = em.createNativeQuery(
					"select a.* from advisor a inner join contact c on c.contact_int_id = a.advisor_int_id where c.BRANCH in "
							+ financialCenters + " and a.SUNLIFE_ADVISOR LIKE '%" + sunlifeAdvNumber + "%'",
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException | NonUniqueResultException e) {
			return new ArrayList<>();
		}

	}

	/**
	 * fetch SunLife Advisor -- New dec 11, 2018 aa -- Active only!! status=1
	 *
	 * @param advisorId
	 * @return
	 */
	public Advisor getAdvisorBySunlifeAdvisorIdStatus(String advisorId, int status) {

		try {
			Query nq = em.createNamedQuery("Advisor.findBySunlifeAdvisorIdStatus", Advisor.class);

			nq.setParameter("sunlifeAdvisor", advisorId);
			nq.setParameter("status", status);// status 1=active 3=terminated

			System.out.println("AdvisorFacade 153 advisorId=" + advisorId + " val:" + nq.getSingleResult().toString());
			return (Advisor) nq.getSingleResult();

		} catch (NoResultException | NonUniqueResultException e) {
			System.out.println("AdvisorFacade 158 advisorId=" + advisorId
					+ " NoResultException or NonUniqueResultException " + e.getMessage());
			return null;
		}

	}

	public boolean isAdvisorIdExist(Integer id) {

		if (id == null || id.intValue() == 0) {
			return false;
		}

		Advisor advisor = findByAdvisorId(id);

		if (advisor == null) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * find all Advisor for a MasterCode, type and name(s)
	 *
	 * @param contactName
	 * @param masterCode
	 * @param type
	 * @return
	 */
	public List<Advisor> findAdvisorByTypeAndNamesRestricted(String contactName, String masterCode, Integer type) {

		System.out.println("193 AdvisorFacade. findAdvisorByTypeAndNamesRestricted : " + contactName + " masterCode="
				+ masterCode + " type=" + type);

// contact.type = 1  Sunlife
// contact.type = 13 InsurFact Advisor
		// long now = System.nanoTime();

		String[] names = contactName.split(" ");

		TypedQuery<Advisor> query;

		String sql = "";

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";

			/*
			 * query = em.createQuery("SELECT a from Advisor a WHERE a.status = 1 "+
			 * " AND a.contact.masterCode = :masterCode "+
			 * " AND a.contact.contactType = :type "+
			 * " AND (UPPER(a.contact.firstname) LIKE :name "+
			 * " OR UPPER(a.contact.lastname) LIKE :name)", Advisor.class);
			 */
			sql = "SELECT a from Advisor a WHERE " + " a.contact.masterCode = :masterCode " + // " AND
																								// a.contact.contactType
																								// = :type "+
					" AND (UPPER(a.contact.firstname) LIKE :name " + " OR UPPER(a.contact.lastname) LIKE :name)";
			query = em.createQuery(sql, Advisor.class);

			query.setParameter("masterCode", masterCode);
			query.setParameter("name", name);
			// query.setParameter("type", type);
		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			/*
			 * query = em.createQuery("SELECT a from Advisor a WHERE a.status = 1 "+
			 * " AND a.contact.masterCode = :masterCode "+
			 * " AND a.contact.contactType = :type "+
			 * " AND UPPER(a.contact.firstname) LIKE :firstname "+
			 * " AND UPPER(a.contact.lastname) LIKE :lastname", Advisor.class);
			 */

			sql = "SELECT a from Advisor a WHERE " + "   a.contact.masterCode = :masterCode " + // " AND
																								// a.contact.contactType
																								// = :type "+
					" AND UPPER(a.contact.firstname) LIKE :firstname "
					+ " AND UPPER(a.contact.lastname) LIKE :lastname";
			query = em.createQuery(sql, Advisor.class);

			query.setParameter("masterCode", masterCode);
			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);
			// query.setParameter("type", type);

			System.out.println(firstname + " " + lastname);
		}

		List<Advisor> advisors = query.getResultList();

		System.out.println("AdvisorFacade 254 masterCode=" + masterCode);
		System.out.println("AdvisorFacade 255 sql=" + sql);
//System.out.println("AdvisorFacade 254 type=" + type);    

		return advisors;
	}

	public List<Advisor> findAdvisorByTypeAndNamesAGA(String contactName, String masterCode, Advisor aga) {

		String[] names = contactName.split(" ");

		TypedQuery<Advisor> query;

		String sql = "";

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";

			sql = "SELECT a from Advisor a WHERE " + "  (UPPER(a.contact.firstname) LIKE :name "
					+ " OR UPPER(a.contact.lastname) LIKE :name)   "
					+ "   AND a.contact.advisor in (select b.advisor from AgaAdvisors b where b.aga= :agaID) ";
			query = em.createQuery(sql, Advisor.class);

			query.setParameter("name", name);
			query.setParameter("agaID", aga);

		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";

			sql = "SELECT a from Advisor a WHERE " + "  UPPER(a.contact.firstname) LIKE :firstname "
					+ " AND UPPER(a.contact.lastname) LIKE :lastname "
					+ " AND  a.contact.advisor in (select b.advisor from AgaAdvisors b where b.aga= :agaID) ";
			query = em.createQuery(sql, Advisor.class);

			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);
			query.setParameter("agaID", aga);
		}

		List<Advisor> advisors = query.getResultList();

		return advisors;
	}

	public List<Advisor> findByLastContactedPeriod(Date fromDate, Date toDate) {

		TypedQuery<Advisor> nq = em.createQuery(
				"SELECT a FROM Advisor a WHERE  a.contact.firstContacted between :startDate AND :endDate",
				Advisor.class);
		nq.setParameter("startDate", fromDate, TemporalType.TIMESTAMP);
		nq.setParameter("endDate", toDate, TemporalType.TIMESTAMP);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<Advisor>
		 * criteriaQuery = cBuilder.createQuery(Advisor.class); Root<Advisor> p =
		 * criteriaQuery.from(Advisor.class);
		 * 
		 * Predicate lastContactedPredicate =
		 * cBuilder.between(p.get(Advisor_.contact).get(Contact_.firstContacted),
		 * fromDate, toDate);
		 * 
		 * criteriaQuery.where(lastContactedPredicate).distinct(true);
		 * TypedQuery<Advisor> query = em.createQuery(criteriaQuery);
		 * 
		 * return query.getResultList();
		 */
	}

	public Advisor findByAdvisorId(Integer advisorId) {

		try {
			Query nq = getEntityManager().createNamedQuery("Advisor.findByAdvisorId", Advisor.class);
			nq.setParameter("advisorId", advisorId);

			Advisor advisor = (Advisor) nq.getSingleResult();

			return advisor;

		} catch (Exception e) {

			return null;
		}
	}

	/**
	 * find all SunLife Advisors
	 *
	 * @param number
	 * @param masterCode
	 * @return
	 */
	public List<Advisor> findBySunlifeAdvisorNumber(String number, String masterCode) {

		number = "%" + number + "%";

		try {
			TypedQuery<Advisor> nq = em.createQuery(
					"SELECT a FROM Advisor a WHERE  a.contact.masterCode = :masterCode AND  a.sunlifeAdvisor like  :sunlifeAdvisor",
					Advisor.class);
			nq.setParameter("sunlifeAdvisor", number);
			nq.setParameter("masterCode", masterCode);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	/**
	 * lookup Advisor using old AGA Number Id (Green SYStem)
	 *
	 * @param number
	 * @param masterCode
	 * @return
	 */
	public Advisor getAGAByOldNumber(Integer number, String masterCode) {

		try {
			Query nq = em.createQuery(
					"SELECT a FROM Advisor a WHERE  a.contact.masterCode = :masterCode AND a.advisorType = 2 AND a.oldAGAId =  :oldNumber",
					Advisor.class);
			nq.setParameter("oldNumber", number);
			nq.setParameter("masterCode", masterCode);
			return (Advisor) nq.getSingleResult();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Advisor> findAgaAdvisors(String masterCode) {

		try {
			TypedQuery<Advisor> nq = em.createQuery(
					"SELECT a FROM Advisor a WHERE  a.contact.masterCode = :masterCode AND a.advisorType = 2",
					Advisor.class);
			nq.setParameter("masterCode", masterCode);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByTool(Profile profile) {

		try {
			Query nq = em.createNativeQuery(
					"select distinct a.* from ADVISOR a  INNER JOIN PROFILE_USERS pu on a.advisor_int_id = pu.USERs where pu.PROFILE = "
							+ profile.getProfileIntId() + " ",
					Advisor.class);

			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByAddressBook(Users user) {

		try {
			Query nq = em.createNativeQuery(
					"SELECT * FROM advisor where ADVISOR_INT_ID in(select contact from ADDRESS_BOOK where OWNER = "
							+ user.getUserIntId() + ") ",
					Advisor.class);

			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByToolWithActivities(Profile profile) {

		try {
			Query nq = em.createNativeQuery(
					"select distinct a.* from ADVISOR a  INNER JOIN PROFILE_USERS pu on a.advisor_int_id = pu.USERs INNER JOIN ACTIVITY act on (act.owner = a.ADVISOR_INT_ID or act.contact = a.ADVISOR_INT_ID) where pu.PROFILE = "
							+ profile.getProfileIntId() + " ",
					Advisor.class);

			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	public List<Advisor> findByCompanyNameAndTypeRestricted(String name, String masterCode, Integer type) {

		name = name.toUpperCase().trim() + "%";
		List<Advisor> advisors = new ArrayList<>();

		try {
			TypedQuery<Company> nq = em
					.createQuery(
							" SELECT c FROM Company c " + "  WHERE c.advisor.contact.masterCode = :masterCode "
									+ "    AND c.advisor.contact.contactType = :type      "
									+ "AND UPPER(c.primaryName) like :name OR UPPER(c.otherName) like :name",
							Company.class);
			nq.setParameter("masterCode", masterCode);
			nq.setParameter("type", type);
			nq.setParameter("name", name);

			List<Company> cies = nq.getResultList();

			if (cies != null && !cies.isEmpty()) {
				for (Company c : cies) {
					if (!advisors.contains(c.getAdvisor())) {
						advisors.add(c.getAdvisor());
					}
				}

			}

		} catch (NoResultException e) {
			return advisors;
		}

		return advisors;
	}

	public List<Advisor> findAllAdvisorByStatusAndTypeRestricted(String masterCode, Integer status, Integer type) {

		List<Advisor> advisors = new ArrayList<>();

		try {
			TypedQuery<Advisor> nq = em.createQuery(
					" SELECT a FROM Advisor a      " + "  WHERE a.contact.masterCode = :masterCode "
							+ "    AND a.advisorType = :type " + "    AND a.status = :status    " + "  ORDER BY a.name",
					Advisor.class);
			nq.setParameter("masterCode", masterCode);
			nq.setParameter("type", type);
			nq.setParameter("status", status);

			advisors = nq.getResultList();

		} catch (NoResultException e) {
			return advisors;
		}

		return advisors;
	}

	/**
	 * list all AGA Advisor (including AGA/Agency)
	 *
	 * @param masterCode
	 * @param advisorType
	 * @return
	 */
	public List<Advisor> findAllAGAAndTypeRestricted(String masterCode, Integer advisorType) {

		List<Advisor> advisors = new ArrayList<>();
		try {
			TypedQuery<Advisor> nq = em.createQuery(" SELECT a FROM Advisor a WHERE a.contact.masterCode = :masterCode "
					+ "    AND a.advisorType = :type ORDER BY a.name", Advisor.class);
			nq.setParameter("masterCode", masterCode);
			nq.setParameter("type", advisorType);

			advisors = nq.getResultList();

		} catch (NoResultException e) {
			return advisors;
		}

		return advisors;
	}

	public List<Advisor> findByPrimaryNameAndTypeRestricted(String name, String masterCode, Integer type) {

		name = name.toUpperCase().trim() + "%";
		List<Advisor> advisors = new ArrayList<>();

		try {
			TypedQuery<Advisor> nq = em.createQuery("SELECT a FROM Advisor a WHERE a.contact.masterCode = :masterCode "
					+ "   AND a.advisorType = :type AND UPPER(a.name) like :name", Advisor.class);
			nq.setParameter("masterCode", masterCode);
			nq.setParameter("type", type);
			nq.setParameter("name", name);

			advisors = nq.getResultList();

		} catch (NoResultException e) {
			return advisors;
		}

		return advisors;
	}

	public List<Advisor> findByPrimaryNameAndTypeRestricted(String name, String masterCode) {

		name = name.toUpperCase().trim() + "%";
		List<Advisor> advisors = new ArrayList<>();

		try {
			TypedQuery<Advisor> nq = em.createQuery("SELECT a FROM Advisor a WHERE a.contact.masterCode = :masterCode "
					+ "   AND (a.advisorType = 2 or a.advisorType = 3 or a.advisorType = 19) AND UPPER(a.name) like :name",
					Advisor.class);
			nq.setParameter("masterCode", masterCode);
			nq.setParameter("name", name);

			advisors = nq.getResultList();

		} catch (NoResultException e) {
			return advisors;
		}

		return advisors;
	}

	/**
	 * List all Advisors doing business with this AGA and using this Agency(MGA)
	 *
	 * @param aga
	 * @param masterCode
	 * @return
	 */
	public List<Advisor> findAllAdvisorsForAGAAgencyRestricted(Advisor aga, String masterCode) {
		List<Advisor> advisors = new ArrayList<>();

		try {

			TypedQuery<Advisor> nq = em.createQuery(
					"SELECT a FROM Advisor a WHERE a.agaAgencyId = :agaId and a.contact.masterCode = :masterCode",
					Advisor.class);

			nq.setParameter("agaId", aga.getAdvisorIntId());
			nq.setParameter("masterCode", masterCode);
			nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

			advisors = nq.getResultList();

		} catch (NoResultException e) {
			e.printStackTrace();

		}

		return advisors;
	}

	public List<AgaAdvisors> findAllAdvisorsForAGAAgencyNEW(Advisor aga) {

		List<AgaAdvisors> agaAdvisors = new ArrayList<>();
		List<AgaAdvisors> agaAdvisorsFiltered = new ArrayList<>();

		try {

			TypedQuery<AgaAdvisors> nq = getEntityManager().createNamedQuery("AgaAdvisors.findByAga",
					AgaAdvisors.class);
			nq.setParameter("aga", aga);
			agaAdvisors = nq.getResultList();
		} catch (NoResultException e) {
			e.printStackTrace();

		}

		aga.setAgaAdvisorsList(agaAdvisorsFiltered);

		return agaAdvisors;
	}

	/**
	 * List all Advisors doing business with this AGA and using this Agency(MGA)
	 *
	 * @param aga
	 * @param agency
	 * @return
	 */
	public List<Advisor> findAllAdvisorsForAGARestricted(Advisor aga, Agency agency) {
		List<Advisor> advisors = new ArrayList<>();

		try {

			TypedQuery<Advisor> nq = em.createQuery(
					"SELECT DISTINCT c.advisor FROM ContractSetup c WHERE c.agaAdvisor = :aga AND c.servicingAgency = :agency",
					Advisor.class);

			nq.setParameter("aga", aga);
			nq.setParameter("agency", agency);
			nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

			advisors = nq.getResultList();

		} catch (NoResultException e) {
			e.printStackTrace();

		}

		return advisors;
	}

	public List<Advisor> findByName(String name) {

		try {
			TypedQuery<Advisor> nq = getEntityManager().createNamedQuery("Advisor.findByName", Advisor.class);
			nq.setParameter("name", "%" + name + "%");

			return nq.getResultList();

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByNameLike(String name) {

		try {
			Query nq = em.createNativeQuery(
					"SELECT a.* from contact c, advisor a WHERE c.contact_int_id = a.advisor_int_id and (UPPER(c.FIRSTNAME) like UPPER('%"
							+ name + "%') or UPPER(c.MIDDLENAME) like UPPER('%" + name
							+ "%') or UPPER(c.LASTNAME) like UPPER('%" + name + "%'))",
					Advisor.class);

			return nq.getResultList();

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByNameLike(String name, String lastName) {

		try {
			Query nq = em.createNativeQuery(
					"SELECT a.* from contact c, advisor a WHERE c.contact_int_id = a.advisor_int_id and ((UPPER(c.FIRSTNAME) like UPPER('%"
							+ name + "%') and UPPER(c.MIDDLENAME) like UPPER('%" + lastName
							+ "%')) or (UPPER(c.FIRSTNAME) like UPPER('%" + name
							+ "%') and UPPER(c.LASTNAME) like UPPER('%" + lastName + "%')) )",
					Advisor.class);

			return nq.getResultList();

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByNameLike(String name, String lastName, String financialCenters) {

		try {
			Query nq = em.createNativeQuery("SELECT a.* from contact c, advisor a WHERE c.BRANCH in " + financialCenters
					+ " and c.contact_int_id = a.advisor_int_id and ((UPPER(c.FIRSTNAME) like UPPER('%" + name
					+ "%') and UPPER(c.MIDDLENAME) like UPPER('%" + lastName
					+ "%')) or (UPPER(c.FIRSTNAME) like UPPER('%" + name + "%') and UPPER(c.LASTNAME) like UPPER('%"
					+ lastName + "%')) )", Advisor.class);

			return nq.getResultList();

		} catch (NoResultException e) {
//            e.printStackTrace();
			return new ArrayList<>();
		}
	}

	/*
	 * public List<Advisor> findByNameLike(String name, String secondName, String
	 * lastName) {
	 * 
	 * try { Query nq = em.
	 * createNativeQuery("SELECT a.* from contact c, advisor a WHERE c.contact_int_id = a.advisor_int_id and (UPPER(c.FIRSTNAME) like UPPER('%"
	 * + name + "%') and UPPER(c.MIDDLENAME) like UPPER('%" + secondName +
	 * "%') and UPPER(c.LASTNAME) like UPPER('%" + lastName + "%'))",
	 * Advisor.class);
	 * 
	 * return nq.getResultList();
	 * 
	 * } catch (NoResultException e) { return new ArrayList<>(); } }
	 */
	@SuppressWarnings("unchecked")
	public List<Advisor> findByNameLike(String name, String secondName, String lastName, String financialCenters) {

		try {
			Query nq = em.createNativeQuery("SELECT a.* from contact c, advisor a WHERE c.BRANCH in " + financialCenters
					+ " and c.contact_int_id = a.advisor_int_id and (UPPER(c.FIRSTNAME) like UPPER('%" + name
					+ "%') and UPPER(c.MIDDLENAME) like UPPER('%" + secondName
					+ "%') and UPPER(c.LASTNAME) like UPPER('%" + lastName + "%'))", Advisor.class);

			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	public List<Advisor> findAllSunAdvisor() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<Advisor> advisors = new ArrayList<>();

		if (connection == null) {
			return advisors;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "SELECT ADVISOR_INT_ID, ADVISOR_ID, NAME, SUNLIFE_ADVISOR FROM SKYTEST.ADVISOR WHERE ((AGA_AGENCY_ID = 45995) AND (STATUS = 1)) ORDER BY NAME";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return advisors;
			}

			while (rst.next()) {

				Advisor advisor = new Advisor();

				advisor.setAdvisorIntId(rst.getInt(1));
				advisor.setAdvisorId(rst.getInt(2));
				advisor.setName(rst.getString(3));
				advisor.setSunlifeAdvisor(rst.getString(4));

				advisors.add(advisor);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(AdvisorFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return advisors;
		} // return the connection to the pool

		return advisors;

	}

	public List<Advisor> findAllAgencyAdvisor() {

		ResultSet rst = null;
		Connection connection = null;

		List<Advisor> advisors = new ArrayList<>();

		connection = getConnection();
		if (connection != null) {

			try (Statement ptStmt = connection.createStatement();) {

				String sql = "SELECT ADVISOR_INT_ID, ADVISOR_ID, NAME, SUNLIFE_ADVISOR FROM SKYTEST.ADVISOR a INNER JOIN SKYTEST.AGENCY_ADVISOR b on a.ADVISOR_INT_ID = b.advisor where STATUS = 1 and  a.SUNLIFE_ADVISOR is null  ORDER BY NAME";

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return advisors;
				}

				while (rst.next()) {

					Advisor advisor = new Advisor();

					advisor.setAdvisorIntId(rst.getInt(1));
					advisor.setAdvisorId(rst.getInt(2));
					advisor.setName(rst.getString(3));
					advisor.setSunlifeAdvisor(rst.getString(4));

					advisors.add(advisor);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					e.printStackTrace();
					connection.close();
					return advisors;
				} catch (SQLException ex) {
					Logger.getLogger(AdvisorFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
			}
		}

		return advisors;

	}

	public List<Advisor> findByCorporateName(String name) {

		try {
			TypedQuery<Advisor> nq = getEntityManager().createNamedQuery("Advisor.findByCorporateName", Advisor.class);
			nq.setParameter("corporateName", "%" + name + "%");
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	public Advisor refreshAdvisorEntity(Advisor advisor) {

		advisor = em.find(advisor.getClass(), advisor.getAdvisorIntId());
		return advisor;
	}

	public List<Integer> findAllAdvisorsForAGARestricted(Advisor aga) {

		List<Integer> advisors = new ArrayList<>();

		try {

			TypedQuery<Integer> nq = em.createQuery(
					"SELECT DISTINCT c.advisor.advisorIntId FROM ContractSetup c WHERE c.agaAdvisor = :aga",
					Integer.class);

			nq.setParameter("aga", aga);
			nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

			advisors = nq.getResultList();

		} catch (NoResultException e) {
			e.printStackTrace();

		}
		return advisors;
	}

	/**
	 * list all Advisors doing business with this Agency(MGA)
	 *
	 * @param agency
	 * @return
	 */
	public List<Integer> findAllAdvisorsForAgencyRestricted(Agency agency) {

		List<Integer> advisors = new ArrayList<>();

		try {

			TypedQuery<Integer> nq = em.createQuery(
					"SELECT c.advisor.advisorIntId FROM ContractSetup c WHERE c.servicingAgency = :agency",
					Integer.class);

			nq.setParameter("agency", agency);
			nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

			advisors = nq.getResultList();

		} catch (NoResultException e) {
			e.printStackTrace();

		}
		return advisors;
	}

	/**
	 * find Advisors by Email
	 *
	 * @param email
	 * @param masterCode
	 * @param type
	 * @return
	 */
	public List<Advisor> findByEmailAddress(String email, String masterCode, Integer type) {

		email = "%" + email + "%";

		List<Advisor> advisors = new ArrayList<>();

		try {
			TypedQuery<Email> nq = em.createQuery("SELECT a FROM Email a WHERE LOWER(a.emailAddress) like  :email",
					Email.class);
			nq.setParameter("email", email);

			List<Email> emails = nq.getResultList();

			if (emails != null && !emails.isEmpty()) {

				for (Email e : emails) {

					for (Contact c : e.getContactList()) {

						if (c.getMasterCode() == null || !c.getMasterCode().equalsIgnoreCase(masterCode)) {
							continue;
						}

						if (c.getContactType() == null || !c.getContactType().equals(type)) {
							continue;
						}

						if (c.getAdvisor() != null && !advisors.contains(c.getAdvisor())) {
							advisors.add(c.getAdvisor());
						}
					}

				}
			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return advisors;
	}

	public List<Advisor> findByEmailAddress(String email, String masterCode) {

		email = "%" + email + "%";

		List<Advisor> advisors = new ArrayList<>();

		try {
			TypedQuery<Email> nq = em.createQuery("SELECT a FROM Email a WHERE LOWER(a.emailAddress) like  :email",
					Email.class);
			nq.setParameter("email", email);

			List<Email> emails = nq.getResultList();

			if (emails != null && !emails.isEmpty()) {

				for (Email e : emails) {

					for (Contact c : e.getContactList()) {

						if (c.getMasterCode() == null || !c.getMasterCode().equalsIgnoreCase(masterCode)) {
							continue;
						}

						if (c.getAdvisor().getAdvisorType() == null || (!c.getAdvisor().getAdvisorType().equals(2)
								&& !c.getAdvisor().getAdvisorType().equals(3)
								&& !c.getAdvisor().getAdvisorType().equals(19))) {
							continue;
						}

						if (c.getAdvisor() != null && !advisors.contains(c.getAdvisor())) {
							advisors.add(c.getAdvisor());
						}
					}

				}
			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return advisors;
	}

	/**
	 * find all Advisors by Email
	 *
	 * @param area
	 * @param number
	 * @param masterCode
	 * @param type
	 * @return
	 */
	public List<Advisor> findByPhoneNumber(String area, String number, String masterCode, Integer type) {

		List<Advisor> advisors = new ArrayList<>();

		if (area != null && area.isEmpty()) {
			area = null;
		}

		if (number != null && number.isEmpty()) {
			number = null;
		}

//        System.out.println("findByPhoneNumber " + masterCode + " type="+ type);
		try {
			TypedQuery<Phone> nq = null;

			if (area != null && number == null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code", Phone.class);
				nq.setParameter("code", area);
			}

			if (area == null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.phoneNumber like :number", Phone.class);
				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (area != null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code AND  a.phoneNumber like :number",
						Phone.class);
				nq.setParameter("code", area.trim());

				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (nq == null) {
				return advisors;
			}

			List<Phone> phones = nq.getResultList();

			if (phones == null || phones.isEmpty()) {
				return advisors;
			}

			for (Phone p : phones) {

				for (Contact c : p.getContactList()) {

//                    System.out.println(c.getName() + " " + c.getContactType() + " master="+ c.getMasterCode());
					if (c.getMasterCode() == null || !c.getMasterCode().equalsIgnoreCase(masterCode)) {
						continue;
					}

					if (c.getContactType() == null || !c.getContactType().equals(type)) {
						continue;
					}

					if (c.getAdvisor() != null && !advisors.contains(c.getAdvisor())) {
						advisors.add(c.getAdvisor());
					}
				}

			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return advisors;
	}

	public List<Advisor> findByPhoneNumber(String area, String number, String masterCode) {

		List<Advisor> advisors = new ArrayList<>();

		if (area != null && area.isEmpty()) {
			area = null;
		}

		if (number != null && number.isEmpty()) {
			number = null;
		}

//        System.out.println("findByPhoneNumber " + masterCode + " type="+ type);
		try {
			TypedQuery<Phone> nq = null;

			if (area != null && number == null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code", Phone.class);
				nq.setParameter("code", area);
			}

			if (area == null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.phoneNumber like :number", Phone.class);
				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (area != null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code AND  a.phoneNumber like :number",
						Phone.class);
				nq.setParameter("code", area.trim());

				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (nq == null) {
				return advisors;
			}

			List<Phone> phones = nq.getResultList();

			if (phones == null || phones.isEmpty()) {
				return advisors;
			}

			for (Phone p : phones) {

				for (Contact c : p.getContactList()) {

//                    System.out.println(c.getName() + " " + c.getContactType() + " master="+ c.getMasterCode());
					if (c.getMasterCode() == null || !c.getMasterCode().equalsIgnoreCase(masterCode)) {
						continue;
					}

					if (c.getAdvisor().getAdvisorType() == null
							|| (!c.getAdvisor().getAdvisorType().equals(2) && !c.getAdvisor().getAdvisorType().equals(3)
									&& !c.getAdvisor().getAdvisorType().equals(19))) {
						continue;
					}

					if (c.getAdvisor() != null && !advisors.contains(c.getAdvisor())) {
						advisors.add(c.getAdvisor());
					}
				}

			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return advisors;
	}

	/**
	 * Persists the modifications on the licenseLiability list of the advisor
	 *
	 * @param agency
	 * @return
	 */
	public List<Advisor> findAdvisorsForMasterGroup(Agency agency) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<Advisor> advisors = new ArrayList<>();

		if (connection == null) {
			return advisors;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select a.ADVISOR_INT_ID, a.ADVISOR_ID, a.NAME, a.ADVISOR_TYPE, a.SUNLIFE_ADVISOR, a.STATUS "
					+ " from advisor a, agency c, AGENCY_ADVISOR l "
					+ " where c.AGENCY_INT_ID = l.AGENCY and c.AGENCY_INT_ID = " + agency.getAgencyIntId()
					+ "  and l.ADVISOR = a.ADVISOR_INT_ID";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return advisors;
			}

			while (rst.next()) {

				Advisor advisor = new Advisor();

				advisor.setAdvisorIntId(rst.getInt(1));
				advisor.setAdvisorId(rst.getInt(2));
				advisor.setName(rst.getString(3));

				advisor.setAdvisorType(rst.getInt(4));

				advisor.setSunlifeAdvisor(rst.getString(5));
				advisor.setStatus(rst.getInt(6));

				advisors.add(advisor);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(AdvisorFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return advisors;
		}

		return advisors;
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findAdvisorsForAgency(Agency agency, int language, String postalCode) {

		String sql;
		Query query;
		List<Advisor> advisors;

		sql = " select a.* from advisor a inner join contact c on a.advisor_int_id = c.CONTACT_INT_ID INNER JOIN "
				+ " CONTACT_ADDRESS ca on ca.CONTACT = c.contact_int_id inner join ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS "
				+ " where UPPER(ad.POSTAL_CODE) like UPPER('" + postalCode + "') and a.ADVISOR_INT_ID in "
				+ " (select aa.advisor from AGENCY_ADVISOR aa where aa.AGENCY = " + agency.getAgencyIntId() + " )";
		query = em.createNativeQuery(sql, Advisor.class);

		advisors = query.getResultList();

		Iterator<Advisor> it = advisors.iterator();

		while (it.hasNext()) {
			Advisor advisor = it.next();
			if (!advisor.knowLanguaje(language)) {
				it.remove();
			}
		}

		return advisors;

	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findAdvisorsForAgency(Agency agency, String lastName) {

		String sql;
		Query query;
		List<Advisor> advisors;

		sql = " select a.* from advisor a inner join contact c on a.advisor_int_id = c.CONTACT_INT_ID "
				+ " where UPPER(c.lastname) LIKE UPPER('" + lastName + "')  and a.ADVISOR_INT_ID in "
				+ " (select aa.advisor from AGENCY_ADVISOR aa where aa.AGENCY = " + agency.getAgencyIntId() + " )";
		query = em.createNativeQuery(sql, Advisor.class);

		advisors = query.getResultList();
		return advisors;
	}

	public List<ReplaceAdvisorSun> findAdvisorsByBranch(Branch branch) {

		ResultSet rst = null;

		Connection connection = null;

		List<ReplaceAdvisorSun> replaceAdvisorSunList = new ArrayList<>();

		connection = getConnection();
		if (connection != null) {

			try (Statement ptStmt = connection.createStatement();) {

				String sql = " select CUSTOM_FIELD2_VALUE, FIRSTNAME, LASTNAME, "
						+ "        BRANCH_PRIMARY_NAME, BRANCH_OTHER_NAME,      "
						+ "        AREA_CODE, PHONE_NUMBER, EMAIL_ADDRESS,      " + "        TIMESTAMP, CREATEDCOUNT  "
						+ "   from VTEST                                        " + "  WHERE BRANCH="
						+ branch.getBranchIntId() + "  ORDER BY LASTNAME, FIRSTNAME, CREATEDCOUNT ";
				rst = ptStmt.executeQuery(sql);
				// add counter - int

				if (rst == null) {
					connection.close();
					return replaceAdvisorSunList;
				}

				while (rst.next()) {

					ReplaceAdvisorSun repAdvisor = new ReplaceAdvisorSun();

					repAdvisor.setSunAdvNo(rst.getString(1));
					repAdvisor.setFirstName(rst.getString(2));
					repAdvisor.setLastName(rst.getString(3));

					repAdvisor.setBranchNameEng(rst.getString(4));
					repAdvisor.setBranchNameFre(rst.getString(5));

					repAdvisor.setAreaCode(rst.getString(6));
					repAdvisor.setPhoneNo(rst.getString(7));

					repAdvisor.setEmailAdd(rst.getString(8));

					repAdvisor.setLastLogin(rst.getDate(9));

					repAdvisor.setCounter(rst.getInt(10));

					replaceAdvisorSunList.add(repAdvisor);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(AdvisorFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return replaceAdvisorSunList;
			} // return the connection to the pool

		}

		return replaceAdvisorSunList;
	}

	public List<Advisor> findAdvisorsByPC(String pc) {

		ResultSet rst = null;

		Connection connection = null;

		List<Advisor> advisors = new ArrayList<>();
		connection = getConnection();
		if (connection != null) {

			try (Statement ptStmt = connection.createStatement();) {

				String sql = "select a.ADVISOR_INT_ID, a.ADVISOR_ID, a.NAME, a.ADVISOR_TYPE, "
						+ "a.SUNLIFE_ADVISOR, a.STATUS " + " from advisor a, contact c, address s, contact_address ca  "
						+ " where c.CONTACT_INT_ID = a.ADVISOR_INT_ID " + "  and a.STATUS = 1  " + // 1 = active
						"  and ca.contact = c.CONTACT_INT_ID   " + "  and ca.address = s.address_int_id      "
						+ "  and s.POSTAL_CODE = '" + pc + "' " + "  and a.SUNLIFE_ADVISOR is not null  ";

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return advisors;
				}

				while (rst.next()) {

					Advisor advisor = new Advisor();

					advisor.setAdvisorIntId(rst.getInt(1));
					advisor.setAdvisorId(rst.getInt(2));
					advisor.setName(rst.getString(3));
					advisor.setAdvisorType(rst.getInt(4));

					advisor.setSunlifeAdvisor(rst.getString(5));

					advisor.setStatus(rst.getInt(6));

					advisors.add(advisor);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(AdvisorFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return advisors;
			} // return the connection to the pool
		}

		return advisors;
	}

	public void removeAgaRelationship(Advisor advisor, Advisor aga) {
		Query nq = getEntityManager().createNativeQuery("delete from aga_advisors where aga = " + aga.getAdvisorIntId()
				+ " and advisor = " + advisor.getAdvisorIntId());
		nq.executeUpdate();
	}

	public void removeAllAgaRelationship(Advisor aga) {
		Query nq = getEntityManager()
				.createNativeQuery("delete from aga_advisors where aga = " + aga.getAdvisorIntId());
		nq.executeUpdate();
	}

	public void deleteLicense(License license) {

		Advisor advisor = license.getAdvisor();

		// Advisor.LicenseList
		if (advisor.getLicenseList() != null && !advisor.getLicenseList().isEmpty()) {

			if (advisor.getLicenseList().contains(license)) {
				advisor.getLicenseList().remove(license);
			}
			// persit change
			edit(advisor);
		}

		// Liability.LicenseList
		List<LicenseLiability> liabilities = advisor.getLicenseLiabilityList();

		if (liabilities != null && !liabilities.isEmpty()) {

			for (LicenseLiability liability : liabilities) {

				if (liability.getLicenseList().contains(license)) {
					liability.getLicenseList().remove(license);
					liabilityFacade.edit(liability);
				}

			}
		}

		// Advisor Link
		license.setAdvisor(null);

		// Company link
		license.setCompany(null);

		// remove license
		licenseFacade.remove(license);

		// NetStorage files must be removed as well
		// remember
	}

	public void deleteLicenseLiability(LicenseLiability liability, Advisor advisor) {

		// Remove Advisor from Liability.AdvisorList
		List<Advisor> advisors = liability.getAdvisorList();
		advisors.remove(advisor);

		// remove Liability from License
		if (advisor.getLicenseLiabilityList() != null && !advisor.getLicenseLiabilityList().isEmpty()) {

			Iterator<License> licenses = advisor.getLicenseList().iterator();

			while (licenses.hasNext()) {
				boolean found = false;

				License l = licenses.next();

				if (l.getLicenseLiability() == null) {
					continue;
				}

				if (l.getLicenseLiability().equals(liability)) {
					l.setLicenseLiability(null);
					found = true;
				}

				if (found) {
					licenseFacade.edit(l);
				}
			}
		}

		// Un Reference Liability from Advisor.LicenseList
		List<LicenseLiability> liabilities = advisor.getLicenseLiabilityList();
		liabilities.remove(liability);
		advisor.setLicenseLiabilityList(liabilities);

		// persit change
		edit(advisor);

		// Company link
		liability.setCompany(null);

		// remove license
		liabilityFacade.remove(liability);

		// NetStorage files must be removed as well
		// remember
	}

	public void deleteContractEft(ContractEft eft) {

		// Remove EFT from Advisor
		Advisor advisor = eft.getAdvisor();

		advisor.getContractEftList().remove(eft);
		edit(advisor);

		// remove EFT from Contract from License
		if (advisor.getContractSetupList() != null && !advisor.getContractSetupList().isEmpty()) {

			List<ContractSetup> css = advisor.getContractSetupList();

			for (ContractSetup cs : css) {

				Contract c = cs.getContract();

				if (c == null) {
					continue;
				}

				if (c.getContractEft() == null) {
					continue;
				}

				if (c.getContractEft().equals(eft)) {
					c.setContractEft(null);

					contractFacade.edit(c);
				}

			}
		}

		eft.setAdvisor(null);
		eft.setCompany(null);

		// remove license
		contractEftFacade.remove(eft);

		// NetStorage files must be removed as well
		// remember
	}

	public void deleteContractSetup(ContractSetup cs) {

		// Remove EFT from Advisor
		Advisor advisor = cs.getAdvisor();

		advisor.getContractSetupList().remove(cs);
		edit(advisor);

		cs.setContract(null);

		cs.setAdvisor(null);
		cs.setCompany(null);

		// remove license
		contractSetupFacade.remove(cs);

		// NetStorage files must be removed as well
		// remember
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByContractToolm(ProductSupplier ps) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select ab.* from Advisor ab INNER JOIN PROFILE_USERS pu on ab.advisor_int_id = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.advisor_int_id = cs.advisor where cs.PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId(),
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByContractToolFake(ProductSupplier ps) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct c.* from Advisor c  INNER JOIN CONTRACT_SETUP cs on c.advisor_int_id = cs.advisor where cs.PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId(),
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByContractToolFakeU(ProductSupplier ps) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select  distinct adv.*  advisor adv   INNER JOIN CONTRACT_SETUP cs on adv.advisor_int_id = cs.advisor where adv.aga_agency_id is null and cs.PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId(),
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByContractToolMgam(ProductSupplier ps) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select ab.* from Advisor ab INNER JOIN PROFILE_USERS pu on ab.advisor_int_id = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.advisor_int_id = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId(),
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByContractToolMgaFake(ProductSupplier ps) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct c.* from Advisor c  INNER JOIN CONTRACT_SETUP cs on c.advisor_int_id = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId() + " or cs.AGA_PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId(),
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}
	
	@SuppressWarnings("unchecked")
    public List<Advisor> findByContractToolMgaFakeU(ProductSupplier ps) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct adv.* from advisor adv   INNER JOIN CONTRACT_SETUP cs on adv.advisor_int_id = cs.advisor where  adv.aga_agency_id is null and (cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " or cs.AGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() +")", Advisor.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

	@SuppressWarnings("unchecked")
	public List<Advisor> findByOwnerContact(Users users, String notIn) {
		try {
			// System.out.println("select * from ADDRESS_BOOK where owner = " +
			// users.getUserIntId() + " and contact is not null and contact not in (" +
			// notIn + ")");
			Query nq = getEntityManager().createNativeQuery(
					"select * from advisor adv inner join contact con on adv.advisor_int_id = con.contact_int_id where adv.aga_agency_id is null and con.contact_int_id = "
							+ users.getUserIntId() + "  and con.contact_int_id not in (" + notIn + ")",
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

	// inner join advisor adv on adv.advisor_int_id = ab.contact
	// where adv.aga_agency_id is null
	@SuppressWarnings("unchecked")
	public List<Advisor> findByContractTool(ProductSupplier ps) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select DISTINCT ab.* from advisor ab INNER JOIN PROFILE_USERS pu on ab.advisor_int_id = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.advisor_int_id = cs.advisor where adv.aga_agency_id is null and cs.PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId(),
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Advisor> findByContractTool(ProductSupplier ps, String notIn) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select DISTINCT ab.* from Advisor ab INNER JOIN PROFILE_USERS pu on ab.advisor_int_id = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.advisor_int_id = cs.advisor where cs.PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId() + " and ab.contact not in (" + notIn + ")",
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

	// inner join advisor adv on adv.advisor_int_id = c.contact_int_id
	// where adv.aga_agency_id is null
	@SuppressWarnings("unchecked")
	public List<Advisor> findByContractToolMga(ProductSupplier ps) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select DISTINCT adv.* from Advisor adv INNER JOIN PROFILE_USERS pu on adv.advisor_int_id = pu.USERS INNER JOIN CONTRACT_SETUP cs on adv.advisor_int_id = cs.advisor where adv.aga_agency_id is null and cs.MGA_PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId(),
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<AddressBook> findByContractToolMga(ProductSupplier ps, String notIn) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select DISTINCT ab.* from Advisor ab INNER JOIN PROFILE_USERS pu on ab.advisor_int_id = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.advisor_int_id = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId() + " and ab.contact not in (" + notIn + ")",
					Advisor.class);
			return nq.getResultList();

		} catch (NoResultException e) {

			return new ArrayList<>();
		}
	}

}
