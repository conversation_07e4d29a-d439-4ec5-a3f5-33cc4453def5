/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.constant.Constants; 
import com.insurfact.skynet.entity.SegFundTransaction;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class SegFundTransactionFacade extends AbstractFacade<SegFundTransaction> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public SegFundTransactionFacade() {
		super(SegFundTransaction.class);
	}

	/**
	 * return the book transaction for a specific fund and fundId
	 *
	 * @param accountNumber
	 * @param mgmtCode
	 * @param fundId
	 * @return
	 */
	public List<SegFundTransaction> findBuyAndSellTransactionsByFundDetail(String accountNumber, String mgmtCode,
			String fundId) {
		TypedQuery<SegFundTransaction> nq = em.createQuery(
				"SELECT a FROM SegFundTransaction a WHERE   a.accountNumber = :accountNumberand and  (a.transactionType = :txTypeSellPredicate or  a.transactionType = :txTypeBuyPredicate)  and a.mgmtCompanyCode = :mgmtCompanyCode and a.fundId = fundId",
				SegFundTransaction.class);
		nq.setParameter("accountNumber", accountNumber);
		nq.setParameter("mgmtCompanyCode", mgmtCode);
		nq.setParameter("fundId", fundId);
		nq.setParameter("txTypeBuyPredicate", Constants.BUY_TX_TYPE);
		nq.setParameter("txTypeSellPredicate", Constants.SELL_TX_TYPE);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * 
		 * CriteriaQuery<SegFundTransaction> criteriaQuery =
		 * cBuilder.createQuery(SegFundTransaction.class); Root<SegFundTransaction> p =
		 * criteriaQuery.from(SegFundTransaction.class);
		 * 
		 * Predicate accountNumberPredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.accountNumber), accountNumber);
		 * Predicate mgmtCodePredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.mgmtCompanyCode), mgmtCode);
		 * Predicate fundIdPredicate = cBuilder.equal(p.get(SegFundTransaction_.fundId),
		 * fundId);
		 * 
		 * Predicate txTypeBuyPredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.transactionType),
		 * Constants.BUY_TX_TYPE); Predicate txTypeSellPredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.transactionType),
		 * Constants.SELL_TX_TYPE);
		 * 
		 * Predicate txTypePredicate = cBuilder.or(txTypeBuyPredicate,
		 * txTypeSellPredicate);
		 * 
		 * //order by tx date desc Order order = new
		 * OrderImpl(p.get(SegFundTransaction_.tradeDate), false);
		 * criteriaQuery.where(cBuilder.and(accountNumberPredicate, mgmtCodePredicate,
		 * fundIdPredicate, txTypePredicate)).orderBy(order);
		 * 
		 * TypedQuery<SegFundTransaction> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * return typeCodeQuery.getResultList();
		 */
	}

	/**
	 * return the book transaction for a specific fund and fundId
	 *
	 * @param accountNumber
	 * @param mgmtCode
	 * @param fundId
	 * @return
	 */
	public List<SegFundTransaction> findBuySellAndFeeTransactionsByFundDetail(String accountNumber, String mgmtCode,
			String fundId) {

		TypedQuery<SegFundTransaction> nq = em.createQuery(
				"SELECT a FROM SegFundTransaction a WHERE   a.accountNumber = :accountNumberand and  (a.transactionType = :txTypeFeePredicate or a.transactionType = :txTypeSellPredicate or  a.transactionType = :txTypeBuyPredicate)  and a.mgmtCompanyCode = :mgmtCompanyCode and a.fundId = fundId",
				SegFundTransaction.class);
		nq.setParameter("accountNumber", accountNumber);
		nq.setParameter("mgmtCompanyCode", mgmtCode);
		nq.setParameter("fundId", fundId);
		nq.setParameter("txTypeBuyPredicate", Constants.BUY_TX_TYPE);
		nq.setParameter("txTypeSellPredicate", Constants.SELL_TX_TYPE);
		nq.setParameter("txTypeFeePredicate", Constants.FEES_TX_TYPE);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * 
		 * CriteriaQuery<SegFundTransaction> criteriaQuery =
		 * cBuilder.createQuery(SegFundTransaction.class); Root<SegFundTransaction> p =
		 * criteriaQuery.from(SegFundTransaction.class);
		 * 
		 * Predicate accountNumberPredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.accountNumber), accountNumber);
		 * Predicate mgmtCodePredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.mgmtCompanyCode), mgmtCode);
		 * Predicate fundIdPredicate = cBuilder.equal(p.get(SegFundTransaction_.fundId),
		 * fundId);
		 * 
		 * Predicate txTypeBuyPredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.transactionType),
		 * Constants.BUY_TX_TYPE); Predicate txTypeSellPredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.transactionType),
		 * Constants.SELL_TX_TYPE); Predicate txTypeFeePredicate =
		 * cBuilder.equal(p.get(SegFundTransaction_.transactionType),
		 * Constants.FEES_TX_TYPE);
		 * 
		 * Predicate txTypePredicate = cBuilder.or(txTypeBuyPredicate,
		 * txTypeSellPredicate, txTypeFeePredicate);
		 * 
		 * // order by tx date desc Order order = new
		 * OrderImpl(p.get(SegFundTransaction_.tradeDate), true);
		 * criteriaQuery.where(cBuilder.and(accountNumberPredicate, mgmtCodePredicate,
		 * fundIdPredicate, txTypePredicate)) .orderBy(order);
		 * 
		 * TypedQuery<SegFundTransaction> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * return typeCodeQuery.getResultList();
		 */
	}

	/**
	 * return a List of SegFundTransaction based on the accountNumber, mgmtCode and
	 * fundId
	 *
	 * @param accountNumber
	 * @param mgmtCode
	 * @param fundId
	 * @return
	 */
	public List<SegFundTransaction> findByFundDetail(String accountNumber, String mgmtCode, String fundId) {
		EntityManager em = getEntityManager();

		List<SegFundTransaction> transactions = null;
		;

		try {
			TypedQuery<SegFundTransaction> nq = em.createNamedQuery("SegFundTransaction.findByFundDetail",
					SegFundTransaction.class);

			nq.setParameter("accountNumber", accountNumber);
			nq.setParameter("mgmtCompanyCode", mgmtCode);
			nq.setParameter("fundId", fundId);

			transactions = nq.getResultList();

			// deal with no price found...
			if ((transactions == null) || (transactions.isEmpty())) {
				System.out.println(
						"********* Cannot find any Transaction for :" + mgmtCode + "-" + accountNumber + "-" + fundId);
				return null;
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return transactions;
	}

	/**
	 * return a List of SegFundTransaction based on the accountNumber and mgmtCode
	 *
	 * @param accountNumber
	 * @param mgmtCode
	 * @return
	 */
	public List<SegFundTransaction> findByAccountDetail(String accountNumber, String mgmtCode) {
		EntityManager em = getEntityManager();

		List<SegFundTransaction> transactions = null;
		;

		try {
			TypedQuery<SegFundTransaction> nq = em.createNamedQuery("SegFundTransaction.findByAccountDetail",
					SegFundTransaction.class);

			nq.setParameter("accountNumber", accountNumber);
			nq.setParameter("mgmtCompanyCode", mgmtCode);

			transactions = nq.getResultList();

			// deal with no price found...
			if ((transactions == null) || (transactions.isEmpty())) {
				System.out.println("********* Cannot find any Transaction for :" + mgmtCode + "-" + accountNumber);
				return null;
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return transactions;
	}
}
