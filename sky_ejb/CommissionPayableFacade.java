/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.CommissionPayable;
import com.insurfact.skynet.entity.ProductSupplier;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CommissionPayableFacade extends AbstractFacade<CommissionPayable> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public CommissionPayableFacade() {
		super(CommissionPayable.class);
	}

	public List<CommissionPayable> findByStartAndEndDates(Date start, Date end, ProductSupplier prodSupp) {

		// EntityManager em = getEntityManager();

		String query = null;

		if (prodSupp == null) {
			query = "SELECT c FROM CommissionPayable c WHERE c.startDate >= :startDate AND c.endDate <= :endDate";
		} else {
			query = "SELECT c FROM CommissionPayable c WHERE c.startDate >= :startDate AND c.endDate <= :endDate AND c.commissionSource.productSupplier = :productSupplier";
		}

		List<CommissionPayable> payables = new ArrayList<CommissionPayable>();

		try {
			TypedQuery<CommissionPayable> nq = getEntityManager().createQuery(query, CommissionPayable.class);
			nq.setParameter("startDate", start);
			nq.setParameter("endDate", end);

			if (prodSupp != null) {
				nq.setParameter("productSupplier", prodSupp);
			}

			payables = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return payables;

	}
}
