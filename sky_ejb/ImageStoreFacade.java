/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.ImageStore;
import com.insurfact.skynet.entity.Types;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ImageStoreFacade extends AbstractFacade<ImageStore> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@EJB
	TypesManagerFacade typesManager;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ImageStoreFacade() {
		super(ImageStore.class);
	}

	/**
	 * Fetch all ImageStore entries that match the Reference ID, the owner ID and
	 * Type, the sub owner ID and Type and the masterCode provided.
	 * 
	 * @param referenceId
	 * @param ownerId
	 * @param ownerType
	 * @param subId
	 * @param subType
	 * @param masterCode
	 * @return a List of ImageStore objects
	 */
	public List<ImageStore> findImagesByOwnersAndMasterCode(Integer referenceId, Integer ownerId, Integer ownerType,
			Integer subId, Integer subType, String masterCode) {

		TypedQuery<ImageStore> nq = em.createQuery(
				"SELECT a FROM ImageStore a WHERE  a.referenceIntId = :referenceIntId and a.ownerIntId = :ownerIntId and a.ownerType = :ownerType and a.subIntId = :subIntId and a.subType = :subType and a.masterCode = :masterCode",
				ImageStore.class);
		nq.setParameter("referenceIntId", referenceId);
		nq.setParameter("ownerIntId", ownerId);
		nq.setParameter("ownerType", ownerType);
		nq.setParameter("subIntId", subId);
		nq.setParameter("subType", subType);
		nq.setParameter("masterCode", masterCode);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<ImageStore>
		 * criteriaQuery = cBuilder.createQuery(ImageStore.class); Root<ImageStore> p =
		 * criteriaQuery.from(ImageStore.class);
		 * 
		 * Predicate referencePredicate =
		 * cBuilder.equal(p.get(ImageStore_.referenceIntId), referenceId);
		 * 
		 * Predicate ownerPredicate = cBuilder.equal(p.get(ImageStore_.ownerIntId),
		 * ownerId); Predicate ownerTypePredicate =
		 * cBuilder.equal(p.get(ImageStore_.ownerType), ownerType);
		 * 
		 * Predicate subPredicate = cBuilder.equal(p.get(ImageStore_.subIntId), subId);
		 * Predicate subTypePredicate = cBuilder.equal(p.get(ImageStore_.subType),
		 * subType);
		 * 
		 * Predicate masterCodePredicate = cBuilder.equal(p.get(ImageStore_.masterCode),
		 * masterCode);
		 * 
		 * criteriaQuery.where(cBuilder.and( referencePredicate, ownerPredicate,
		 * ownerTypePredicate, subPredicate, subTypePredicate, masterCodePredicate));
		 * 
		 * TypedQuery<ImageStore> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * List<ImageStore> images = typeCodeQuery.getResultList();
		 * 
		 * return images;
		 */
	}

	/*
	 * Creates a new ImageStore : if an existing one with the same ImageType and
	 * same referenceId is found it will be delete form the DB and only the new
	 * Image is kept
	 */
	public void replace(ImageStore imageStore) {

		ImageStore existingImage = findImageByOwnersAndMasterCode(imageStore.getReferenceIntId(),
				imageStore.getSubIntId(), imageStore.getMasterCode());

		if (existingImage != null) {

			remove(existingImage);
		}

		Date now = Calendar.getInstance().getTime();
		imageStore.setCreationDate(now);
		imageStore.setLastModificationDate(now);

		create(imageStore);
	}

	public List<ImageStore> findImageByOwnerTypeAndOwnerID(Integer ownerType, Integer ownerID) {

		TypedQuery<ImageStore> nq = em.createQuery(
				"SELECT a FROM ImageStore a WHERE   a.ownerIntId = :ownerIntId and a.ownerType = :ownerType",
				ImageStore.class);
		nq.setParameter("ownerIntId", ownerID);
		nq.setParameter("ownerType", ownerType);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<ImageStore>
		 * criteriaQuery = cBuilder.createQuery(ImageStore.class); Root<ImageStore> p =
		 * criteriaQuery.from(ImageStore.class);
		 * 
		 * Predicate ownerTypePredicate = cBuilder.equal(p.get(ImageStore_.ownerType),
		 * ownerType); Predicate ownerIDPredicate =
		 * cBuilder.equal(p.get(ImageStore_.ownerIntId), ownerID);
		 * 
		 * criteriaQuery.where(cBuilder.and(ownerTypePredicate, ownerIDPredicate));
		 * 
		 * TypedQuery<ImageStore> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * return typeCodeQuery.getResultList();
		 */
	}

	public List<ImageStore> findImageByProperties(Integer referenceID, Integer ownerType, Integer ownerID,
			Integer subType) {

		TypedQuery<ImageStore> nq = em.createQuery(
				"SELECT a FROM ImageStore a WHERE  a.referenceIntId = :referenceIntId and a.ownerIntId = :ownerIntId and a.ownerType = :ownerType  and a.subType = :subType ",
				ImageStore.class);
		nq.setParameter("referenceIntId", referenceID);
		nq.setParameter("ownerIntId", ownerID);
		nq.setParameter("ownerType", ownerType);
		nq.setParameter("subType", subType);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<ImageStore>
		 * criteriaQuery = cBuilder.createQuery(ImageStore.class); Root<ImageStore> p =
		 * criteriaQuery.from(ImageStore.class);
		 * 
		 * Predicate ownerTypePredicate = cBuilder.equal(p.get(ImageStore_.ownerType),
		 * ownerType); Predicate ownerIDPredicate =
		 * cBuilder.equal(p.get(ImageStore_.ownerIntId), ownerID); Predicate
		 * referencePerdicate = cBuilder.equal(p.get(ImageStore_.referenceIntId),
		 * referenceID); Predicate subTypePredicate =
		 * cBuilder.equal(p.get(ImageStore_.subType), subType);
		 * 
		 * criteriaQuery.where(cBuilder.and(ownerTypePredicate, ownerIDPredicate,
		 * referencePerdicate, subTypePredicate));
		 * 
		 * TypedQuery<ImageStore> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * return typeCodeQuery.getResultList();
		 */
	}

	public void destroy(Integer referenceID, Integer ownerType, Integer ownerID, Integer subType) {

		List<ImageStore> images = findImageByProperties(referenceID, ownerType, ownerID, subType);

		Iterator<ImageStore> imageIter = images.iterator();

		while (imageIter.hasNext()) {

			ImageStore image = imageIter.next();
			imageIter.remove();

			em.remove(image);
		}
	}

	/**
	 * Fetch All the types for IMAGESTORE_TYPE Type_Class
	 * 
	 * @return
	 */
	public List<Types> findAllTypes() {

		return typesManager.findByTypeClassName("IMAGESTORE_TYPE");
	}

	private ImageStore findImageByOwnersAndMasterCode(int referenceIntId, Integer subIntId, String masterCode) {
		throw new UnsupportedOperationException("Not yet implemented");
	}
}