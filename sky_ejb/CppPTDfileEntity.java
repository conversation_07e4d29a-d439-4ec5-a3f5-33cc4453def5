/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Policy;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CppPTDfileEntity extends AbstractFacade<Policy> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    public EntityManager getEntityManager() {
        return em;
    }

    public CppPTDfileEntity() {
        super(Policy.class);
    }

    
    public Policy getPolicyByNumber(String polNum) {

        Policy policy = new Policy();
        
        try {
            Query nq = getEntityManager().createQuery("SELECT p FROM Policy p WHERE p.policyNumber = :policyNumber", Policy.class);

            nq.setParameter("policyNumber", polNum);

            policy = (Policy) nq.getSingleResult();
            
        } catch (NoResultException e) {
            return null;
        }

        return policy;
    }

    
}    
    


