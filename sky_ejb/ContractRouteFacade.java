/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.AccountManager;
import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.ContractRoute;
import com.insurfact.skynet.entity.ContractSetup;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ContractRouteFacade extends AbstractFacade<ContractRoute> {
    
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }
    
    public ContractRouteFacade(){
        super(ContractRoute.class);
    }

    @Override
    public void create(ContractRoute route) {
        super.create(route);
        
        ContractSetup routeCS = route.getContractSetup();
        if(routeCS != null) {
            routeCS.appendContractRoute(route);
            
            em.merge(routeCS);
        }
        
        Agency routeAgency = route.getAgency();
        if(routeAgency != null){
            routeAgency.appendContractRoute(route);
            
            em.merge(routeAgency);
        }
        
        AccountManager accountManager = route.getAccountManager();
        if(accountManager != null){
            accountManager.appendContractRoute(route);
            
            em.merge(accountManager);
        }
    }

    @Override
    public void remove(ContractRoute route) {

        ContractSetup routeCS = route.getContractSetup();
        if (routeCS != null) {
            routeCS.getContractRouteList().remove(route);

            em.merge(routeCS);
        }

        Agency routeAgency = route.getAgency();
        if (routeAgency != null) {
            routeAgency.getContractRouteList().remove(route);

            em.merge(routeAgency);
        }

        AccountManager accountManager = route.getAccountManager();
        if (accountManager != null) {
            accountManager.getContractRouteList().remove(route);

            em.merge(accountManager);
        }

        super.remove(route);
    }
}
