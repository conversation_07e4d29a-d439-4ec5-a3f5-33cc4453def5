/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Address;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AddressFacade extends AbstractFacade<Address> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public AddressFacade() {
        super(Address.class);
    }
    
    public Address quickEdit(Address entity) {

        try {
            em.merge(entity);

        } catch (Exception ex) {
            String msg = ex.getLocalizedMessage();
            System.err.println(msg);
           
        } 
  
        return entity;
    }
    
        
    public Address refresh(Address entity) {
        return find(entity.getAddressIntId());
    }
    
    public List<Address> findAllInRange(int start, int end){
        
        TypedQuery <Address>aq = em.createQuery("SELECT a FROM Address a where a.longitude is NULL AND  a.addressIntId >= :start AND a.addressIntId <= :end", Address.class);
        
        aq.setParameter("start", start);
        aq.setParameter("end", end);   
        
        return aq.getResultList();
            
    }
    
    
    public Address findAddressByDetail(String addressLine1, String addressLine2, String city, String postalCode, String careOf) {

        List<Address> addresses = null;
        
        // Convert to Lower case
        addressLine1 = addressLine1.toLowerCase();
        addressLine2 = addressLine2.toLowerCase();
        city = city.toLowerCase();
        postalCode = postalCode.toLowerCase();
        careOf = careOf.toLowerCase();
        
        String queryStr = "";
        
        if((addressLine2 !=null) && (!addressLine2.isEmpty()) )
           queryStr = "SELECT a FROM Address a WHERE LOWER(a.addressLine1) LIKE :addressLine1 and LOWER(a.addressLine2) LIKE :addressLine2 and LOWER(a.city) = :city and LOWER(a.postalCode) = :postalCode ";
        else
           queryStr = "SELECT a FROM Address a WHERE a.addressLine1 LIKE :addressLine1 and a.city = :city and a.postalCode = :postalCode ";
        
        if( (careOf != null) && (!careOf.isEmpty()) ){
            queryStr += " AND LOWER(a.careOf) LIKE :careOf";
        }
        
        try { 
            TypedQuery<Address> aq;
            aq = em.createQuery(queryStr, Address.class);

            aq.setParameter("addressLine1", "%"+addressLine1+"%");
            
            if((addressLine2 !=null) && (!addressLine2.isEmpty()) )
                aq.setParameter("addressLine2", "%"+addressLine2+"%");

            if( (careOf != null) && (!careOf.isEmpty()) )
                 aq.setParameter("careOf", "%"+careOf+"%");
            
            
            aq.setParameter("city",         city );
            aq.setParameter("postalCode",   postalCode );    
            
            addresses = aq.getResultList();
            
            if(addresses.size() >=1){
                
//                System.out.println("-----  AddressFacade, More than one ADDRESS was returned ="+addresses.size());
                return addresses.get(0);
            }
            
            return null;
        }
        catch(NoResultException e){
            return null;
        }    
        
    } 
}
