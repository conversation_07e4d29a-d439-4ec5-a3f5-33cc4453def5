/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Province;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ProvinceFacade extends AbstractFacade<Province> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ProvinceFacade() {
		super(Province.class);
	}

	public Province findByCode(String code) {
		if (code != null) {
			return em.find(Province.class, code);
		}
		return null;
	}

	public Province findByOldCode(Integer code) {
		if (code != null) {
			Query nq = getEntityManager().createNamedQuery("Province.findByOldProvinceCode", Province.class);
			nq.setParameter("oldCode", code);

			return (Province) nq.getSingleResult();

		}
		return null;
	}

	public List<Province> findByNameEn(String value) {
		try {
			TypedQuery<Province> nq = getEntityManager().createNamedQuery("Province.findByNameEn", Province.class);
			nq.setParameter("nameEn", "%" + value + "%");
			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Province> findByNameFr(String value) {
		try {
			TypedQuery<Province> nq = getEntityManager().createNamedQuery("Province.findByNameFr", Province.class);
			nq.setParameter("nameFr", "%" + value + "%");
			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Province> findByCountry(String countryCode) {
		try {
			TypedQuery<Province> nq = getEntityManager().createNamedQuery("Province.findByCountryCode", Province.class);
			nq.setParameter("countryCode", "%" + countryCode + "%");
			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Province> findProvinceByCountryOrEnName(String countryCode, String province) {
		if (countryCode == null) {

//            System.out.println("Looking for province by NameEn = " + province);

			return findByNameEn(province);
		}

		try {

//            System.out.println("Looking for province by NameEn = " + province + " and countryCode = " + countryCode);

			TypedQuery<Province> nq = getEntityManager().createNamedQuery("Province.findByCountryCodeAndNameEn",
					Province.class);
			nq.setParameter("countryCode", "%" + countryCode + "%");
			nq.setParameter("nameEn", "%" + province + "%");
			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}
}
