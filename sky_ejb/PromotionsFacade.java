/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.im.IMProduct;
import com.insurfact.skynet.entity.Promotions;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class PromotionsFacade extends AbstractFacade<Promotions> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Resource(lookup = "jdbc/Skytest")
	private DataSource ds;

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (SQLException e) {

			e.printStackTrace();
		}
		return null;
	}

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public PromotionsFacade() {
		super(Promotions.class);
	}

	public List<Promotions> findByCompanyId(int companyId) {
		TypedQuery<Promotions> nq = em.createNamedQuery("Promotions.findByCompanyId", Promotions.class);
		nq.setParameter("companyId", companyId);
		return nq.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Promotions> findByProductId(int productId) {
		Query nq = em.createNativeQuery(
				"select * from promotions p, promotions_product p2 where p.promotion_int_id = p2.promotion_id AND    ((SYSDATE BETWEEN p.START_DATE and p.END_DATE) or ( SYSDATE >= p.START_DATE and p.END_DATE is null  )) and p2.product_id = "
						+ productId,
				Promotions.class);
		return nq.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Promotions> findByProductIdToTest(int productId) {
		// System.out.println("promotion to test sql: " + "select * from promotions p,
		// promotions_product p2 where p.promotion_int_id = p2.promotion_id AND SYSDATE
		// < p.START_DATE and p2.product_id = " + productId);
		Query nq = em.createNativeQuery(
				"select * from promotions p, promotions_product p2 where p.promotion_int_id = p2.promotion_id AND  SYSDATE < p.START_DATE and p2.product_id = "
						+ productId,
				Promotions.class);
		return nq.getResultList();
	}

	public Promotions findByCode(String promoCode) {
		Query nq = em.createNamedQuery("Promotions.findByPromoCode", Promotions.class);
		nq.setParameter("promoCode", promoCode);
		if (nq.getResultList().isEmpty()) {
			return null;
		}
		return (Promotions) nq.getResultList().get(0);
	}

	public void fillManyToMany(Promotions p, List<IMProduct> products) {
		Query q = em.createNativeQuery("delete from promotions_product where promotion_id = " + p.getPromotionIntId());
		q.executeUpdate();
		for (IMProduct prod : products) {
			q = em.createNativeQuery("insert into promotions_product values (" + p.getPromotionIntId() + " , "
					+ prod.getProductid() + ")");
			q.executeUpdate();
		}
	}

	public void fillManyToManyProvince(Promotions p, List<String> province) {
		Query q = em.createNativeQuery("delete from PROVINCE_PROMOTION where promotion = " + p.getPromotionIntId());
		q.executeUpdate();
		for (String prov : province) {
			q = em.createNativeQuery(
					"insert into PROVINCE_PROMOTION values (" + prov + " , " + p.getPromotionIntId() + ")");
			q.executeUpdate();
		}
	}

	public List<Integer> findAllProduct(Promotions p) {

		ResultSet rst = null;
		List<Integer> types = new ArrayList<>();
		Connection connection = getConnection();
		if (connection == null) {
			return types;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select product_id from promotions_product where promotion_id = " + p.getPromotionIntId();

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return types;
			}

			while (rst.next()) {
				types.add(rst.getInt(1));
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(PromotionsFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return types;
		} // return the connection to the pool

		return types;
	}

	public List<Integer> findAllProductPromoTest() {

		ResultSet rst = null;
		List<Integer> types = new ArrayList<>();
		Connection connection = getConnection();
		if (connection == null) {
			return types;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select product_id from promotions_product p, promotions p2 where p.promotion_id = p2.promotion_int_id and SYSDATE < p2.START_DATE and p2.TEST_ZONE = 'Y'";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return types;
			}

			while (rst.next()) {
				types.add(rst.getInt(1));
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(PromotionsFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return types;
		} // return the connection to the pool

		return types;
	}

	public List<String> findAllProvince(Promotions p) {

		ResultSet rst = null;
		List<String> types = new ArrayList<>();
		Connection connection = getConnection();
		if (connection == null) {
			return types;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select province from PROVINCE_PROMOTION where promotion = " + p.getPromotionIntId();

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return types;
			}

			while (rst.next()) {
				types.add(rst.getString(1));
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(PromotionsFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return types;
		} // return the connection to the pool

		return types;
	}

}
