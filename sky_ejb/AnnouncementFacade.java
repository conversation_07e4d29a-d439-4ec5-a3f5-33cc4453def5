/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Announcement;
import com.insurfact.skynet.entity.ProductSupplier;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AnnouncementFacade extends AbstractFacade<Announcement> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public AnnouncementFacade() {
		super(Announcement.class);
	}

	public Announcement findAnnouncementById(Integer id) {
		Announcement content = null;
		if (id != null) {
			content = em.find(Announcement.class, id);
		}
		return content;
	}

	public List<Announcement> findByProfileTypes(Integer type) {

		TypedQuery<Announcement> query = em.createNamedQuery("Announcement.findByProfileTypes", Announcement.class);
		query.setParameter("profileTypes", type);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return null;
	}

	public List<Announcement> findByCategoryType(Integer type) {

		TypedQuery<Announcement> query = em.createNamedQuery("Announcement.findByCategoryType", Announcement.class);
		query.setParameter("categoryType", type);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return null;
	}

	public List<Announcement> findByTitleEn(String title) {

		TypedQuery<Announcement> query = em.createNamedQuery("Announcement.findByTitleEn", Announcement.class);
		query.setParameter("titleEn", title);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return null;
	}

	@SuppressWarnings("unchecked")
	public List<Announcement> findAllSorted() {

		Query query = em.createNativeQuery("select * from ANNOUNCEMENT order by START_DATE desc", Announcement.class);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return null;
	}

	@SuppressWarnings("unchecked")
	public List<Announcement> findAllBySupplier(int suppId) {

		Query query = em.createNativeQuery(
				"select * from ANNOUNCEMENT where COMPANY_ID = " + suppId + " order by START_DATE desc",
				Announcement.class);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return new ArrayList<>();
	}

	@SuppressWarnings("unchecked")
	public List<Announcement> findVipAnnouncements(int suppId) {

		Query query = em.createNativeQuery(
				"select * from ANNOUNCEMENT where PRODUCT_SUPPLIER = " + suppId
						+ " and SYSDATE BETWEEN START_DATE and END_DATE and VIP = 1 order by START_DATE desc",
				Announcement.class);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return new ArrayList<>();
	}

	@SuppressWarnings("unchecked")
	public List<Announcement> findAllBySupplierAndProfile(int suppId, int profileIntId) {

		Query query = em.createNativeQuery(
				"select a.* from ANNOUNCEMENT a INNER JOIN PROFILE_ANNOUNCEMENT pa "
						+ " on a.ANNOUNCEMENT_INT_ID = pa.ANNOUNCEMENT where SYSDATE > a.END_DATE and pa.PROFILE = "
						+ profileIntId + " and a.PRODUCT_SUPPLIER = " + suppId + " order by a.START_DATE desc",
				Announcement.class);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return new ArrayList<>();
	}

	@SuppressWarnings("unchecked")
	public List<Announcement> findAllBySupplierPromo(int suppId) {

		Query query = em.createNativeQuery("select * from ANNOUNCEMENT where PRODUCT_SUPPLIER = " + suppId
				+ " and category_type = 15 order by START_DATE desc", Announcement.class);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return null;
	}

	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findAllSupplierActiveByProfile(int profileIntId) {

		Query query = em.createNativeQuery(
				"select * from PRODUCT_SUPPLIER where PRODUCT_SUPPLIER_INT_ID in (select DISTINCT a.PRODUCT_SUPPLIER from ANNOUNCEMENT a INNER JOIN PROFILE_ANNOUNCEMENT pa "
						+ " on a.ANNOUNCEMENT_INT_ID = pa.ANNOUNCEMENT where SYSDATE > a.END_DATE and pa.PROFILE = "
						+ profileIntId + ")",
				ProductSupplier.class);

		List<ProductSupplier> suppliers = query.getResultList();

		if (suppliers != null && !suppliers.isEmpty()) {
			return suppliers;
		}

		return new ArrayList<>();
	}

	public List<Announcement> findByTitleFr(String title) {

		TypedQuery<Announcement> query = em.createNamedQuery("Announcement.findByTitleRf", Announcement.class);
		query.setParameter("titleFr", title);

		List<Announcement> contents = query.getResultList();

		if (contents != null && !contents.isEmpty()) {
			return contents;
		}

		return null;
	}
	/*
	 * public List<Announcement> findByCategory(Integer categoryId) { Query query =
	 * em.
	 * createQuery("SELECT w FROM OnlineHelp w WHERE w.onlineCategory = :category",
	 * OnlineHelp.class); query.setParameter("category", categoryId);
	 * 
	 * return query.getResultList(); }
	 * 
	 * public List<OnlineHelp> findByName(String name){
	 * 
	 * name = "%" + name.toUpperCase() + "%";
	 * 
	 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<OnlineHelp>
	 * criteriaQuery = cBuilder.createQuery(OnlineHelp.class); Root<OnlineHelp> p =
	 * criteriaQuery.from(OnlineHelp.class);
	 * 
	 * Predicate englishName =
	 * cBuilder.like(cBuilder.upper(p.get(OnlineHelp_.name)), name);
	 * 
	 * criteriaQuery.where(englishName); TypedQuery query =
	 * em.createQuery(criteriaQuery); return query.getResultList(); }
	 */

}
