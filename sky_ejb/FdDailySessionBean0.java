/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdDaily; 
import java.util.Calendar;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdDailySessionBean0 extends AbstractFacade<FdDaily> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdDailySessionBean0() {
		super(FdDaily.class);
	}

	public List<FdDaily> findDailysByDataKey(Integer fundDataKey) {

		// FdDaily.findYearlyByDailyFundatakey

		List<FdDaily> result = null;

		Calendar now = Calendar.getInstance();
		Calendar then = Calendar.getInstance();
		then.add(Calendar.YEAR, -1);

		try {
			TypedQuery<FdDaily> nq = getEntityManager().createNamedQuery("FdDaily.findYearlyByDailyFundatakey",
					FdDaily.class);
			nq.setParameter("dailyFundatakey", fundDataKey);
			nq.setParameter("startDate", then.getTime());
			nq.setParameter("endDate", now.getTime());
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			result = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		System.out.println("*****findYearlyDailysByDataKey : daily size : " + result.size());

		return result;

	}

	public List<FdDaily> findAllDailysByDataKey(Integer fundDataKey) {
		
		List<FdDaily> result = null;
		  

		try {
			TypedQuery<FdDaily> nq = getEntityManager().createNamedQuery("FdDaily.findAllByDailyFundatakey",
					FdDaily.class);
			nq.setParameter("dailyFundatakey", fundDataKey); 
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			result = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No results found...");
			return null;
		} 

		return result;

		/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<FdDaily> criteriaQuery = cBuilder.createQuery(FdDaily.class);
		Root<FdDaily> p = criteriaQuery.from(FdDaily.class);

		Predicate keyPredicate = cBuilder.equal(p.get(FdDaily_.dailyFundatakey), fundDataKey);

		Order order = new OrderImpl(p.get(FdDaily_.dailyFundataDate), true);
		criteriaQuery.where(cBuilder.and(keyPredicate)).orderBy(order);

		TypedQuery<FdDaily> typeCodeQuery = em.createQuery(criteriaQuery);

		List<FdDaily> result = typeCodeQuery.getResultList();

		System.out.println("*****findAllDailysByDataKey : daily size : " + result.size());

		return result;*/

	}
}
