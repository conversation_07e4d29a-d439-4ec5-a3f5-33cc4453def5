/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.cannex.entity.CannexInterestRateNewRrif;
import com.insurfact.cannex.entity.CannexProductNew; 

import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class RRIFInterestRateFacadeNew extends AbstractFacade<CannexInterestRateNewRrif> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public RRIFInterestRateFacadeNew() {
        super(CannexInterestRateNewRrif.class);
    }

    public List<CannexInterestRateNewRrif> findRrifRates(CannexProductNew product, Integer termTypeId) {

    	TypedQuery<CannexInterestRateNewRrif> query = em.createQuery("SELECT r FROM CannexInterestRateNewRrif r WHERE r.rrifProduct = :product AND r.rrifProduct.termTypeId = :duration  ORDER BY r.interestRateValue ASC", CannexInterestRateNewRrif.class);
        query.setParameter("product", product);
        query.setParameter("duration", termTypeId);

        return query.getResultList();
    }

    public void deleteRates(CannexProductNew termProduct, Integer termTypeId) {
        List<CannexInterestRateNewRrif> rates = findRrifRates(termProduct, termTypeId);

        if (rates != null && !rates.isEmpty()) {

            for (CannexInterestRateNewRrif rate : rates) {
                em.remove(rate);
            }
        }
    }
    
    public void safeManualProduct(CannexInterestRateNewRrif rate, Integer product){
        Query query = em.createNativeQuery("update CANNEX_INTEREST_RATE_NEW_RRIF set CANNEX_PRODUCT = "+product+" WHERE INTEREST_RATE_INT_ID = "+rate.getInterestRateIntId()) ;  
        query.executeUpdate();
    }

}
