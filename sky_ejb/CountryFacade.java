/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Country;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CountryFacade extends AbstractFacade<Country> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public CountryFacade() {
        super(Country.class);
    }

     public Country findByCountryCode(String countryCode) {
        Country country = null;
        try {
            Query nq = getEntityManager().createNamedQuery("Country.findByCountryCode", Country.class);
            nq.setParameter("countryCode",  countryCode );

            country = (Country) nq.getSingleResult();

        } catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        }

        return country;
    }

     
    public List<Country> findByNameFr(String countryName) {
        List<Country> countries = null;
        try {
        	TypedQuery<Country> nq = getEntityManager().createNamedQuery("Country.findByCountryNameFr", Country.class);
            nq.setParameter("countryNameFr", "%" + countryName + "%");

            countries = nq.getResultList();

        } catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        }

        return countries;
    }

    public List<Country> findByNameEn(String countryName) {
        List<Country> countries = null;
        try {
        	TypedQuery<Country> nq = getEntityManager().createNamedQuery("Country.findByCountryNameEn", Country.class);
            nq.setParameter("countryNameEn", "%" + countryName + "%");

            countries = nq.getResultList();

        } catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        }

        return countries;
    }

    public List<Country> findByAlpha(String countryCode) {

//        System.out.println("looking for country with Alpha2 code = " + countryCode);

        List<Country> countries = null;
        try {
        	TypedQuery<Country> nq = getEntityManager().createNamedQuery("Country.findByCountryAlpha2", Country.class);
            nq.setParameter("alpha2", "%" + countryCode + "%");

            countries = nq.getResultList();

        } catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        }

        return countries;
    }
}
