/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdCategory;
import com.insurfact.fundata.entity.FdFundFlexSearchPy;
import com.insurfact.fundata.entity.FdPerformance;
import com.insurfact.fundata.entity.FflexFinal;
import com.insurfact.fundata.entity.FflexFinalMonthly;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdPerformanceSessionBean extends AbstractFacade<FdPerformance> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdPerformanceSessionBean() {
		super(FdPerformance.class);
	}

	public List<FdPerformance> findTopsByCategory(int rows, int highLow, int column, FdCategory category,
			Locale locale) {

		try {

			TypedQuery<FdPerformance> nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "p.perfYtdReturn";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}
			}

			if (highLow == 0) {
				sort = " DESC";
			} else {
				sort = " ASC";
			}

//            String sql = "SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND p. ORDER BY " + order + sort ;
//            nq = em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdFund.class);
			nq = em.createQuery("SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND " + order
					+ " IS NOT NULL ORDER BY " + order + sort, FdPerformance.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");
//            nq = em.createQuery("SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdPerformance.class);

			nq.setParameter("category", category);

			if (rows != 0) {
				nq.setMaxResults(rows);
			}

//            List<FdFund> funds = new ArrayList<>(); 
//         
//            List<FdPerformance> perfs = 
//            
//            for(FdPerformance performance: perfs){
//                FdFund fund = performance.getFdFund();
//                
//                fund.getPerformance();
//                
//                funds.add(fund);
//            }
			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

	public List<FdPerformance> findTop10ByCategory(int highLow, int column, FdCategory category, Locale locale) {

		try {

			TypedQuery<FdPerformance> nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "p.perfYtdReturn";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}
			}

			if (highLow == 0) {
				sort = " DESC";
			} else {
				sort = " ASC";
			}

//            String sql = "SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND p. ORDER BY " + order + sort ;
//            nq = em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdFund.class);
			nq = em.createQuery("SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND " + order
					+ " IS NOT NULL ORDER BY " + order + sort, FdPerformance.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");
//            nq = em.createQuery("SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdPerformance.class);

			nq.setParameter("category", category);

			nq.setMaxResults(10);

//            List<FdFund> funds = new ArrayList<>(); 
//         
//            List<FdPerformance> perfs = 
//            
//            for(FdPerformance performance: perfs){
//                FdFund fund = performance.getFdFund();
//                
//                fund.getPerformance();
//                
//                funds.add(fund);
//            }
			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

	public List<FdPerformance> findTopByCategory(int highLow, int column, FdCategory category) {

		try {

			TypedQuery<FdPerformance> nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "p.perfYtdReturn";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 2: { // 2 year
				order = "p.perfTwoYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 4: { // 4 year
				order = "p.perfFourYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}

			case 10: { // Inception
				order = "p.perfInceptionReturn";
				break;
			}

			case 11: { // 1 Month
				order = "p.perfOneMonthReturn";
				break;
			}
			case 13: { // 3 Month
				order = "p.perfThreeMonthReturn";
				break;
			}

			}

			if (highLow == 0) {
				sort = " DESC";
			} else {
				sort = " ASC";
			}

//            String sql = "SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND p. ORDER BY " + order + sort ;
//            nq = em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdFund.class);
			nq = em.createQuery("SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND " + order
					+ " IS NOT NULL ORDER BY " + order + sort, FdPerformance.class);
			nq.setParameter("category", category);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			nq.setMaxResults(1);

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

	public List<FdPerformance> findTopOverall(int highLow, int column) {

		try {

			TypedQuery<FdPerformance> nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "p.perfYtdReturn";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 2: { // 2 year
				order = "p.perfTwoYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 4: { // 4 year
				order = "p.perfFourYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}

			case 10: { // Inception
				order = "p.perfInceptionReturn";
				break;
			}

			case 11: { // 1 Month
				order = "p.perfOneMonthReturn";
				break;
			}
			case 13: { // 3 Month
				order = "p.perfThreeMonthReturn";
				break;
			}

			}

			if (highLow == 0) {
				sort = " DESC";
			} else {
				sort = " ASC";
			}

			nq = em.createQuery("SELECT p FROM FdPerformance p   " + " WHERE " + order + " IS NOT NULL " + " ORDER BY "
					+ order + sort, FdPerformance.class);
			nq.setMaxResults(1);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	// came from the Dashboard to the new py
	@SuppressWarnings("unchecked")
	public List<FdFundFlexSearchPy> findTopOverallPY(int highLow, int column) {

		Calendar cal = Calendar.getInstance();
		cal.setTime(Calendar.getInstance().getTime());
		cal.add(Calendar.DATE, -30);
		// Date dateBefore30Days = cal.getTime();
		String dateToCheck = String.valueOf(cal.get(Calendar.DATE)) + "-" + String.valueOf(cal.get(Calendar.MONTH) + 1)
				+ "-" + String.valueOf(cal.get(Calendar.YEAR) + 1900);

		try {

			Query nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "perf_Ytd_Return";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 2: { // 2 year
				order = "p.perfTwoYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 4: { // 4 year
				order = "p.perfFourYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}

			case 10: { // Inception
				order = "p.perfInceptionReturn";
				break;
			}

			case 11: { // 1 Month
				order = "p.perfOneMonthReturn";
				break;
			}
			case 13: { // 3 Month
				order = "p.perfThreeMonthReturn";
				break;
			}

			}

			if (highLow == 0) {
				sort = " DESC";
			} else {
				sort = " ASC";
			}

			System.out.println("OOOJJJJJJJJJOOOOOOOO");
			System.out.println("SELECT * FROM FD_FUND_FLEX_SEARCH_PY   " + " WHERE " + order
					+ " IS NOT NULL AND daily_Price_Navps_Date > TO_DATE('" + dateToCheck + "' , 'DD-MM-YYYY')"
					+ " ORDER BY " + order + sort);

			nq = em.createNativeQuery("SELECT * FROM FD_FUND_FLEX_SEARCH_PY   " + " WHERE " + order
					+ " IS NOT NULL AND daily_Price_Navps_Date > TO_DATE('" + dateToCheck + "' , 'DD-MM-YYYY')"
					+ " ORDER BY " + order + sort, FdFundFlexSearchPy.class);
			nq.setMaxResults(1);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	public FdPerformance getByFundataKeyPY(Integer fundatakey) {

		EntityManager em = getEntityManager();

		try {
			Query nq = em.createNamedQuery("FdPerformance.findByPerfFundatakey", FdPerformance.class);
			nq.setParameter("perfFundatakey", fundatakey);
			FdPerformance fdPerf = (FdPerformance) nq.getSingleResult();

			return fdPerf;

		} catch (NoResultException e) {
			return null;
		}
	}

	// for the Best YTD (%) By Category
	public List<FflexFinal> findTop5perfYTD() {
		try {

			TypedQuery<FflexFinal> nq = null;
			String order = "p.perfYtdReturn";
			String sort = " DESC";
			nq = em.createQuery("SELECT p FROM FflexFinal p  " + " WHERE " + order
					+ " IS NOT NULL AND p.fundEnabled = 'Y' " + " ORDER BY " + order + sort, FflexFinal.class);
			nq.setMaxResults(5);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<FflexFinalMonthly> findTop5OMR() {
		try {

			TypedQuery<FflexFinalMonthly> nq = null;
			String order = "p.perfOneMonthReturn";
			String sort = " DESC";
			nq = em.createQuery("SELECT p FROM FflexFinalMonthly p  " + " WHERE " + order
					+ " IS NOT NULL AND p.fundEnabled = 'Y' " + " ORDER BY " + order + sort, FflexFinalMonthly.class);
			nq.setMaxResults(5);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<FflexFinal> findTopPerfYTD() {
		try {

			TypedQuery<FflexFinal> nq = null;
			String order = "p.perfYtdReturn";
			String sort = " DESC";
			nq = em.createQuery("SELECT p FROM FflexFinal p  " + " WHERE " + order
					+ " IS NOT NULL AND p.fundEnabled = 'Y' " + " ORDER BY " + order + sort, FflexFinal.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<FflexFinalMonthly> findTopOMR() {
		try {

			TypedQuery<FflexFinalMonthly> nq = null;
			String order = "p.perfOneMonthReturn";
			String sort = " DESC";
			nq = em.createQuery("SELECT p FROM FflexFinalMonthly p  " + " WHERE " + order
					+ " IS NOT NULL AND p.fundEnabled = 'Y' " + " ORDER BY " + order + sort, FflexFinalMonthly.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	public FflexFinal getFlexFinalById(long FunDataKey) {
		try {

			Query nq = null;
			// String order = "p.perfOneMonthReturn";
			// String sort = " DESC";
			nq = em.createQuery("SELECT p FROM FflexFinal p  " + " WHERE p.fundFundatakey = " + FunDataKey
					+ " AND p.fundEnabled = 'Y' ", FflexFinal.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			if (!nq.getResultList().isEmpty()) {
				return (FflexFinal) nq.getResultList().get(0);
			} else {
				return null;
			}
		} catch (NoResultException e) {
			return null;
		}
	}

	public FflexFinalMonthly getFlexFinalMontlyById(long FunDataKey) {
		try {

			TypedQuery<FflexFinalMonthly> nq = null;
			// String order = "p.perfOneMonthReturn";
			// String sort = " DESC";
			nq = em.createQuery("SELECT p FROM FflexFinalMonthly p  " + " WHERE p.fundFundatakey = " + FunDataKey
					+ " AND p.fundEnabled = 'Y' ", FflexFinalMonthly.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return (FflexFinalMonthly) nq.getResultList().get(0);

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<FdFundFlexSearchPy> getDailyMovers(int topCant, int descAsc, int col) {
		try {

			TypedQuery<FdFundFlexSearchPy> nq = null;
			String order = "p.priceChangeFromPreviousDay";
			String sort = " DESC";
			if (descAsc == 1) {
				sort = " ASC";
			}
			if (col == 1) {
				order = "p.perfOneDayReturn";
			}
			nq = em.createQuery("SELECT p FROM FdFundFlexSearchPy p  " + " WHERE " + order + " IS NOT NULL "
					+ " ORDER BY " + order + sort, FdFundFlexSearchPy.class);
			nq.setMaxResults(topCant);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	public List<FdFundFlexSearchPy> getDailyMoversView(int descAsc, int col) {
		try {

			Query nq = null;
			String order = "a.PRICE_CHANGE_FROM_PREVIOUS_DAY";
			if (col == 1) {
				order = "a.PERF_ONE_DAY_RETURN";
			}
			String sort = " DESC";
			if (descAsc == 1) {
				sort = " ASC";
			}

			String sql = "(SELECT a.* FROM FD_FUND_FLEX_SEARCH_PY a INNER JOIN FD_FUND_TYPE_PY c  "
					+ "ON c.TYPE_KEY = a.FD_TYPE INNER JOIN FD_GEN_FUND_INFO_PY d on a.FUND_FUNDATAKEY "
					+ "= d.FUND_FUNDATAKEY INNER JOIN FD_FUND_QUARTILES_PY F0 on a.FUND_FUNDATAKEY = "
					+ "F0.FUNDATAKEY INNER JOIN FD_BENCHMARK_PY b on F0.ASSOC_CAN_INDEX_KEY = "
					+ "b.ASSOC_CAN_INDEX_ID WHERE " + order + " IS NOT NULL  ORDER BY " + order + sort
					+ " FETCH FIRST 5 ROWS ONLY)";

			nq = em.createNativeQuery(sql, FdFundFlexSearchPy.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	public List<FdFundFlexSearchPy> getDailyMoversViewBottom(int col) {
		try {

			Query nq = null;
			String order = "a.PRICE_CHANGE_FROM_PREVIOUS_DAY";
			if (col == 1) {
				order = "a.PERF_ONE_DAY_RETURN";
			}

			String sql = "(SELECT a.* FROM FD_FUND_FLEX_SEARCH_PY a INNER JOIN FD_FUND_TYPE_PY c  "
					+ "ON c.TYPE_KEY = a.FD_TYPE INNER JOIN FD_GEN_FUND_INFO_PY d on a.FUND_FUNDATAKEY "
					+ "= d.FUND_FUNDATAKEY INNER JOIN FD_FUND_QUARTILES_PY F0 on a.FUND_FUNDATAKEY = "
					+ "F0.FUNDATAKEY INNER JOIN FD_BENCHMARK_PY b on F0.ASSOC_CAN_INDEX_KEY = "
					+ "b.ASSOC_CAN_INDEX_ID WHERE " + order + " IS NOT NULL  ORDER BY " + order
					+ " DESC FETCH FIRST 5 ROWS ONLY)";

			nq = em.createNativeQuery(sql, FdFundFlexSearchPy.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	/**
	 * Return the count of all entries for FdPerformnce
	 *
	 * @return
	 */
	@SuppressWarnings("unused")
	private Long getCountOfPerfEntries() {

		CriteriaBuilder builder = em.getCriteriaBuilder();
		CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
		countQuery.select(builder.count(countQuery.from(FdPerformance.class)));
		return em.createQuery(countQuery).getSingleResult();
	}

}
