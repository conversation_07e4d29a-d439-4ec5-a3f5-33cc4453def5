/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Users;
import com.insurfact.skynet.entity.UsersInvoice;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class UsersInvoiceFacade extends AbstractFacade<UsersInvoice> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public UsersInvoiceFacade() {
		super(UsersInvoice.class);
	}

	public List<UsersInvoice> findByUsers(Users users) {

		List<UsersInvoice> list = new ArrayList<>();

//        System.out.println("**** findByUsers() : users="+users.getUserIntId() );
		try {
			TypedQuery<UsersInvoice> nq = em.createNamedQuery("UsersInvoice.findByUsers", UsersInvoice.class);

			nq.setParameter("users", users);

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;

	}

	public void editUsersInvoice(UsersInvoice usersInvoice) {
		em.merge(usersInvoice);
	}

	public int getNextInvoiceNo() {
		Query q = em.createNativeQuery("SELECT INVOICE_SEQUENCE.nextval from DUAL");
		BigDecimal result = (BigDecimal) q.getSingleResult();
		return result.intValue();
	}

}
