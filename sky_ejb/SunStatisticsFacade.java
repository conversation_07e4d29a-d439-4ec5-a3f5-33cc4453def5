/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

/**
 *
 * <AUTHOR>
 */
import com.insurfact.avue.navigation.agency.admin.sunlife.PolicyRatio;
import com.insurfact.avue.navigation.agency.admin.sunlife.PolicyTop;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import javax.sql.DataSource;

@Stateless
public class SunStatisticsFacade {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Resource(lookup = "jdbc/Skytest")
	private DataSource ds;

	protected EntityManager getEntityManager() {
		return em;
	}

	public SunStatisticsFacade() {
		Logger.getLogger(SunStatisticsFacade.class.getName()).log(Level.SEVERE, null, "Constructor");
	}

	private Connection getConnection() {

		try {
			return ds.getConnection();
		} catch (SQLException ex) {
			Logger.getLogger(SunStatisticsFacade.class.getName()).log(Level.SEVERE, null, ex);
		}
		return null;
	}

	public List<PolicyTop> findTopPolicyByCity() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(DISTINCT POLICY_NUMBER) as total, ad.CITY from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS GROUP BY ad.CITY ORDER BY total DESC ";
			// todo String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, CITY FROM
			// SUN_ALL_POLICY_WITH_ADVISORT GROUP BY CITY ORDER BY total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCity(rst.getString(2));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public List<PolicyTop> findTopPolicyByCity(String financialCenter, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, ad.CITY from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS where adCon.BRANCH in "
						+ financialCenter + " GROUP BY ad.CITY ORDER BY total DESC ";
			} else {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, ad.CITY from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS where adCon.CONTACT_INT_ID in "
						+ financialCenter + " GROUP BY ad.CITY ORDER BY total DESC ";
			}
			// todo String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, CITY FROM
			// SUN_ALL_POLICY_WITH_ADVISORT GROUP BY CITY ORDER BY total DESC ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCity(rst.getString(2));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public List<PolicyTop> findTopPolicyByProvince() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select count(DISTINCT POLICY_NUMBER) as total, prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE GROUP BY prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR ORDER BY total DESC";
			// todo String sql = " SELECT count(DISTINCT a.POLICYNUMBER) as total,
			// b.PROVINCE_CODE_EN, b.PROVINCE_CODE_FR, b.NAME_EN, b.NAME_FR FROM
			// SUN_ALL_POLICY_WITH_ADVISORT a INNER JOIN PROVINCE b on a.NAME_EN = b.NAME_EN
			// GROUP BY b.PROVINCE_CODE_EN, b.PROVINCE_CODE_FR, b.NAME_EN, b.NAME_FR ORDER
			// BY total DESC ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCodeProvEn(rst.getString(2));
				policy.setCodeProvFr(rst.getString(3));
				policy.setNameProvEn(rst.getString(4));
				policy.setNameProvFr(rst.getString(5));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public List<PolicyTop> findTopPolicyByProvince(String financialCenter, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = "select count(DISTINCT POLICY_NUMBER) as total, prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE  where adCon.BRANCH in "
						+ financialCenter
						+ "  GROUP BY prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR ORDER BY total DESC";
			} else {
				sql = "select count(DISTINCT POLICY_NUMBER) as total, prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE  where adCon.CONTACT_INT_ID in "
						+ financialCenter
						+ "  GROUP BY prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR ORDER BY total DESC";
			}
			// todo String sql = " SELECT count(DISTINCT a.POLICYNUMBER) as total,
			// b.PROVINCE_CODE_EN, b.PROVINCE_CODE_FR, b.NAME_EN, b.NAME_FR FROM
			// SUN_ALL_POLICY_WITH_ADVISORT a INNER JOIN PROVINCE b on a.NAME_EN = b.NAME_EN
			// GROUP BY b.PROVINCE_CODE_EN, b.PROVINCE_CODE_FR, b.NAME_EN, b.NAME_FR ORDER
			// BY total DESC ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCodeProvEn(rst.getString(2));
				policy.setCodeProvFr(rst.getString(3));
				policy.setNameProvEn(rst.getString(4));
				policy.setNameProvFr(rst.getString(5));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public List<PolicyTop> findTopPolicyByCityToogle() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(DISTINCT POLICY_NUMBER) as total, ad.CITY from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS where p.POLICY_STATUS = 11 GROUP BY ad.CITY ORDER BY total DESC ";
			// todo String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, CITY FROM
			// SUN_ALL_POLICY_WITH_ADVISORT where POLICYSTATUS = 6 GROUP BY CITY ORDER BY
			// total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCity(rst.getString(2));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public List<PolicyTop> findTopPolicyByCityToogle(String financialCenter, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, ad.CITY from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID  INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS where p.POLICY_STATUS = 11  and adCon.BRANCH in "
						+ financialCenter + "  GROUP BY ad.CITY ORDER BY total DESC ";
			} else {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, ad.CITY from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID  INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS where p.POLICY_STATUS = 11  and adCon.CONTACT_INT_ID in "
						+ financialCenter + "  GROUP BY ad.CITY ORDER BY total DESC ";
			}
			// todo String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, CITY FROM
			// SUN_ALL_POLICY_WITH_ADVISORT where POLICYSTATUS = 6 GROUP BY CITY ORDER BY
			// total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCity(rst.getString(2));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public List<PolicyTop> findTopPolicyByProvinceToogle() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE where p.POLICY_STATUS = 11 GROUP BY prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR ORDER BY total DESC ";
			// todo String sql = " SELECT count(DISTINCT a.POLICYNUMBER) as total,
			// b.PROVINCE_CODE_EN, b.PROVINCE_CODE_FR, b.NAME_EN, b.NAME_FR FROM
			// SUN_ALL_POLICY_WITH_ADVISORT a INNER JOIN PROVINCE b on a.NAME_EN = b.NAME_EN
			// where a.POLICYSTATUS = 6 GROUP BY b.PROVINCE_CODE_EN, b.PROVINCE_CODE_FR,
			// b.NAME_EN, b.NAME_FR ORDER BY total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCodeProvEn(rst.getString(2));
				policy.setCodeProvFr(rst.getString(3));
				policy.setNameProvEn(rst.getString(4));
				policy.setNameProvFr(rst.getString(5));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;

	}

	public List<PolicyTop> findTopPolicyByProvinceToogle(String financialCenter, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE where p.POLICY_STATUS = 11  and adCon.BRANCH in "
						+ financialCenter
						+ "  GROUP BY prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR ORDER BY total DESC ";
			} else {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR INNER JOIN CONTACT con on con.CONTACT_INT_ID = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE where p.POLICY_STATUS = 11  and adCon.CONTACT_INT_ID in "
						+ financialCenter
						+ "  GROUP BY prov.PROVINCE_CODE_EN, prov.PROVINCE_CODE_FR, prov.NAME_EN, prov.NAME_FR ORDER BY total DESC ";
			}
			// todo String sql = " SELECT count(DISTINCT a.POLICYNUMBER) as total,
			// b.PROVINCE_CODE_EN, b.PROVINCE_CODE_FR, b.NAME_EN, b.NAME_FR FROM
			// SUN_ALL_POLICY_WITH_ADVISORT a INNER JOIN PROVINCE b on a.NAME_EN = b.NAME_EN
			// where a.POLICYSTATUS = 6 GROUP BY b.PROVINCE_CODE_EN, b.PROVINCE_CODE_FR,
			// b.NAME_EN, b.NAME_FR ORDER BY total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCodeProvEn(rst.getString(2));
				policy.setCodeProvFr(rst.getString(3));
				policy.setNameProvEn(rst.getString(4));
				policy.setNameProvFr(rst.getString(5));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;

	}

	public List<PolicyTop> findTopPolicyByCityYear(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT count(DISTINCT POLICYNUMBER) as total,"
					+ " CITY FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE ISSUEDATE"
					+ " BETWEEN TO_DATE(2005, 'YYYY') and TO_DATE(" + year + ", "
					+ "'YYYY')  GROUP BY CITY ORDER BY total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setCity(rst.getString(2));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public List<PolicyTop> findTopPolicyByProvinceYear(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyTop> policyTopList = new ArrayList<>();
		if (connection == null) {
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, NAME_EN, NAME_FR FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE ISSUEDATE BETWEEN TO_DATE('01-01-"
					+ year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1)
					+ "', 'DD-MM-YYYY') GROUP BY NAME_EN, NAME_FR ORDER BY total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return policyTopList;
			}

			while (rst.next()) {

				PolicyTop policy = new PolicyTop();

				policy.setTotal(rst.getInt(1));
				policy.setNameProvEn(rst.getString(2));
				policy.setNameProvFr(rst.getString(3));

				policyTopList.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public PolicyTop findTopPolicyByProvinceYear(int year, String prov) {

		ResultSet rst = null;

		Connection connection = getConnection();

		PolicyTop policyTopList = new PolicyTop();
		if (connection == null) {
			policyTopList.setTotal(0);
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN"
					+ " account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID"
					+ " = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID"
					+ " = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID"
					+ " = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE WHERE p.ISSUE_DATE "
					+ " BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1)
					+ "', 'DD-MM-YYYY') and NAME_EN = '" + prov + "' "
					+ " GROUP BY prov.NAME_EN, prov.NAME_FR ORDER BY total DESC  ";
			// todo String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, NAME_EN,
			// NAME_FR FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE ISSUEDATE BETWEEN
			// TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1)
			// + "', 'DD-MM-YYYY') and NAME_EN = '" + prov + "' GROUP BY NAME_EN, NAME_FR
			// ORDER BY total DESC ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				policyTopList.setTotal(0);
				connection.close();
				return policyTopList;
			}

			if (rst.next()) {
				policyTopList.setTotal(rst.getInt(1));
				policyTopList.setNameProvEn(rst.getString(2));
				policyTopList.setNameProvFr(rst.getString(3));

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public PolicyTop findTopPolicyByProvinceYear(int year, String prov, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		PolicyTop policyTopList = new PolicyTop();
		if (connection == null) {
			policyTopList.setTotal(0);
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql;
			if (mode != 5) {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN"
						+ " account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID"
						+ " = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID"
						+ " = cli.CLIENT_INT_ID INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID"
						+ " = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE WHERE adCon.BRANCH in "
						+ financialCenters + " and p.ISSUE_DATE " + " BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') and NAME_EN = '"
						+ prov + "' " + " GROUP BY prov.NAME_EN, prov.NAME_FR ORDER BY total DESC  ";
			} else {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN"
						+ " account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID"
						+ " = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID"
						+ " = cli.CLIENT_INT_ID INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID"
						+ " = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters + " and p.ISSUE_DATE " + " BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') and NAME_EN = '"
						+ prov + "' " + " GROUP BY prov.NAME_EN, prov.NAME_FR ORDER BY total DESC  ";
			}
			// todo String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, NAME_EN,
			// NAME_FR FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE ISSUEDATE BETWEEN
			// TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1)
			// + "', 'DD-MM-YYYY') and NAME_EN = '" + prov + "' GROUP BY NAME_EN, NAME_FR
			// ORDER BY total DESC ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				policyTopList.setTotal(0);
				connection.close();
				return policyTopList;
			}

			if (rst.next()) {
				policyTopList.setTotal(rst.getInt(1));
				policyTopList.setNameProvEn(rst.getString(2));
				policyTopList.setNameProvFr(rst.getString(3));

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public PolicyTop findTopPolicyByProvinceYearToogle(int year, String prov) {

		ResultSet rst = null;

		Connection connection = getConnection();

		PolicyTop policyTopList = new PolicyTop();
		if (connection == null) {
			policyTopList.setTotal(0);
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN"
					+ " account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID"
					+ " = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID"
					+ " = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID"
					+ " = ca.ADDRESS INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE WHERE p.ISSUE_DATE "
					+ " BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1)
					+ "', 'DD-MM-YYYY') and p.POLICY_STATUS = 11 and NAME_EN = '" + prov + "' "
					+ " GROUP BY prov.NAME_EN, prov.NAME_FR ORDER BY total DESC  ";
			// todo String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, NAME_EN,
			// NAME_FR FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE POLICYSTATUS = 6 and
			// ISSUEDATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and
			// TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') and NAME_EN = '" + city +
			// "' GROUP BY NAME_EN, NAME_FR ORDER BY total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				policyTopList.setTotal(0);
				connection.close();
				return policyTopList;
			}

			if (rst.next()) {
				policyTopList.setTotal(rst.getInt(1));
				policyTopList.setNameProvEn(rst.getString(2));
				policyTopList.setNameProvFr(rst.getString(3));

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	public PolicyTop findTopPolicyByProvinceYearToogle(int year, String prov, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		PolicyTop policyTopList = new PolicyTop();
		if (connection == null) {
			policyTopList.setTotal(0);
			return policyTopList;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN"
						+ " account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID"
						+ " = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID"
						+ " = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID"
						+ " = ca.ADDRESS INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE WHERE adCon.BRANCH in "
						+ financialCenters + " and  p.ISSUE_DATE " + " BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1)
						+ "', 'DD-MM-YYYY') and p.POLICY_STATUS = 11 and NAME_EN = '" + prov + "' "
						+ " GROUP BY prov.NAME_EN, prov.NAME_FR ORDER BY total DESC  ";
			} else {
				sql = " select count(DISTINCT POLICY_NUMBER) as total, prov.NAME_EN, prov.NAME_FR from policy p INNER JOIN"
						+ " account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID"
						+ " = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN CONTACT con on con.CONTACT_INT_ID"
						+ " = cli.CLIENT_INT_ID INNER JOIN CONTACT_ADDRESS ca on con.CONTACT_INT_ID = ca.CONTACT INNER JOIN ADDRESS ad on ad.ADDRESS_INT_ID"
						+ " = ca.ADDRESS INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR INNER JOIN PROVINCE prov on prov.PROVINCE_CODE = ad.PROVINCE WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters + " and p.ISSUE_DATE " + " BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1)
						+ "', 'DD-MM-YYYY') and p.POLICY_STATUS = 11 and NAME_EN = '" + prov + "' "
						+ " GROUP BY prov.NAME_EN, prov.NAME_FR ORDER BY total DESC  ";
			}
			// todo String sql = " SELECT count(DISTINCT POLICYNUMBER) as total, NAME_EN,
			// NAME_FR FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE POLICYSTATUS = 6 and
			// ISSUEDATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and
			// TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') and NAME_EN = '" + city +
			// "' GROUP BY NAME_EN, NAME_FR ORDER BY total DESC ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				policyTopList.setTotal(0);
				connection.close();
				return policyTopList;
			}

			if (rst.next()) {
				policyTopList.setTotal(rst.getInt(1));
				policyTopList.setNameProvEn(rst.getString(2));
				policyTopList.setNameProvFr(rst.getString(3));

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return policyTopList;
		} // return the connection to the pool

		return policyTopList;
	}

	///////////////////// PREMIUM///////////////////////////////////
	public Double findPremiumPolicyByYear(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID   WHERE p.ISSUE_DATE BETWEEN TO_DATE('01-01-"
					+ year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			// TODO String sql = " SELECT AVG(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where ISSUEDATE BETWEEN TO_DATE(
			// '01-01-" + year + "', 'DD-MM-YY' ) AND to_date( '01-01-" + (year + 1) + "',
			// 'DD-MM-YY' ) )";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				result = 0.0;
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findPremiumPolicyByYear(int year, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.BRANCH in "
						+ financialCenters + " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			} else {
				sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters + " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			}
			// TODO String sql = " SELECT AVG(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where ISSUEDATE BETWEEN TO_DATE(
			// '01-01-" + year + "', 'DD-MM-YY' ) AND to_date( '01-01-" + (year + 1) + "',
			// 'DD-MM-YY' ) )";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				result = 0.0;
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findPremiumPolicyByYearToogle(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID   WHERE p.POLICY_STATUS = 11 and p.ISSUE_DATE BETWEEN TO_DATE('01-01-"
					+ year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			// TODO String sql = " SELECT AVG(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where POLICYSTATUS = 6 and
			// ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND to_date(
			// '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findPremiumPolicyByYearToogle(int year, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR   WHERE adCon.BRANCH in "
						+ financialCenters + " and p.POLICY_STATUS = 11 and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			} else {
				sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR   WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters + " and p.POLICY_STATUS = 11 and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			}
			// TODO String sql = " SELECT AVG(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where POLICYSTATUS = 6 and
			// ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND to_date(
			// '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findPremiumPolicyFaceByYear(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID   WHERE p.POLICY_STATUS = 11 and p.ISSUE_DATE BETWEEN TO_DATE('01-01-"
					+ year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			// TODO String sql = " SELECT AVG(FACEAMT) from ( SELECT DISTINCT POLICYNUMBER,
			// FACEAMT FROM SUN_ALL_POLICY_WITH_ADVISORT where ISSUEDATE BETWEEN TO_DATE(
			// '01-01-" + year + "', 'DD-MM-YY' ) AND to_date( '01-01-" + (year + 1) + "',
			// 'DD-MM-YY' ))";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findPremiumPolicyFaceByYear(int year, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE  adCon.BRANCH in "
						+ financialCenters + " and p.POLICY_STATUS = 11 and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			} else {
				sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE  adCon.CONTACT_INT_ID in "
						+ financialCenters + " and p.POLICY_STATUS = 11 and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			}
			// TODO String sql = " SELECT AVG(FACEAMT) from ( SELECT DISTINCT POLICYNUMBER,
			// FACEAMT FROM SUN_ALL_POLICY_WITH_ADVISORT where ISSUEDATE BETWEEN TO_DATE(
			// '01-01-" + year + "', 'DD-MM-YY' ) AND to_date( '01-01-" + (year + 1) + "',
			// 'DD-MM-YY' ))";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findPremiumPolicyFaceByYearToogle(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID   WHERE p.ISSUE_DATE BETWEEN TO_DATE('01-01-"
					+ year + "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			// TODO String sql = " SELECT AVG(FACEAMT) from ( SELECT DISTINCT POLICYNUMBER,
			// FACEAMT FROM SUN_ALL_POLICY_WITH_ADVISORT where POLICYSTATUS = 6 and
			// ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND to_date(
			// '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findPremiumPolicyFaceByYearToogle(int year, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.BRANCH in "
						+ financialCenters + " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			} else {
				sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters + " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
						+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			}
			// TODO String sql = " SELECT AVG(FACEAMT) from ( SELECT DISTINCT POLICYNUMBER,
			// FACEAMT FROM SUN_ALL_POLICY_WITH_ADVISORT where POLICYSTATUS = 6 and
			// ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND to_date(
			// '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findTermOfPermByYearAnnPrem(int year, boolean term) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}

			String sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID   WHERE ps.TERMEXP_DATE is "
					+ termPart + " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
					+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			// TODO String sql = " SELECT AVG(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where TERM_OR_PERM = '" + term + "'
			// and ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND
			// to_date( '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findTermOfPermByYearAnnPrem(int year, boolean term, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}
			String sql;
			if (mode != 5) {
				sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.BRANCH in "
						+ financialCenters + " and ps.TERMEXP_DATE is " + termPart
						+ " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-"
						+ (year + 1) + "', 'DD-MM-YYYY') ) ";
			} else {
				sql = "  SELECT AVG(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters + " and ps.TERMEXP_DATE is " + termPart
						+ " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-"
						+ (year + 1) + "', 'DD-MM-YYYY') ) ";
			}
			// TODO String sql = " SELECT AVG(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where TERM_OR_PERM = '" + term + "'
			// and ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND
			// to_date( '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findTermOfPermByYearFaceAmt(int year, boolean term) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}

			String sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID   WHERE ps.TERMEXP_DATE is "
					+ termPart + " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
					+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			// TODO String sql = " SELECT AVG(FACEAMT) from ( SELECT DISTINCT POLICYNUMBER,
			// FACEAMT FROM SUN_ALL_POLICY_WITH_ADVISORT where TERM_OR_PERM = '" + term + "'
			// and ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND
			// to_date( '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findTermOfPermByYearFaceAmt(int year, boolean term, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}
			String sql;
			if (mode != 5) {
				sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.BRANCH in "
						+ financialCenters + " and ps.TERMEXP_DATE is " + termPart
						+ " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-"
						+ (year + 1) + "', 'DD-MM-YYYY') ) ";
			} else {
				sql = "  SELECT AVG(FACEAMT) from (select DISTINCT p.POLICY_NUMBER, ps.FACEAMT from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters + " and ps.TERMEXP_DATE is " + termPart
						+ " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-"
						+ (year + 1) + "', 'DD-MM-YYYY') ) ";
			}
			// TODO String sql = " SELECT AVG(FACEAMT) from ( SELECT DISTINCT POLICYNUMBER,
			// FACEAMT FROM SUN_ALL_POLICY_WITH_ADVISORT where TERM_OR_PERM = '" + term + "'
			// and ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND
			// to_date( '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findSumTermOfPermByYearAnnPrem(int year, boolean term) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}

			String sql = "  SELECT SUM(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID   WHERE ps.TERMEXP_DATE is "
					+ termPart + " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
					+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			// TODO String sql = " SELECT SUM(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where TERM_OR_PERM = '" + term + "'
			// and ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND
			// to_date( '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findSumTermOfPermByYearAnnPrem(int year, boolean term, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}
			String sql;
			if (mode != 5) {
				sql = "  SELECT SUM(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR   WHERE adCon.BRANCH in "
						+ financialCenters + " and ps.TERMEXP_DATE is " + termPart
						+ " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-"
						+ (year + 1) + "', 'DD-MM-YYYY') ) ";
			} else {
				sql = "  SELECT SUM(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR   WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters + " and ps.TERMEXP_DATE is " + termPart
						+ " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-"
						+ (year + 1) + "', 'DD-MM-YYYY') ) ";
			}
			// TODO String sql = " SELECT SUM(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where TERM_OR_PERM = '" + term + "'
			// and ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND
			// to_date( '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public List<Integer> findAllCompanies() {
		List<Integer> resultList = new ArrayList<>();
		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return resultList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "  select DISTINCT pl.COMPANY_ID  from policy p INNER JOIN POLICY_LIFE pl on p.POLICY_INT_ID = pl.POLICY_LIFE_INT_ID";

			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {
				resultList.add(rst.getInt(1));
			}

			sql = "  select DISTINCT pd.COMPANY_ID  from policy p INNER JOIN POLICY_DI pd on p.POLICY_INT_ID = pd.POLICY_DI_INT_ID";

			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {
				if (!resultList.contains(rst.getInt(1))) {
					resultList.add(rst.getInt(1));
				}
			}

			sql = "  select DISTINCT pt.COMPANY_ID  from policy p INNER JOIN POLICY_TRAVEL pt on p.POLICY_INT_ID = pt.POLICY_TRAVEL_INT_ID";

			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {
				if (!resultList.contains(rst.getInt(1))) {
					resultList.add(rst.getInt(1));
				}
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return resultList;
		} // return the connection to the pool

		return resultList;
	}

	public List<Integer> findAllCompanies(String financialCenters, int mode) {
		List<Integer> resultList = new ArrayList<>();
		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return resultList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql;
			if (mode != 5) {
				sql = "  select DISTINCT pl.COMPANY_ID  from policy p INNER JOIN POLICY_LIFE pl on p.POLICY_INT_ID = pl.POLICY_LIFE_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.BRANCH in "
						+ financialCenters;
			} else {
				sql = "  select DISTINCT pl.COMPANY_ID  from policy p INNER JOIN POLICY_LIFE pl on p.POLICY_INT_ID = pl.POLICY_LIFE_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters;
			}

			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {
				resultList.add(rst.getInt(1));
			}

			if (mode != 5) {
				sql = "  select DISTINCT pd.COMPANY_ID  from policy p INNER JOIN POLICY_DI pd on p.POLICY_INT_ID = pd.POLICY_DI_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.BRANCH in "
						+ financialCenters;
			} else {
				sql = "  select DISTINCT pd.COMPANY_ID  from policy p INNER JOIN POLICY_DI pd on p.POLICY_INT_ID = pd.POLICY_DI_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR WHERE adCon.CONTACT_INT_ID in "
						+ financialCenters;
			}

			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {
				if (!resultList.contains(rst.getInt(1))) {
					resultList.add(rst.getInt(1));
				}
			}

			sql = "  select DISTINCT pt.COMPANY_ID  from policy p INNER JOIN POLICY_TRAVEL pt on p.POLICY_INT_ID = pt.POLICY_TRAVEL_INT_ID";

			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {
				if (!resultList.contains(rst.getInt(1))) {
					resultList.add(rst.getInt(1));
				}
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return resultList;
		} // return the connection to the pool

		return resultList;
	}

	public Double findFilteredCompanies(int year, int companyId, String type) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String joinPart;
			String wherePart;
			if (type.equalsIgnoreCase("Life")) {
				joinPart = "INNER JOIN POLICY_LIFE pl on p.POLICY_INT_ID = pl.POLICY_LIFE_INT_ID";
				wherePart = " pl.COMPANY_ID = " + companyId;
			} else if (type.equalsIgnoreCase("DI")) {
				joinPart = "INNER JOIN POLICY_DI pd on p.POLICY_INT_ID = pd.POLICY_DI_INT_ID";
				wherePart = " pd.COMPANY_ID = " + companyId;
			} else {
				joinPart = "INNER JOIN POLICY_TRAVEL pt on p.POLICY_INT_ID = pt.POLICY_TRAVEL_INT_ID";
				wherePart = " pt.COMPANY_ID = " + companyId;
			}

			String sql = "  SELECT SUM(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID "
					+ joinPart + "  WHERE  " + wherePart + " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year
					+ "', 'DD-MM-YYYY') and TO_DATE('01-01-" + (year + 1) + "', 'DD-MM-YYYY') ) ";
			// TODO String sql = " SELECT SUM(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where COMPANYID ='" + companyId +
			// "' and ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND
			// to_date( '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public Double findFilteredCompanies(int year, int companyId, String type, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Double result = 0.0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String joinPart;
			String wherePart;
			if (type.equalsIgnoreCase("Life")) {
				joinPart = "INNER JOIN POLICY_LIFE pl on p.POLICY_INT_ID = pl.POLICY_LIFE_INT_ID";
				wherePart = " pl.COMPANY_ID = " + companyId;
			} else if (type.equalsIgnoreCase("DI")) {
				joinPart = "INNER JOIN POLICY_DI pd on p.POLICY_INT_ID = pd.POLICY_DI_INT_ID";
				wherePart = " pd.COMPANY_ID = " + companyId;
			} else {
				joinPart = "INNER JOIN POLICY_TRAVEL pt on p.POLICY_INT_ID = pt.POLICY_TRAVEL_INT_ID";
				wherePart = " pt.COMPANY_ID = " + companyId;
			}
			String sql;
			if (mode != 5) {
				sql = "  SELECT SUM(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  "
						+ joinPart + "  WHERE  adCon.BRANCH in " + financialCenters + " and " + wherePart
						+ " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-"
						+ (year + 1) + "', 'DD-MM-YYYY') ) ";
			} else {
				sql = "  SELECT SUM(ANNPREM) from (select DISTINCT p.POLICY_NUMBER, ps.ANNPREM from policy p INNER JOIN POLICY_SETTLING ps on p.POLICY_INT_ID = ps.POLICY_SETTLING_INT_ID INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR "
						+ joinPart + "  WHERE adCon.CONTACT_INT_ID in " + financialCenters + " and  " + wherePart
						+ " and p.ISSUE_DATE BETWEEN TO_DATE('01-01-" + year + "', 'DD-MM-YYYY') and TO_DATE('01-01-"
						+ (year + 1) + "', 'DD-MM-YYYY') ) ";
			}
			// TODO String sql = " SELECT SUM(ANNPREM) from ( SELECT DISTINCT POLICYNUMBER,
			// ANNPREM FROM SUN_ALL_POLICY_WITH_ADVISORT where COMPANYID ='" + companyId +
			// "' and ISSUEDATE BETWEEN TO_DATE( '01-01-" + year + "', 'DD-MM-YY' ) AND
			// to_date( '01-01-" + (year + 1) + "', 'DD-MM-YY' )) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getDouble(1);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSold(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\"  ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSold(int year, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\"  WHERE BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE CONTACT_INT_ID in "
						+ financialCenters;
			}

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalAnnPremByYearSold(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\"  ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalAnnPremByYearSold(int year, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\"  WHERE BRANCH in "
						+ financialCenters;
			} else {
				sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE CONTACT_INT_ID in "
						+ financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatement(int year, String statement, char state) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= '" + state
					+ "' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatement(int year, String statement, char state, String financialCenters,
			int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= '" + state
						+ "'  and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= '" + state
						+ "' and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementGender(int year, String statement, int state) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= " + state;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementGender(int year, String statement, int state,
			String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= " + state
						+ " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= " + state
						+ " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementTermOrPerm(int year, String statement, String state) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + " is " + state;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementTermOrPerm(int year, String statement, String state,
			String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + " is " + state
						+ " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + " is " + state
						+ " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldActive(int year, int monthPlus, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M" + monthPlus
						+ " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M" + monthPlus
						+ " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldActive(int year, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M"
					+ monthPlus + " ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldActive0(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where POLICY_STATUS = 11 ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldActive0(int year, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where POLICY_STATUS = 11 and BRANCH in "
						+ financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year
						+ "\" where POLICY_STATUS = 11 and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldAnnprem(int year, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M"
					+ monthPlus + "  ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldAnnprem(int year, int monthPlus, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M"
						+ monthPlus + " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M"
						+ monthPlus + " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldAnnprem0(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\" where POLICY_STATUS = 11  ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldAnnprem0(int year, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year
						+ "\" where POLICY_STATUS = 11  and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year
						+ "\" where POLICY_STATUS = 11  and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public List<PolicyRatio> findAllPolicyByYearSold(int year) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyRatio> result = new ArrayList<>();
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			PolicyRatio policy;

			String sql = " SELECT * FROM \"CHECK_YEAR_POLICY_" + year + "\" ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			while (rst.next()) {
				policy = new PolicyRatio();
				policy.setPoliciNumber(rst.getString(1));
				policy.setIssueDate(rst.getDate(2));
				policy.setStatusDate(rst.getDate(3));
				policy.setPoliciStatus(rst.getInt(4));
				policy.setAnnPrem(rst.getInt(5));

				if (rst.getDate(6).after(policy.getStatusDate())) {
					policy.setM13('N');
				} else {
					policy.setM13('Y');
				}

				if (rst.getDate(7).after(policy.getStatusDate())) {
					policy.setM25('N');
				} else {
					policy.setM25('Y');
				}

				if (rst.getDate(8).after(policy.getStatusDate())) {
					policy.setM37('N');
				} else {
					policy.setM37('Y');
				}

				if (rst.getDate(9).after(policy.getStatusDate())) {
					policy.setM49('N');
				} else {
					policy.setM49('Y');
				}

				if (rst.getDate(10).after(policy.getStatusDate())) {
					policy.setM61('N');
				} else {
					policy.setM61('Y');
				}

				result.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldSex(int year, int sex, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M"
					+ monthPlus + " and gender = " + sex;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldSex(int year, int sex, int monthPlus, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M" + monthPlus
						+ " and gender = " + sex + " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M" + monthPlus
						+ " and gender = " + sex + " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldSex0(int year, int sex) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year
					+ "\" where POLICY_STATUS = 11 and gender = " + sex;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldSex0(int year, int sex, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where POLICY_STATUS = 11 and gender = "
						+ sex + " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where POLICY_STATUS = 11 and gender = "
						+ sex + " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldTermOrPer(int year, boolean term, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M"
					+ monthPlus + " and TERMEXP_DATE is " + termPart;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldTermOrPer(int year, boolean term, int monthPlus, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M" + monthPlus
						+ " and TERMEXP_DATE is " + termPart + " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M" + monthPlus
						+ " and TERMEXP_DATE is " + termPart + " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldTermOrPer0(int year, boolean term) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}
			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year
					+ "\" where POLICY_STATUS = 11 and TERMEXP_DATE is " + termPart;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldTermOrPer0(int year, boolean term, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year
						+ "\" where POLICY_STATUS = 11 and TERMEXP_DATE is " + termPart + " and BRANCH in "
						+ financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year
						+ "\" where POLICY_STATUS = 11 and TERMEXP_DATE is " + termPart + " and CONTACT_INT_ID in "
						+ financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldSmoker(int year, char smoker, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M"
					+ monthPlus + " and SMOKER = '" + smoker + "' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldSmoker(int year, char smoker, int monthPlus, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M" + monthPlus
						+ " and SMOKER = '" + smoker + "' and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where (STATUS_DATE+30) > M" + monthPlus
						+ " and SMOKER = '" + smoker + "' and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldSmoker0(int year, char smoker) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year
					+ "\" where POLICY_STATUS = 11 and SMOKER = '" + smoker + "' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findPolicyByYearSoldSmoker0(int year, char smoker, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where POLICY_STATUS = 11 and SMOKER = '"
						+ smoker + "' and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" where POLICY_STATUS = 11 and SMOKER = '"
						+ smoker + "' and CONTACT_INT_ID in " + financialCenters;
			}

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public List<PolicyRatio> findAllPolicyByYearSoldSex(int year, int sex) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyRatio> result = new ArrayList<>();
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			PolicyRatio policy;

			String sql = " SELECT * FROM \"CHECK_YEAR_POLICY_" + year + "\" where  gender = " + sex;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				ptStmt.close();
				connection.close();
				return result;
			}

			while (rst.next()) {
				policy = new PolicyRatio();
				policy.setPoliciNumber(rst.getString(1));
				policy.setIssueDate(rst.getDate(2));
				policy.setStatusDate(rst.getDate(3));
				policy.setPoliciStatus(rst.getInt(4));
				policy.setAnnPrem(rst.getInt(5));

				if (rst.getDate(6).after(policy.getStatusDate())) {
					policy.setM13('N');
				} else {
					policy.setM13('Y');
				}

				if (rst.getDate(7).after(policy.getStatusDate())) {
					policy.setM25('N');
				} else {
					policy.setM25('Y');
				}

				if (rst.getDate(8).after(policy.getStatusDate())) {
					policy.setM37('N');
				} else {
					policy.setM37('Y');
				}

				if (rst.getDate(9).after(policy.getStatusDate())) {
					policy.setM49('N');
				} else {
					policy.setM49('Y');
				}

				if (rst.getDate(10).after(policy.getStatusDate())) {
					policy.setM61('N');
				} else {
					policy.setM61('Y');
				}

				policy.setSelection(rst.getString(11));

				result.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public List<PolicyRatio> findAllPolicyByYearSoldTerm(int year, boolean term) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyRatio> result = new ArrayList<>();
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String termPart;
			if (term) {
				termPart = " not null ";
			} else {
				termPart = " null ";
			}
			PolicyRatio policy;

			String sql = " SELECT * FROM \"CHECK_YEAR_POLICY_" + year + "\" where  TERMEXP_DATE is " + termPart;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			while (rst.next()) {
				policy = new PolicyRatio();
				policy.setPoliciNumber(rst.getString(1));
				policy.setIssueDate(rst.getDate(2));
				policy.setStatusDate(rst.getDate(3));
				policy.setPoliciStatus(rst.getInt(4));
				policy.setAnnPrem(rst.getInt(5));

				if (rst.getDate(6).after(policy.getStatusDate())) {
					policy.setM13('N');
				} else {
					policy.setM13('Y');
				}

				if (rst.getDate(7).after(policy.getStatusDate())) {
					policy.setM25('N');
				} else {
					policy.setM25('Y');
				}

				if (rst.getDate(8).after(policy.getStatusDate())) {
					policy.setM37('N');
				} else {
					policy.setM37('Y');
				}

				if (rst.getDate(9).after(policy.getStatusDate())) {
					policy.setM49('N');
				} else {
					policy.setM49('Y');
				}

				if (rst.getDate(10).after(policy.getStatusDate())) {
					policy.setM61('N');
				} else {
					policy.setM61('Y');
				}
				if (term) {
					policy.setSelection("T");
				} else {
					policy.setSelection("P");
				}
				result.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public List<PolicyRatio> findAllPolicyByYearSoldSmoker(int year, int smoker) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<PolicyRatio> result = new ArrayList<>();
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			PolicyRatio policy;

			String sql = " SELECT * FROM \"CHECK_YEAR_POLICY_" + year + "\" where  SMOKER = " + smoker;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			while (rst.next()) {
				policy = new PolicyRatio();
				policy.setPoliciNumber(rst.getString(1));
				policy.setIssueDate(rst.getDate(2));
				policy.setStatusDate(rst.getDate(3));
				policy.setPoliciStatus(rst.getInt(4));
				policy.setAnnPrem(rst.getInt(5));

				if (rst.getDate(6).after(policy.getStatusDate())) {
					policy.setM13('N');
				} else {
					policy.setM13('Y');
				}

				if (rst.getDate(7).after(policy.getStatusDate())) {
					policy.setM25('N');
				} else {
					policy.setM25('Y');
				}

				if (rst.getDate(8).after(policy.getStatusDate())) {
					policy.setM37('N');
				} else {
					policy.setM37('Y');
				}

				if (rst.getDate(9).after(policy.getStatusDate())) {
					policy.setM49('N');
				} else {
					policy.setM49('Y');
				}

				if (rst.getDate(10).after(policy.getStatusDate())) {
					policy.setM61('N');
				} else {
					policy.setM61('Y');
				}

				policy.setSelection(rst.getString(13));

				result.add(policy);

			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	////////// testing totals
	public int findTotalPoliciesByYearSoldForList(int year, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE M0 >= M" + monthPlus + " ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalPoliciesByYearSoldForList(int year, int monthPlus, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE M0 >= M" + monthPlus
						+ " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE M0 >= M" + monthPlus
						+ " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalAnnPremByYearSoldForList(int year, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\"WHERE M0 >= M" + monthPlus + "  ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalAnnPremByYearSoldForList(int year, int monthPlus, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\"WHERE M0 >= M" + monthPlus
						+ "  and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT SUM(ANNPREM) FROM \"CHECK_YEAR_POLICY_" + year + "\"WHERE M0 >= M" + monthPlus
						+ "  and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementForList(int year, String statement, char state, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= '" + state
					+ "' and M0 >= M" + monthPlus + " ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementForList(int year, String statement, char state, int monthPlus,
			String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= '" + state
						+ "' and M0 >= M" + monthPlus + " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= '" + state
						+ "' and M0 >= M" + monthPlus + " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementForListTermOrPerm(int year, String statement, String state,
			int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + " is " + state
					+ " and M0 >= M" + monthPlus + " ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementForListTermOrPerm(int year, String statement, String state,
			int monthPlus, String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + " is " + state
						+ " and M0 >= M" + monthPlus + " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + " is " + state
						+ " and M0 >= M" + monthPlus + " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementForListGender(int year, String statement, int state, int monthPlus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= " + state
					+ " and M0 >= M" + monthPlus + " ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

	public int findTotalpoliciesByYearSoldStatementForListGender(int year, String statement, int state, int monthPlus,
			String financialCenters, int mode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		int result = 0;
		if (connection == null) {
			return result;
		}
		try (Statement ptStmt = connection.createStatement();) {
			String sql;
			if (mode != 5) {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= " + state
						+ " and M0 >= M" + monthPlus + " and BRANCH in " + financialCenters;
			} else {
				sql = " SELECT COUNT(*) FROM \"CHECK_YEAR_POLICY_" + year + "\" WHERE " + statement + "= " + state
						+ " and M0 >= M" + monthPlus + " and CONTACT_INT_ID in " + financialCenters;
			}
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return result;
			}

			if (rst.next()) {
				result = rst.getInt(1);
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return result;
		} // return the connection to the pool

		return result;
	}

}
