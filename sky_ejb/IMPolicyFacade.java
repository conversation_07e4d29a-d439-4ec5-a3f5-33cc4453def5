/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.im.IMAddress;
import com.insurfact.im.IMAgent;
import com.insurfact.im.IMClient;
import com.insurfact.im.IMLifePolicy;
import com.insurfact.im.IMPhone;
import com.insurfact.im.IMPolicySettling;
import com.insurfact.skynet.entity.Branch; 

/**
 *
 * <AUTHOR>
 */
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class IMPolicyFacade {

    @Resource(lookup = "jdbc/Skytest")
    private javax.sql.DataSource orainsDS;

    @EJB
    protected BranchFacade branchFacade;

    @EJB
    private TypesManagerFacade typesManager;

    public IMPolicyFacade() {

    }

    private Connection getConnection() {
        try {
            return orainsDS.getConnection();
        } catch (Exception e) {

            e.printStackTrace();
        }
        return null;
    }

    public IMLifePolicy getPolicyByNumberTerminatedView(String polNum) {

        ResultSet rst = null;
        Connection connection = getConnection();

        IMLifePolicy polDet = null;
        IMPolicySettling polSett = null;
        IMClient polClient = null;
        IMAddress cliAddr = null;
        IMPhone cliPhone = null;
        IMAgent polAgent = null;

        if (connection == null) {
            return null;
        }

        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select FIRSTNAME, LASTNAME,         "
                    + //1-2
                    "        SEX,    BIRTHDAY,            "
                    + //3-4
                    "        NAMEENGLISH, NAMEFRENCH,     "
                    + //5-6  //company name
                    "        ISSUEDATE, STATUSDATE,       "
                    + //7-8
                    "        FACEAMT,  MODPREM,           "
                    + //9-10
                    "        ANNPREM, POLICYNUMBER,       "
                    + //11-12
                    "        ENGLISH_NAME, FRENCH_NAME,   "
                    + //13-14
                    "        AREACODE, PHONENUMBER,       "
                    + //15-16 	
                    "        ADDRESSLINE1, ADDRESSLINE2,  "
                    + //17-18 		  
                    "        CITY,     PROVSTATE,         "
                    + //19-20 	
                    "        NAME_EN,  NAME_FR,           "
                    + //21-22 	province name
                    "        POSTALZIPCODE,               "
                    + //23
                    "        ENGLISH_NAME, FRENCH_NAME,   "
                    + //24-25 product name
                    "        ISSUEAGE, PREFERREDLANGUAGE, "
                    + //26-27
                    "        SMOKER,                      "
                    + //28
                    "        SHORT_ENGLISH_DESC,          "
                    + //29
                    "        SHORT_FRENCH_DESC,           "
                    + //30 
                    "        ADVISORNUMBER,               "
                    + //31
                    "        OLD_AGENT_FIRSTNAME,         "
                    + //32
                    "        OLD_AGENT_LASTNAME,          "
                    + //33
                    "        SUNTERMDATE,                 "
                    + //34
                    "        PAYFREQ,                     "
                    + //35
                    "        EXPIRYDATE,                  "
                    + //36
                    "        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE))  "
                    +//37
                    "   from ADVISOR_CLIENTS_LC_POLICIES  "
                    + //terminated view under skynet
                    "  where POLICYNUMBER = '" + polNum + "'  "
                    + "  ORDER BY 2,1 ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return polDet;
            }

            while (rst.next()) {

                polDet = new IMLifePolicy();
                polSett = new IMPolicySettling();
                polClient = new IMClient();
                cliAddr = new IMAddress();
                cliPhone = new IMPhone();
                polAgent = new IMAgent();

                polAgent.setFirstname(rst.getString(32));
                polAgent.setLastname(rst.getString(33));
                if (rst.getString(31) != null) {
                    polAgent.setAdvisornumber(rst.getString(31));
                } else {
                    polAgent.setAdvisornumber("n/a");
                }

                if (rst.getString(34) != null) {
                    polAgent.setSuntermdate(rst.getDate(34));
                }

                polDet.setPolicyAgent(polAgent);

                polClient.setFirstname(rst.getString(1));
                polClient.setLastname(rst.getString(2));
                polClient.setBirthday(rst.getDate(4));
                polClient.setSexStr(rst.getString(3));
                polClient.setIssueAge(rst.getInt(26));

                if (rst.getInt(27) == 1) {
                    polClient.setPrefLangStrEn("English");
                    polClient.setPrefLangStrFr("Anglais");
                }

                if (rst.getInt(27) == 2) {
                    polClient.setPrefLangStrEn("French");
                    polClient.setPrefLangStrFr("Français");
                }

                cliPhone.setAreacode(rst.getString(15));
                cliPhone.setPhoneNumber(rst.getString(16));

                polClient.setPrimPhone(cliPhone);

                cliAddr.setAddressLine1(rst.getString(17));
                cliAddr.setAddressLine2(rst.getString(18));
                cliAddr.setCity(rst.getString(19));
                cliAddr.setProvince(rst.getString(21));
                cliAddr.setPostalCode(rst.getString(23));

                polClient.setPrimAddress(cliAddr);

                polDet.setPolicyClient(polClient);
                polDet.setPolicynumber(rst.getString(12));
                polDet.setCompNameEn(rst.getString(5));
                polDet.setCompNameFr(rst.getString(6));
                polDet.setProdNameEn(rst.getString(24));
                polDet.setProdNameFr(rst.getString(25));
                polDet.setProdDescEn(rst.getString(29));
                polDet.setProdDescFr(rst.getString(30));

                polSett.setFaceamt(rst.getDouble(9));
                polSett.setAnnprem(rst.getDouble(11));
                polSett.setModprem(rst.getDouble(10));
                polSett.setIssuedate(rst.getDate(7));
                polSett.setStatusdate(rst.getDate(8));
                polSett.setSmoker(rst.getString(28));
                polSett.setExpiryDate(rst.getDate(36));
                polSett.setExpiryMonths(rst.getInt(37));

// 1 Annual  2 Monthly  3 Semi-Annual  4 Quarterly
                if (rst.getInt(35) == 1) {
                    polSett.setPayFreqStrEn("Annual");
                    polSett.setPayFreqStrFr("Annuel");
                }

                if (rst.getInt(35) == 2) {
                    polSett.setPayFreqStrEn("Monthly");
                    polSett.setPayFreqStrFr("Mensuel");
                }

                if (rst.getInt(35) == 3) {
                    polSett.setPayFreqStrEn("Semi-Annual");
                    polSett.setPayFreqStrFr("Semestriel");
                }

                if (rst.getInt(35) == 4) {
                    polSett.setPayFreqStrEn("Quarterly");
                    polSett.setPayFreqStrFr("Trimestriel");
                }

                polDet.setPolicySettling(polSett);
            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (SQLException ex) {
                Logger.getLogger(IMPolicyFacade.class.getName()).log(Level.SEVERE, null, ex);
            }
            e.printStackTrace();
            return polDet;
        } // return the connection to the pool

        return polDet;
    }

    public IMLifePolicy getPolicyByNumberSearchAll(String polNum) {

        ResultSet rst = null;
        Connection connection = getConnection();

        IMLifePolicy polDet = null;
        IMPolicySettling polSett = null;
        IMClient polClient = null;
        IMAddress cliAddr = null;
        IMPhone cliPhone = null;
        IMAgent polAgent = null;
        if (connection == null) {
            return null;
        }

        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select FIRSTNAME, LASTNAME,    "
                    + //1-2
                    "        SEX,    BIRTHDAY,            "
                    + //3-4
                    "        NAMEENGLISH, NAMEFRENCH,     "
                    + //5-6  //company name
                    "        ISSUEDATE, STATUSDATE,       "
                    + //7-8
                    "        FACEAMT,  MODPREM,           "
                    + //9-10
                    "        ANNPREM, POLICYNUMBER,       "
                    + //11-12
                    "        ENGLISH_NAME, FRENCH_NAME,   "
                    + //13-14
                    "        AREACODE, PHONENUMBER,       "
                    + //15-16 	
                    "        ADDRESSLINE1, ADDRESSLINE2,  "
                    + //17-18 		  
                    "        CITY,     PROVSTATE,         "
                    + //19-20 	
                    "        NAME_EN,  NAME_FR,           "
                    + //21-22 	province name
                    "        POSTALZIPCODE,               "
                    + //23
                    "        ENGLISH_NAME, FRENCH_NAME,   "
                    + //24-25 product name
                    "        ISSUEAGE, PREFERREDLANGUAGE, "
                    + //26-27
                    "        SMOKER,                      "
                    + //28
                    "        SHORT_ENGLISH_DESC,          "
                    + //29
                    "        SHORT_FRENCH_DESC,           "
                    + //30 
                    "        ADVISORNUMBER,               "
                    + //31
                    "        OLD_AGENT_FIRSTNAME,         "
                    + //32
                    "        OLD_AGENT_LASTNAME,          "
                    + //33
                    "        SUNTERMDATE,                 "
                    + //34
                    "        PAYFREQ,                     "
                    + //35
                    "        EXPIRYDATE,                  "
                    + //36
                    "        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)), "
                    +//37
                    "        BRANCH_NAME_EN,              "
                    + //38
                    "        BRANCH_NAME_FR,              "
                    + //39
                    "        MARKETING_REG_NAME_EN,       "
                    + //40
                    "        MARKETING_REG_NAME_FR,       "
                    + //41
                    "        POLICYSTATUS,                "
                    + //42
                    "        POLICY_DESCRIPTION_EN,       "
                    + //43
                    "        POLICY_DESCRIPTION_FR        "
                    + //44
                    "   from SUN_ALL_POLICY_WITH_ADVISORT  "
                    + "  where POLICYNUMBER = '" + polNum + "'  "
                    + "  ORDER BY 2,1 ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return polDet;
            }

            while (rst.next()) {

                polDet = new IMLifePolicy();
                polSett = new IMPolicySettling();
                polClient = new IMClient();
                cliAddr = new IMAddress();
                cliPhone = new IMPhone();
                polAgent = new IMAgent();

                polAgent.setFirstname(rst.getString(32));
                polAgent.setLastname(rst.getString(33));
                if (rst.getString(31) != null) {
                    polAgent.setAdvisornumber(rst.getString(31));
                } else {
                    polAgent.setAdvisornumber("n/a");
                }

                if (rst.getString(34) != null) {
                    polAgent.setSuntermdate(rst.getDate(34));
                }

                polDet.setPolicyAgent(polAgent);
                polClient.setFirstname(rst.getString(1));
                polClient.setLastname(rst.getString(2));
                polClient.setBirthday(rst.getDate(4));
                polClient.setSexStr(rst.getString(3));
                polClient.setIssueAge(rst.getInt(26));
                if (rst.getInt(27) == 1) {
                    polClient.setPrefLangStrEn("English");
                    polClient.setPrefLangStrFr("Anglais");
                }

                if (rst.getInt(27) == 2) {
                    polClient.setPrefLangStrEn("French");
                    polClient.setPrefLangStrFr("Français");
                }
                cliPhone.setAreacode(rst.getString(15));
                cliPhone.setPhoneNumber(rst.getString(16));

                polClient.setPrimPhone(cliPhone);

                cliAddr.setAddressLine1(rst.getString(17));
                cliAddr.setAddressLine2(rst.getString(18));
                cliAddr.setCity(rst.getString(19));
                cliAddr.setProvince(rst.getString(21));
                cliAddr.setPostalCode(rst.getString(23));

                polClient.setPrimAddress(cliAddr);
                polDet.setPolicyClient(polClient);
                polDet.setPolicynumber(rst.getString(12));
                polDet.setCompNameEn(rst.getString(5));
                polDet.setCompNameFr(rst.getString(6));
                polDet.setProdNameEn(rst.getString(24));
                polDet.setProdNameFr(rst.getString(25));
                polDet.setProdDescEn(rst.getString(29));
                polDet.setProdDescFr(rst.getString(30));

                polSett.setFaceamt(rst.getDouble(9));
                polSett.setAnnprem(rst.getDouble(11));
                polSett.setModprem(rst.getDouble(10));
                polSett.setIssuedate(rst.getDate(7));
                polSett.setStatusdate(rst.getDate(8));
                polSett.setSmoker(rst.getString(28));
                polSett.setExpiryDate(rst.getDate(36));
                polSett.setExpiryMonths(rst.getInt(37));
                polSett.setPolicystatus(rst.getInt(42));
                polSett.setPolicystatusDescEN(rst.getString(43));
                polSett.setPolicystatusDescFR(rst.getString(44));

                polAgent.setBranchNameEn(rst.getString(38));
                polAgent.setBranchNameFr(rst.getString(39));

                polAgent.setMarketRegionEn(rst.getString(40));
                polAgent.setMarketRegionFr(rst.getString(41));

// 1 Annual  2 Monthly  3 Semi-Annual  4 Quarterly
                if (rst.getInt(35) == 1) {
                    polSett.setPayFreqStrEn("Annual");
                    polSett.setPayFreqStrFr("Annuel");
                }

                if (rst.getInt(35) == 2) {
                    polSett.setPayFreqStrEn("Monthly");
                    polSett.setPayFreqStrFr("Mensuel");
                }

                if (rst.getInt(35) == 3) {
                    polSett.setPayFreqStrEn("Semi-Annual");
                    polSett.setPayFreqStrFr("Semestriel");
                }

                if (rst.getInt(35) == 4) {
                    polSett.setPayFreqStrEn("Quarterly");
                    polSett.setPayFreqStrFr("Trimestriel");
                }

                polDet.setPolicySettling(polSett);
            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (SQLException ex) {
                Logger.getLogger(IMPolicyFacade.class.getName()).log(Level.SEVERE, null, ex);
            }
            e.printStackTrace();
            return polDet;
        } // return the connection to the pool

        return polDet;
    }

    public IMLifePolicy getPolicyByNumberInforceView(String polNum) {

        ResultSet rst = null;

        Connection connection = getConnection();

        IMLifePolicy polDet = null;
        IMPolicySettling polSett = null;
        IMClient polClient = null;
        IMAddress cliAddr = null;
        IMPhone cliPhone = null;
        IMAgent polAgent = null;

        if (connection == null) {
            return null;
        }

        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select FIRSTNAME, LASTNAME,         "
                    + //1-2
                    "        SEX,    BIRTHDAY,            "
                    + //3-4
                    "        NAMEENGLISH, NAMEFRENCH,     "
                    + //5-6  //company name
                    "        ISSUEDATE, STATUSDATE,       "
                    + //7-8
                    "        FACEAMT,  MODPREM,           "
                    + //9-10
                    "        ANNPREM, POLICYNUMBER,       "
                    + //11-12
                    "        ENGLISH_NAME, FRENCH_NAME,   "
                    + //13-14
                    "        AREACODE, PHONENUMBER,       "
                    + //15-16 	
                    "        ADDRESSLINE1, ADDRESSLINE2,  "
                    + //17-18 		  
                    "        CITY,     PROVSTATE,         "
                    + //19-20 	
                    "        NAME_EN,  NAME_FR,           "
                    + //21-22 	province name
                    "        POSTALZIPCODE,               "
                    + //23
                    "        ENGLISH_NAME, FRENCH_NAME,   "
                    + //24-25 product name
                    "        ISSUEAGE, PREFERREDLANGUAGE, "
                    + //26-27
                    "        SMOKER,                      "
                    + //28
                    "        SHORT_ENGLISH_DESC,          "
                    + //29
                    "        SHORT_FRENCH_DESC,           "
                    + //30 
                    "        ADVISORNUMBER,               "
                    + //31
                    "        OLD_AGENT_FIRSTNAME,         "
                    + //32
                    "        OLD_AGENT_LASTNAME,          "
                    + //33
                    "        SUNTERMDATE,                 "
                    + //34
                    "        PAYFREQ,                     "
                    + //35
                    "        EXPIRYDATE,                  "
                    + //36
                    "        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)), "
                    +//37
                    "        BRANCH ,                      "
                    + //38   
                    "        BRANCH_NAME_EN,                      "
                    + //39
                    "        BRANCH_NAME_FR ,                "
                    + //40
                    "        MARKETING_REG_NAME_EN,                      "
                    + //41
                    "        MARKETING_REG_NAME_FR,                 "
                    + //42
                    "        POLICY_DESCRIPTION_EN,                 "
                    + //43
                    "        POLICY_DESCRIPTION_FR                 "
                    + //44
                    "   from SUN_ALL_POLICY_WITH_ADVISORT   "
                    + //inforce view skynet
                    "  where POLICYNUMBER = '" + polNum + "'  "
                    + "  ORDER BY 2,1 ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return polDet;
            }

            while (rst.next()) {

                polDet = new IMLifePolicy();
                polSett = new IMPolicySettling();
                polClient = new IMClient();
                cliAddr = new IMAddress();
                cliPhone = new IMPhone();
                polAgent = new IMAgent();

                polAgent.setFirstname(rst.getString(32));
                polAgent.setLastname(rst.getString(33));
                if (rst.getString(31) != null) {
                    polAgent.setAdvisornumber(rst.getString(31));
                } else {
                    polAgent.setAdvisornumber("n/a");
                }

                if (rst.getString(34) != null) {
                    polAgent.setSuntermdate(rst.getDate(34));
                }

                if (rst.getInt(38) != 0) {
                    polAgent.setBranchNo(rst.getInt(38));

                    Branch branch = new Branch();
                    branch = branchFacade.getByBranchId(rst.getInt(38));

                    polAgent.setBranch(branch.getBranchOtherName());
                    polAgent.setMarketRegNo(branch.getMarketingRegion());
                    //Types t = typesManager.getTypesByDetails("MARKETING_REGION", branch.getMarketingRegion());

                }
                polAgent.setMarketRegionEn(rst.getString(41));
                polAgent.setMarketRegionFr(rst.getString(42));
                polAgent.setBranchNameEn(rst.getString(39));
                polAgent.setBranchNameFr(rst.getString(40));

                polDet.setPolicyAgent(polAgent);

                polClient.setFirstname(rst.getString(1));
                polClient.setLastname(rst.getString(2));
                polClient.setBirthday(rst.getDate(4));
                polClient.setSexStr(rst.getString(3));
                polClient.setIssueAge(rst.getInt(26));

                if (rst.getInt(27) == 1) {
                    polClient.setPrefLangStrEn("English");
                    polClient.setPrefLangStrFr("Anglais");
                }

                if (rst.getInt(27) == 2) {
                    polClient.setPrefLangStrEn("French");
                    polClient.setPrefLangStrFr("Français");
                }

                if (rst.getString(15) != null) {
                    cliPhone.setAreacode(rst.getString(15));
                } else {
                    cliPhone.setAreacode("n/a");
                }

                if (rst.getString(16) != null) {
                    cliPhone.setPhoneNumber(rst.getString(16));
                } else {
                    cliPhone.setPhoneNumber("n/a");
                }

                polClient.setPrimPhone(cliPhone);

                cliAddr.setAddressLine1(rst.getString(17));
                cliAddr.setAddressLine2(rst.getString(18));
                cliAddr.setCity(rst.getString(19));
                cliAddr.setProvince(rst.getString(21));
                cliAddr.setPostalCode(rst.getString(23));

                polClient.setPrimAddress(cliAddr);

                polDet.setPolicyClient(polClient);
                polDet.setPolicynumber(rst.getString(12));
                polDet.setCompNameEn(rst.getString(5));
                polDet.setCompNameFr(rst.getString(6));
                polDet.setProdNameEn(rst.getString(24));
                polDet.setProdNameFr(rst.getString(25));
                polDet.setProdDescEn(rst.getString(29));
                polDet.setProdDescFr(rst.getString(30));

                polSett.setFaceamt(rst.getDouble(9));
                polSett.setAnnprem(rst.getDouble(11));
                polSett.setModprem(rst.getDouble(10));
                polSett.setIssuedate(rst.getDate(7));
                polSett.setStatusdate(rst.getDate(8));
                polSett.setSmoker(rst.getString(28));
                polSett.setExpiryDate(rst.getDate(36));
                polSett.setExpiryMonths(rst.getInt(37));
                polSett.setPolicystatusDescEN(rst.getString(43));
                polSett.setPolicystatusDescFR(rst.getString(44));

// 1 Annual  2 Monthly  3 Semi-Annual  4 Quarterly
                if (rst.getInt(35) == 1) {
                    polSett.setPayFreqStrEn("Annual");
                    polSett.setPayFreqStrFr("Annuel");
                }

                if (rst.getInt(35) == 2) {
                    polSett.setPayFreqStrEn("Monthly");
                    polSett.setPayFreqStrFr("Mensuel");
                }

                if (rst.getInt(35) == 3) {
                    polSett.setPayFreqStrEn("Semi-Annual");
                    polSett.setPayFreqStrFr("Semestriel");
                }

                if (rst.getInt(35) == 4) {
                    polSett.setPayFreqStrEn("Quarterly");
                    polSett.setPayFreqStrFr("Trimestriel");
                }

                polDet.setPolicySettling(polSett);
            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (SQLException ex) {
                Logger.getLogger(IMPolicyFacade.class.getName()).log(Level.SEVERE, null, ex);
            }
            e.printStackTrace();
            return polDet;
        } // return the connection to the pool

        return polDet;
    }

}
