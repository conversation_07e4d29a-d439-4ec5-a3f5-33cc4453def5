/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.isurvey.SurveyAnswer;
import com.insurfact.skynet.entity.ComplianceDocument;
import com.insurfact.skynet.entity.Contact;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class SurveyAnswerFacade extends AbstractFacade<SurveyAnswer> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public SurveyAnswerFacade() {
        super(SurveyAnswer.class);
    }
    
    public List<SurveyAnswer> findByContactAndConpliance(Contact contact, ComplianceDocument complianceDocument){
        
        TypedQuery<SurveyAnswer> query = em.createNamedQuery("SurveyAnswer.findByContactAndCompliance", SurveyAnswer.class);
        
        query.setParameter("contact",contact);
        query.setParameter("compliance",complianceDocument);
        
        List<SurveyAnswer> list = query.getResultList();
        
        if(list == null)
            return new ArrayList<>();
        
        return list;      
    }
    
}
