/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.avue.navigation.ApplicationMainBean;
import com.insurfact.avue.navigation.shop4rates.Shop4RatesMortgageSearchParameter;
import com.insurfact.cannex.entity.CannexCompany;
import com.insurfact.cannex.entity.CannexCompanyNew; 
import com.insurfact.cannex.entity.CannexProductNew; 
import com.insurfact.cannex.entity.TermProduct;
import com.insurfact.cannex.entity.TermsInterestRate;
import com.insurfact.skynet.constant.Constants;
import com.insurfact.skynet.entity.CompanySelection;

import com.insurfact.skynet.entity.Province;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery; 

import com.insurfact.skynet.entity.Users;
import java.math.BigDecimal;
import java.util.Iterator;
import jakarta.inject.Inject;

/**
 *
 * <AUTHOR> rebuilded raul
 */
@Stateless
public class CannexFacadeNew extends AbstractFacade<CannexCompanyNew> {

	@Inject
	ApplicationMainBean applicationBean;

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	public CannexFacadeNew() {
		super(CannexCompanyNew.class);
	}

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public List<TermsInterestRate> find10BestGICS() {
		List<TermsInterestRate> rates = new ArrayList<>();

		TermsInterestRate result = null;

		// 30 days
		result = getBestGICInterestRate(0, 1000, "N", "N", "E", "30");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 30 days : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 30 days");
		}

		// 60 days
		result = getBestGICInterestRate(0, 1000, "N", "N", "E", "60");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 60 days : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 60 days");
		}

		// 90 days
		result = getBestGICInterestRate(0, 1000, "N", "N", "E", "90");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 90 days : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 90 days");
		}

		// 1 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "1");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 1 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 1 Year");
		}

		// 2 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "2");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 2 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 2 Year");
		}

		// 3 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "3");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 3 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 3 Year");
		}

		// 4 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "4");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 4 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 4 Year");
		}

		// 5 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "5");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 5 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 5 Year");
		}

		// 6 year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "6");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 6 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 6 Year");
		}

		// 10 Year
		result = getBestGICInterestRate(4, 1000, "N", "A", "E", "10");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 10 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 10 Year");
		}

		return rates;

	}

	// productTypeId
	// GIC = 1 (TERM + TAX=N) **new taxIndicator = N
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years
	public TermsInterestRate getBestGICInterestRate(Integer termDuration, Integer amount, String redeem,
			String compound, String payment, String termValue) {

		TypedQuery<TermsInterestRate> query = null;
		String taxIndicator = "N";

		/*
		 * System.out.println("CannexFacadeNew.getBestGICProduct() | duration=" +
		 * termDuration + " amount=" + amount + " redeem=" + redeem + " compound=" +
		 * compound + " payment_freq=" + payment + " termValue=" + termValue);
		 */
		query = em.createQuery("SELECT r FROM CannexInterestRateNewTerm r " + "WHERE r.termValue = :termValue "
		// + "AND r.termProduct.productTypeId=1 "
				+ "AND r.termProduct.taxIndicator = :taxIndicator  " + "AND r.termProduct.termTypeId = :duration "
				+ "AND :deposit BETWEEN r.termProduct.minAmount AND r.termProduct.maxAmount "
				+ "AND r.termProduct.redeemability = :redeem " + "AND r.termProduct.compoundFrequency = :compound "
				+ "AND r.termProduct.paymentFrequency = :payment " + "ORDER BY r.interestRateValue DESC ",
				TermsInterestRate.class);

		query.setParameter("termValue", termValue);
		query.setParameter("duration", termDuration);
		query.setParameter("deposit", amount);
		query.setParameter("redeem", redeem);
		query.setParameter("compound", compound);
		query.setParameter("payment", payment);
		query.setParameter("taxIndicator", taxIndicator);

		query.setMaxResults(1);

		List<TermsInterestRate> results = query.getResultList();
		TermsInterestRate best = null;

		if (results != null && !results.isEmpty()) {
			best = results.get(0);
		}

		return best;
	}

	public List<CannexProductNew> findTermProducts(CannexCompanyNew company) {
		TypedQuery<CannexProductNew> query = null;

		query = em.createQuery(
				"SELECT p FROM CannexProductNew p WHERE p.cannexCompany = :company  ORDER by p.termTypeId",
				CannexProductNew.class); // and p.productTypeId in(1,2)
		query.setParameter("company", company);

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		System.out.println("result for findTermProducts: " + results);
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			if (!next.isTermProduct() && !next.getProductType().equalsIgnoreCase("TERM")) {
				iterator.remove();
			}

		}

		return results;
	}

	public List<CannexProductNew> findRrifProducts(CannexCompanyNew company) {
		TypedQuery<CannexProductNew> query = null;

		query = em.createQuery(
				"SELECT p FROM CannexProductNew p WHERE p.cannexCompany = :company  ORDER by p.termTypeId",
				CannexProductNew.class); // and p.productTypeId in(1,2)
		query.setParameter("company", company);

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		System.out.println("result for findRrifProducts: " + results);
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			if (!next.getProductType().equalsIgnoreCase("RRIF")) {
				System.out.println("removing: " + next);
				iterator.remove();
			}

		}
		System.out.println("result for findRrifProducts after filtering: " + results);

		return results;
	}

	// productTypeId
	// GIC = 1 (TERM + TAX=N)
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years
	public List<CannexProductNew> findGICProducts(Integer cannexCompanyType, Province province, Integer termDuration,
			Integer amount, String redeem, String compound, String payment) {

		Users user = applicationBean.getUsers();
		String taxIndicator = "N";

		TypedQuery<CannexProductNew> query = null;
		String sql = "";
//        System.out.println("CannexFacade.findGICProducts()  FULL  | cie_type="+ cannexCompanyType +" duration="+termDuration + " amount="+amount+" redeem="+redeem +" compound="+compound+ " payment_freq="+payment);

		if (cannexCompanyType != null) {
			sql = " SELECT p FROM CannexProductNew p                " + "  WHERE p.cannexCompany.companyType = :type "
					+ "    AND :province MEMBER OF p.cannexCompany.provinceList " + "    AND p.termTypeId = :duration " // AND
																														// p.productTypeId
																														// =
																														// 1
					+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
					+ "    AND p.redeemability = :redeem                        "
					+ "    AND p.compoundFrequency = :compound                  "
					+ "    AND p.taxIndicator = :taxIndicator  " + "    AND p.paymentFrequency = :payment ";
			query = em.createQuery(sql, CannexProductNew.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", termDuration);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);
			query.setParameter("taxIndicator", taxIndicator);
		} else {

			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents

				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null && compSel.getShop4rates().equalsIgnoreCase("y")) {
						compIds.add(compSel.getCompanyId());
						System.out.println("line288: " + compSel.getCompanyId());
					}
					System.out.println("line290: " + compIds.toString());
				}
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p " + " WHERE :province MEMBER OF p.cannexCompany.provinceList "
								+ "   AND p.cannexCompany.companyId IN :compID  " + "   AND p.termTypeId = :duration " // AND
																														// p.productTypeId
																														// =
																														// 1
								+ "   AND :deposit BETWEEN p.minAmount AND p.maxAmount "
								+ "   AND p.redeemability = :redeem " + "   AND p.compoundFrequency = :compound "
								+ "    AND p.taxIndicator = :taxIndicator  " + "   AND p.paymentFrequency = :payment",
						CannexProductNew.class);

				query.setParameter("compID", compIds);
				query.setParameter("province", province);
				query.setParameter("duration", termDuration);
				query.setParameter("deposit", amount);
				query.setParameter("redeem", redeem);
				query.setParameter("compound", compound);
				query.setParameter("payment", payment);
				query.setParameter("taxIndicator", taxIndicator);

			} else {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList "
								+ "AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount " // AND
																													// p.productTypeId
																													// =
																													// 1
								+ "AND p.redeemability = :redeem AND p.compoundFrequency = :compound "
								+ "    AND p.taxIndicator = :taxIndicator  " + "AND p.paymentFrequency = :payment",
						CannexProductNew.class);

				query.setParameter("province", province);
				query.setParameter("duration", termDuration);
				query.setParameter("deposit", amount);
				query.setParameter("redeem", redeem);
				query.setParameter("compound", compound);
				query.setParameter("payment", payment);
				query.setParameter("taxIndicator", taxIndicator);

			}
		}

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			if (!next.isTermProduct()) {
				iterator.remove();
			}

		}

		return results;
	}

	// productTypeId
	// GIC = 1 (TERM + TAX=N)
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years
	public List<CannexProductNew> findGICProducts(Integer cannexCompanyType, Province province, Integer termDuration,
			Integer amount) {

		Users user = applicationBean.getUsers();
		String taxIndicator = "N";

		Query query = null;

//        System.out.println("CannexFacade.findGICProducts()  QUICK  | cie_type="+ cannexCompanyType +" duration="+termDuration +" amount="+amount );
		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM CannexProductNew p WHERE p.cannexCompany.companyType = :type  AND p.taxIndicator = :taxIndicator  AND :province MEMBER OF p.cannexCompany.provinceList  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
					TermProduct.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", termDuration);
			query.setParameter("deposit", amount);
			query.setParameter("taxIndicator", taxIndicator);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents

				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null) {
						if (compSel.getShop4rates().equalsIgnoreCase("y")) {
							compIds.add(compSel.getCompanyId());
							System.out.println("line 356: " + compSel.getCompanyId());
						}
					}

				}
				System.out.println("line 356: " + compIds.toString());
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE  p.taxIndicator = :taxIndicator  p.cannexCompany.companyId IN :compID AND :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						CannexProductNew.class);
				query.setParameter("compID", compIds);
				System.out.println("line 364: ");
			} else {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE  p.taxIndicator = :taxIndicator  AND :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						CannexProductNew.class);
			}

			query.setParameter("duration", termDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
			query.setParameter("taxIndicator", taxIndicator);
		}

		@SuppressWarnings("unchecked")
		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			if (!next.isTermProduct()) {
				iterator.remove();
			}

		}

		return results;
	}

	// productTypeId
	// RRSP = 2 (TERM + TAX=R
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years
	public List<CannexProductNew> findRRSPProducts(Integer cannexCompanyType, Province province, Integer termDuration,
			Integer amount, String redeem, String compound, String payment) {

		Users user = applicationBean.getUsers();

		TypedQuery<CannexProductNew> query = null;
		String taxIndicator = "N";

//        System.out.println("CannexFacade.findRRSPProducts()  FULL  | cie_type="+ cannexCompanyType +" duration="+termDuration + " amount="+amount+" redeem="+redeem +" compound="+compound+ " payment_freq="+payment);
		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM CannexProductNew p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList  AND p.taxIndicator = :taxIndicator  AND p.termTypeId = :duration AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
					CannexProductNew.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", termDuration);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);
			query.setParameter("taxIndicator", taxIndicator);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents

				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null) {
						if (compSel.getShop4rates().equalsIgnoreCase("y")) {
							compIds.add(compSel.getCompanyId());
							System.out.println("line415: " + compSel.getCompanyId());
						}
					}

				}

				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID  AND p.taxIndicator = :taxIndicator  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
						CannexProductNew.class);
				query.setParameter("compID", compIds);
			} else {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList  AND p.taxIndicator = :taxIndicator  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
						CannexProductNew.class);
			}

			query.setParameter("duration", termDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);
			query.setParameter("taxIndicator", taxIndicator);
		}

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			if (!next.isTermProduct()) {
				iterator.remove();
			}

		}

		return results;
	}

	public List<CannexProductNew> findRRSPProducts(Integer cannexCompanyType, Province province, Integer termDuration,
			Integer amount) {

		Users user = applicationBean.getUsers();

		TypedQuery<CannexProductNew> query = null;
		String taxIndicator = "N";

//        System.out.println("CannexFacade.findRRSPProducts()  QUICK  | cie_type="+ cannexCompanyType +" duration="+termDuration + " amount="+amount);
		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM CannexProductNew p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList  AND p.taxIndicator = :taxIndicator  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
					CannexProductNew.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", termDuration);
			query.setParameter("deposit", amount);
			query.setParameter("taxIndicator", taxIndicator);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents

				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null) {
						if (compSel.getShop4rates().equalsIgnoreCase("y")) {
							compIds.add(compSel.getCompanyId());
							System.out.println("line468: " + compSel.getCompanyId());
						}
					}

				}
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID  AND p.taxIndicator = :taxIndicator  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						CannexProductNew.class);
				query.setParameter("compID", compIds);

			} else {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList  AND p.taxIndicator = :taxIndicator  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						CannexProductNew.class);
			}

			query.setParameter("duration", termDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
			query.setParameter("taxIndicator", taxIndicator);
		}

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			if (!next.isTermProduct()) {
				iterator.remove();
			}

		}

		return results;
	}

	// productTypeId
	// RRIF = 3
	// Duration
	// 1 = 1 - 5 years (default)
	// 2 = 10 - 25 Years
	// 4 = 1.5 - 6.5 years
	public List<CannexProductNew> findRRIFProductsFix(Integer cannexCompanyType, Province province,
			Integer rrifDuration, Integer amount) {

		Users user = applicationBean.getUsers();

		TypedQuery<CannexProductNew> query = null;

		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM CannexProductNew p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
					CannexProductNew.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", rrifDuration);
			query.setParameter("deposit", amount);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents
				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null && compSel.getShop4rates().equalsIgnoreCase("y")) {
						compIds.add(compSel.getCompanyId());
					}

				}
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND  p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						CannexProductNew.class);
				query.setParameter("compID", compIds);

			} else {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						CannexProductNew.class);
			}

			query.setParameter("duration", rrifDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
		}

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			if (!next.isRrifProduct()) {
				iterator.remove();
			}

		}

		return results;
	}

	public List<CannexProductNew> findRRIFProductsFix(Integer cannexCompanyType, Province province,
			Integer rrifDuration, Integer amount, String redeem, String compound, String payment) {

		Users user = applicationBean.getUsers();

		TypedQuery<CannexProductNew> query = null;

		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM CannexProductNew p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND  p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
					CannexProductNew.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", rrifDuration);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents
				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null && compSel.getShop4rates().equalsIgnoreCase("y")) {
						compIds.add(compSel.getCompanyId());
					}

				}

				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
						CannexProductNew.class);
				query.setParameter("compID", compIds);
			} else {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
						CannexProductNew.class);
			}

			query.setParameter("duration", rrifDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);
		}

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			if (!next.isRrifProduct()) {
				iterator.remove();
			}

		}

		return results;
	}

	public List<String> findDistinctTermTypes(String productType, String companyType) {

		TypedQuery<String> nq = em.createQuery(
				"SELECT distinct a.termType FROM CannexProduct a WHERE  a.companyType = :companyType  and a.productType = :productType",
				String.class);
		nq.setParameter("productType", productType);
        nq.setParameter("companyType", companyType); 
		return nq.getResultList();
		
		/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<String> criteriaQuery = cBuilder.createQuery(String.class);
		Root<CannexProductNew> p = criteriaQuery.from(CannexProductNew.class);

		Predicate companyPredicate = cBuilder
				.equal(p.get(CannexProductNew_.cannexCompany).get(CannexCompanyNew_.companyType), companyType);
		Predicate prodTypePredicate = cBuilder.equal(p.get(CannexProductNew_.productType), productType);

		criteriaQuery.where(cBuilder.and(companyPredicate, prodTypePredicate)).select(p.get(CannexProductNew_.termType))
				.distinct(true);

		TypedQuery<String> query = em.createQuery(criteriaQuery);

		// List<String> results = query.getResultList();
		return query.getResultList();*/
	}

	public String findTermUnit(String termType, String productType) {

		TypedQuery<String> nq = em.createQuery(
				"SELECT distinct a.termType FROM CannexProduct a WHERE  a.termType = :termType  and a.productType = :productType",
				String.class);
		nq.setParameter("productType", productType);
        nq.setParameter("termType", termType); 
		return nq.getSingleResult();
		
		/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<String> criteriaQuery = cBuilder.createQuery(String.class);
		Root<CannexProductNew> p = criteriaQuery.from(CannexProductNew.class);

		Predicate prodTypePredicate = cBuilder.equal(p.get(CannexProductNew_.productType), productType);
		Predicate termTypePredicate = cBuilder.equal(p.get(CannexProductNew_.termType), termType);

		criteriaQuery.where(cBuilder.and(prodTypePredicate, termTypePredicate))
				.select(p.get(CannexProductNew_.termUnit)).distinct(true);

		TypedQuery<String> query = em.createQuery(criteriaQuery);

		List<String> results = query.getResultList();
		if (results != null && !results.isEmpty()) {
			return results.get(0);
		}

		return null;*/
	}

	public CannexCompanyNew getCompanyByIPNO(String ipno) {

		Query query = em.createQuery("SELECT c FROM CannexCompanyNew c WHERE c.ipno = :ipno", CannexCompanyNew.class);
		query.setParameter("ipno", ipno);

		CannexCompanyNew cie;

		try {
			cie = (CannexCompanyNew) query.getSingleResult();
		} catch (jakarta.persistence.NoResultException e) {
			return null;
		}

		return cie;
	}

	public List<CannexCompanyNew> findAllCannexComps() {

		try {
			TypedQuery<CannexCompanyNew> nq = em.createNamedQuery("CannexCompanyNew.findAll", CannexCompanyNew.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			e.printStackTrace();
			return null;
		}

	}

	public List<CannexCompanyNew> findAllCannexCompsNoSel(String comps) {

		try {
			TypedQuery<CannexCompanyNew> query = em.createQuery(
					"SELECT p FROM CannexCompanyNew p where p.companyId NOT IN ( :comps )", CannexCompanyNew.class);
			query.setParameter("comps", comps);
			return query.getResultList();

		} catch (NoResultException e) {
			e.printStackTrace();
			return null;
		}

	}

	public CannexCompanyNew getCompanyByCode(String code) {

		TypedQuery<CannexCompanyNew> query = em
				.createQuery("SELECT c FROM CannexCompanyNew c WHERE c.companyCode = :code", CannexCompanyNew.class);
		query.setParameter("code", code);
		CannexCompanyNew cie;

		try {
			cie = (CannexCompanyNew) query.getSingleResult();
		} catch (jakarta.persistence.NoResultException e) {
			return null;
		}

		return cie;
	}

	// productTypeId
	// GIC = 1 (TERM + TAX=N)
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years (9, a, 1, "C");
	public List<CannexProductNew> findMORTProducts(Integer cannexCompanyType, Province province, Integer terms,
			String openClosed) {

		// Users user = applicationBean.getUsers();

		/*
		 * List<CompanySelection> compSelList = user.getCompanySelectionList();
		 * List<Integer> compIds = new ArrayList<>();
		 * 
		 * for (CompanySelection compSel : compSelList) { if (compSel.getShop4rates() !=
		 * null) { if (compSel.getShop4rates().equalsIgnoreCase("y")) {
		 * compIds.add(compSel.getCompanyId()); } } }
		 */
		TypedQuery<CannexProductNew> query = null;
		String sql = "";
		System.out.println("CannexProductNew.findMORTProducts()  FULL  | cie_type=" + cannexCompanyType + " terms="
				+ terms + " openClosed=" + openClosed);

		if (cannexCompanyType != null) {

			if (terms == 0) {
				/*
				 * if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode())
				 * {//dashBoard for Sun Tool agents sql =
				 * " SELECT p FROM MortProduct p                " +
				 * "  WHERE p.cannexCompany.companyType = :type " +
				 * "    AND :province MEMBER OF p.cannexCompany.provinceList  " +
				 * "    AND p.cannexCompany.companyId IN :compID              " +
				 * "    AND p.productTypeId = 4                               " +
				 * "    AND p.termTypeId = 0 AND p.rateType = 'V' "; query = em.createQuery(sql,
				 * MortProduct.class); System.out.println("CannexFacade line743:  sql=" + sql);
				 * query.setParameter("compID", compIds); query.setParameter("province",
				 * province); query.setParameter("type", cannexCompanyType); } else {
				 */
				sql = " SELECT p FROM CannexProductNew p                  "
						+ "  WHERE p.cannexCompany.companyType = :type   "
						+ "    AND :province MEMBER OF p.cannexCompany.provinceList "
						// + " AND p.productTypeId = 4 "
						+ "    AND p.termTypeId = 0 AND p.rateType = 'V' ";
				query = em.createQuery(sql, CannexProductNew.class);
				System.out.println("CannexFacade line755:  sql=" + sql);
				query.setParameter("province", province);
				query.setParameter("type", cannexCompanyType);
				// }
			}
			if (terms == 1) {
				/*
				 * if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode())
				 * {//dashBoard for Sun Tool agents query =
				 * em.createQuery("SELECT p FROM MortProduct p " +
				 * "WHERE p.cannexCompany.companyType = :type " +
				 * "AND :province MEMBER OF p.cannexCompany.provinceList " +
				 * "AND p.cannexCompany.companyId IN :compID " +
				 * "AND p.productTypeId = 4 AND p.termTypeId = 0 " +
				 * "AND p.openClosed = :openClosed AND p.rateType = 'F'", MortProduct.class);
				 * 
				 * query.setParameter("compID", compIds);
				 * 
				 * query.setParameter("openClosed", openClosed); query.setParameter("type",
				 * cannexCompanyType); query.setParameter("province", province);
				 * 
				 * } else {
				 */
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList  AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'",
						CannexProductNew.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("type", cannexCompanyType);
				query.setParameter("province", province);

				// }
			}
			if (terms == 2) {
				/*
				 * if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode())
				 * {//dashBoard for Sun Tool agents query = em.
				 * createQuery("SELECT p FROM MortProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4 AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'"
				 * , MortProduct.class);
				 * 
				 * query.setParameter("compID", compIds); query.setParameter("openClosed",
				 * openClosed); query.setParameter("type", cannexCompanyType);
				 * query.setParameter("province", province);
				 * 
				 * } else {
				 */
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList  AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
						CannexProductNew.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("type", cannexCompanyType);
				query.setParameter("province", province);

				// }
			}
		} else {
			/*
			 * if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode())
			 * {//for Sun Tool agents
			 * 
			 * if (terms == 0) { query = em.
			 * createQuery("SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4  AND p.termTypeId = 0  AND p.rateType = 'V'"
			 * , MortProduct.class);
			 * 
			 * query.setParameter("compID", compIds); query.setParameter("province",
			 * province); } if (terms == 1) {
			 * 
			 * query = em.
			 * createQuery("SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4  AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'"
			 * , MortProduct.class); System.out.println("line 800 " + compIds.toString());
			 * query.setParameter("compID", compIds); query.setParameter("openClosed",
			 * openClosed); query.setParameter("province", province); } if (terms == 2) {
			 * query = em.
			 * createQuery("SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4  AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'"
			 * , MortProduct.class);
			 * 
			 * query.setParameter("compID", compIds); query.setParameter("openClosed",
			 * openClosed); query.setParameter("province", province); }
			 * 
			 * } else {
			 */

			if (terms == 0) {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList  AND p.termTypeId = 0  AND p.rateType = 'V'",
						CannexProductNew.class);

				query.setParameter("province", province);
			}
			if (terms == 1) {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'",
						CannexProductNew.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("province", province);
			}
			if (terms == 2) {
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList  AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
						CannexProductNew.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("province", province);
			}

			// }
		}

		List<CannexProductNew> results = new ArrayList<>();

		if (query != null) {
			results = query.getResultList();
			Iterator<CannexProductNew> iterator = results.iterator();
			while (iterator.hasNext()) {
				CannexProductNew next = iterator.next();
				if (!next.isMortProduct()) {
					iterator.remove();
				}

			}
		}

		return results;
	}

	// not on use mortAmount
	public List<CannexProductNew> findMORTProductsNew(Province province, int termDurationNew, String openClosed,
			String fixedVariable, int mortAmount) {

		TypedQuery<CannexProductNew> query = null; // AND p.termTypeId = 1
		int termTypeId = 0;
		if (termDurationNew == 120) {
			termTypeId = 1;
		}
		if (termDurationNew == 0) {
			if (openClosed.equalsIgnoreCase("OC") && fixedVariable.equalsIgnoreCase("FV")) {
				// System.out.println("1");
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList ",
						CannexProductNew.class);
				query.setParameter("province", province);

			} else if (openClosed.equalsIgnoreCase("OC") && !fixedVariable.equalsIgnoreCase("FV")) {
				// System.out.println("2");
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList   AND  p.rateType = :rateType ",
						CannexProductNew.class);

				query.setParameter("province", province);
				query.setParameter("rateType", fixedVariable);

			} else if (!openClosed.equalsIgnoreCase("OC") && fixedVariable.equalsIgnoreCase("FV")) {
				// System.out.println("3");
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList  AND p.openClosed = :openClosed ",
						CannexProductNew.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("province", province);

			} else {
				// System.out.println("4");
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList   AND p.openClosed = :openClosed AND p.rateType = :rateType ",
						CannexProductNew.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("province", province);
				query.setParameter("rateType", fixedVariable);

			}
		} else {

			if (openClosed.equalsIgnoreCase("OC") && fixedVariable.equalsIgnoreCase("FV")) {
				// System.out.println("1");
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :termTypeId  ",
						CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("termTypeId", termTypeId);

			} else if (openClosed.equalsIgnoreCase("OC") && !fixedVariable.equalsIgnoreCase("FV")) {
				// System.out.println("2");
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :termTypeId  AND  p.rateType = :rateType ",
						CannexProductNew.class);

				query.setParameter("province", province);
				query.setParameter("rateType", fixedVariable);
				query.setParameter("termTypeId", termTypeId);

			} else if (!openClosed.equalsIgnoreCase("OC") && fixedVariable.equalsIgnoreCase("FV")) {
				// System.out.println("3");
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :termTypeId  AND p.openClosed = :openClosed ",
						CannexProductNew.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("province", province);
				query.setParameter("termTypeId", termTypeId);

			} else {
				// System.out.println("4");
				query = em.createQuery(
						"SELECT p FROM CannexProductNew p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :termTypeId  AND p.openClosed = :openClosed AND p.rateType = :rateType ",
						CannexProductNew.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("province", province);
				query.setParameter("rateType", fixedVariable);
				query.setParameter("termTypeId", termTypeId);

			}
		}

		// }
		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		System.out.println("not filtered: " + results);
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			// System.out.println("termDurationNew: "+termDurationNew);
			next.setTermSelected(termDurationNew);
			System.out.println("next.isMortProduct(): " + next.isMortProduct());
			System.out.println("next.getInterestMortByTerm(): " + next.getInterestMortByTerm());
			if (termDurationNew != 0) {
				if (!next.isMortProduct() || next.getInterestMortByTerm() == null) {
					// System.out.println("removing: "+next);
					iterator.remove();
				}
			} else {
				if (!next.isMortProduct()) {
					// System.out.println("removing: "+next);
					iterator.remove();
				}
			}

		}
		System.out.println("filtered: " + results);

		return results;
	}

	public List<CannexProductNew> findGICProductsNew(Province province, String taxIndicator, String redeemability,
			String compoundFrequency, String paymentFrequency, int termType, BigDecimal amount) {

		TypedQuery<CannexProductNew> query = null;
		String sql;

		// System.out.println("4");
		if (redeemability.equalsIgnoreCase("All")) {
			if (compoundFrequency.equalsIgnoreCase("All")) {
				if (paymentFrequency.equalsIgnoreCase("All")) {
					sql = " SELECT p FROM CannexProductNew p                "
							+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
							+ "    AND p.termTypeId = :duration and p.productType='TERM'"
							+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
							+ "    AND p.taxIndicator = :taxIndicator  ";

					query = em.createQuery(sql, CannexProductNew.class);
					query.setParameter("province", province);
					query.setParameter("duration", termType);
					query.setParameter("deposit", amount);
					query.setParameter("taxIndicator", taxIndicator);
				} else {
					sql = " SELECT p FROM CannexProductNew p                "
							+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
							+ "    AND p.termTypeId = :duration and p.productType='TERM'"
							+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
							+ "    AND p.taxIndicator = :taxIndicator  " + "    AND p.paymentFrequency = :payment ";

					query = em.createQuery(sql, CannexProductNew.class);
					query.setParameter("province", province);
					query.setParameter("duration", termType);
					query.setParameter("deposit", amount);
					query.setParameter("payment", paymentFrequency);
					query.setParameter("taxIndicator", taxIndicator);
				}
			} else if (paymentFrequency.equalsIgnoreCase("All")) {
				sql = " SELECT p FROM CannexProductNew p                "
						+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
						+ "    AND p.termTypeId = :duration and p.productType='TERM'"
						+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
						+ "    AND p.compoundFrequency = :compound                  "
						+ "    AND p.taxIndicator = :taxIndicator  ";

				query = em.createQuery(sql, CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("duration", termType);
				query.setParameter("deposit", amount);
				query.setParameter("compound", compoundFrequency);
				query.setParameter("taxIndicator", taxIndicator);
			} else {

				sql = " SELECT p FROM CannexProductNew p                "
						+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
						+ "    AND p.termTypeId = :duration and p.productType='TERM'"
						+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
						+ "    AND p.compoundFrequency = :compound                  "
						+ "    AND p.taxIndicator = :taxIndicator  " + "    AND p.paymentFrequency = :payment ";

				query = em.createQuery(sql, CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("duration", termType);
				query.setParameter("deposit", amount);
				query.setParameter("compound", compoundFrequency);
				query.setParameter("payment", paymentFrequency);
				query.setParameter("taxIndicator", taxIndicator);
			}
		} else if (paymentFrequency.equalsIgnoreCase("All")) {
			if (compoundFrequency.equalsIgnoreCase("All")) {
				sql = " SELECT p FROM CannexProductNew p                "
						+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
						+ "    AND p.termTypeId = :duration and p.productType='TERM'" // AND p.productTypeId = 1
						+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
						+ "    AND p.redeemability = :redeem                        "
						+ "    AND p.taxIndicator = :taxIndicator  ";

				query = em.createQuery(sql, CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("duration", termType);
				query.setParameter("deposit", amount);
				query.setParameter("redeem", redeemability);
				query.setParameter("taxIndicator", taxIndicator);
			} else {
				sql = " SELECT p FROM CannexProductNew p                "
						+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
						+ "    AND p.termTypeId = :duration and p.productType='TERM'" // AND p.productTypeId = 1
						+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
						+ "    AND p.redeemability = :redeem                        "
						+ "    AND p.compoundFrequency = :compound                  "
						+ "    AND p.taxIndicator = :taxIndicator  ";

				query = em.createQuery(sql, CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("duration", termType);
				query.setParameter("deposit", amount);
				query.setParameter("redeem", redeemability);
				query.setParameter("compound", compoundFrequency);
				query.setParameter("taxIndicator", taxIndicator);
			}
		} else if (compoundFrequency.equalsIgnoreCase("All")) {
			sql = " SELECT p FROM CannexProductNew p                "
					+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
					+ "    AND p.termTypeId = :duration and p.productType='TERM'" // AND p.productTypeId = 1
					+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
					+ "    AND p.redeemability = :redeem                        "
					+ "    AND p.taxIndicator = :taxIndicator  " + "    AND p.paymentFrequency = :payment ";

			query = em.createQuery(sql, CannexProductNew.class);
			query.setParameter("province", province);
			query.setParameter("duration", termType);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeemability);
			query.setParameter("payment", paymentFrequency);
			query.setParameter("taxIndicator", taxIndicator);
		} else {
			sql = " SELECT p FROM CannexProductNew p                "
					+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
					+ "    AND p.termTypeId = :duration and p.productType='TERM'" // AND p.productTypeId = 1
					+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
					+ "    AND p.redeemability = :redeem                        "
					+ "    AND p.compoundFrequency = :compound                  "
					+ "    AND p.taxIndicator = :taxIndicator  " + "    AND p.paymentFrequency = :payment ";

			query = em.createQuery(sql, CannexProductNew.class);
			query.setParameter("province", province);
			query.setParameter("duration", termType);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeemability);
			query.setParameter("compound", compoundFrequency);
			query.setParameter("payment", paymentFrequency);
			query.setParameter("taxIndicator", taxIndicator);

		}

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			// System.out.println("termDurationNew: "+termDurationNew);

			if (!next.isTermProduct()
					|| (!next.getRedeemability().equalsIgnoreCase("R")
							&& !next.getRedeemability().equalsIgnoreCase("N"))
					|| (!next.getCompoundFrequency().equalsIgnoreCase("A")
							&& !next.getCompoundFrequency().equalsIgnoreCase("N"))
					|| (!next.getPaymentFrequency().equalsIgnoreCase("A")
							&& !next.getPaymentFrequency().equalsIgnoreCase("E"))) {
				// System.out.println("removing: "+next);
				iterator.remove();
			}

		}

		return results;
	}

	public List<CannexProductNew> findRRIFProductsNew(Province province, String redeemability, String compoundFrequency,
			String paymentFrequency, int termType, BigDecimal amount) {

		TypedQuery<CannexProductNew> query = null;
		String sql;

		// System.out.println("4");
		if (redeemability.equalsIgnoreCase("All")) {
			if (compoundFrequency.equalsIgnoreCase("All")) {
				if (paymentFrequency.equalsIgnoreCase("All")) {
					sql = " SELECT p FROM CannexProductNew p                "
							+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
							+ "    AND p.termTypeId = :duration and p.productType='RRIF'"
							+ "    and p.type in ('GT','LD')"
							+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     ";

					query = em.createQuery(sql, CannexProductNew.class);
					query.setParameter("province", province);
					query.setParameter("duration", termType);
					query.setParameter("deposit", amount);
				} else {
					sql = " SELECT p FROM CannexProductNew p                "
							+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
							+ "    AND p.termTypeId = :duration and p.productType='RRIF'"
							+ "    and p.type in ('GT','LD')"
							+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
							+ "    AND p.paymentFrequency = :payment ";

					query = em.createQuery(sql, CannexProductNew.class);
					query.setParameter("province", province);
					query.setParameter("duration", termType);
					query.setParameter("deposit", amount);
					query.setParameter("payment", paymentFrequency);
				}
			} else if (paymentFrequency.equalsIgnoreCase("All")) {
				sql = " SELECT p FROM CannexProductNew p                "
						+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
						+ "    AND p.termTypeId = :duration and p.productType='RRIF'" + "    and p.type in ('GT','LD')"
						+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
						+ "    AND p.compoundFrequency = :compound                  ";

				query = em.createQuery(sql, CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("duration", termType);
				query.setParameter("deposit", amount);
				query.setParameter("compound", compoundFrequency);
			} else {

				sql = " SELECT p FROM CannexProductNew p                "
						+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
						+ "    AND p.termTypeId = :duration and p.productType='RRIF'" + "    and p.type in ('GT','LD')"
						+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
						+ "    AND p.compoundFrequency = :compound                  "
						+ "    AND p.paymentFrequency = :payment ";

				query = em.createQuery(sql, CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("duration", termType);
				query.setParameter("deposit", amount);
				query.setParameter("compound", compoundFrequency);
				query.setParameter("payment", paymentFrequency);
			}
		} else if (paymentFrequency.equalsIgnoreCase("All")) {
			if (compoundFrequency.equalsIgnoreCase("All")) {
				sql = " SELECT p FROM CannexProductNew p                "
						+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
						+ "    AND p.termTypeId = :duration and p.productType='RRIF'" // AND p.productTypeId = 1
						+ "    and p.type in ('GT','LD')" + "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
						+ "    AND p.redeemability = :redeem                        ";

				query = em.createQuery(sql, CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("duration", termType);
				query.setParameter("deposit", amount);
				query.setParameter("redeem", redeemability);
			} else {
				sql = " SELECT p FROM CannexProductNew p                "
						+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
						+ "    AND p.termTypeId = :duration and p.productType='RRIF'" // AND p.productTypeId = 1
						+ "    and p.type in ('GT','LD')" + "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
						+ "    AND p.redeemability = :redeem                        "
						+ "    AND p.compoundFrequency = :compound                  ";

				query = em.createQuery(sql, CannexProductNew.class);
				query.setParameter("province", province);
				query.setParameter("duration", termType);
				query.setParameter("deposit", amount);
				query.setParameter("redeem", redeemability);
				query.setParameter("compound", compoundFrequency);
			}
		} else if (compoundFrequency.equalsIgnoreCase("All")) {
			sql = " SELECT p FROM CannexProductNew p                "
					+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
					+ "    AND p.termTypeId = :duration and p.productType='RRIF'" // AND p.productTypeId = 1
					+ "    and p.type in ('GT','LD')" + "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
					+ "    AND p.redeemability = :redeem                        "
					+ "    AND p.paymentFrequency = :payment ";

			query = em.createQuery(sql, CannexProductNew.class);
			query.setParameter("province", province);
			query.setParameter("duration", termType);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeemability);
			query.setParameter("payment", paymentFrequency);
		} else {
			sql = " SELECT p FROM CannexProductNew p                "
					+ "  WHERE   :province MEMBER OF p.cannexCompany.provinceList "
					+ "    AND p.termTypeId = :duration and p.productType='RRIF'" // AND p.productTypeId = 1
					+ "    and p.type in ('GT','LD')" + "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
					+ "    AND p.redeemability = :redeem                        "
					+ "    AND p.compoundFrequency = :compound                  "
					+ "    AND p.paymentFrequency = :payment ";

			query = em.createQuery(sql, CannexProductNew.class);
			query.setParameter("province", province);
			query.setParameter("duration", termType);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeemability);
			query.setParameter("compound", compoundFrequency);
			query.setParameter("payment", paymentFrequency);

		}

		List<CannexProductNew> results = (List<CannexProductNew>) query.getResultList();
		System.out.println("new check for rrif: " + results);
		Iterator<CannexProductNew> iterator = results.iterator();
		while (iterator.hasNext()) {
			CannexProductNew next = iterator.next();
			// System.out.println("termDurationNew: "+termDurationNew);

			if (!next.isRrifProduct()
					|| (!next.getRedeemability().equalsIgnoreCase("R")
							&& !next.getRedeemability().equalsIgnoreCase("N"))
					|| (!next.getCompoundFrequency().equalsIgnoreCase("A")
							&& !next.getCompoundFrequency().equalsIgnoreCase("N"))
					|| (!next.getPaymentFrequency().equalsIgnoreCase("A")
							&& !next.getPaymentFrequency().equalsIgnoreCase("E")
							&& !next.getPaymentFrequency().equalsIgnoreCase("M"))) {
				System.out.println("removing: " + next);
				iterator.remove();
			}

		}
		System.out.println("new check for rrif after removing: " + results);

		return results;
	}

	public List<CannexProductNew> findMORTProducts(Shop4RatesMortgageSearchParameter params) {

		/*
		 * Users user = applicationBean.getUsers();
		 * 
		 * List<CompanySelection> compSelList = user.getCompanySelectionList();
		 * List<Integer> compIds = new ArrayList<>();
		 * 
		 * for (CompanySelection compSel : compSelList) { if (compSel.getShop4rates() !=
		 * null) { if (compSel.getShop4rates().equalsIgnoreCase("y")) {
		 * compIds.add(compSel.getCompanyId()); System.out.println("line 850: " +
		 * compSel.getCompanyId());
		 * 
		 * } } }
		 */
		TypedQuery<CannexProductNew> query = null;

		CannexCompany company = params.getSelectedCompany();
		Province province = params.getSelectedProvince();
		Integer terms = params.getTerms();
		String openClosed = params.getOpenClosed();

//        System.out.println("CannexFacade.findMORTProducts()  FULL  | cie_type="+ company.getCompanyDescEn() + " terms="+ terms +" openClosed="+openClosed);
		/*
		 * if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode())
		 * {//for Sun Tool agents if (terms == 0) { query = em.
		 * createQuery("SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.rateType = 'V'"
		 * , MortProduct.class);
		 * 
		 * query.setParameter("compID", compIds); query.setParameter("province",
		 * province); query.setParameter("company", company); } if (terms == 1) { query
		 * = em.
		 * createQuery("SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'"
		 * , MortProduct.class);
		 * 
		 * query.setParameter("compID", compIds); query.setParameter("openClosed",
		 * openClosed); query.setParameter("company", company);
		 * query.setParameter("province", province); } if (terms == 2) { query = em.
		 * createQuery("SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4 AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'"
		 * , MortProduct.class);
		 * 
		 * query.setParameter("compID", compIds); query.setParameter("openClosed",
		 * openClosed); query.setParameter("company", company);
		 * query.setParameter("province", province); } } else {
		 */
		if (terms == 0) {
			query = em.createQuery(
					"SELECT p FROM CannexProductNew p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.rateType = 'V'",
					CannexProductNew.class);
			query.setParameter("province", province);
			query.setParameter("company", company);
		}
		if (terms == 1) {
			query = em.createQuery(
					"SELECT p FROM CannexProductNew p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'",
					CannexProductNew.class);

			query.setParameter("openClosed", openClosed);
			query.setParameter("company", company);
			query.setParameter("province", province);
		}
		if (terms == 2) {
			query = em.createQuery(
					"SELECT p FROM CannexProductNew p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4 AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
					CannexProductNew.class);

			query.setParameter("openClosed", openClosed);
			query.setParameter("company", company);
			query.setParameter("province", province);
		}

		// }
		List<CannexProductNew> results = new ArrayList<>();

		if (query != null) {
			results = (List<CannexProductNew>) query.getResultList();
			Iterator<CannexProductNew> iterator = results.iterator();
			while (iterator.hasNext()) {
				CannexProductNew next = iterator.next();
				if (!next.isMortProduct()) {
					iterator.remove();
				}

			}
		}

		return results;
	}

	//
}
