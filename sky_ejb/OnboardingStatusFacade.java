/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.OnboardingStatus;
import com.insurfact.skynet.entity.ProductSupplier;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class OnboardingStatusFacade extends AbstractFacade<OnboardingStatus> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public OnboardingStatusFacade() {
        super(OnboardingStatus.class);
    }

    @SuppressWarnings("unchecked")
	public List<OnboardingStatus> findByProductSupplier(ProductSupplier ps, Contact contact) {

        List<OnboardingStatus> list = new ArrayList<>();

        System.out.println("sql onboarding: " + "select os.* from onboarding_status os inner join onboarding o on os.onboarding = o.onboarding_int_id where"
                + " o.product_supplier = " + ps.getProductSupplierIntId() + " and os.contact = " + contact.getContactIntId() + " ORDER BY o.ORDER_NO asc");

//        System.out.println("**** findByUsers() : users="+users.getUserIntId() );
        try {
            Query nq = em.createNativeQuery("select os.* from onboarding_status os inner join onboarding o on os.onboarding = o.onboarding_int_id where"
                    + " o.product_supplier = " + ps.getProductSupplierIntId() + " and os.contact = " + contact.getContactIntId() + " ORDER BY o.ORDER_NO asc", OnboardingStatus.class);

            list = nq.getResultList();

        } catch (NoResultException e) {
            return list;
        }

        return list;

    }

}
