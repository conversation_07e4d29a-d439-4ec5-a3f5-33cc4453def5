/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.AboutMe;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AboutMeFacade extends AbstractFacade<AboutMe> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public AboutMeFacade() {
        super(AboutMe.class);
    }

    /*public AboutMe findForAdvisor(Advisor advisor){

        AboutMe aboutme = null;
        try {
            Query nq = getEntityManager().createNamedQuery("AboutMe.findByAdvisor", AboutMe.class);
            nq.setParameter("advisor", advisor);
            
            aboutme = (AboutMe) nq.getSingleResult();

        } catch (Exception e) {

            return null;
        }
         
        return aboutme; 
    }
    
    public AboutMe findForUsers(Users users){
        
        AboutMe aboutme = null;
         try {
            Query nq = getEntityManager().createNamedQuery("AboutMe.findByUsers", AboutMe.class);
            nq.setParameter("users", users);
            
            aboutme = (AboutMe) nq.getSingleResult();

        } catch (Exception e) {

            return null;
        }
         
        return aboutme; 
    }*/
}
