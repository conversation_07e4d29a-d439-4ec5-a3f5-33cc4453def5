/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2018 All Right Reserved, https://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdBestOverall2;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdBestOverall2Facade extends AbstractFacade<FdBestOverall2> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdBestOverall2Facade() {
        super(FdBestOverall2.class);
    }
    
    public FdBestOverall2 getBestOverall(){
        
        List<FdBestOverall2> best = findAll();

        if(best != null && !best.isEmpty())
            return best.get(0);
        
        return null;
    }
}
