/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.fundata.entity.FdCategory2;
import com.insurfact.fundata.entity.FdFundserv2;

import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFundServ2SessionBean extends AbstractFacade<FdFundserv2> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdFundServ2SessionBean() {
		super(FdFundserv2.class);
	}

	public FdFundserv2 getByFundServCode(String fundservCode) {
		
		if (fundservCode.startsWith("SSQG")) {

			// SSQG094
			String tmp = "SSQ";

			tmp += fundservCode.substring(4);

			fundservCode = tmp;
		}
		
		TypedQuery<FdFundserv2> nq = em.createQuery(
				"SELECT a FROM FdFundserv2 a WHERE  a.servCode = :fundservCode", FdFundserv2.class);
		nq.setParameter("fundservCode", fundservCode);
		return nq.getSingleResult();

		/*FdFundserv2 fdFundserv = null;

		if (fundservCode.startsWith("SSQG")) {

			// SSQG094
			String tmp = "SSQ";

			tmp += fundservCode.substring(4);

			fundservCode = tmp;
		}

		CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<FdFundserv2> criteriaQuery = cBuilder.createQuery(FdFundserv2.class);
		Root<FdFundserv2> p = criteriaQuery.from(FdFundserv2.class);

		Predicate codePredicate = cBuilder.equal(p.get(FdFundserv2_.servCode), fundservCode);
		criteriaQuery.where(codePredicate);

		TypedQuery<FdFundserv2> typeCodeQuery = em.createQuery(criteriaQuery);

		List<FdFundserv2> results = typeCodeQuery.getResultList();
		if (results != null && !results.isEmpty()) {
			fdFundserv = results.get(0);
		}

		return fdFundserv;*/
	}

	public List<Long> findDataKeysByFundServCode(String fundservCode) {
		
		if (fundservCode != null && !fundservCode.isEmpty()) {
		
		fundservCode = "%" + fundservCode.toUpperCase() + "%";
		
		TypedQuery<Long> nq = em.createQuery(
				"SELECT a FROM FdFundserv2 a.servFundatakey WHERE  a.servCode = :fundservCode", Long.class);
		nq.setParameter("fundservCode", fundservCode);
		return nq.getResultList();
		}
		return new ArrayList<>();

		/*List<Long> resultList = null;

		if (fundservCode != null && !fundservCode.isEmpty()) {

			CriteriaBuilder cBuilder = em.getCriteriaBuilder();
			CriteriaQuery<Long> criteriaQuery = cBuilder.createQuery(Long.class);
			Root<FdFundserv2> p = criteriaQuery.from(FdFundserv2.class);

			fundservCode = "%" + fundservCode.toUpperCase() + "%";

			Predicate codePredicate = cBuilder.like(cBuilder.upper(p.get(FdFundserv2_.servCode)), fundservCode);
			criteriaQuery.where(codePredicate).select(p.get(FdFundserv2_.servFundatakey));
			TypedQuery<Long> typeCodeQuery = em.createQuery(criteriaQuery);
			resultList = typeCodeQuery.getResultList();
		}

		return resultList;*/
	}

	public FdFundserv2 getByFundataKey(Long fundatakey) {
		
		TypedQuery<FdFundserv2> nq = em.createQuery(
				"SELECT a FROM FdFundserv2 a WHERE  a.servFundatakey = :fundatakey", FdFundserv2.class);
		nq.setParameter("fundservCode", fundatakey);
		return nq.getSingleResult();


		/*FdFundserv2 fdFundserv = null;

		CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<FdFundserv2> criteriaQuery = cBuilder.createQuery(FdFundserv2.class);
		Root<FdFundserv2> p = criteriaQuery.from(FdFundserv2.class);

		Predicate codePredicate = cBuilder.equal(p.get(FdFundserv2_.servFundatakey), fundatakey);
		criteriaQuery.where(codePredicate);

		TypedQuery<FdFundserv2> typeCodeQuery = em.createQuery(criteriaQuery);

		List<FdFundserv2> results = typeCodeQuery.getResultList();
		if (results != null && !results.isEmpty()) {
			fdFundserv = results.get(0);
		}

		return fdFundserv;*/
	}

	public List<FdFundserv2> findByMgmtCompanyCode(String code) {
		EntityManager em = getEntityManager();

		try {
			TypedQuery<FdFundserv2> nq = em.createNamedQuery("FdFundserv2.findStartWithServCode", FdFundserv2.class);
			nq.setParameter("servCode", "%" + code + "%");

			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<FdFundserv2> findFundservByCriterias(String fsrvCode, FdCategory2 category) {// [[[TODO]]] washere
																								// smasse 2018-9-25
																								// review sql

		TypedQuery<FdFundserv2> nq;

		if (fsrvCode != null && !fsrvCode.isEmpty()) {

			String code = "%" + fsrvCode.toUpperCase() + "%";

			if (category != null)
				nq = em.createQuery(
						"SELECT f FROM FdFundserv2 f WHERE f.servCode like :fsrvcode AND (f.fdFund2.fdCategory = :category) AND (f.fdFund2.fundGrade IS NOT NULL)",
						FdFundserv2.class);
			else
				nq = em.createQuery(
						"SELECT f FROM FdFundserv2 f WHERE f.servCode like :fsrvcode AND (f.fdFund2.fundGrade IS NOT NULL)",
						FdFundserv2.class);

			nq.setParameter("fsrvcode", code);

			if (category != null)
				nq.setParameter("fdCategory", category);

			List<FdFundserv2> listFSRV = nq.getResultList();

			return listFSRV;
		}

		return null;
	}
}
