/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdFundFlexSearchPy;
import com.insurfact.logging.SimpleLogger;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR> 
 * @since 2019.3.29
 */
@Stateless
public class FdFundFlexSearchPyFacade extends AbstractFacade<FdFundFlexSearchPy> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdFundFlexSearchPyFacade() {
        super(FdFundFlexSearchPy.class);
    }
    
    public List<FdFundFlexSearchPy> getAllFunds() { //smasse 2019.3.29
        try{
            //Query nq= em.createQuery("SELECT f FROM FdFundFlexSearchPy f",FdFundFlexSearchPy.class);  
            TypedQuery<FdFundFlexSearchPy> nq = em.createNamedQuery("FdFundFlexSearchPy.findAll", FdFundFlexSearchPy.class);

            return nq.getResultList();
        }catch(Throwable e){
            System.out.println("FdFundFlexSearchPyFacade.getAllFunds(): "+e
                +"\n"+SimpleLogger.getStackTrace(e, 20));
            return new ArrayList<FdFundFlexSearchPy>();
        }
    }
    
    public List<FdFundFlexSearchPy> getAllFundsEnabled() { //smasse 2019.4.15
        try{
        	TypedQuery<FdFundFlexSearchPy> nq = em.createNamedQuery("FdFundFlexSearchPy.findByFundEnabled", FdFundFlexSearchPy.class);
            
            nq.setParameter("fundEnabled", "Y" );

            return nq.getResultList();
        }catch(Throwable e){
            System.out.println("FdFundFlexSearchPyFacade.getAllFundsEnabled(): "+e
                +"\n"+SimpleLogger.getStackTrace(e, 20));
            return new ArrayList<FdFundFlexSearchPy>();
        }
    }
    
    
//    public FdFundFlexSearchPy getByFundataKey(Integer fundatakey) { //smasse 2019.4.15
//
//        try {      
//            Query nq = em.createNamedQuery("FdFundFlexSearchPy.findByFundFundatakey",FdFundFlexSearchPy.class);
//            
//            nq.setParameter("fundFundatakey", fundatakey );
//            
//            return (FdFundFlexSearchPy) nq.getSingleResult();
//        }
//        catch(NoResultException e){
//            return null;
//        }    
//    }
    
    public FdFundFlexSearchPy getByFundataKey(Long fundatakey) { //smasse 2019.6.20

        try {
            Query nq = em.createNamedQuery("FdFundFlexSearchPy.findByFundFundatakey",FdFundFlexSearchPy.class);
            
            nq.setParameter("fundFundatakey", fundatakey );
            
            return (FdFundFlexSearchPy) nq.getSingleResult();
        }
        catch(NoResultException e){
            return null;
        }    
    }
}
