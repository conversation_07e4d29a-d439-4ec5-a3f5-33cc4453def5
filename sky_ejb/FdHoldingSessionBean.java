/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdHolding;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdHoldingSessionBean extends AbstractFacade<FdHolding> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdHoldingSessionBean() {
		super(FdHolding.class);
	}

	@SuppressWarnings("finally")
	public List<FdHolding> getByFundataKey(Integer fundatakey) {
		List<FdHolding> holdings = new ArrayList<>();

		EntityManager em = getEntityManager();

		try {
			TypedQuery<FdHolding> nq = em.createNamedQuery("FdHolding.findByHoldingFundatakey", FdHolding.class);
			nq.setParameter("holdingFundatakey", fundatakey);

			holdings = nq.getResultList();
		} catch (NoResultException e) {
			//
		} finally {
			return holdings;
		}
	}

	public List<FdHolding> findByHoldingIdName(Integer securityId, String holdingName, boolean market, boolean asset) {

		String sql = "";

		if (securityId != null)
			sql += "SELECT h FROM FdHolding h WHERE h.securityNameId = :holdingNameId ";
		else
			sql += "SELECT h FROM FdHolding h WHERE h.holdingSecurityName = :holdingName ";

		if (!market)
			sql += " AND (h.holdingMarketPercent IS NOT NULL)";

		if (!asset)
			sql += " AND (h.fdFund.fundTotalAssets IS NOT NULL)";

		sql += " ORDER BY h.holdingSecurityName ASC";

		TypedQuery<FdHolding> nq = em.createQuery(sql, FdHolding.class);

		if (securityId != null)
			nq.setParameter("holdingNameId", securityId);
		else
			nq.setParameter("holdingName", holdingName);

		return nq.getResultList();

	}
}
