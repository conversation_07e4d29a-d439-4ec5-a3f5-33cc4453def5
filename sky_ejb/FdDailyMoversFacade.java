/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.fundata.entity.FdDailyMovers;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdDailyMoversFacade extends AbstractFacade<FdDailyMovers> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdDailyMoversFacade() {
        super(FdDailyMovers.class);
    }
    
    public List<FdDailyMovers> findAllMovers() {
        try {
        	TypedQuery<FdDailyMovers> nq = getEntityManager().createNamedQuery("FdDailyMovers.findAll", FdDailyMovers.class);
            nq.setHint("eclipselink.read-only", "true");
            
            return nq.getResultList();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
    }
    
}
