/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.ContactsRelationship;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ContactsRelationshipFacade extends AbstractFacade<ContactsRelationship> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@EJB
	private ContactFacade contactFacade;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ContactsRelationshipFacade() {
		super(ContactsRelationship.class);
	}

	public ContactsRelationship findSingleRelationship(Contact contact1, Advisor advisor) {
		ContactsRelationship cr = null;

		System.out.println("findSingleRelationship : " + contact1);

		try {
			Query nq = getEntityManager().createQuery(
					"SELECT c FROM ContactsRelationship c WHERE  c.advisor = :advisor AND c.firstContactIntId = :contact1 AND c.type = 'S'",
					ContactsRelationship.class);
			nq.setParameter("contact1", contact1.getContactIntId());
			nq.setParameter("advisor", advisor);

			cr = (ContactsRelationship) nq.getSingleResult();

			Contact contact = contactFacade.find(cr.getFirstContactIntId());
			cr.setFirst(contact);

		} catch (Exception e) {

			return null;
		}

		return cr;
	}

	public ContactsRelationship findCoupleRelationship(Contact contact1, Contact contact2, Advisor advisor) {

		System.out.println("findCoupleRelationship : " + contact1 + " " + contact2);

		List<ContactsRelationship> list = new ArrayList<>();

		try {
			TypedQuery<ContactsRelationship> nq = getEntityManager().createQuery(
					"SELECT c FROM ContactsRelationship c WHERE  c.advisor = :advisor AND  c.firstContactIntId  IN (:contact1, :contact2)  AND c.secondContactIntId IN (:contact1, :contact2) AND c.type = 'C'",
					ContactsRelationship.class);
			nq.setParameter("contact1", contact1.getContactIntId());
			nq.setParameter("contact2", contact2.getContactIntId());
			nq.setParameter("advisor", advisor);
			nq.setMaxResults(1);

			list = nq.getResultList();

			if (list != null && !list.isEmpty()) {

				ContactsRelationship cr = list.get(0);

				Contact contact = contactFacade.find(cr.getFirstContactIntId());
				cr.setFirst(contact);

				contact = contactFacade.find(cr.getSecondContactIntId());
				cr.setSecond(contact);

				return cr;
			}
		} catch (Exception e) {

		}

		return null;
	}

	public ContactsRelationship modify(ContactsRelationship cr) {

		if (cr == null)
			return cr;

		if (cr.getFirst() != null)
			cr.setFirstContactIntId(cr.getFirst().getContactIntId());

		if (cr.getSecond() != null)
			cr.setSecondContactIntId(cr.getSecond().getContactIntId());

		// save changes
		em.merge(cr);

		return cr;
	}

	public ContactsRelationship save(ContactsRelationship cr) {

		if (cr == null)
			return cr;

		if (cr.getFirst() != null)
			cr.setFirstContactIntId(cr.getFirst().getContactIntId());

		if (cr.getSecond() != null)
			cr.setSecondContactIntId(cr.getSecond().getContactIntId());

		// create new Entity
		em.persist(cr);

		return cr;
	}

}
