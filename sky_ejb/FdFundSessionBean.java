/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdCategory;
import com.insurfact.fundata.entity.FdFund;
import com.insurfact.fundata.entity.FdMaster;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.metamodel.SingularAttribute;
import org.eclipse.persistence.internal.jpa.querydef.OrderImpl;

/**
 *
 * <AUTHOR>
 */

@Stateless
public class FdFundSessionBean extends AbstractFacade<FdFund> {

	@EJB
	private FdFundServSessionBean fundservFacade;

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdFundSessionBean() {
		super(FdFund.class);
	}

	public FdFund getByFundataKey(Integer fundatakey) {

		try {
			Query nq = em.createNamedQuery("FdFund.findByFundFundatakey", FdFund.class);

			nq.setParameter("fundFundatakey", fundatakey);

			return (FdFund) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	public FdFund getByMasterNameEn(String master) {

		try {
			Query nq = em.createNamedQuery("FdFund.findByFundMasterNameEn", FdFund.class);

			nq.setParameter("fundMasterNameEn", master);

			return (FdFund) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<String> findDistinctAttributeEntries(SingularAttribute<FdFund, String> attribute) {

		CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<String> criteriaQuery = cBuilder.createQuery(String.class);
		Root<FdFund> p = criteriaQuery.from(FdFund.class);

		Order order = new OrderImpl(p.get(attribute), true);
		criteriaQuery.select(p.get(attribute)).distinct(true).orderBy(order)
				.where(cBuilder.isNotNull(p.get(attribute)));

		TypedQuery<String> typeCodeQuery = em.createQuery(criteriaQuery);

		return typeCodeQuery.getResultList();
	}

	public List<FdFund> findByAssets(int merRange, FdCategory category, Locale locale) {

		/*
		 * <f:selectItem itemLabel="0 - 1%" itemValue="0"/> <f:selectItem
		 * itemLabel="1 - 1.5%" itemValue="1"/> <f:selectItem itemLabel="1.5 - 2%"
		 * itemValue="2"/> <f:selectItem itemLabel="2.0 - 2.5%" itemValue="3"/>
		 * <f:selectItem itemLabel="2.5 - 3%" itemValue="4"/> <f:selectItem
		 * itemLabel="3 - 3.5%" itemValue="5"/> <f:selectItem itemLabel="3.5-4%"
		 * itemValue="6"/> <f:selectItem itemLabel="4% +" itemValue="7"/> <f:selectItem
		 * itemLabel="ALL " itemValue="9"/>
		 */

		float start = 0, end = 0;
		List<FdFund> funds;

		TypedQuery<FdFund> nq = em.createQuery(
				"SELECT f FROM FdFund f WHERE f.category = :category AND f.fundMer BETWEEN :startMer AND :finishMer AND f.fundEnabled ='Y' ORDER BY f.fundMer DESC",
				FdFund.class);

		switch (merRange) {
		case 0: {
			start = 0;
			end = 1;
			break;
		}
		case 1: {
			start = 1;
			end = 1.5f;
			break;
		}
		case 2: {
			start = 1.5f;
			end = 2;
			break;
		}
		case 3: {
			start = 2;
			end = 2.5f;
			break;
		}
		case 4: {
			start = 2.5f;
			end = 3;
			break;
		}
		case 5: {
			start = 3;
			end = 3.5f;
			break;
		}
		case 6: {
			start = 3.5f;
			end = 4;
			break;
		}
		case 7: {
			start = 4;
			end = 100;
			break;
		}
		default:
			start = 0;
			end = 100;
		}

		nq.setParameter("category", category);
		nq.setParameter("startMer", start);
		nq.setParameter("finishMer", end);

		try {
			funds = nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

		return funds;
	}

	/**
	 * 
	 * @param master
	 * @param category
	 * @param french
	 * @return
	 */
	public List<FdFund> findFundByCriterias(FdMaster master, FdCategory category, boolean french) {

		TypedQuery<FdFund> nq;

		if (master != null) {
			if (category != null) {

				if (french)
//                    nq= em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND f.master = :master AND (f.fundGrade IS NOT NULL) ORDER BY f.fundNameFr",FdFund.class);
					nq = em.createQuery(
							"SELECT f FROM FdFund f WHERE f.category = :category AND f.master = :master AND f.fundEnabled ='Y' AND (f.navps IS NOT NULL) ORDER BY f.fundNameFr",
							FdFund.class);
				else
//                    nq= em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND f.master = :master AND (f.fundGrade IS NOT NULL) ORDER BY f.fundNameEn",FdFund.class);
					nq = em.createQuery(
							"SELECT f FROM FdFund f WHERE f.category = :category AND f.master = :master AND f.fundEnabled ='Y' AND (f.navps IS NOT NULL) ORDER BY f.fundNameEn",
							FdFund.class);

				nq.setParameter("category", category);
				nq.setParameter("master", master);

				return nq.getResultList();
			}

			if (french)
//                nq= em.createQuery("SELECT f FROM FdFund f WHERE f.master = :master AND (f.fundGrade IS NOT NULL) ORDER BY f.fundNameFr",FdFund.class);
				nq = em.createQuery(
						"SELECT f FROM FdFund f WHERE f.master = :master AND f.fundEnabled ='Y' AND (f.navps IS NOT NULL) ORDER BY f.fundNameFr",
						FdFund.class);
			else
//                nq= em.createQuery("SELECT f FROM FdFund f WHERE f.master = :master AND (f.fundGrade IS NOT NULL) ORDER BY f.fundNameEn",FdFund.class);
				nq = em.createQuery(
						"SELECT f FROM FdFund f WHERE f.master = :master AND f.fundEnabled ='Y' AND (f.navps IS NOT NULL) ORDER BY f.fundNameEn",
						FdFund.class);

			nq.setParameter("master", master);

			return nq.getResultList();
		}

		return null;
	}

	public List<FdFund> findAllByCategory(FdCategory category) {

		TypedQuery<FdFund> nq = em.createQuery(
				"SELECT f FROM FdFund f WHERE f.category = :category AND f.fundEnabled ='Y' ORDER BY f.fundNameEn",
				FdFund.class);
		nq.setParameter("category", category);

		return nq.getResultList();
	}

	public List<FdFund> findFundByMasterEn(String name) {

		TypedQuery<FdFund> nq = em.createQuery(
				"SELECT f FROM FdFund f WHERE f.fundMasterNameEn = :name AND f.fundEnabled ='Y'", FdFund.class);
		nq.setParameter("name", name);

		return nq.getResultList();

	}

	public List<FdFund> findTop5Gainers(Date date) {

		TypedQuery<FdFund> nq = em
				.createQuery("SELECT f FROM FdFund f WHERE f.navpsDate = :date ORDER BY f.average DESC", FdFund.class);
		nq.setParameter("date", date);
		nq.setHint("eclipselink.read-only", "true");

		nq.setMaxResults(5);

		return nq.getResultList();

	}

	public List<FdFund> findTop5Loosers(Date date) {

		TypedQuery<FdFund> nq = em
				.createQuery("SELECT f FROM FdFund f WHERE f.navpsDate = :date ORDER BY f.average ASC", FdFund.class);
		nq.setParameter("date", date);
		nq.setHint("eclipselink.read-only", "true");

		nq.setMaxResults(5);

		return nq.getResultList();
	}

}
