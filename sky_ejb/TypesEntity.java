/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.TypeClass;
import com.insurfact.skynet.entity.Types;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class TypesEntity extends AbstractFacade<Types> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    public EntityManager getEntityManager() {
        return em;
    }

    public TypesEntity() {
        super(Types.class);
    }

    public List<Types> findByIds(List<Long> ids) {
        CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
        CriteriaQuery<Types> query = criteriaBuilder.createQuery(Types.class);
        Root<Types> root = query.from(Types.class);
        query.select(root).where(root.get("typeIntId").in(ids));
        return em.createQuery(query).getResultList();
    }

    public List<Types> findTypesByTypeClass(TypeClass typeClass) {

        List<Types> types;

        try {
        	TypedQuery<Types> nq = getEntityManager().createQuery("SELECT t FROM Types t WHERE t.typeClass = :typeClass ORDER by t.typeValue ASC", Types.class);
            nq.setParameter("typeClass", typeClass);

            types = (List<Types>) nq.getResultList();

        } catch (NoResultException e) {
            return null;
        }

        return types;
    }

    public List<Types> findTypesByTypeClassId(Integer typeClassId) {

        List<Types> types;

        try {
            TypedQuery<Types> nq = getEntityManager().createQuery("SELECT t FROM Types t WHERE t.typeClass.typeClassIntId = :typeClassId ORDER by t.typeValue ASC", Types.class);
            nq.setParameter("typeClassId", typeClassId);

            types = (List<Types>) nq.getResultList();

        } catch (NoResultException e) {
            return null;
        }

        return types;
    }

    public List<Types> findByTypeClassId(Integer typeClassId) {
        TypedQuery<Types> query = em.createQuery(
                "SELECT t FROM Types t WHERE t.typeClass.typeClassIntId = :typeClassId", Types.class);
        query.setParameter("typeClassId", typeClassId);
        return query.getResultList();
    }

    public List<Types> findByTypeClassIdOrderedByValue(Integer typeClassId) {
        TypedQuery<Types> query = em.createQuery(
                "SELECT t FROM Types t WHERE t.typeClass.typeClassIntId = :typeClassId ORDER BY t.typeValue ASC", Types.class);
        query.setParameter("typeClassId", typeClassId);
        return query.getResultList();
    }

    public Types findReverseDocumentType(TypeClass tc) {
        Types type;

        try {
            Query nq = getEntityManager().createNativeQuery("select * from types where SUB_TYPE = " + tc.getTypeClassIntId(), Types.class);
            type = (Types) nq.getSingleResult();

        } catch (NoResultException e) {
            return null;
        }

        return type;
    }

    //smasse 2025-1-13 not in old c2admin project
    public Types findReverseDocumentType(Integer id) {
        Types type;

        try {
            Query nq = getEntityManager().createNativeQuery("select * from types where SUB_TYPE = " + id, Types.class);
            type = (Types) nq.getSingleResult();

        } catch (NoResultException e) {
            return null;
        }

        return type;
    }

    public Types findDocumentTypeAndSub(Integer classid, Integer id) {
        Types type;

        try {
            Query nq = getEntityManager().createNativeQuery("select * from types where type_class = " + classid + " and SUB_TYPE = " + id, Types.class);
            type = (Types) nq.getSingleResult();

        } catch (NoResultException e) {
            return null;
        }

        return type;
    }

    //smasse 2025-1-13 not in old c2admin project
    public Types findDocumentTypeAndValue(Integer classid, Integer id) {
        Types type;

        try {
            Query nq = getEntityManager().createNativeQuery("select * from types where type_class = " + classid + " and type_value = " + id, Types.class);
            type = (Types) nq.getSingleResult();

        } catch (NoResultException e) {
            return null;
        }

        return type;
    }

    //smasse 2025-1-13 not in old c2admin project
    public Types findDocumentTypeAndId(Integer classid, Integer id) {
        Types type;
        System.out.println("sql for type: " + "select * from types where type_class = " + classid + " and TYPE_INT_ID = " + id);

        try {
            Query nq = getEntityManager().createNativeQuery("select * from types where type_class = " + classid + " and TYPE_INT_ID = " + id, Types.class);
            type = (Types) nq.getSingleResult();

        } catch (NoResultException e) {
            return null;
        }

        return type;
    }

    //smasse 2025-1-13 not in old c2admin project
    @SuppressWarnings("unchecked")
	public List<Types> findAllCategoriesTypesForDocuments(String listOfTypes, Integer typeClass) {
        List<Types> types;
        try {
            Query nq = getEntityManager().createNativeQuery("select * from TYPES where TYPE_CLASS = " + typeClass + " and  TYPE_INT_ID in (" + listOfTypes + ")", Types.class);
            types = nq.getResultList();

        } catch (NoResultException e) {
            return null;
        }

        return types;
    }

    //smasse 2025-1-13 not in old c2admin project
    @SuppressWarnings("unchecked")
	public List<Types> findAllCategoriesForDocuments(String listOfTypes) {
        List<Types> types;
        /*System.out.println("sqll: " + "select * from TYPES where  SUB_TYPE in (select TYPE_CLASS_INT_ID from TYPE_CLASS "
                + "where TYPE_CLASS_INT_ID in (SELECT TYPE_CLASS from TYPES where TYPE_INT_ID in (select sf.FILE_TYPE from STORED_FILE "
                + "sf where sf.TYPE_ = 9 and sf.FILE_TYPE in (" + listOfTypes + "))))");*/
        try {
            Query nq = getEntityManager().createNativeQuery("select * from TYPES where  SUB_TYPE in (select TYPE_CLASS_INT_ID from TYPE_CLASS "
                    + "where TYPE_CLASS_INT_ID in (SELECT TYPE_CLASS from TYPES where TYPE_INT_ID in (select sf.FILE_TYPE from STORED_FILE "
                    + "sf where sf.TYPE_ = 9 and sf.FILE_TYPE in (" + listOfTypes + "))))", Types.class);
            types = nq.getResultList();

        } catch (NoResultException e) {
            return null;
        }

        return types;
    }

    public Integer getNextValue(TypeClass typeClass) {

        Integer val;

        try {
            Query nq = getEntityManager().createQuery("SELECT MAX(t.typeValue) FROM Types t WHERE t.typeClass = :typeClass ORDER by t.typeValue ASC", Types.class);
            nq.setParameter("typeClass", typeClass);

            val = (Integer) nq.getSingleResult();

        } catch (NoResultException e) {
            return 0;
        }

        if (val == null) {
            return 0;
        }

        return ++val;
    }
}
