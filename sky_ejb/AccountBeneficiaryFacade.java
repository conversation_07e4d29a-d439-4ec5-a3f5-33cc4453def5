/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.AccountBeneficiary;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AccountBeneficiaryFacade extends AbstractFacade<AccountBeneficiary> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public AccountBeneficiaryFacade() {
        super(AccountBeneficiary.class);
    }
    
    public AccountBeneficiary getPrimary(AccountBeneficiary accountBeneficiary){
        AccountBeneficiary primary = null;
        
        if(accountBeneficiary.getHouseHoldId() !=null){
            primary = em.find(AccountBeneficiary.class, accountBeneficiary.getHouseHoldId());
        }
        
        return primary;
    }

}
