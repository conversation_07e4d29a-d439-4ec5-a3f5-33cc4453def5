/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.YahooFinanceFeed;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class YahooFinanceFeedFacade extends AbstractFacade<YahooFinanceFeed> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public YahooFinanceFeedFacade() {
        super(YahooFinanceFeed.class);
    }
    
    public List<YahooFinanceFeed> findAllEnabledSymbols() {

        try {      
        	TypedQuery<YahooFinanceFeed> nq = em.createNamedQuery("YahooFinanceFeed.findByEnabled",YahooFinanceFeed.class);
            nq.setParameter("enabled", "Y" );
            
            return nq.getResultList();
        }
        catch(NoResultException e){
            return null;
        }    
        
    } 
    
}
