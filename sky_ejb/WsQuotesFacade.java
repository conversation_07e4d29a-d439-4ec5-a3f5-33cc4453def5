/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.WsQuotes;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList; 
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class WsQuotesFacade extends AbstractFacade<WsQuotes> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    @Resource(lookup = "jdbc/Skytest")
    private DataSource ds;

    private Connection getConnection() {

        try {

            return ds.getConnection();
        } catch (SQLException e) {

            e.printStackTrace();
        }
        return null;
    }

    public WsQuotesFacade() {
        super(WsQuotes.class);
    }

    public void purgeQuote(WsQuotes w) {
        em.remove(getEntityManager().merge(w));
    }

    public List<WsQuotes> findByUsers(int number) {

        Query query = em.createNativeQuery("SELECT * from WS_QUOTES where WEB_SERVICE_USERS = '" + number + "'", WsQuotes.class);

        @SuppressWarnings("unchecked")
		List<WsQuotes> list = query.getResultList();

        return list;
    }

    /*
    public List<SunlifeDeclined> findByBranch(Branch branch) {

        Query nq = getEntityManager().createNamedQuery("SunlifeDeclined.findByBranch", SunlifeDeclined.class);
        nq.setParameter("branch", branch);
        System.out.println("sunDeclFacade 49 markRegCode=" + branch.getBranchPrimaryName());
        return nq.getResultList();
    }
     */
    public List<Integer[]> findWSUsers() {

        List<Integer[]> users = new ArrayList<>();

        ResultSet rst = null;

        Connection connection = getConnection();
        if (connection == null) {
            return users;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = "SELECT WEB_SERVICE_INT_ID , USERS FROM WEB_SERVICE_USERS ";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return users;
            }

            while (rst.next()) {
                Integer[] toAdd = new Integer[2];
                toAdd[0] = rst.getInt("WEB_SERVICE_INT_ID");
                toAdd[1] = rst.getInt("USERS");
                users.add(toAdd);
            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return users;
        } // return the connection to the pool

        return users;
    }

    public Integer findIdWSUsers(int userIntId) {

        Integer users = 0;

        ResultSet rst = null;

        Connection connection = getConnection();
        if (connection == null) {
            return users;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = "SELECT WEB_SERVICE_INT_ID , USERS FROM WEB_SERVICE_USERS where USERS = '" + userIntId + "'";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                ptStmt.close();
                connection.close();
                return null;
            }

            if (rst.next()) {
                users = rst.getInt("WEB_SERVICE_INT_ID");
            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return null;
        } // return the connection to the pool

        return users;
    }

}
