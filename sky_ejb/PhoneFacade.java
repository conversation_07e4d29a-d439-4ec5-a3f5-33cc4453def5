/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Phone;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class PhoneFacade extends AbstractFacade<Phone> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public PhoneFacade() {
		super(Phone.class);
	}

	public Phone quickEdit(Phone entity) {

		try {
			em.merge(entity);

		} catch (Exception ex) {
			String msg = ex.getLocalizedMessage();
			System.err.println(msg);
		}

		return entity;
	}

	public Phone refresh(Phone entity) {
		return find(entity.getPhoneIntId());
	}

	public Phone findPhoneByDetail(String areaCode, String number, String extension, int type, String primaryStr) {

		List<Phone> phones = null;

		String queryStr = "SELECT p FROM Phone p WHERE p.areaCode = :areaCode AND p.phoneNumber = :phoneNumber ";

		if ((extension != null) && (!extension.isEmpty())) {
			queryStr += " AND p.extension = :extension ";
		}

		queryStr += " AND p.isPrimary = :isPrimary AND p.type = :type";

//        System.out.println("-----  Phone Query = "+ QueryStr);

		try {
			TypedQuery<Phone> pq = em.createQuery(queryStr, Phone.class);

			pq.setParameter("areaCode", areaCode);
			pq.setParameter("phoneNumber", number);

			if ((extension != null) && (!extension.isEmpty())) {
				pq.setParameter("extension", extension);
			}

			pq.setParameter("type", type);
			pq.setParameter("isPrimary", primaryStr);

			// retrieve the Entity(es)
			phones = pq.getResultList();

			if ((phones != null) && (phones.size() > 0)) {
//                System.out.println("-----  PhoneFacade, More than one Phone was returned ="+phones.size());
				return phones.get(0);
			}
			return null;

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Contact> findByPhoneNumber(String area, String number, String masterCode, Integer type) {

		List<Contact> contacts = new ArrayList<>();

		if (area != null && area.isEmpty())
			area = null;

		if (number != null && number.isEmpty())
			number = null;

//        System.out.println("findByPhoneNumber " + masterCode + " type="+ type);

		try {
			TypedQuery<Phone> nq = null;

			if (area != null && number == null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code", Phone.class);
				nq.setParameter("code", area);
			}

			if (area == null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.phoneNumber like :number", Phone.class);
				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (area != null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code AND  a.phoneNumber like :number",
						Phone.class);
				nq.setParameter("code", area.trim());

				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (nq == null)
				return contacts;

			List<Phone> phones = nq.getResultList();

			if (phones == null || phones.isEmpty()) {
				return contacts;
			}

			for (Phone p : phones) {

				for (Contact c : p.getContactList()) {

//                    System.out.println(c.getName() + " " + c.getContactType() + " master="+ c.getMasterCode());
					if (c.getMasterCode() == null || !c.getMasterCode().equalsIgnoreCase(masterCode)) {
						continue;
					}

					if (c.getContactType() == null || !c.getContactType().equals(type)) {
						continue;
					}

					contacts.add(c);
				}

			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return contacts;
	}

	public List<Contact> findByPhoneNumber(String area, String number, String masterCode, List<Integer> types) {

		List<Contact> contacts = new ArrayList<>();

		if (area != null && area.isEmpty())
			area = null;

		if (number != null && number.isEmpty())
			number = null;

//        System.out.println("findByPhoneNumber " + masterCode + " type="+ type);

		try {
			TypedQuery<Phone> nq = null;

			if (area != null && number == null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code", Phone.class);
				nq.setParameter("code", area);
			}

			if (area == null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.phoneNumber like :number", Phone.class);
				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (area != null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code AND  a.phoneNumber like :number",
						Phone.class);
				nq.setParameter("code", area.trim());

				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (nq == null)
				return contacts;

			List<Phone> phones = nq.getResultList();

			if (phones == null || phones.isEmpty()) {
				return contacts;
			}

			for (Phone p : phones) {

				for (Contact c : p.getContactList()) {

//                    System.out.println(c.getName() + " " + c.getContactType() + " master="+ c.getMasterCode());
					if (c.getMasterCode() == null || !c.getMasterCode().equalsIgnoreCase(masterCode)) {
						continue;
					}

					if (c.getContactType() == null || !types.contains(c.getContactType())) {
						continue;
					}

					contacts.add(c);
				}

			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return contacts;
	}
}
