/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.constant.Constants;
import com.insurfact.skynet.entity.Address;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Branch;
import com.insurfact.skynet.entity.Client;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Country;
import com.insurfact.skynet.entity.Email;
import com.insurfact.skynet.entity.HouseholdContact;
import com.insurfact.skynet.entity.Opportunity;
import com.insurfact.skynet.entity.Organization;
import com.insurfact.skynet.entity.Phone;
import com.insurfact.skynet.entity.Province;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CRMFacade {

	@EJB
	private CountryFacade countryFacade;

	@EJB
	private ProvinceFacade provinceFacade;

	@EJB
	private ContactFacade contactFacade;

	@EJB
	private AddressFacade addressFacade;

	@EJB
	private AdvisorFacade advisorFacade;

	@EJB
	private PhoneFacade phoneFacade;

	@EJB
	private EmailFacade emailFacade;

	@EJB
	private AlertFacade alertFacade;

	@EJB
	private ActivityFacade activityFacade;

	@EJB
	private ClientFacade clientFacade;

	@EJB
	private UsersFacade usersFacade;

	@EJB
	private BranchFacade branchFacade;

	@EJB
	private OrganizationFacade organizationFacade;

	@EJB
	private HouseholdContactFacade householdContactFacade;

	@EJB
	private OpportunityFacade opportunityFacade;

	@EJB
	private AddressBookFacade addressBookFacade;

	@EJB
	private ContactsRelationshipFacade contactsRelationshipFacade;

	public void safeDeleteFullAdvisor(Advisor advisor) {
		// remove contractEft

		if (advisor.getContact().getUsers() != null) {
			alertFacade.removeByOwner(advisor.getContact().getUsers());
			activityFacade.removeByContact(advisor.getContact().getUsers());
			activityFacade.removeByOwner(advisor.getContact().getUsers());
			usersFacade.remove(advisor.getContact().getUsers());
		}
		/*
		 * if (advisor.getContact().getAddressBook() != null) {
		 * addressBookFacade.remove(advisor.getContact().getAddressBook()); }
		 */

		advisor.getContact().setUsers(null);

		safeDeleteContact(advisor.getContact());

	}

	public void safeDeleteFullUser(Users user) {
		// remove contractEft

		alertFacade.removeByOwner(user);
		activityFacade.removeByContact(user);
		activityFacade.removeByOwner(user);
		usersFacade.remove(user);
		advisorFacade.remove(user.getContact().getAdvisor());
		usersFacade.remove(user);
		safeDeleteContact(user.getContact());

	}

	public void safeDeleteFullAdvisor(Advisor advisor, Users user) {
		// remove contractEft

		if (advisor.getContact().getUsers() != null) {
			alertFacade.removeByOwner(advisor.getContact().getUsers());
			activityFacade.removeByContact(advisor.getContact().getUsers());
			activityFacade.removeByOwner(advisor.getContact().getUsers());
			activityFacade.removeByOwnerNull(user, advisor.getContact());
			usersFacade.remove(advisor.getContact().getUsers());
		}
		/*
		 * if (advisor.getContact().getAddressBook() != null) {
		 * addressBookFacade.remove(advisor.getContact().getAddressBook()); }
		 */

		if (advisor.getContact().getUsers() != null) {
			advisor.getContact().getOnboardingStatuses().remove(advisor.getContact().getUsers().getOnboarding());
		}

		advisor.getContact().setUsers(null);

		safeDeleteContact(advisor.getContact());

	}

	public void safeDeleteContact(Contact contact) {

		List<Address> addresses = contact.getAddressList();
		List<Phone> phones = contact.getPhoneList();
		List<Email> emails = contact.getEmailList();

		System.out.println(">>> safeDeleteContact [before] : " + contact.getName() + "[" + contact.getContactIntId()
				+ "] addresses=" + addresses.size() + " emails=" + emails.size() + " phones=" + phones.size());

		// safe Delete Address
		List<Integer> ids = new ArrayList<>();
		for (Address address : addresses) {
			ids.add(address.getAddressIntId());
		}

		for (Integer id : ids) {
			Address address = getAddress(id);
			if (address != null) {
				safeDeleteAddress(address, contact);
			}
		}

		// safe Delete Email
		ids = new ArrayList<>();
		for (Email email : emails) {
			ids.add(email.getEmailIntId());
		}

		for (Integer id : ids) {
			Email email = getEmail(id);

			if (email != null) {
				safeDeleteEmail(email, contact);
			}
		}

		// safe Delete Phone
		ids = new ArrayList<>();
		for (Phone phone : phones) {
			ids.add(phone.getPhoneIntId());
		}

		for (Integer id : ids) {
			Phone phone = getPhone(id);

			if (phone != null) {
				safeDeletePhone(phone, contact);
			}
		}

		// refetch Contact
		contact = refreshContact(contact);
		// addresses = contact.getAddressList();
		// phones = contact.getPhoneList();
		// emails = contact.getEmailList();

		for (HouseholdContact c : householdContactFacade.findHouseholdContact(contact)) {
			householdContactFacade.remove(c);
		}

		System.out.println(">>> safeDeleteContact [after] : " + contact.getName() + "[" + contact.getContactIntId()
				+ "] addresses=" + addresses.size() + " emails=" + emails.size() + " phones=" + phones.size());

		/*
		 * if (contact.getAddressBook() != null) {
		 * addressBookFacade.remove(contact.getAddressBook()); }
		 */
		// delete Contact
		contactFacade.remove(contact);
	}

	// new for the household
	public void safeDeleteContactH(Contact contact) {

		List<Address> addresses = contact.getAddressList();
		List<Phone> phones = contact.getPhoneList();
		List<Email> emails = contact.getEmailList();

		System.out.println(">>> safeDeleteContacth [before] : " + contact.getName() + "[" + contact.getContactIntId()
				+ "] addresses=" + addresses.size() + " emails=" + emails.size() + " phones=" + phones.size());

		// safe Delete Address
		List<Integer> ids = new ArrayList<>();
		for (Address address : addresses) {
			ids.add(address.getAddressIntId());
		}

		for (Integer id : ids) {
			Address address = getAddress(id);
			if (address != null) {
				safeDeleteAddress(address, contact);
			}
		}

		// safe Delete Email
		ids = new ArrayList<>();
		for (Email email : emails) {
			ids.add(email.getEmailIntId());
		}

		for (Integer id : ids) {
			Email email = getEmail(id);

			if (email != null) {
				safeDeleteEmail(email, contact);
			}
		}

		// safe Delete Phone
		ids = new ArrayList<>();
		for (Phone phone : phones) {
			ids.add(phone.getPhoneIntId());
		}

		for (Integer id : ids) {
			Phone phone = getPhone(id);

			if (phone != null) {
				safeDeletePhone(phone, contact);
			}
		}

		// refetch Contact
		contact = refreshContact(contact);
		// addresses = contact.getAddressList();
		// phones = contact.getPhoneList();
		// emails = contact.getEmailList();

		if (contact != null) {
			for (HouseholdContact c : householdContactFacade.findHouseholdContact(contact)) {
				householdContactFacade.remove(c);
			}

			List<Opportunity> oppoToDelete = opportunityFacade.findByContact(contact);
			for (Opportunity o : oppoToDelete) {
				// contactsRelationshipFacade.remove(o.getContactsRelationship());
				opportunityFacade.remove(o);
			}

			System.out.println(">>> safeDeleteContacth [after] : " + contact.getName() + "[" + contact.getContactIntId()
					+ "] addresses=" + addresses.size() + " emails=" + emails.size() + " phones=" + phones.size());

			// delete Contact
			contactFacade.deleteContact(contact);
		}
	}

	public void safeDeleteContactHAll(List<Contact> contacts) {

		for (Contact contact : contacts) {
			List<Address> addresses = contact.getAddressList();
			List<Phone> phones = contact.getPhoneList();
			List<Email> emails = contact.getEmailList();

			System.out.println(">>> safeDeleteContact [before] : " + contact.getName() + "[" + contact.getContactIntId()
					+ "] addresses=" + addresses.size() + " emails=" + emails.size() + " phones=" + phones.size());

			// safe Delete Address
			List<Integer> ids = new ArrayList<>();
			for (Address address : addresses) {
				ids.add(address.getAddressIntId());
			}

			for (Integer id : ids) {
				Address address = getAddress(id);
				if (address != null) {
					safeDeleteAddress(address, contact);
				}
			}

			// safe Delete Email
			ids = new ArrayList<>();
			for (Email email : emails) {
				ids.add(email.getEmailIntId());
			}

			for (Integer id : ids) {
				Email email = getEmail(id);

				if (email != null) {
					safeDeleteEmail(email, contact);
				}
			}

			// safe Delete Phone
			ids = new ArrayList<>();
			for (Phone phone : phones) {
				ids.add(phone.getPhoneIntId());
			}

			for (Integer id : ids) {
				Phone phone = getPhone(id);

				if (phone != null) {
					safeDeletePhone(phone, contact);
				}
			}

			List<Opportunity> oppoToDelete = opportunityFacade.findByContact(contact);
			for (Opportunity o : oppoToDelete) {
				// contactsRelationshipFacade.remove(o.getContactsRelationship());
				opportunityFacade.remove(o);
			}

			// refetch Contact
			contact = refreshContact(contact);
			// addresses = contact.getAddressList();
			// phones = contact.getPhoneList();
			// emails = contact.getEmailList();

			System.out.println(">>> safeDeleteContact [after] : " + contact.getName() + "[" + contact.getContactIntId()
					+ "] addresses=" + addresses.size() + " emails=" + emails.size() + " phones=" + phones.size());

			// delete Contact
			contactFacade.deleteContact(contact);
		}
	}

	public void safeDeleteAddress(Address address, Contact contact) {

		// list of Contact sharing this Address
		List<Contact> contacts = address.getContactList();

		// list of Address for this Contact
		List<Address> addresses = contact.getAddressList();

		// remove the reference of Address in the Contact
		addresses.remove(address);
		contact.setAddressList(addresses);

		contact.getOnboardingStatuses().remove(address.getOnboarding());

		// update Contact
		contactFacade.edit(contact);

		if (contacts != null && contacts.size() > 1) {
			System.out.println("** Address is shared amount " + contacts.size() + " Contacts.  NOT removing Address : "
					+ address.getAddressIntId());
		} else {
			// Address no shared, go ahead with delete Address
			System.out.println("** removing address : " + address.getAddressIntId());

			addressFacade.remove(address);
		}
	}

	public void safeDeleteEmail(Email email, Contact contact) {

		// list of Contact sharing this Email
		List<Contact> contacts = email.getContactList();

		// list of Address for this Contact
		List<Email> emails = contact.getEmailList();

		// remove the reference of Email in the Contact
		emails.remove(email);
		contact.setEmailList(emails);

		contact.getOnboardingStatuses().remove(email.getOnboarding());

		// update Contact
		contactFacade.edit(contact);

		if (contacts != null && contacts.size() > 1) {
			System.out.println("** Email is shared amount " + contacts.size() + " Contacts.  NOT removing Email : "
					+ email.getEmailIntId());
		} else {
			// Email no shared, go ahead with delete Email
			System.out.println("** Removing email : " + email.getEmailIntId());

			emailFacade.remove(email);
		}
	}

	public void safeDeletePhone(Phone phone, Contact contact) {

		// list of Contact sharing this Phone
		List<Contact> contacts = phone.getContactList();

		// list of Phone for this Contact
		List<Phone> phones = contact.getPhoneList();

		// remove the reference of Phone in the Contact
		phones.remove(phone);
		contact.setPhoneList(phones);

		contact.getOnboardingStatuses().remove(phone.getOnboarding());

		// update Contact
		contactFacade.edit(contact);

		if (contacts != null && contacts.size() > 1) {
			System.out.println("** Phone is shared amount " + contacts.size() + " Contacts.  NOT removing Phone : "
					+ phone.getPhoneIntId());
		} else {
			// Phone no shared, go ahead with delete Phone
			System.out.println("** Removing phone : " + phone.getPhoneIntId());

			phoneFacade.remove(phone);
		}
	}

	public void createClient(Client client) {

		clientFacade.create(client);

	}

	public Integer getNextClientId() {

		return clientFacade.getNextClientId();
	}

	public List<Address> findAllAddress() {
		return addressFacade.findAll();
	}

	public List<Address> findAllAddress(int start, int end) {

		return addressFacade.findAllInRange(start, end);
	}

	public void editAddress(Address address) {
		addressFacade.edit(address);
	}

	public List<Phone> findAllPhone() {
		return phoneFacade.findAll();
	}

	public List<Email> findAllEmail() {
		return emailFacade.findAll();
	}

	public List<Branch> findAllBranchesForOrganization(Organization org) {
		return branchFacade.findAllBranchesForOrganization(org);
	}

	public List<Branch> findAllBranchesForMarketingRegion(Integer region, Organization org) {
		return branchFacade.findByMarketingRegion(region, org);
	}

	public List<Users> findMarketingRegionUsers(Integer region, Integer advisorLock) {
		return usersFacade.findByMarketingRegion(region, advisorLock);
	}

	public List<Users> findBranchUsers(Branch branch) {
		return usersFacade.findByBranch(branch);
	}

	public List<Users> findAdvisorUsers(Advisor advisor) {

		return usersFacade.findAllForAdvisorLock(advisor.getAdvisorIntId());
	}

	public void createBranch(Branch branch) {
		branchFacade.create(branch);
	}

	public void editBranch(Branch branch) {
		branchFacade.edit(branch);
	}

	public void editOrganization(Organization o) {
		organizationFacade.edit(o);
	}

	public void createOrganization(Organization o) {
		organizationFacade.create(o);
	}

	public Contact createContact(Contact contact) {

		try {
			if (contact != null) {
				contactFacade.create(contact);

				contactFacade.refresh(contact);
			}

		} catch (Exception e) {
			System.err.println("##### ERROR Creating Contact : " + contact.getPublicName());

			return null;
		}

		return contact;
	}

	public void createEmail(Email email) {

		emailFacade.create(email);
	}

	public void removeEmail(Email email) {

		emailFacade.remove(email);
	}

	public void removeContact(Contact contact) {
		contactFacade.remove(contact);
	}

	public void createPhone(Phone phone) {

		phoneFacade.create(phone);
	}

	public void removePhone(Phone phone) {

		phoneFacade.remove(phone);
	}

	public void createAddress(Address address) {

		addressFacade.create(address);
	}

	public void removeAddress(Address address) {

		addressFacade.remove(address);
	}

	/**
	 * Perform full Contact with child edit
	 *
	 * @param contact
	 * @return
	 */
	public Contact editContact(Contact contact) {

		if (contact != null) {

			contactFacade.edit(contact);
		}

		return contact;
	}

	/**
	 * Perform Contact Only Entity edit
	 *
	 * @param contact
	 * @return
	 */
	public Contact quickEdit(Contact contact) {
		return contactFacade.quickEdit(contact);
	}

	public Email quickEdit(Email entity) {
		return emailFacade.quickEdit(entity);
	}

	public Phone quickEdit(Phone entity) {
		return phoneFacade.quickEdit(entity);
	}

	public Address quickEdit(Address entity) {
		return addressFacade.quickEdit(entity);
	}

	//////////// REFRESH ENTITIES ////////////////
	//
	public Contact refreshContact(Contact entity) {
		return contactFacade.refresh(entity);
	}

	public Address refreshAddress(Address entity) {
		return addressFacade.refresh(entity);
	}

	public Phone refreshPhone(Phone entity) {
		return phoneFacade.refresh(entity);
	}

	public Email refreshEmail(Email entity) {
		return emailFacade.refresh(entity);
	}

	/**
	 * returns the first country found by the Alpha name
	 *
	 * @param alphaName
	 * @return
	 */
	public Country getCountryByAlpha(String alphaName) {

		List<Country> resultList = countryFacade.findByAlpha(alphaName);

		if (resultList != null && !resultList.isEmpty()) {
			return resultList.get(0);
		}
		return null;
	}

	/**
	 * retrieve the Country Entity matching the 3 characters ISO Ex.: CAN for Canada
	 *
	 * @param code
	 * @return Country or a null if not found
	 */
	public Country getCountryByCode(String code) {

		Country country = null;

		country = countryFacade.findByCountryCode(code);

		if (country == null) {
			System.out.println("******** Could not find a Country Entity with country-code:" + code);
		}
		return country;
	}

	public Branch getBranch(Integer id) {
		return branchFacade.find(id);
	}

	public Branch findBranch(Organization org, Integer branchId) {
		return branchFacade.getByBranchNumber(org, branchId);
	}

	public Contact getContact(Integer id) {
		return contactFacade.find(id);
	}

	public Address getAddress(Integer id) {
		return addressFacade.find(id);
	}

	public Email getEmail(Integer id) {
		return emailFacade.find(id);
	}

	public Phone getPhone(Integer id) {
		return phoneFacade.find(id);
	}

	public Organization getOrganization(Integer id) {
		return organizationFacade.find(id);
	}

	public Contact getSunlifeAdvisorContact(String number) {
		System.out.println("line 457 CRMFacade number='" + number + "'");
		return contactFacade.getSunlifeAdvisorContact(number);
	}

	public Integer resetContactImportFlag(String masterCode) {
		return contactFacade.resetContactImportFlag(masterCode);
	}

	/**
	 * Returns the list of Contacts that matches the given name 'system wide'
	 *
	 * @param org
	 * @return
	 */
	public List<Contact> findAllForOrganization(Organization org) {
		return contactFacade.findAllForOrganization(org);
	}

	public List<Client> findClientsByNameRestricted(String name, String masterCode) {
		return contactFacade.findClientsByNamesRestricted(name, masterCode);
	}

	public List<Contact> findContactsByNameRestricted(String name, String masterCode) {
		return contactFacade.findContactByNamesRestricted(name, masterCode);
	}

	public List<Contact> findContactUsersByTypeAndNamesRestricted(String name, String masterCode, Integer type,
			Advisor aga) {
		return contactFacade.findContactUsersByTypeAndNamesRestricted(name, masterCode, type, aga);
	}

	public List<Contact> findContactByTypeAndNamesRestricted(String name, String masterCode, Integer type) {
		return contactFacade.findContactByTypeAndNamesRestricted(name, masterCode, type);
	}

	public List<Contact> findContactByTypeAndNamesRestricted(String name, String masterCode, List<Integer> types) {
		return contactFacade.findContactByTypesAndNamesRestricted(name, masterCode, types);
	}

	public List<Contact> findContactsByTypeRestricted(Integer type, String masterCode) {
		return contactFacade.findContactByTypeRestricted(type, masterCode);
	}

	public List<Contact> findContactsByType(Integer type) {
		return contactFacade.findContactByType(type);
	}

	public List<Contact> findProvidersByType(Integer type) {
		return contactFacade.findProvidersByType(type);
	}

	public List<Contact> findContactByTypesRestricted(String masterCode, List<Integer> types) {
		return contactFacade.findContactByTypesRestricted(masterCode, types);
	}

	public List<Contact> findContactByTypesAndNames(String name, List<Integer> types) {
		return contactFacade.findContactByTypesAndNames(name, types);
	}

	public List<Contact> findContactByNames(String name) {
		return contactFacade.findContactByNames(name);
	}

	public List<Contact> findContactByFlagRestricted(String flag, String masterCode) {
		return contactFacade.findContactByFlagRestricted(flag, masterCode);
	}

	public List<Contact> findContactUsersByTypeAndEmployeeNumberRestricted(String number, String masterCode,
			Integer type) {
		return contactFacade.findContactUsersByTypeAndEmployeeNumberRestricted(number, masterCode, type);
	}

	public List<Contact> findActiveUsersContactsByTypeRestricted(Integer type, String masterCode) {
		return contactFacade.findActiveUsersContactByTypeRestricted(type, masterCode);
	}

	public List<Contact> findInactivatedSunlifeContactsRestricted(String masterCode) {
		return contactFacade.findInactivatedSunlifeContactsRestricted(masterCode);
	}

	public List<Contact> findSunlifeContacts() {
		return contactFacade.findSunlifeContacts();
	}

	public List<Contact> findByEmailAddress(String email, String masterCode, Integer type) {
		return emailFacade.findByEmailAddress(email, masterCode, type);
	}

	public List<Contact> findByEmailAddress(String email, String masterCode, List<Integer> types) {
		return emailFacade.findByEmailAddress(email, masterCode, types);
	}

	public List<Contact> findByPhoneNumber(String area, String number, String masterCode, Integer type) {
		return phoneFacade.findByPhoneNumber(area, number, masterCode, type);
	}

	public List<Contact> findByPhoneNumber(String area, String number, String masterCode, List<Integer> types) {
		return phoneFacade.findByPhoneNumber(area, number, masterCode, types);
	}

	public Email findByEmail(String email) {
		return emailFacade.findByEMail(email);
	}

	/**
	 * Returns the list of Contacts for a given Master Code
	 *
	 * @param code
	 * @return
	 */
	public List<Contact> findContactsByMasterCode(String code) {
		return contactFacade.findByMasterCode(code);
	}

	public List<Country> findAllCountries() {
		return countryFacade.findAll();
	}

	public Province getProvinceByCode(String code) {
		return provinceFacade.findByCode(code);
	}

	public Province getProvinceByOldCode(Integer code) {
		return provinceFacade.findByOldCode(code);
	}

	/**
	 * Build a new Address Entity with valid Province and Country entities. This
	 * method will optionally persist the new Address entity
	 *
	 * @param line1
	 * @param line2
	 * @param line3
	 * @param city
	 * @param postalCode
	 * @param provinceCode
	 * @param countryCode
	 * @param primary
	 * @param careOf
	 * @param solicitation
	 * @param persist
	 * @return a new Address entity
	 */
	public Address createAddressEntity(String line1, String line2, String line3, String city, String postalCode,
			String provinceCode, String countryCode, String careOf, boolean primary, boolean solicitation) {

		Address address = null;

		// convert everything to upper case
		line1 = line1.toUpperCase();
		line2 = line2.toUpperCase();
		line3 = line3.toUpperCase();
		city = city.toUpperCase();
		postalCode = postalCode.toUpperCase();
		countryCode = countryCode.toUpperCase();

		// fix potential white space and remove other characters
		postalCode = fixPostalCode(postalCode);

		//////////////////////////////////////
		// try to lookup an existing Address entity...
		address = addressFacade.findAddressByDetail(line1, line2, city, postalCode, careOf);

		if (address != null) {
//            System.out.println("####### Warning, CRMFacade.createAddressEntity()  found an existing Address Entity with the same properties.  Returning it instead of creating a new one.");
			return address;
		}

		// no existing Address found, lets create one
		if (address == null) {
			address = new Address();
		}

		///////////////////////////////////////////////////////////
		//
		// Try to fix address with line1 or line2 empty
		//
		// Let's getContact out if we need to do a left shift of line1 <- line2 <- line3
		if ((line1 == null) || (line1.isEmpty())) {
//            System.out.println("!!!!!!!!  Shifting address lines:  line1 <-- line2 <-- (null)");

			line1 = line2;
			line2 = line3;
			line3 = "";

			// re-check again if line1 is empty, and shift one last time!
			if ((line1 == null) || (line1.isEmpty())) {
//                System.out.println("!!!!!!!!  Shifting again address lines:  line1 <-- (null) -- (null)");
				line1 = line2;
				line2 = ""; // empty
				line3 = ""; // empty
			}

			if ((line1 == null) || (line1.isEmpty())) {
				line1 = "NO valid address lines.  Please fix manually!";
//                System.out.println("*******  Shifting address lines failed..");
				return null;
			}
		}

		if (line2.isEmpty()) {
			line2 = null;
		}

		if (line3.isEmpty()) {
			line3 = null;
		}

		// set adress lines
		address.setAddressLine1(line1);
		address.setAddressLine2(line2);
		address.setAddressLine3(line3);

		// city name
		address.setCity(city);

		// maybe need a method to cleanup space in the postal code
		address.setPostalCode(postalCode);

		// find matching Country entity with a three (3) letter code. Using ISO spec
		Country country = getCountryByCode(countryCode);

		if (country == null) {
			return null;
		}

		address.setCountry(country);

		// find matching Province with a two (2) letter code
		Province province = getProvinceByCode(provinceCode);

		if (province == null) {
			return null;
		}

		address.setProvince(province);

		address.setPrimary(primary);
		address.setSendSolicitation(solicitation);

		address.setCareOf(careOf);

		// 2 = business
		address.setType(Constants.Email_Type.Business.getCode());

		//////////////////////////////////////
		// save the new Address Entity
		addressFacade.create(address);

		//////////////////////////////////////
		// refetch the Entity with a populated PK :)
		// address = addressDAO.findAddressByDetail(line1, line2, city, postalCode,
		////////////////////////////////////// careOf);
//        System.out.println(">>>  Address Entity : " + address.toString() + " created.");
		return address;
	}

	public Phone createPhoneEntity(String areaCode, String number, String extension, int type, boolean primary) {

		String primaryStr = primary ? "Y" : "N";
		Phone phone = null;

		phone = phoneFacade.findPhoneByDetail(areaCode, number, extension, type, primaryStr);

		if (phone != null) {
//            System.out.println("####### Warning, CRMFacade.createPhoneEntity()  found an existing Phone Entity with the same properties.  Returning it instead of creating a new one.");
			return phone;
		}

		phone = new Phone();

		phone.setAreaCode(areaCode);
		phone.setPhoneNumber(number);
		phone.setExtension(extension);
		phone.setPrimary(primary);

		phone.setType(type);

		phoneFacade.create(phone);

		// re fetch it with the PK populated
		// phone = phoneDAO.findPhoneByDetail(areaCode, number, extension, type,
		// primaryStr);
		return phone;
	}

	public Email createEmailEntity(String emailAddress, int type, boolean primary, boolean sendSolicatation) {

		String primaryStr = primary ? "Y" : "N";
		String sendSolicatationStr = sendSolicatation ? "Y" : "N";

		Email email = null;

		email = emailFacade.findEmailByDetail(emailAddress, type, primaryStr, sendSolicatationStr);

		if (email != null) {
//            System.out.println("####### Warning, CRMFacade.createEmailEntity()  found an existing Email Entity with the same properties.  Returning it instead of creating a new one.");
			return email;
		}

		email = new Email();

		email.setEmailAddress(emailAddress);
		email.setSendSolicitation(sendSolicatation);
		email.setPrimary(primary);

		email.setType(type);

		emailFacade.create(email);

		// re fetch it with the PK populated
		// email = emailDAO.findEmailByDetail(emailAddress, type, primaryStr,
		// sendSolicatationStr);
		return email;
	}

	public Phone findPhoneByDetail(String areaCode, String number, String extension, int type, String primaryStr) {
		return phoneFacade.findPhoneByDetail(areaCode, number, extension, type, primaryStr);
	}

	public Email findEmailByDetail(String emailAddress, int type, String primaryStr, String sendSolicatationStr) {
		return emailFacade.findEmailByDetail(emailAddress, type, primaryStr, sendSolicatationStr);
	}

	public Address findAddressByDetail(String addressLine1, String addressLine2, String city, String postalCode,
			String careOf) {
		return addressFacade.findAddressByDetail(addressLine1, addressLine2, city, postalCode, careOf);
	}

	/**
	 * convenient method that cleans up postal code and insure proper format
	 *
	 * @param postalCode
	 * @return String cleaned up postal code
	 */
	public String fixPostalCode(String postalCode) {

		// clean up white space
		postalCode = postalCode.trim();
		postalCode = postalCode.replace('-', ' ');
		postalCode = postalCode.replace(',', ' ');
		postalCode = postalCode.replace('/', ' ');
		postalCode = postalCode.replace('.', ' ');

		String tmp = "";

		// nothing to do
		if (postalCode.length() < 7) {
			return postalCode;
		}

		int s = postalCode.indexOf(" ");
		int e = postalCode.lastIndexOf(" ");

		if (s > -1) {
			tmp += postalCode.substring(0, s);
			tmp += postalCode.substring(e + 1);
		}
		return tmp;

	}

	public List<Contact> findClients() {

		return contactFacade.findClients();
	}

	public List<Contact> findClientAdvisors() {

		return contactFacade.findClientAdvisors();
	}

	public Contact getContactByContactIntId(int contactIntId) {
		return contactFacade.findByContactId(contactIntId);
	}

	public List<Contact> findContactsByCode(String code) {
		return contactFacade.getContactByCode(code);
	}

	public List<Contact> findContactsByBranch(String financialCenters) {
		return contactFacade.findContactsByBranch(financialCenters);
	}

	public List<Contact> findContactsByBranchInforce(String financialCenters) {
		return contactFacade.findContactsByBranchInforce(financialCenters);
	}

}
