/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.EApplication;
import com.insurfact.skynet.entity.ProductSupplier;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class EApplicationFacade extends AbstractFacade<EApplication> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public EApplicationFacade() {
		super(EApplication.class);
	}

	public EApplication findByFilename(String filename) {
		EntityManager em = getEntityManager();

		try {
			Query nq = em.createNamedQuery("EApplication.findByFilename", EApplication.class);

			nq.setParameter("filename", filename);

			return (EApplication) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<EApplication> findByProductSupplierAndProcessed(ProductSupplier productSupplier, boolean processed) {
		EntityManager em = getEntityManager();

		try {
			TypedQuery<EApplication> nq = em.createNamedQuery("EApplication.findByDetail", EApplication.class);

			nq.setParameter("productSupplier", productSupplier);

			if (processed) {
				nq.setParameter("processed", "Y");
			} else {
				nq.setParameter("processed", "N");
			}

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

	public List<EApplication> findByProcessed(boolean processed) {
		EntityManager em = getEntityManager();

		try {
			TypedQuery<EApplication> nq = em.createNamedQuery("EApplication.findByProcessed", EApplication.class);

			if (processed) {
				nq.setParameter("processed", "Y");
			} else {
				nq.setParameter("processed", "N");
			}

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

}
