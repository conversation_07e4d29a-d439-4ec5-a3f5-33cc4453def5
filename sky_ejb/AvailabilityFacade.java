/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Availability;
import com.insurfact.skynet.entity.Users; 
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AvailabilityFacade extends AbstractFacade<Availability> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public AvailabilityFacade() {
        super(Availability.class);
    }
    
    public Availability findForAdvisor(Advisor advisor){
        
        Availability availability = null;
         try {
            Query nq = getEntityManager().createNamedQuery("Availability.findByAdvisor", Availability.class);
            nq.setParameter("advisor", advisor);
            
            availability = (Availability) nq.getSingleResult();

        } catch (Exception e) {

            return null;
        }
         
        return availability;
    }
    
    public Availability findForUsers(Users users){
        
         Availability availability = null;
         try {
            Query nq = getEntityManager().createNamedQuery("Availability.findByUsers", Availability.class);
            nq.setParameter("users", users);
            
            availability = (Availability) nq.getSingleResult();

        } catch (Exception e) {

            return null;
        }
         
        return availability;
    }
    
}
