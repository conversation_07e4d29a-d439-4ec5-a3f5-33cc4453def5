/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.im.IMCompanySoftware;
import com.insurfact.im.IMCompany; 
import com.insurfact.skynet.entity.Company; 
import com.insurfact.skynet.entity.Contact; 
import com.insurfact.skynet.entity.ContractSetup;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery; 
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CompanyFacade extends AbstractFacade<Company> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Resource(lookup = "jdbc/Orains")
	private DataSource ds;

	public CompanyFacade() {
		super(Company.class);
	}

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (SQLException e) {

			e.printStackTrace();
		}
		return null;
	}

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public int getNextCompanyNumber() {
		Query q = em.createNativeQuery("SELECT COMPANY_NUMBER_SEQ.nextval from DUAL");
		BigDecimal result = (BigDecimal) q.getSingleResult();
		return result.intValue();
	}

	public List<Company> findByCompanyType(int type) {
		TypedQuery<Company> query = em.createNamedQuery("Company.findByCompanyType", Company.class);
		query.setParameter("companyType", type);

		List<Company> companies = query.getResultList();

		if (companies != null && !companies.isEmpty()) {
			return companies;
		}

		return new ArrayList<>();
	}

	public List<Company> findByBuildingType(int type) {
		TypedQuery<Company> query = em.createNamedQuery("Company.findByBuildingType", Company.class);
		query.setParameter("buildingType", type);

		List<Company> companies = query.getResultList();

		if (companies != null && !companies.isEmpty()) {
			return companies;
		}

		return new ArrayList<>();
	}

	public Company findCompanyByCompanyId(int id) {

		Query query = em.createNamedQuery("Company.findByCompanyId", Company.class);
		query.setParameter("companyId", id);

		Company company = (Company) query.getSingleResult();

		if (company != null) {
			return company;
		}

		return null;
	}

	@Override
	public void create(Company company) {

		Date now = Calendar.getInstance().getTime();

		if (company.getContractSetupList() == null) {
			company.setContractSetupList(new ArrayList<>());
		}

		// setting the contractsetup list
		List<ContractSetup> attachedContractSetupList = new ArrayList<>();
		for (ContractSetup contractSetupListContractSetupToAttach : company.getContractSetupList()) {
			contractSetupListContractSetupToAttach = em.getReference(contractSetupListContractSetupToAttach.getClass(),
					contractSetupListContractSetupToAttach.getContractSetupIntId());
			attachedContractSetupList.add(contractSetupListContractSetupToAttach);
		}
		company.setContractSetupList(attachedContractSetupList);

		// persist company
		company.setLastModificationDate(now);
		company.setCreationDate(now);
		em.persist(company);

	}

	public void quickCreate(Company company) {

		em.persist(company);

	}

	@Override
	public void edit(Company company) {

		Date now = Calendar.getInstance().getTime();

		// Company persistentCompany = em.find(Company.class,
		// company.getCompanyIntId());

		// List<ContractSetup> contractSetupListOld =
		// persistentCompany.getContractSetupList();
		List<ContractSetup> contractSetupListNew = company.getContractSetupList();

		// setting the contractSetup list
		List<ContractSetup> attachedContractSetupListNew = new ArrayList<>();
		for (ContractSetup contractSetupListNewContractSetupToAttach : contractSetupListNew) {
			contractSetupListNewContractSetupToAttach = em.getReference(
					contractSetupListNewContractSetupToAttach.getClass(),
					contractSetupListNewContractSetupToAttach.getContractSetupIntId());
			attachedContractSetupListNew.add(contractSetupListNewContractSetupToAttach);
		}
		contractSetupListNew = attachedContractSetupListNew;
		company.setContractSetupList(contractSetupListNew);

		// merging the company
		company.setLastModificationDate(now);
		company = em.merge(company);

	}

	public void destroy(Integer id) {

		// Date now = Calendar.getInstance().getTime();

		Company company;
		company = em.getReference(Company.class, id);

		em.remove(company);
	}

	public List<Company> companiesByContact(Integer contactId) {

		TypedQuery<Company> nq = em.createQuery("SELECT a FROM Company a WHERE  a.contact.contactIntId = :contactId",
				Company.class);
		nq.setParameter("contactIntId", contactId);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<Company>
		 * criteriaQuery = cBuilder.createQuery(Company.class); Root<Company> p =
		 * criteriaQuery.from(Company.class);
		 * 
		 * Predicate contactPredicate =
		 * cBuilder.equal((p.get(Company_.contact).get(Contact_.contactIntId)),
		 * contactId);
		 * 
		 * criteriaQuery.where(contactPredicate); TypedQuery<Company> query =
		 * em.createQuery(criteriaQuery); return query.getResultList();
		 */
	}

	/*
	 * The companyName param will be split by space and we will fetch each part of
	 * it on the three column of contact table that is French and English Name we
	 * will retain the intersection of all the lookup results.
	 */
	public List<Company> findCompanyByNames(String companyName) {

		// long now = System.nanoTime();

		TypedQuery<Company> nq;

		String[] names = companyName.split(" ");

		Set<Company> companySet = new HashSet<>();
		// Set<Company> tempSet = new HashSet<>();

		for (String name : names) {

			name = "%" + name.toUpperCase() + "%";

			nq = em.createQuery(
					"SELECT a FROM Company a WHERE  UPPER(a.nameFr) like :companyName or UPPER(a.nameEn) like :companyName",
					Company.class);
			nq.setParameter("companyName", companyName);

			if (companySet.isEmpty()) {
				companySet.addAll(nq.getResultList());
			} else {
				companySet.retainAll(nq.getResultList());
			}

			/*
			 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<Company>
			 * criteriaQuery = cBuilder.createQuery(Company.class); Root<Company> p =
			 * criteriaQuery.from(Company.class);
			 * 
			 * Predicate frenchName = cBuilder.like(cBuilder.upper(p.get(Company_.nameFr)),
			 * name); Predicate englishName =
			 * cBuilder.like(cBuilder.upper(p.get(Company_.nameEn)), name);
			 * 
			 * criteriaQuery.where(frenchName); TypedQuery<Company> firstNameQuery =
			 * em.createQuery(criteriaQuery);
			 * tempSet.addAll(firstNameQuery.getResultList());
			 * 
			 * criteriaQuery.where(englishName); TypedQuery<Company> lastNameQuery =
			 * em.createQuery(criteriaQuery); tempSet.addAll(lastNameQuery.getResultList());
			 * 
			 * if (companySet.isEmpty()) {
			 * 
			 * companySet.addAll(tempSet);
			 * 
			 * } else {
			 * 
			 * companySet.retainAll(tempSet); }
			 * 
			 * tempSet.clear();
			 */

		}

//        System.out.println("CompanyFacade.findByNames took = " + ((double) System.nanoTime() - now) / 1000000.0 + "ms");
		return new ArrayList<>(companySet);
	}

	public List<Company> findCompanyByNameEn(String companyName) {
		
		TypedQuery<Company> nq = em.createQuery("SELECT a FROM Company a WHERE  UPPER(a.nameEn) like :companyName",
				Company.class);
		nq.setParameter("companyName", companyName);
		return nq.getResultList();

		/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<Company> criteriaQuery = cBuilder.createQuery(Company.class);
		Root<Company> p = criteriaQuery.from(Company.class);

		Predicate englishName = cBuilder.like(cBuilder.upper(p.get(Company_.nameEn)), companyName);

		criteriaQuery.where(englishName);
		TypedQuery<Company> query = em.createQuery(criteriaQuery);
		return query.getResultList();*/
	}

	public byte[] getCompanyLogo(int companyId, boolean french) {

		Connection connection;

		ResultSet rst = null;

		byte[] array = null;

		String sql;

		if (french) {
			sql = "select logo_fr from im_company_logo where companyid=" + companyId;
		} else {
			sql = "select logo from im_company_logo where companyid=" + companyId;
		}

		connection = getConnection();
		if (connection != null) {
			try (Statement ptStmt = connection.createStatement();) {
				rst = ptStmt.executeQuery(sql);

				while (rst.next()) {
					array = rst.getBytes(1);
				}
			} catch (Exception e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
			}
		}
		return array;
	}

	public List<IMCompanySoftware> getIMCompanySoftware() {

		ResultSet rst = null;

		IMCompanySoftware software = null;
		Connection connection = getConnection();

		List<IMCompanySoftware> compSoft = new ArrayList<>();

		if (connection != null) {

			try (Statement ptStmt = connection.createStatement();) {

				String sql = "select c.NAMEENGLISH, c.NAMEFRENCH,               "
						+ "       s.SOFTWAREID, s.COMPANYID, s.DOWNLOAD_URL, "
						+ "       s.DOWNLOAD_URL_FR,s.VERSION, s.SFT_NAME,   " + "       s.SFT_NAME_FRE       "
						+ "  from COMPANY_SOFTWARE s,  " + "       IM_COMPANY       c   "
						+ " where s.CD='Y'             " + "   and c.COMPANYID=s.COMPANYID ";
				rst = ptStmt.executeQuery(sql);

				while (rst.next()) {

					// fetch all the software information
					software = new IMCompanySoftware();

					software.setCompanyId(rst.getInt("COMPANYID"));
					software.setCompanyNameEn(rst.getString("NAMEENGLISH"));
					software.setCompanyNameFr(rst.getString("NAMEFRENCH"));
					software.setSoftwareId(rst.getInt("SOFTWAREID"));
					software.setVersion(rst.getString("VERSION"));
					software.setDownloadUrl(rst.getString("DOWNLOAD_URL"));
					software.setDownloadUrlFR(rst.getString("DOWNLOAD_URL_FR"));
					software.setSoftwareName(rst.getString("SFT_NAME"));
					software.setSoftwareNameFR(rst.getString("SFT_NAME_FRE"));

					compSoft.add(software);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return new ArrayList<>();
			}
		}

		return compSoft;
	}

	public String getIMCompanyName(int compID, int lang) {

		ResultSet rst = null;

		Connection connection = getConnection();

		String compName = "";

		if (connection != null) {

			try (Statement ptStmt = connection.createStatement();) {

				String sql = " select c.NAMEENGLISH,  " + "        c.NAMEFRENCH    " + "   from IM_COMPANY  c   "
						+ "  where c.COMPANYID=" + compID;
				rst = ptStmt.executeQuery(sql);

				while (rst.next()) {
					if (lang == 1) {
						compName = rst.getString("NAMEENGLISH");
					} else {
						compName = rst.getString("NAMEFRENCH");
					}
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return "";
			} // return the connection to the pool

		}

		return compName;
	}

	/**
	 *
	 * @param type
	 * @return
	 */
	public List<IMCompany> findBanksInIMCompanies() {

		List<IMCompany> companies = new ArrayList<>();

		String sqlReq = "select * from IM_COMPANY WHERE INSTITUTIONNO IS NOT NULL ORDER BY NAMEENGLISH";

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(sqlReq);

			if (rs == null) {
				connection.close();
				return new ArrayList<>();
			}

			// iterate through the resultset
			while (rs.next()) {
				IMCompany cie = new IMCompany();
				cie.setCompanyId(rs.getInt("COMPANYID"));
				cie.setCompanyType(rs.getString("COMPANYTYPE"));
				cie.setDescriptionEN(rs.getString("NAMEENGLISH"));
				cie.setDescriptionFR(rs.getString("NAMEFRENCH"));
				cie.setParentCompany(rs.getInt("PARENTCOMPANY"));
				cie.setInstitutionNumber(rs.getString("INSTITUTIONNO"));

				// adding to the list
				companies.add(cie);
			}
			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return new ArrayList<>();
		}

		return companies;
	}

	public List<IMCompany> findAllIMCompanies() {

		List<IMCompany> companies = new ArrayList<>();

		String sqlReq = " select * from IM_COMPANY ";

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(sqlReq);

			// iterate through the resultset
			while (rs.next()) {
				IMCompany cie = new IMCompany();
				cie.setCompanyId(rs.getInt("COMPANYID"));
				cie.setCompanyType(rs.getString("COMPANYTYPE"));
				cie.setDescriptionEN(rs.getString("NAMEENGLISH"));
				cie.setDescriptionFR(rs.getString("NAMEFRENCH"));

				// adding to the list
				companies.add(cie);

			}

			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return new ArrayList<>();
		}

		return companies;
	}

	public List<IMCompany> findBanksInIMCompaniesByNumber(String number) {

		List<IMCompany> companies = new ArrayList<>();

		StringBuilder buffer = new StringBuilder();

		buffer.append("select * from IM_COMPANY WHERE INSTITUTIONNO=\'").append(number).append('\'');

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(buffer.toString());

			// iterate through the resultset
			while (rs.next()) {
				IMCompany cie = new IMCompany();
				cie.setCompanyId(rs.getInt("COMPANYID"));
				cie.setCompanyType(rs.getString("COMPANYTYPE"));
				cie.setDescriptionEN(rs.getString("NAMEENGLISH"));
				cie.setDescriptionFR(rs.getString("NAMEFRENCH"));
				cie.setParentCompany(rs.getInt("PARENTCOMPANY"));
				cie.setInstitutionNumber(rs.getString("INSTITUTIONNO"));

				// adding to the list
				companies.add(cie);

//                System.out.println(cie.toString());
			}

//            System.out.println(">>>> findIMCompanyByType >>> companies : " + companies.size());
			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return new ArrayList<>();
		}

		return companies;
	}

	public List<IMCompany> findInsuranceCompaniesNoSel(String compIDs) { // excludes compIDs sent in parameter

		List<IMCompany> companies = new ArrayList<>();
		ResultSet rst = null;
		IMCompany company = null;
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH " + "   FROM im_company c        "
					+ "  WHERE c.companytype in ('I','B')   " + "    AND (HAS_LIFE='Y' OR HAS_CRIT='Y' OR HAS_DI='Y') ";
			if (!compIDs.equals("")) {
				sql += "        AND c.COMPANYID NOT IN (" + compIDs + ")";
			}

			sql += " ORDER by 2";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findInsuranceCompanies(int provinceId) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH FROM im_company c, company_by_prov p WHERE c.companytype='I'"
					+ " AND p.companyid=c.companyid" + " AND p.provstate IN (0," + provinceId + ")";

			sql += " ORDER by 2";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	/**
	 *
	 * @param provinceId
	 * @param dieFistLast 1=die_fist, 2, die_last
	 * @param lang        eng=1, french=2
	 * @return
	 */
	public List<IMCompany> findJointLIFEInsuranceCompanies(int provinceId, int dieFistLast, int lang) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "SELECT  DISTINCT p.companyid, c.NAMEENGLISH, c.NAMEFRENCH "
					+ "FROM im_lifeproductdetail l, im_companyproduct p, im_company c, company_by_prov v "
					+ " WHERE l.productid = p.productid  AND p.companyid = c.companyid and c.companytype = 'I' and p.AVAILABLE = 'Y'";

			if (dieFistLast == 1) {
				sql += " AND l.sing_jo1_jolast in (3,2,6,7) ";
			} else {
				sql += " AND l.sing_jo1_jolast in (5,4,6,7) ";
			}

			sql += " AND c.HAS_LIFE = 'Y' AND p.productclass = 'LIFE' AND v.companyid=c.companyid AND v.provstate IN (0,"
					+ provinceId + ") ";

			sql += "ORDER by 2";
			// sql += (lang == 1) ? "ORDER by 2" : " ORDER BY 3";

			// System.out.println(sql);
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findJointLIFEInsuranceCompaniesTest(int provinceId, int dieFistLast, int lang) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;
		IMCompany company = null;
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "SELECT  DISTINCT p.companyid, c.NAMEENGLISH, c.NAMEFRENCH "
					+ "FROM im_lifeproductdetail l, im_companyproduct p, im_company c, company_by_prov v "
					+ " WHERE l.productid = p.productid  AND p.companyid = c.companyid and c.companytype = 'I'";

			if (dieFistLast == 1) {
				sql += " AND l.sing_jo1_jolast in (3,2,6,7) ";
			} else {
				sql += " AND l.sing_jo1_jolast in (5,4,6,7) ";
			}

			sql += " AND c.HAS_LIFE = 'Y' AND p.productclass = 'LIFE' AND v.companyid=c.companyid AND v.provstate IN (0,"
					+ provinceId + ") ";

			sql += "ORDER by 2";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	/**
	 * @param provinceId
	 * @param dieFistLast 1=die_fist, 2, die_last
	 * @param lang        eng=1, french=2
	 * @return
	 */
	public List<IMCompany> findJointCRITInsuranceCompanies(int provinceId, int dieFistLast, int lang) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "SELECT  DISTINCT p.companyid, c.NAMEENGLISH, c.NAMEFRENCH "
					+ " FROM im_lifeproductdetail l, im_companyproduct p, im_company c, company_by_prov v "
					+ " WHERE l.productid = p.productid  AND p.companyid = c.companyid and c.companytype = 'I' and p.AVAILABLE = 'Y'";

			if (dieFistLast == 1) {
				sql += " AND l.sing_jo1_jolast in (3,2,6,7) ";
			} else {
				sql += " AND l.sing_jo1_jolast in (5,4,6,7) ";
			}

			sql += " AND c.HAS_CRIT = 'Y' AND p.productclass = 'CRIT' AND v.companyid=c.companyid AND v.provstate IN (0,"
					+ provinceId + ") ";

			sql += (lang == 1) ? "ORDER by 2" : " ORDER BY 3";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findAllLIFEInsuranceCompanies() {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;
		IMCompany company = null;
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH FROM im_company c WHERE c.companytype='I' AND c.has_life='Y'  ORDER by 1";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				ptStmt.close();
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findAllCRITInsuranceCompanies() {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH FROM im_company c WHERE c.companytype='I' AND c.has_crit='Y'  ORDER by 1";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findAllTravInsuranceCompanies() {

		List<IMCompany> companies = new ArrayList<>();
		ResultSet rst = null;
		IMCompany company = null;
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH FROM im_company c WHERE c.has_travel='Y' ORDER by 2";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findAllCompaniesWithPRodChanges() {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select c.COMPANYID,       " + "        c.NAMEENGLISH,     " + "        c.NAMEFRENCH       "
					+ "   FROM im_company c       " + "  WHERE c.companytype='I'  " + "    AND c.has_life='Y'     "
					+ "    AND c.COMPANYID IN     " + "    (SELECT p.COMPANYID    " + "       FROM IM_COMPANYPRODUCT p "
					+ "      WHERE AVAILABLE='Y'  " + "        AND ACTIVE_DATE > ADD_MONTHS(SYSDATE, -1))"
					+ "ORDER by 1";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findAllTravInsuranceCompaniesNoSel(String compIDs) {// excludes compIDs from search

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH " + "  FROM im_company c     "
					+ " WHERE c.has_travel='Y' ";
			if (!compIDs.equals("")) {
				sql += "     AND c.COMPANYID NOT IN (" + compIDs + ")";
			}

			sql += " ORDER by 2";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			ptStmt.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findAllInvestmentCompanies() {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH FROM im_company c "
					+ "  WHERE c.companytype='F' AND c.has_invest='Y'  " + "  ORDER by 1";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findLIFEInsuranceCompaniesDets(int provinceId, String companyType, boolean isFrench,
			String polType, int nearAge) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH, c.ABBRV_ENGLISH, c.ABBRV_FRENCH  "
					+ "   FROM im_company c, company_by_prov p          " + "  WHERE c.companytype='" + companyType
					+ "'         " + "    AND c.has_life='Y'                     "
					+ "    AND p.companyid=c.companyid            " + "    AND p.provstate IN (0," + provinceId + ")  "
					+ "    AND c.COMPANYID IN        " + "    (SELECT p.COMPANYID       "
					+ "       FROM IM_COMPANYPRODUCT p "
					+ "      WHERE (p.AVAILABLE='Y' OR (p.MANAGEMENT_ONLY='Y' AND p.COMPANYID=61)) ";

			if (polType.equals("G")) {
				sql += "           AND p.PROFILE_TYPE IN (1, 2)  "; // profile type 1= cfiles and suntool 2= suntool
			}
			sql += "        AND p.PRODUCTCLASS='LIFE' " + "        AND p.PRODUCTID IN      "
					+ "       (SELECT DISTINCT PRODUCTID   " + "          FROM IM_LIFE_PREMIUM_RATE "
					+ "         WHERE " + nearAge + " BETWEEN   "
					+ "            PLAN_MIN_ISSUE_AGE AND PLAN_MAX_ISSUE_AGE)) ";
			sql += " ORDER by 2";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));
				company.setShortNameEN(rst.getString(4));
				company.setShortNameFR(rst.getString(5));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findLIFEInsuranceCompanies(int provinceId, String companyType) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;
		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH, c.ABBRV_ENGLISH, c.ABBRV_FRENCH FROM im_company c, company_by_prov p WHERE c.companytype='"
					+ companyType + "' AND c.has_life='Y'" + " AND p.companyid=c.companyid" + " AND p.provstate IN (0,"
					+ provinceId + ")";

			sql += " ORDER by 4";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));
				company.setShortNameEN(rst.getString(4));
				company.setShortNameFR(rst.getString(5));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findLIFEInsuranceCompaniesFrench(int provinceId, String companyType) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH, c.ABBRV_ENGLISH, c.ABBRV_FRENCH FROM im_company c, company_by_prov p WHERE c.companytype='"
					+ companyType + "' AND c.has_life='Y'" + " AND p.companyid=c.companyid" + " AND p.provstate IN (0,"
					+ provinceId + ") ";

			sql += " ORDER by 4";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));
				company.setShortNameEN(rst.getString(4));
				company.setShortNameFR(rst.getString(5));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findCRITInsuranceCompanies(int provinceId, String companyType) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH, c.ABBRV_ENGLISH, c.ABBRV_FRENCH FROM im_company c, company_by_prov p WHERE c.companytype='"
					+ companyType + "' AND c.has_crit='Y'" + " AND p.companyid=c.companyid" + " AND p.provstate IN (0,"
					+ provinceId + ")";

			sql += " ORDER by 4";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));
				company.setShortNameEN(rst.getString(4));
				company.setShortNameFR(rst.getString(5));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findCRITInsuranceCompaniesFrench(int provinceId, String companyType) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH, c.ABBRV_ENGLISH, c.ABBRV_FRENCH FROM im_company c, company_by_prov p WHERE c.companytype='"
					+ companyType + "' AND c.has_crit='Y'" + " AND p.companyid=c.companyid" + " AND p.provstate IN (0,"
					+ provinceId + ")";

			sql += " ORDER by 4";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));
				company.setShortNameEN(rst.getString(4));
				company.setShortNameFR(rst.getString(5));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findDIInsuranceCompanies(int provinceId, String companyType) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH, c.ABBRV_ENGLISH, c.ABBRV_FRENCH FROM im_company c, company_by_prov p WHERE c.companytype='"
					+ companyType + "' AND c.has_di='Y'" + " AND p.companyid=c.companyid" + " AND p.provstate IN (0,"
					+ provinceId + ")";

			sql += " ORDER by 4";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));
				company.setShortNameEN(rst.getString(4));
				company.setShortNameFR(rst.getString(5));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public List<IMCompany> findDIInsuranceCompaniesFrench(int provinceId, String companyType) {

		List<IMCompany> companies = new ArrayList<>();

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.COMPANYID, c.NAMEENGLISH, c.NAMEFRENCH, c.ABBRV_ENGLISH, c.ABBRV_FRENCH FROM im_company c, company_by_prov p WHERE c.companytype='"
					+ companyType + "' AND c.has_di='Y'" + " AND p.companyid=c.companyid" + " AND p.provstate IN (0,"
					+ provinceId + ")";

			sql += " ORDER by 4";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				// fetch all the product information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));
				company.setShortNameEN(rst.getString(4));
				company.setShortNameFR(rst.getString(5));

				companies.add(company);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return companies;
	}

	public IMCompany getIMCompany(int companyId) {

		ResultSet rst = null;

		IMCompany company = null;
		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select COMPANYID, NAMEENGLISH, NAMEFRENCH, ABBRV_ENGLISH, ABBRV_FRENCH, CANNEXID,INSTITUTIONNO  from im_company where companyId="
					+ companyId;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {
				// fetch all the company information
				company = new IMCompany();
				company.setCompanyId(rst.getInt(1));
				company.setDescriptionEN(rst.getString(2));
				company.setDescriptionFR(rst.getString(3));
				company.setShortNameEN(rst.getString(4));
				company.setShortNameFR(rst.getString(5));
				company.setCannexid(rst.getInt("cannexid"));
				company.setInstitutionNumber(rst.getString("INSTITUTIONNO"));
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return company;
	}

	public List<Integer> findSupportedTypes(int companyId, String prodClass) {

		ResultSet rst = null;

		List<Integer> types = new ArrayList<>();
		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select distinct producttype from im_companyproduct where available='Y' and companyId="
					+ companyId + " and productclass='" + prodClass + "'";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return new ArrayList<>();
			}

			while (rst.next()) {

				types.add(rst.getInt(1));

			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return new ArrayList<>();
		} // return the connection to the pool

		return types;
	}

	public Company findByContact(Contact contact) {

		try {
			Query nq = em.createNativeQuery("SELECT * FROM COMPANY where CONTACT = " + contact.getContactIntId(),
					Company.class);

			if (nq.getMaxResults() > 0) {
				return (Company) nq.getResultList().get(0);
			} else {
				return null;
			}

		} catch (ArrayIndexOutOfBoundsException e) {
		}

		return null;
	}

}
