/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.KnowledgeBase;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class KnowledgeBaseFacade extends AbstractFacade<KnowledgeBase> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public KnowledgeBaseFacade() {
        super(KnowledgeBase.class);
    }
    
    public List<KnowledgeBase> findByCategory(Integer category){
        
        TypedQuery<KnowledgeBase> query = em.createQuery("SELECT k FROM KnowledgeBase k WHERE k.category = :category AND k.webContent.contentCategory = 9 ORDER by k.creationDate DESC", KnowledgeBase.class); 
        query.setParameter("category", category);
        
        return query.getResultList();
    }
    
    public List<KnowledgeBase> findByTag(List<String> tags){
        
    	TypedQuery<KnowledgeBase> query = em.createQuery("SELECT k FROM KnowledgeBase k WHERE k.tags IN :tags ORDER by k.creationDate DESC", KnowledgeBase.class); 
        query.setParameter("tags", tags);
        
        return query.getResultList();
    }
    
}
