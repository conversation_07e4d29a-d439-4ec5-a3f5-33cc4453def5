/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Policy;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class PolicyFacade extends AbstractFacade<Policy> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public PolicyFacade() {
		super(Policy.class);
	}

	public Policy refreshPolicy(Policy policy) {

		Integer policyID = policy.getPolicyIntId();
		if (policyID != null) {
			policy = em.find(policy.getClass(), policyID);
		}
		return policy;
	}

	public Policy getByNumber(String policyNumber) {

		TypedQuery<Policy> nq = em.createQuery("SELECT a FROM Policy a WHERE a.policyNumber = :policyNumber ",
				Policy.class);
		nq.setParameter("policyNumber", policyNumber);
		return nq.getSingleResult();

		/*
		 * List<Policy> results = new ArrayList<>();
		 * 
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<Policy>
		 * criteriaQuery = cBuilder.createQuery(Policy.class); Root<Policy> p =
		 * criteriaQuery.from(Policy.class);
		 * 
		 * Predicate policyPredicate = cBuilder.equal(p.get(Policy_.policyNumber),
		 * policyNumber);
		 * 
		 * criteriaQuery.where(policyPredicate);
		 * 
		 * TypedQuery<Policy> query = em.createQuery(criteriaQuery);
		 * 
		 * results = query.getResultList();
		 * 
		 * if (results != null && !results.isEmpty()) { return results.get(0); }
		 * 
		 * return null;
		 */
	}

	@SuppressWarnings("unchecked")
	public List<Policy> findPolicyByNumber(String policyNumber) {
		Query nq = em.createNativeQuery("select * from policy where POLICY_NUMBER LIKE '%" + policyNumber + "%'",
				Policy.class);
		return nq.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Policy> findPolicyByNumber(String policyNumber, String financialCenters) {
		Query nq = em.createNativeQuery(
				"select p.* from policy p INNER JOIN account ac on p.ACCOUNT = ac.ACCOUNT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP ao on ac.ACCOUNT_INT_ID = ao.ACCOUNT INNER JOIN client cli on cli.CLIENT_INT_ID = ao.CLIENT INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  where adCon.BRANCH in "
						+ financialCenters + " and p.POLICY_NUMBER LIKE '%" + policyNumber + "%'",
				Policy.class);
		return nq.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Policy> findPolicyByExpire(int expire, boolean filter) {
		String expireSQL = "";
		if (expire == 0) {
			expireSQL = "";
		} else if (expire == 1) {
			expireSQL = "   AND ADD_MONTHS(ps.TERMEXP_DATE, -" + expire + ") <= CURRENT_DATE ";
		} else {
			expireSQL = "   AND CURRENT_DATE BETWEEN  ADD_MONTHS(ps.TERMEXP_DATE, -" + expire
					+ ") and ADD_MONTHS(ps.TERMEXP_DATE, -" + (expire - 2) + ") ";
		}
		String filterString = "";
		if (filter) {
			filterString = "and p.POLICY_STATUS in (5, 12, 21)";
		} else {
			filterString = "and p.POLICY_STATUS in (11, 36, 43, 44)";
		}
		Query nq = em.createNativeQuery(
				"select p.* from policy p, POLICY_SETTLING ps where p.policy_int_id = ps.POLICY_SETTLING_INT_ID "
						+ expireSQL + filterString,
				Policy.class);
		return nq.getResultList();
	}

}
