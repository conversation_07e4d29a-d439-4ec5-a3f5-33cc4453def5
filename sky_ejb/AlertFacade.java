/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Activity; 
import com.insurfact.skynet.entity.Alert;
import com.insurfact.skynet.entity.CalendarEvent;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TemporalType;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AlertFacade extends AbstractFacade<Alert> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public AlertFacade() {
        super(Alert.class);
    }

    public List<Alert> findByOwner(Users users) {
        try {
        	TypedQuery<Alert> nq = getEntityManager().createNamedQuery("Alert.findByOwner", Alert.class);
            nq.setParameter("owner", users);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public void removeByOwner(Users user) {
        Query nq = getEntityManager().createNativeQuery("delete from alert where owner = " + user.getUserIntId());
        nq.executeUpdate();
    }

    public List<Alert> findByActivity(Activity activity) {
        try {
        	TypedQuery<Alert> nq = getEntityManager().createNamedQuery("Alert.findByActivity", Alert.class);
            nq.setParameter("activity", activity);
            nq.setMaxResults(1);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    
    public List<Alert> findByCalendarEvent(CalendarEvent calendarEvent) {
        try {
        	TypedQuery<Alert> nq = getEntityManager().createNamedQuery("Alert.findByCalendarEvent", Alert.class);
            nq.setParameter("calendarEvent", calendarEvent);
            nq.setMaxResults(1);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Alert> findAlertByOwnerAndDateRange(Calendar startDate, Calendar endDate, Users users) {

        //System.out.println("AlertFacade line78 startDate=" + startDate.getTime() + " endDate=" + endDate.getTime());
        try {
            /*Query nq = getEntityManager().createQuery(" SELECT a FROM Alert a    "+
                    "  WHERE a.owner= :owner  "+
                    "    AND a.startDate between :startDate AND :endDate "+
                    "    AND a.endDate between :startDate AND :endDate "+
                    "    AND a.acknowledge = 'N'", Alert.class);*/
        	TypedQuery<Alert> nq = getEntityManager().createQuery(" SELECT a FROM Alert a    "
                    + "  WHERE a.owner= :owner  "
                    + "    AND a.startDate <= :startDate "
                    + "    AND a.endDate between :startDate AND :endDate "
                    + "    AND a.acknowledge = 'N'", Alert.class);
            nq.setParameter("owner", users);
            nq.setParameter("startDate", startDate, TemporalType.TIMESTAMP);
            nq.setParameter("endDate", endDate, TemporalType.TIMESTAMP);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

}
