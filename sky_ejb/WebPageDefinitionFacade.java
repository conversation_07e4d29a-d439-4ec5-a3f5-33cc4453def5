/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.WebPageDefinition;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class WebPageDefinitionFacade extends AbstractFacade<WebPageDefinition> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public WebPageDefinitionFacade() {
        super(WebPageDefinition.class);
    }
    
    public WebPageDefinition findByPageId(String id) {
        try {
                
            Query nq = getEntityManager().createQuery("SELECT w FROM WebPageDefinition w WHERE w.pageId = :id", WebPageDefinition.class);

            nq.setParameter("id", id);

            return (WebPageDefinition) nq.getSingleResult();

        } catch (NoResultException e) {
            return null;
        }
    }
    
    public WebPageDefinition findByPageName(String name) {
        try {
                
            Query nq = getEntityManager().createQuery("SELECT w FROM WebPageDefinition w WHERE w.pageName = :name", WebPageDefinition.class);

            nq.setParameter("name", name);

            return (WebPageDefinition) nq.getSingleResult();

        } catch (NoResultException e) {
            return null;
        }
    }    
    
    public WebPageDefinition findWebPageDefinitionById(Integer id){
        WebPageDefinition page = null;
        if(id != null){
            page = em.find(WebPageDefinition.class, id);
        }
        return page;
    }
}
