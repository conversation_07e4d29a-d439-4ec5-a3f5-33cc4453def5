/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.PromotionsDescription;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class PromotionsDescriptionFacade extends AbstractFacade<PromotionsDescription> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public PromotionsDescriptionFacade() {
        super(PromotionsDescription.class);
    }

    public void removeAlldescrptionByPromotion(int promotionIntId) {
        Query q = em.createNativeQuery("delete from promotions_description where promotions = " + promotionIntId);
        q.executeUpdate(); 
    }

}
