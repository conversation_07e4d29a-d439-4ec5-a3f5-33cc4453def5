/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.fundata.entity.FdQuartiles; 
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdQuartilesSessionBean extends AbstractFacade<FdQuartiles> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdQuartilesSessionBean() {
        super(FdQuartiles.class);
    }
    
    public FdQuartiles getByFundataKey(Long fundatakey) {
    	
    	TypedQuery<FdQuartiles> nq = em.createQuery(
				"SELECT a FROM FdQuartiles a WHERE  a.fundatakey = :fundatakey", FdQuartiles.class);
		nq.setParameter("fundatakey", fundatakey);
		return nq.getSingleResult();

        /*FdQuartiles quartile = null;

        CriteriaBuilder cBuilder = em.getCriteriaBuilder();
        CriteriaQuery<FdQuartiles> criteriaQuery = cBuilder.createQuery(FdQuartiles.class);
        Root<FdQuartiles> p = criteriaQuery.from(FdQuartiles.class);

        Predicate codePredicate = cBuilder.equal(p.get(FdQuartiles_.fundatakey), fundatakey);
        criteriaQuery.where(codePredicate);

        TypedQuery<FdQuartiles> typeCodeQuery = em.createQuery(criteriaQuery);

        List<FdQuartiles> results = typeCodeQuery.getResultList();
        if (results != null && !results.isEmpty()) {
            quartile = results.get(0);
        }

        return quartile;*/
    }
}
