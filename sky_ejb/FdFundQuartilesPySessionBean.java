/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdFundQuartilesPy;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFundQuartilesPySessionBean extends AbstractFacade<FdFundQuartilesPy> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdFundQuartilesPySessionBean() {
		super(FdFundQuartilesPy.class);
	}

//    public FdFundQuartilesPy getByFundataKey(Long fundatakey) {
//
//        FdFundQuartilesPy quartile = null;
//
//        CriteriaBuilder cBuilder = em.getCriteriaBuilder();
//        CriteriaQuery<FdFundQuartilesPy> criteriaQuery = cBuilder.createQuery(FdFundQuartilesPy.class);
//        Root<FdFundQuartilesPy> p = criteriaQuery.from(FdFundQuartilesPy.class);
//
//        Predicate codePredicate = cBuilder.equal(p.get(FdFundQuartilesPy_.mastFundatakey), fundatakey);
//        criteriaQuery.where(codePredicate);
//
//        TypedQuery typeCodeQuery = em.createQuery(criteriaQuery);
//
//        List<FdFundQuartilesPy> results = typeCodeQuery.getResultList();
//        if (results != null && !results.isEmpty()) {
//            quartile = results.get(0);
//        }
//
//        return quartile;
//    }

	@SuppressWarnings("finally")
	public List<FdFundQuartilesPy> getByFundataKey(Long fundatakey) {

		List<FdFundQuartilesPy> qs = new ArrayList<>();

		try {
			TypedQuery<FdFundQuartilesPy> nq = em.createNamedQuery("FdFundQuartilesPy.findByFundatakey",
					FdFundQuartilesPy.class);
			nq.setParameter("fundatakey", fundatakey);

			qs = nq.getResultList();
		} catch (NoResultException e) {

		} finally {
			return qs;
		}
	}
}
