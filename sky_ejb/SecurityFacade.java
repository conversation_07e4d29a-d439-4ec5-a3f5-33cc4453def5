/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.sdk.ldap.LdapUtil;
import com.insurfact.sdk.security.PasswordUtils;
import com.insurfact.skynet.entity.Activity;
import com.insurfact.skynet.entity.LoginAccess;
import com.insurfact.skynet.entity.Users;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class SecurityFacade {

    // Add business logic below. (Right-click in editor and choose
    // "Insert Code > Add Business Method")
    @EJB
    private UsersFacade usersEJB;

    @EJB
    private ActivityFacade activityFacade;

    @EJB
    private LoginAccessFacade loginAccessEJB;

    @EJB
    private EmailManager emailManager;

    public boolean isFirstTimeLogin(String username) {

        // to lower case on the username
        username = username.toLowerCase();

        Users user = usersEJB.findByUsername(username);

        if (user != null) {
            return user.isFirstTimeLogin();
        }

        return false;
    }

    public Activity autenticateExternalClient(String username, String code) {
        List<Activity> activities;
        Calendar now = Calendar.getInstance();
        activities = activityFacade.findByOtpUser(username);
        for (Activity act : activities) {
            if (act.getOtpValue() != null && act.getOtpValue().equals(code) && act.getOtpCreationDate().after(now.getTime())) {
                return act;
            }
        }
        return null;

    }

    public Users authenticate(String username, String password, String application) {

        Users user;

        user = usersEJB.findByUsername(username);

        if (user == null) {
            System.out.println("******** Security.authenticate() unable to find username {" + username + "}");
            return null;
        }

        if (user.getProfileType() == 1) // suntool 
        {
            System.out.println("******** SecurityFacade.authenticate() suntool user; profile type = 1; username {" + username + "}");
            //           return null;
        }

        if (user.getAdvisorLock() == null && user.getAgencyLock() == null) {
            System.out.println("******** SecurityFacade.authenticate(): login refused because user advisor lock is null and user agency lock is null; username {" + username + "}");
            return null;
        }

        // 1 == Application
        // 2 == LDAP
        // 3 == External
        if (user.getAuthMethod() == 1) {

//            System.out.println("******** Security.authenticate() doing Application for : " + username);
            if (authApplication(user, password)) {

                return user;
            } else {

                return null;
            }
        }

        return null;
    }

    public Users authenticate(String username, String password, String application, Object obj) {

        Users user = null;

        if (obj instanceof HttpServletRequest) {
            obj = (HttpServletRequest) obj;
        }

        // to lower case on the username
//        username = username.toLowerCase();
        user = usersEJB.findByUsername(username);

        if (user == null) {
            System.out.println("******** Security.authenticate() unable to find : " + username);
            return null;
        }

        // 1 == Application
        // 2 == LDAP
        // 3 == External
        if (user.getAuthMethod() == 2) {

            System.out.println("******** Security.authenticate() doing LDAP for : " + username);
            if (authLDAP(username, password)) {

                loginAccess(user, true, application, (HttpServletRequest) obj, "LDAP Login");

                return user;
            } else {
                loginAccess(user, false, application, (HttpServletRequest) obj, "LDAP Login");
                return null;
            }
        }

        if (user.getAuthMethod() == 1) {

            System.out.println("******** Security.authenticate() doing Application for : " + username);
            if (authApplication(user, password)) {
                loginAccess(user, true, application, (HttpServletRequest) obj, "Application Login");
                return user;
            } else {
                loginAccess(user, false, application, (HttpServletRequest) obj, "Application Login");
                return null;
            }
        }

        return null;

    }

    public LoginAccess createLoginAccess(Users users, boolean status, String application, String remoteIpAddress, String hostname, String browserDetail, String sessionId, String message) {

        LoginAccess access = new LoginAccess();

        access.setApplicationName(application);
        access.setMessage(message);

        if (status) {
            access.setStatus("SUCCESS");
        } else {
            access.setStatus("FAILED");
        }

        access.setUsers(users);

        access.setIpAddress(remoteIpAddress);

        access.setHostname(hostname);

        access.setMessage(message);

        access.setTimestamp(new Date());

        access.setBrowserDetail(browserDetail);

        access.setSessionId(sessionId);

//        Enumeration<String> headers = request.getHeaderNames();
//        
//        while (headers.hasMoreElements()){
//            
//            String header = headers.nextElement();
//            
//            System.out.println("**** header name : " + header);
//            System.out.println("**** header val  : " + request.getHeader(header));
//        }
        loginAccessEJB.create(access);

        return access;
    }

    private void loginAccess(Users user, boolean status, String application, HttpServletRequest request, String message) {

        LoginAccess access = new LoginAccess();

        access.setApplicationName(application);
        access.setMessage(message);

        if (status) {
            access.setStatus("SUCCESS");
        } else {
            access.setStatus("FAILED");
        }

        access.setUsers(user);
        String tmp = request.getRemoteAddr();

        if (tmp.equalsIgnoreCase("**********")) {
            tmp = request.getHeader("X-Real-IP");
        }

        access.setIpAddress(tmp);

        access.setHostname(request.getLocalName());

        access.setTimestamp(new Date());
        StringBuffer buffer = new StringBuffer();

        buffer.append("Browser Type : ").append(request.getHeader("user-agent")).append("\n");
        buffer.append("Accept       : ").append(request.getHeader("accept")).append("\n");
        buffer.append("Language(s)  : ").append(request.getHeader("accept-language")).append("\n");

        access.setBrowserDetail(buffer.toString());

//        Enumeration<String> headers = request.getHeaderNames();
//        
//        while (headers.hasMoreElements()){
//            
//            String header = headers.nextElement();
//            
//            System.out.println("**** header name : " + header);
//            System.out.println("**** header val  : " + request.getHeader(header));
//        }
        loginAccessEJB.create(access);

        buffer = null;
    }

    /**
     * Provide an interface to LDAP authentication
     *
     * @param username
     * @param password
     * @return Users or null if not found
     */
    private boolean authLDAP(String username, String password) {

        boolean result = LdapUtil.authLDAP(username, password);

        // maybe something else later
        return result;
    }

    /**
     * Provide an interface to LDAP authentication
     *
     * @param username
     * @param password
     * @return Users or null if not found
     */
    private boolean authApplication(Users user, String password) {

        if (password == null || password.isEmpty()) {
            System.err.println("******** Security.authApplication() detected NULL password for username : " + user.getUsername());
            return false;
        }

        boolean result = false;

        if (user == null) {
            //System.out.println("******** Security.authApplication() unable to find username : " + user.getUsername());
            return result;
        }

        if (password != null) {
            if (compareDigest(user, password)) {
                result = true;
            }
        }

        return result;
    }

    public String getConfirmationKey(Users users) {

        Date now = Calendar.getInstance().getTime();

        String key = users.getUsername() + "But above all things truth beareth away the victory" + now.getTime();
        users.setConfirmationKey(PasswordUtils.md5(key));
        users.setSentConfirmationDate(now);
        usersEJB.edit(users);

        return PasswordUtils.md5(key);
    }

    public boolean verifyAccount(String key) {

        Users users = usersEJB.findUsersByConfirmationKey(key);

        if (users == null) {
            System.out.println("######### SecurityFacade.verifyAccount >>USERNAME NOT FOUND<< : key=" + key);

            return false;
        }

        String storedKey = users.getConfirmationKey();

        if (storedKey.equalsIgnoreCase(key)) {

            users.setNeedConfirmation(false);
            users.setActive(true);
            users.setLastModificationDate(Calendar.getInstance().getTime());

            usersEJB.edit(users);
            System.out.println("**** SecurityFacade.verifyAccount -- CONFIRMED : username=" + users.getUsername() + " key=" + key);

            return true;

        }

        System.out.println("**** SecurityFacade.verifyAccount -- FAILED : username=" + users.getUsername() + " key=" + key);
        return false;

    }

    public boolean changePassword(Users user, final String oldPassword, final String newPassword, boolean firsttime) {

        if (oldPassword == null || oldPassword.isEmpty()) {
            return false;
        }

        if (newPassword == null || newPassword.isEmpty()) {
            return false;
        }

        // oldPassword must match before changing it
        if (compareDigest(user, oldPassword)) {
            System.out.println("**** ChangePassword for username : " + user.getUsername() + " old password matches!");

            String md5 = PasswordUtils.md5(newPassword);

            user.setPassword(md5);
            user.setLastPasswordReset(new Date());

            if (firsttime) {
                user.setFirstTimeLogin(false);
                user.setActive(true);
                user.setValidUntil(null);
            }

            usersEJB.edit(user);
            md5 = "";

            System.out.println("**** ChangePassword for username : " + user.getUsername() + " new password saved!");
            return true;

        }

        return false;
    }

    public boolean changePasswordForced(Users user, final String newPassword, boolean firsttime) {

        if (newPassword == null || newPassword.isEmpty()) {
            return false;
        }

        System.out.println("**** ChangePassword for username : " + user.getUsername() + " old password matches!");

        String md5 = PasswordUtils.md5(newPassword);

        user.setPassword(md5);
        user.setLastPasswordReset(new Date());

        if (firsttime) {
            user.setFirstTimeLogin(false);
            user.setActive(true);
            user.setValidUntil(null);
        }

        usersEJB.edit(user);
        md5 = "";

        System.out.println("**** ChangePassword for username : " + user.getUsername() + " new password saved!");
        return true; 
    }

    public String encrypt(String password) {

        return PasswordUtils.md5(password);
    }

    private boolean compareDigest(Users user, final String password) {

        String md5Password = PasswordUtils.md5(password);

//        System.out.println(md5Password + "|" + user.getPassword());
        // oldPassword must match before changing it
        if (md5Password.equals(user.getPassword())) {
            return true;
        }

        return false;

    }
}
