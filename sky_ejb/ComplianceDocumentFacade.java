/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.ComplianceDocument;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ComplianceDocumentFacade extends AbstractFacade<ComplianceDocument> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ComplianceDocumentFacade() {
		super(ComplianceDocument.class);
	}

	public List<ComplianceDocument> findByUsersContact(Users users, Contact contact) {
		try {
			TypedQuery<ComplianceDocument> nq = getEntityManager().createQuery(
					"SELECT c from ComplianceDocument c WHERE c.users = :users AND c.contact = :contact ORDER by c.creationDate DESC",
					ComplianceDocument.class);

			nq.setParameter("users", users);
			nq.setParameter("contact", contact);

			return nq.getResultList();

		} catch (NoResultException e) {
		}

		return new ArrayList<>();
	}
}
