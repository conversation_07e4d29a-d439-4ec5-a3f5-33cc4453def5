/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.LicenseLiability;
import com.insurfact.skynet.entity.Province;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.eclipse.persistence.config.QueryHints;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class LicenseLiabilityFacade extends AbstractFacade<LicenseLiability> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public LicenseLiabilityFacade() {
		super(LicenseLiability.class);
	}

	public List<LicenseLiability> findLicenseLiabilityByNumber(String number) {

		TypedQuery<LicenseLiability> nq = em.createQuery(
				"SELECT a FROM LicenseLiability a WHERE   a.liabilityNumber = :liabilityNumber",
				LicenseLiability.class);
		nq.setParameter("liabilityNumber", number);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * CriteriaQuery<LicenseLiability> criteriaQuery =
		 * cBuilder.createQuery(LicenseLiability.class); Root<LicenseLiability> p =
		 * criteriaQuery.from(LicenseLiability.class);
		 * 
		 * Predicate liabilityNumber =
		 * cBuilder.equal(p.get(LicenseLiability_.liabilityNumber), number);
		 * 
		 * criteriaQuery.where(liabilityNumber);
		 * 
		 * TypedQuery<LicenseLiability> query = em.createQuery(criteriaQuery); return
		 * query.getResultList();
		 */
	}

	public void editLiabilityLastRequestdate(Integer id) {
		Date now = Calendar.getInstance().getTime();

		LicenseLiability liability = em.find(LicenseLiability.class, id);

		if (liability != null) {

			liability.setRequestDate(now);
			em.merge(liability);
		}
	}

// getByOldLiabilityId  //LicenseLiability.
	public LicenseLiability findByOldLiabilityNumber(Integer id) {

		try {
			Query nq = getEntityManager().createNamedQuery("LicenseLiability.findByOldLiabilityId",
					LicenseLiability.class);
			nq.setParameter("oldLiabilityId", id);

			LicenseLiability l = (LicenseLiability) nq.getSingleResult();

			return l;

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

	}

	public List<LicenseLiability> findByLiabilityNumber(String number) {

		number = "%" + number + "%";

		TypedQuery<LicenseLiability> query = em.createQuery(
				"SELECT l from LicenseLiability l where UPPER(l.liabilityNumber) LIKE :number ORDER BY l.company.primaryName, l.endDate DESC",
				LicenseLiability.class);
		query.setParameter("number", number);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findByStatus(Integer status) {

		TypedQuery<LicenseLiability> query = em.createQuery(
				"SELECT l from LicenseLiability l where l.status = :status AND l.endDate IS NOT NULL ORDER BY l.company.primaryName, l.endDate DESC",
				LicenseLiability.class);
		query.setParameter("status", status);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findByStatus(Integer status, Integer type) {

		TypedQuery<LicenseLiability> query = em.createQuery(
				"SELECT l from LicenseLiability l where l.status = :status AND l.company.companyType = :type AND l.endDate IS NOT NULL ORDER BY l.company.primaryName, l.endDate DESC",
				LicenseLiability.class);
		query.setParameter("status", status);
		query.setParameter("type", type);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findByStatusProvince(Integer status, Province province) {

		TypedQuery<LicenseLiability> query;

		query = em.createQuery(
				"SELECT l from LicenseLiability L where l.status = :status AND l.province = :province ORDER BY l.company.primaryName",
				LicenseLiability.class);
		query.setParameter("province", province.getProvinceCode());

		query.setParameter("status", status);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findAllAboutToExpire(Date date) {
		TypedQuery<LicenseLiability> query = em.createQuery(
				"SELECT l from LicenseLiability l where l.endDate IS NOT NULL AND l.endDate >= CURRENT_DATE AND l.endDate <= :date ORDER BY l.endDate, l.company.primaryName, l.province",
				LicenseLiability.class);
		query.setParameter("date", date);
//        query.setParameter("status", status);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findExpiredByDate(Date date) {
		TypedQuery<LicenseLiability> query;

		query = em.createQuery(
				"SELECT l from LicenseLiability l where (l.endDate <= CURRENT_DATE AND l.endDate >= :date) OR l.endDate IS NULL ORDER BY l.endDate, l.company.primaryName",
				LicenseLiability.class);

		query.setParameter("date", date);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findExpiredByDate(Date date, Integer type) {
		TypedQuery<LicenseLiability> query;

		query = em.createQuery(
				"SELECT l from LicenseLiability l where (l.endDate <= CURRENT_DATE AND l.endDate >= :date) OR l.endDate IS NULL AND l.company.companyType = :type ORDER BY l.endDate, l.company.primaryName",
				LicenseLiability.class);

		query.setParameter("date", date);
		query.setParameter("type", type);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findAboutExpireByDate(Date date) {
		TypedQuery<LicenseLiability> query;

		query = em.createQuery(
				"SELECT l from LicenseLiability l where l.endDate >= CURRENT_DATE AND l.endDate <=:date ORDER BY l.endDate, l.company.primaryName",
				LicenseLiability.class);

		query.setParameter("date", date);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findAboutExpireByDate(Date date, Integer type) {
		TypedQuery<LicenseLiability> query;

		query = em.createQuery(
				"SELECT l from LicenseLiability l where l.endDate >= CURRENT_DATE AND l.endDate <=:date AND l.company.companyType = :type ORDER BY l.endDate, l.company.primaryName",
				LicenseLiability.class);

		query.setParameter("date", date);
		query.setParameter("type", type);
		query.setHint(QueryHints.READ_ONLY, true);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

	public List<LicenseLiability> findByAgency(List<Advisor> advisors) {
		TypedQuery<LicenseLiability> query = em
				.createQuery("SELECT l from LicenseLiability L where l.advisor IN :advisors", LicenseLiability.class);
		query.setParameter("advisors", advisors);

		List<LicenseLiability> list = query.getResultList();

		return list;
	}

}
