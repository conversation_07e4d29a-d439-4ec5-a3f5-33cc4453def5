/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.NewsCommunique;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class NewsCommuniqueFacade extends AbstractFacade<NewsCommunique> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public NewsCommuniqueFacade() {
        super(NewsCommunique.class);
    }
    
    public List<NewsCommunique> findAllForUsers(Users users) {
        
        List<NewsCommunique> news ;
    
        try{
            TypedQuery<NewsCommunique> nq = em.createQuery("select c from NewsCommunique c WHERE c.users = :users ORDER BY c.effectiveDate DESC ",NewsCommunique.class);
   
            nq.setParameter("users", users);
 
            news = nq.getResultList();
  
        } catch (NoResultException e) {
            return new ArrayList<>();
        }
    
        return news;
    }
    
    public List<NewsCommunique> findAllActiveForUsers(Users users, Date date) {
        
        List<NewsCommunique> news ;
    
        try{
        	TypedQuery<NewsCommunique> nq = em.createQuery("SELECT c FROM NewsCommunique c WHERE c.users = :users AND (:date BETWEEN c.effectiveDate AND c.endingDate) ORDER BY c.effectiveDate DESC",NewsCommunique.class);

            nq.setParameter("users", users);
            nq.setParameter("date", date);
 
            news = nq.getResultList();
  
        } catch (NoResultException e) {
            return new ArrayList<>();
        }
    
        return news;
    }
}
