package com.insurfact.skynet.ejb;

/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
import com.insurfact.skynet.entity.Branch; 
import com.insurfact.skynet.entity.SunlifeDeclined;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 * <AUTHOR>
 */
@Stateless
public class SunDeclinedFacade extends AbstractFacade<SunlifeDeclined> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public SunDeclinedFacade() {
        super(SunlifeDeclined.class);
    }

    public List<SunlifeDeclined> findByBranch(Branch branch) {

    	TypedQuery<SunlifeDeclined> nq = getEntityManager().createNamedQuery("SunlifeDeclined.findByBranch", SunlifeDeclined.class);
        nq.setParameter("branch", branch);
        System.out.println("sunDeclFacade 49 markRegCode=" + branch.getBranchPrimaryName());
        return nq.getResultList();
    }

    public List<SunlifeDeclined> findByAdvisor(String advisorNum) {

    	TypedQuery<SunlifeDeclined> nq = getEntityManager().createNamedQuery("SunlifeDeclined.findByAdvisorNo", SunlifeDeclined.class);
        nq.setParameter("advisorNo", advisorNum);
        System.out.println("sunDeclFacade 57 advisorNum=" + advisorNum);
        return nq.getResultList();
    }

    public List<SunlifeDeclined> findByMarkRegCode(Integer markRegCode) {

    	TypedQuery<SunlifeDeclined> nq = getEntityManager().createNamedQuery("SunlifeDeclined.findByMarkRegCode", SunlifeDeclined.class);
        nq.setParameter("marketingRegCode", markRegCode);
        System.out.println("sunDeclFacade 57 markRegCode=" + markRegCode);

        return nq.getResultList();

    }
    public List<SunlifeDeclined> findByAdvisorId(String advisor) {

    	TypedQuery<SunlifeDeclined> nq = getEntityManager().createNamedQuery("SunlifeDeclined.findByMarkRegCode", SunlifeDeclined.class);
        nq.setParameter("marketingRegCode", advisor);
        System.out.println("sunDeclFacade 57 markRegCode=" + advisor);

        return nq.getResultList();

    }

}
