/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.isurvey.Survey;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class SurveyFacade extends AbstractFacade<Survey> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public SurveyFacade() {
        super(Survey.class);
    }
    
    public List<Survey> findByCompanyId(Integer companyId){
        
        TypedQuery<Survey> query = em.createNamedQuery("Survey.findByCompanyId", Survey.class);
        
        query.setParameter("companyId", companyId);
        
        List<Survey> list = query.getResultList();
        
        if(list == null)
            return new ArrayList<>();
        
        return list;      
    }
    
    public List<Survey> findByType(String planType){
        
    	TypedQuery<Survey> query = em.createNamedQuery("Survey.findByPlanType", Survey.class);
        
        query.setParameter("planType", planType);
        
        List<Survey> list = query.getResultList();
        
        if(list == null)
            return new ArrayList<>();
        
        return list;      
    }
    
    public List<Survey> findByCompanyAndType(Integer companyId, String planType){
        
    	TypedQuery<Survey> query = em.createNamedQuery("Survey.findByCompanyIdAndPlanType", Survey.class);
        
        query.setParameter("companyId", companyId);
        query.setParameter("planType", planType);
        
        List<Survey> list = query.getResultList();
        
        if(list == null)
            return new ArrayList<>();
        
        return list;      
    }
    
    
}
