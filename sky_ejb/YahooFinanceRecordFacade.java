/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.YahooFinanceRecord;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class YahooFinanceRecordFacade extends AbstractFacade<YahooFinanceRecord> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public YahooFinanceRecordFacade() {
        super(YahooFinanceRecord.class);
    }
    
    
    public YahooFinanceRecord findLatestTicker(String ticker) {
        try {
                
        	TypedQuery<YahooFinanceRecord> nq = getEntityManager().createNamedQuery("YahooFinanceRecord.findByLatestTicker", YahooFinanceRecord.class);

            nq.setParameter("ticker", ticker);
            nq.setMaxResults(1);
            
            List<YahooFinanceRecord> list = nq.getResultList();

            if(list != null)
                return list.get(0);
            

        } catch (NoResultException e) {
            
        }
        
        return null;
    }
    public YahooFinanceRecord findLatestTicker(Integer tickerId) {
        try {
                
        	TypedQuery<YahooFinanceRecord> nq = getEntityManager().createNamedQuery("YahooFinanceRecord.findByLatestTickerById", YahooFinanceRecord.class);

            nq.setParameter("tickerId", tickerId);
            nq.setMaxResults(1);
            
            List<YahooFinanceRecord> list = nq.getResultList();

            if(list != null)
                return list.get(0);
            

        } catch (NoResultException e) {
            
        }
        
        return null;
    }    
}
