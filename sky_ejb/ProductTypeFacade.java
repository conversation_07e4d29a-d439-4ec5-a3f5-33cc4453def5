/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.ProductClass;
import com.insurfact.skynet.entity.ProductType;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */

@Stateless
public class ProductTypeFacade extends AbstractFacade<ProductType> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	public EntityManager getEntityManager() {
		return em;
	}

	public ProductTypeFacade() {
		super(ProductType.class);
	}

	public List<ProductType> findTypesByProductClass(ProductClass prodClass) {

		List<ProductType> prodType;

		try {
			TypedQuery<ProductType> nq = getEntityManager().createNamedQuery("ProductType.findByProductClass",
					ProductType.class);

			nq.setParameter("productClass", prodClass);

			prodType = nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}

		return prodType;
	}

}
