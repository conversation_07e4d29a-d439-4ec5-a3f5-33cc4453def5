/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdBestCategory;
import com.insurfact.fundata.entity.FdCategory;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdBestCategorySessionBean extends AbstractFacade<FdBestCategory> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdBestCategorySessionBean() {
        super(FdBestCategory.class);
    }
    
    public FdBestCategory getBestCategory(FdCategory category){
        
        Query nq = em.createQuery("SELECT b FROM FdBestCategory b WHERE b.fdCategory = :category ",FdCategory.class);
        nq.setParameter("category", category);
            
        return (FdBestCategory) nq.getSingleResult();
    }
}
