/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.AddressBook;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AddressBookFacade extends AbstractFacade<AddressBook> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public AddressBookFacade() {
        super(AddressBook.class);
    }

    public List<AddressBook> findByOwner(Users users) {
        try {
        	TypedQuery<AddressBook> nq = getEntityManager().createNamedQuery("AddressBook.findByOwner", AddressBook.class);
            nq.setParameter("owner", users);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<AddressBook> findByContact(Contact contact) {
        try {
        	TypedQuery<AddressBook> nq = getEntityManager().createNamedQuery("AddressBook.findByContact", AddressBook.class);
            nq.setParameter("contact", contact);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    @SuppressWarnings("unchecked")
	public List<AddressBook> findByOwnerLimit50(Users users) {
        try {
            Query nq = getEntityManager().createNativeQuery("select * from ADDRESS_BOOK where owner = " + users.getUserIntId() + " order by FIRSTNAME asc FETCH NEXT 50 ROWS ONLY", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByOwnerContact(Users users) {
        try {
            Query nq = getEntityManager().createNativeQuery("select * from ADDRESS_BOOK where owner = " + users.getUserIntId() + " and contact is not null order by FIRSTNAME", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByOwnerEmailLimit50(Users users) {
        try {
            Query nq = getEntityManager().createNativeQuery("select * from ADDRESS_BOOK where owner = " + users.getUserIntId() + " and ADDRESS_EMAIL is not null order by FIRSTNAME asc FETCH NEXT 50 ROWS ONLY", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    
    // inner join advisor adv on adv.advisor_int_id = c.contact_int_id
    // where adv.aga_agency_id is null
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByOwnerContact(Users users, String notIn) {
        try {
            //System.out.println("select * from ADDRESS_BOOK where owner = " + users.getUserIntId() + " and contact is not null and contact not in (" + notIn + ")");
            Query nq = getEntityManager().createNativeQuery("select * from ADDRESS_BOOK ab inner join advisor adv on adv.advisor_int_id = ab.contact where adv.aga_agency_id is null and ab.owner = " + users.getUserIntId() + " and ab.contact is not null and ab.contact not in (" + notIn + ")", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByOwnerContactPhone(Users users, String phone) {
        try {
            Query nq = getEntityManager().createNativeQuery("select * from ADDRESS_BOOK where owner = " + users.getUserIntId() + " and contact is not null and phone_number like ('%" + phone + "%')", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByOwnerContactEmail(Users users, String email) {
        try {
            Query nq = getEntityManager().createNativeQuery("select * from ADDRESS_BOOK where owner = " + users.getUserIntId() + " and contact is not null and lower(address_email) like ('%" + email + "%')", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByOwnerContactLike(Users users, String firstName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select * from ADDRESS_BOOK where owner = " + users.getUserIntId() + " and contact is not null and (lower(firstname) like ('%" + firstName + "%') or lower(lastname) like ('%" + firstName + "%'))", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByOwnerContactLike(Users users, String firstName, String lastName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select  * from ADDRESS_BOOK where owner = " + users.getUserIntId() + " and contact is not null and ( (lower(firstname) like ('%" + firstName + "%') and lower(lastname) like ('%" + lastName + "%')) or (lower(firstname) like ('%" + lastName + "%') and lower(lastname) like ('%" + firstName + "%')))", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    
    
    // inner join advisor adv on adv.advisor_int_id = ab.contact
    // where adv.aga_agency_id is null
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractTool(ProductSupplier ps) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS inner join advisor adv on adv.advisor_int_id = ab.contact INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where adv.aga_agency_id is null and cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId(), AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractTool(ProductSupplier ps, String notIn) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and ab.contact not in (" + notIn + ")", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolPhone(ProductSupplier ps, String phone) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and phone_number like ('%" + phone + "%')", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolEmail(ProductSupplier ps, String email) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and lower(ab.address_email) like ('%" + email + "%')", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolLike(ProductSupplier ps, String firstName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and (lower(ab.firstname) like ('%" + firstName + "%') or lower(ab.lastname) like ('%" + firstName + "%'))", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolLike(ProductSupplier ps, String firstName, String lastName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and ( (lower(firstname) like ('%" + firstName + "%') and lower(lastname) like ('%" + lastName + "%')) or (lower(firstname) like ('%" + lastName + "%') and lower(lastname) like ('%" + firstName + "%')))", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    
    // inner join advisor adv on adv.advisor_int_id = c.contact_int_id
    // where adv.aga_agency_id is null
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolMga(ProductSupplier ps) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab inner join advisor adv on adv.advisor_int_id = ab.contact INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where adv.aga_agency_id is null and cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId(), AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolMga(ProductSupplier ps, String notIn) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and ab.contact not in (" + notIn + ")", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolMgaPhone(ProductSupplier ps, String phone) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and phone_number like ('%" + phone + "%')", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolMgaEmail(ProductSupplier ps, String email) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and lower(address_email) like ('%" + email + "%')", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolMgaLike(ProductSupplier ps, String firstName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and (lower(ab.firstname) like ('%" + firstName + "%') or lower(ab.lastname) like ('%" + firstName + "%'))", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<AddressBook> findByContractToolMgaLike(ProductSupplier ps, String firstName, String lastName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select DISTINCT ab.* from ADDRESS_BOOK ab INNER JOIN PROFILE_USERS pu on ab.CONTACT = pu.USERS INNER JOIN CONTRACT_SETUP cs on ab.contact = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and ( (lower(firstname) like ('%" + firstName + "%') and lower(lastname) like ('%" + lastName + "%')) or (lower(firstname) like ('%" + lastName + "%') and lower(lastname) like ('%" + firstName + "%')))", AddressBook.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    
    // inner join advisor adv on adv.advisor_int_id = c.contact_int_id
    // where adv.aga_agency_id is null
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolFake(ProductSupplier ps) {
        try {
            Query nq = getEntityManager().createNativeQuery("select  distinct c.* from CONTACT c inner join advisor adv on adv.advisor_int_id = c.contact_int_id  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where adv.aga_agency_id is null and cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId(), Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolFake(ProductSupplier ps, String notIn) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and c.contact_int_id not in (" + notIn + ")", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolFakePhone(ProductSupplier ps, String area, String number) {

        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c INNER JOIN CONTACT_PHONE cp on c.CONTACT_INT_ID = cp.contact  inner join phone ph on cp.phone = ph.phone_int_id INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and ph.AREA_CODE like ('%" + area + "%') and ph.PHONE_NUMBER like ('%" + number + "%')", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolFakeEmail(ProductSupplier ps, String email) {

        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c INNER JOIN CONTACT_EMAIL ce on c.CONTACT_INT_ID = ce.contact  inner join email em on ce.email = em.email_int_id INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and lower(em.EMAIL_ADDRESS) like ('%" + email + "%')", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolFakeLike(ProductSupplier ps, String firstName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and (lower(c.firstname) like ('%" + firstName + "%') or lower(c.lastname) like ('%" + firstName + "%'))", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolFakeLike(ProductSupplier ps, String firstName, String lastName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and ( (lower(c.firstname) like ('%" + firstName + "%') and lower(c.lastname) like ('%" + lastName + "%')) or (lower(c.firstname) like ('%" + lastName + "%') and lower(c.lastname) like ('%" + firstName + "%')))", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    
     // inner join advisor adv on adv.advisor_int_id = c.contact_int_id
    // where adv.aga_agency_id is null
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolMgaFake(ProductSupplier ps) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c inner join advisor adv on adv.advisor_int_id = c.contact_int_id  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where  adv.aga_agency_id is null and (cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " or cs.AGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() +")", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolMgaFake(ProductSupplier ps, String notIn) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " or cs.AGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and c.contact_int_id not in (" + notIn + ")", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolMgaFakePhone(ProductSupplier ps, String area, String number) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c inner join CONTACT_PHONE cp on c.CONTACT_INT_ID = cp.contact  inner join phone ph on cp.phone = ph.phone_int_id INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " or cs.AGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and ph.AREA_CODE like ('%" + area + "%') and ph.PHONE_NUMBER like ('%" + number + "%')", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolMgaFakeEmail(ProductSupplier ps, String email) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c inner join CONTACT_EMAIL ce on c.CONTACT_INT_ID = ce.contact  inner join email em on ce.email = em.email_int_id  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " or cs.AGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and lower(em.EMAIL_ADDRESS) like ('%" + email + "%')", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolMgaFakeLike(ProductSupplier ps, String firstName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " or cs.AGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and (lower(c.firstname) like ('%" + firstName + "%') or lower(c.lastname) like ('%" + firstName + "%'))", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }
    @SuppressWarnings("unchecked")
    public List<Contact> findByContractToolMgaFakeLike(ProductSupplier ps, String firstName, String lastName) {
        try {
            Query nq = getEntityManager().createNativeQuery("select distinct c.* from CONTACT c  INNER JOIN CONTRACT_SETUP cs on c.contact_int_id = cs.advisor where cs.MGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " or cs.AGA_PRODUCT_SUPPLIER =  " + ps.getProductSupplierIntId() + " and ( (lower(c.firstname) like ('%" + firstName + "%') and lower(c.lastname) like ('%" + lastName + "%')) or (lower(c.firstname) like ('%" + lastName + "%') and lower(c.lastname) like ('%" + firstName + "%')))", Contact.class);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

}
