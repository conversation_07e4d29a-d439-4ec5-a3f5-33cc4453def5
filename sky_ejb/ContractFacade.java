/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.Contract;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ContractFacade extends AbstractFacade<Contract>  {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public ContractFacade() {
        super(Contract.class);
    }
    
    public List<Contract> findByContractNumber(String number) {

        try{

        	TypedQuery<Contract> nq = em.createNamedQuery("Contract.findByContractNumber",Contract.class);

            nq.setParameter("contractNumber", "%"+number+"%");
            
            
 
            return  nq.getResultList();
        
        
        } catch (NoResultException e) {
             e.printStackTrace();
            return null;
        }
      
    }  
    
    public boolean validContractNumber(String number) {

        try{

        	TypedQuery<Contract> nq = em.createNamedQuery("Contract.validContractNumber",Contract.class);

            nq.setParameter("contractNumber", number);
 
            List<Contract> contracts = nq.getResultList();
            
            if(contracts != null && !contracts.isEmpty() ){
                Contract contract = contracts.get(0);
                
                if(contract.getContractNumber().equalsIgnoreCase(number)){
                    return true;
                }
            }
        
        } catch (NoResultException e) {
            return false;
        }
        
        
        return false;
        
      
    } 
}
