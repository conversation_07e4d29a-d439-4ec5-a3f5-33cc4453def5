/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.ProductClass;
import com.insurfact.skynet.entity.ProductType;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */


@Stateless
public class ProdTypeManagerFacade {
    
    
    @EJB
    private ProductTypeFacade productTypeFacade;
    
    @EJB
    private ProductClassFacade productClassFacade;
    
    
    public List<ProductClass> findAllProductClass() {

        return productClassFacade.findAll();

    }
    
    public List<ProductType> findTypesByProdClass(ProductClass productClass) {

        List<ProductType> types;

        types = productTypeFacade.findTypesByProductClass(productClass);

        return types;
    }

    public void saveProdClass(ProductClass productClass) {

        productClass.setLastModificationDate(new Date());
        productClassFacade.edit(productClass);
    }

    public void createProdClass(ProductClass productClass) {

        Date now = Calendar.getInstance().getTime();
            
        productClass.setCreationDate(now);

        ProductType prodType = new ProductType();
        prodType.setCreationDate(now);

/*
        types.setTypeValue(0);
        types.setDescEn("None");
        types.setDescFr("Aucun(e)");
*/
        prodType.setProductClass(productClass);
        
        List<ProductType> prodTypeList = productClass.getProductTypeList();
        
        if (prodTypeList == null) {
            prodTypeList = new ArrayList<ProductType>();
        }

        prodTypeList.add(prodType);
        productClass.setProductTypeList(prodTypeList);

        
        productClassFacade.create(productClass);

    }
    

    public void saveProdType(ProductType prodType) {

        Date now = Calendar.getInstance().getTime();
        prodType.setLastModificationDate(now);
        productTypeFacade.edit(prodType);
    }
    
    public void createProdType(ProductType prodType) {

        Date now = Calendar.getInstance().getTime();
        prodType.setCreationDate(now);
        productTypeFacade.create(prodType);
    }

    public ProductClass findProdClassByName(String productClassName){
      
        String queryStr = "SELECT t FROM ProductClass t WHERE t.typeName = :typeName";
        
        TypedQuery<ProductClass> query = productTypeFacade.getEntityManager().createQuery(queryStr,ProductClass.class);
        query.setParameter("typeName", productClassName);
        
        ProductClass productClass = null;
        
        try {
            
            productClass = (ProductClass) query.getSingleResult();
            
        } catch (Exception exp) {
            
            System.err.println("******** Unable to find ProductClass : "+ productClassName);
        }        
        
        if(productClass == null){
            System.err.println("******** Unable to find ProductClass : "+ productClassName);
        }
        
        return productClass;
    }
    
    
    public List<ProductType> findByProdClassName(String prodClassName) {
        
        ProductClass prodClass = findProdClassByName(prodClassName);
        
        if(prodClass != null){
            return prodClass.getProductTypeList();
        }
        
        System.err.println("******** Unable to find List<ProductType> for ProductClass : "+ prodClassName);
        
        return new ArrayList<ProductType>();
    }
    
    
    /*
    public ProductType getTypesByDetails(String prodClassName, int value){
        
        ProductClass prodClass = findProductClassByName(prodClassName);
        
        if(prodClass == null){
             System.err.println("******** Unable to find ProductType with value : " + value + " for ProductClass : "+ prodClassName);
            return null;
        }
        
        for(ProductType prodType : prodClass.getProductTypeList()){
            
            if(prodType.getProductType() == value)
                return prodType;
        }
        
        System.err.println("******** Unable to find Types with value : " + value + " for ProductClass : "+ prodClassName);
        return null;
    }
    
    
    public int getNextValueForProductClass(ProductClass productClass){
        
        int val =0;

        if(productClass == null ){
             System.err.println("******** Unable to find ProductClass with null value!" );
            return val;
        }
        
        val = productTypeFacade.getNextValue(productClass);
        
        return val;
    }

    */

}
