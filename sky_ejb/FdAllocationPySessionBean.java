/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdAllocationPy; 

import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdAllocationPySessionBean extends AbstractFacade<FdAllocationPy> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdAllocationPySessionBean() {
		super(FdAllocationPy.class);
	}

	/**
	 * 
	 * @param fundatakey
	 * @return List of FdAllocationPy
	 * @deprecated
	 */
	@SuppressWarnings("finally")
	public List<FdAllocationPy> getByFundataKey(Integer fundatakey) {

		List<FdAllocationPy> allocations = new ArrayList<FdAllocationPy>();

		try {
			TypedQuery<FdAllocationPy> nq = em.createNamedQuery("FdAllocationPy.findByAllocFundatakey",
					FdAllocationPy.class);
			nq.setParameter("allocFundatakey", fundatakey);

			allocations = nq.getResultList();
		} catch (NoResultException e) {
			//
		} finally {
			return allocations;
		}
	}

	@SuppressWarnings("finally")
	public List<FdAllocationPy> getByFundataKey(Long fundatakey) {

		List<FdAllocationPy> allocations = new ArrayList<FdAllocationPy>();

		try {
			TypedQuery<FdAllocationPy> nq = em.createNamedQuery("FdAllocationPy.findByAllocFundatakey", FdAllocationPy.class);
			nq.setParameter("allocFundatakey", fundatakey);

			allocations = nq.getResultList();

		} catch (NoResultException e) {
			//
		} finally {
			return allocations;
		}
	}
}
