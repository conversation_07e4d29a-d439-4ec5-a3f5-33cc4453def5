/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Contract;
import com.insurfact.skynet.entity.ContractEft;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ContractEftFacade extends AbstractFacade<ContractEft> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public ContractEftFacade() {
        super(ContractEft.class);
    }


    
    @Override
    public void edit(ContractEft contractEft) {
        Date now = Calendar.getInstance().getTime();

        ContractEft persistentContractEft = em.find(ContractEft.class, contractEft.getContractEftIntId());
       

        List<Contract> contractsOld = persistentContractEft.getContractList();
        List<Contract> contractsNew = contractEft.getContractList();
        
        if(contractsNew != null){
            Iterator<Contract> contractsIter = contractsNew.iterator();
            while(contractsIter.hasNext()){
                
                Contract contractNew = contractsIter.next();
                
                if(contractNew != null){
                    contractNew = em.getReference(contractNew.getClass(), contractNew.getContractIntId());
                    
                    contractNew.setLastModificationDate(now);
                    contractEft.appendContract(contractNew);
                }
            }
        }

        
        //MERGING
        contractEft.setLastModificationDate(now);
        contractEft = em.merge(contractEft);
        
        if(contractsOld != null){
            Iterator<Contract> contractsIter = contractsOld.iterator();
            while(contractsIter.hasNext()){
                
                Contract contractOld = contractsIter.next();
                
                if(contractOld != null && !contractsNew.contains(contractOld)){
                    contractOld.setContractEft(null);
                    
                    contractOld.setLastModificationDate(now);
                    contractOld = em.merge(contractOld);
                }
            }
        }
        
        if(contractsNew != null){
            Iterator<Contract> contractsIter = contractsNew.iterator();
            while(contractsIter.hasNext()){
                
                Contract contractNew = contractsIter.next();
                
                if(contractNew != null && !contractsOld.contains(contractNew)){
                    contractNew.setContractEft(contractEft);
                    contractNew.setLastModificationDate(now);
                    contractNew = em.merge(contractNew);
                }
            }
        }

    }
    
    @Override
    public void create(ContractEft contractEft) {

        Date now = Calendar.getInstance().getTime();

        List<Contract> contracts = contractEft.getContractList();

        contractEft.setLastModificationDate(now);
        contractEft.setCreationDate(now);
        em.persist(contractEft);
        
        if(contracts != null){
            Iterator<Contract> contractIter = contracts.iterator();
            while(contractIter.hasNext()){
                
                Contract contract = contractIter.next();
                
                if(contract != null){
                    contract.setContractEft(contractEft);
                    contract.setLastModificationDate(now);
                    contract = em.merge(contract);
                }
            }
        }
    }
    
    public void destroy(Integer eftID){
        //TODO
    }
    
     public void editContractEftLastSentDate(Integer id) {
        Date now = Calendar.getInstance().getTime();

        ContractEft eft = em.find(ContractEft.class, id);

        if (eft != null) {

            eft.setSentDate(now);
            em.merge(eft);
        }
    }
}
