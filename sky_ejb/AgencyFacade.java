/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.im.IMCompanyContact;
import com.insurfact.skynet.entity.*;
import java.util.*;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery; 
import org.eclipse.persistence.config.HintValues;
import org.eclipse.persistence.config.QueryHints;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AgencyFacade extends AbstractFacade<Agency> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@EJB
	private LicenseFacade licenseFacade;

	@EJB
	private LicenseLiabilityFacade liabilityFacade;

	@EJB
	private ContractEftFacade contractEftFacade;

	@EJB
	private ContractFacade contractFacade;

	@EJB
	private ProductSupplierFacade productSupplierFacade;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public AgencyFacade() {
		super(Agency.class);
	}

	public List<Agency> findAllAgencyLock() {

		TypedQuery<Agency> nq = em.createQuery(
				"SELECT a FROM Agency a WHERE a.masterGroup is not NULL ORDER BY a.organization.organizationDescEn",
				Agency.class);
		nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
		return nq.getResultList();
	}

	public Agency refreshAgencyEntity(Agency agency) {
		Integer agencyId = agency.getAgencyIntId();
		if (agencyId != null) {
			agency = em.find(agency.getClass(), agencyId);
		}

		return agency;
	}

	public List<Agency> findByTypeAndMasterGroup(int type, MasterGroup masterGroup) {

		TypedQuery<Agency> nq = em.createNamedQuery("Agency.findAgencyByTypeAndMasterGroup", Agency.class);
		nq.setParameter("type", type);
		nq.setParameter("masterGroup", masterGroup);

		return nq.getResultList();
	}

	public void editContractSetup(ContractSetup agencyContractSetup) {
		em.merge(agencyContractSetup);
	}

	public List<LicenseCategory> findLicenseCategoriesByProvince(String provinceCode) {

		TypedQuery<LicenseCategory> nq = em.createNamedQuery("LicenseCategory.findByProvince", LicenseCategory.class);
		nq.setParameter("province", provinceCode);
		return nq.getResultList();

	}

	public List<LicenseCategory> findLicenseCategoryByCode(String categoryCode) {
		TypedQuery<LicenseCategory> nq = em.createNamedQuery("LicenseCategory.findByLicenseCategoryCode",
				LicenseCategory.class);
		nq.setParameter("licenseCategoryCode", categoryCode);
		return nq.getResultList();
	}

//    public List findByLiabilityNumber(String number) {
//
//        CriteriaBuilder cBuilder = em.getCriteriaBuilder();
//        CriteriaQuery<LicenseLiability> criteriaQuery = cBuilder.createQuery(LicenseLiability.class);
//        Root<LicenseLiability> p = criteriaQuery.from(LicenseLiability.class);
//
//        Predicate liabilityNumber = cBuilder.equal(p.get(LicenseLiability_.liabilityNumber), number);
//
//        criteriaQuery.where(liabilityNumber);
//
//        TypedQuery query = em.createQuery(criteriaQuery);
//        return query.getResultList();
//    }
	public List<Agency> findAllSubAgenciesByMasterGroupCode(String masterGroupCode) {

		if (masterGroupCode != null) {
			
			TypedQuery<Agency> nq = em.createQuery(
					"SELECT a FROM Agency a WHERE  a.masterGroup.masterGroupCode = :masterGroupCode AND a.agencyType = Constants.SUBAGENCY_AGENCY_TYPE",
					Agency.class);
			nq.setParameter("masterGroupCode", masterGroupCode); 
			return nq.getResultList();

			/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
			CriteriaQuery<Agency> criteriaQuery = cBuilder.createQuery(Agency.class);
			Root<Agency> p = criteriaQuery.from(Agency.class);

			Predicate agencyType = cBuilder.equal(p.get(Agency_.agencyType), Constants.SUBAGENCY_AGENCY_TYPE);
			Predicate masterPredicate = cBuilder.equal(p.get(Agency_.masterGroup).get(MasterGroup_.masterGroupCode),
					masterGroupCode);

			criteriaQuery.where(cBuilder.and(agencyType, masterPredicate));

			TypedQuery<Agency> query = em.createQuery(criteriaQuery);

			return query.getResultList();*/
		}

		return new ArrayList<>();
	}

	public List<Agency> findAllNonSubAgenciesByMasterGroup(String masterGroupCode) {

		if (masterGroupCode != null) {
			
			TypedQuery<Agency> nq = em.createQuery(
					"SELECT a FROM Agency a WHERE  a.masterGroup.masterGroupCode = :masterGroupCode AND a.agencyType != Constants.SUBAGENCY_AGENCY_TYPE",
					Agency.class);
			nq.setParameter("masterGroupCode", masterGroupCode); 
			return nq.getResultList();

			/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
			CriteriaQuery<Agency> criteriaQuery = cBuilder.createQuery(Agency.class);
			Root<Agency> p = criteriaQuery.from(Agency.class);

			Predicate agencyType = cBuilder.notEqual(p.get(Agency_.agencyType), Constants.SUBAGENCY_AGENCY_TYPE);
			Predicate masterPredicate = cBuilder.equal(p.get(Agency_.masterGroup).get(MasterGroup_.masterGroupCode),
					masterGroupCode);

			criteriaQuery.where(cBuilder.and(agencyType, masterPredicate));

			TypedQuery<Agency> query = em.createQuery(criteriaQuery);

			return query.getResultList();*/
		}

		return new ArrayList<>();
	}

//    public List<Advisor> findAffiliatedAdvisors(Agency agency) {
//        
//        CriteriaBuilder cBuilder = em.getCriteriaBuilder();
//        CriteriaQuery<Advisor> criteriaQuery = cBuilder.createQuery(Advisor.class);
//        Root<Advisor> p = criteriaQuery.from(Advisor.class);
//
//        Join<Advisor, ContractSetup> contractSetups = p.join(Advisor_.contractSetupList);
//        Join<ContractSetup, ContractRoute> routes = contractSetups.join(ContractSetup_.contractRouteList);
//
//        Predicate routePredicate = cBuilder.equal(routes.get(ContractRoute_.agency), agency);
//
//        criteriaQuery.where(routePredicate).distinct(true);
//        TypedQuery query = em.createQuery(criteriaQuery);
//
//        return query.getResultList();       
//    }
	public List<String> fetchSuppliersContactEmails(Agency agency) {

		List<IMCompanyContact> contacts = new ArrayList<>();

		List<String> emails = new ArrayList<>();

		if (agency != null) {

			List<ContractSetup> contractSetupList = agency.getContractSetupList();

			for (ContractSetup setup : contractSetupList) {

				ProductSupplier supplier = setup.getProductSupplier();

				if (supplier != null) {

					Integer companyId = supplier.getCompanyId();

					if (companyId != null) {

						contacts.addAll(productSupplierFacade.findIMContactByCompanyId(companyId));
					}
				}
			}
		}

		if (contacts != null && !contacts.isEmpty()) {

			for (IMCompanyContact contact : contacts) {

				String email = contact.getEmail();

				if (email != null && !email.isEmpty()) {
					emails.add(email);
				}
			}
		}

//        System.out.println("public List<String> fetchSuppliersContactEmails(Agency agency) >>>>> " + emails.size());
		return emails;
	}

	public void modifyAgency(Agency agency) {

		Date now = Calendar.getInstance().getTime();

		if (agency != null) {

			Integer agencyID = agency.getAgencyIntId();

			if (agencyID == null) {

				if (!agency.isDeleted()) {

					agency.setCreationDate(now);
//                            agency.setLastModificationDate(now);
					em.persist(agency);
				}

			} else {

				if (agency.isDeleted()) {

					agency = em.find(Agency.class, agencyID);
					em.remove(agency);

				} else {

					agency.setLastModificationDate(now);
					em.merge(agency);
				}
			}
		}
	}

	public void modifyMasterGroupAgency(MasterGroup masterGroup) {

		Date now = Calendar.getInstance().getTime();

		if (masterGroup != null) {

			List<Agency> agencies = masterGroup.getAgencyList();

			if (agencies != null && !agencies.isEmpty()) {

				Iterator<Agency> agencyIter = agencies.iterator();

				while (agencyIter.hasNext()) {

					Agency agency = agencyIter.next();

					Integer agencyID = agency.getAgencyIntId();

					if (agencyID == null) {

						if (agency.isDeleted()) {

							agencyIter.remove();

						} else {

							agency.setCreationDate(now);
//                            agency.setLastModificationDate(now);
							em.persist(agency);
						}

					} else {

						if (agency.isDeleted()) {

							agency = em.find(Agency.class, agencyID);
							em.remove(agency);
							agencyIter.remove();

						} else {

							agency.setLastModificationDate(now);
							em.merge(agency);
						}
					}
				}
			}
		}
	}

	public void deleteLicense(License license) {

		Agency agency = license.getAgency();

		// Advisor.LicenseList
		if (agency.getLicenseList() != null && !agency.getLicenseList().isEmpty()) {

			Iterator<License> licenses = agency.getLicenseList().iterator();

			while (licenses.hasNext()) {
				License l = licenses.next();
				if (l.equals(license)) {
					System.out
							.println("** found license link in agency : " + agency.getOrganization().getPrimaryName());
					licenses.remove();
					break;
				}
			}

			// persit change
			edit(agency);
		}

		// Liability.LicenseList
		List<LicenseLiability> liabilities = agency.getLicenseLiabilityList();

		if (liabilities != null && !liabilities.isEmpty()) {

			for (LicenseLiability liability : liabilities) {

				Iterator<License> iterator = liability.getLicenseList().iterator();

				boolean found = false;

				while (iterator.hasNext()) {
					License l = iterator.next();

					if (l.equals(license)) {

						System.out.println("** found license link in Liability : " + liability);
						iterator.remove();
						found = true;
						break;
					}
				}

				if (found) {
					liabilityFacade.edit(liability);
				}
			}
		}

		// Advisor Link
		license.setAdvisor(null);

		// Company link
		license.setCompany(null);

		// remove license
		licenseFacade.remove(license);

		// NetStorage files must be removed as well
		// remember
	}

	public void deleteLicenseLiability(LicenseLiability liability, Agency agency) {

		// Remove Advisor from Liability.AdvisorList
		List<Agency> agencies = liability.getAgencyList();
		agencies.remove(agency);

		// remove Liability from License
		if (agency.getLicenseLiabilityList() != null && !agency.getLicenseLiabilityList().isEmpty()) {

			Iterator<License> licenses = agency.getLicenseList().iterator();

			while (licenses.hasNext()) {
				boolean found = false;

				License l = licenses.next();

				if (l.getLicenseLiability() == null) {
					continue;
				}

				if (l.getLicenseLiability().equals(liability)) {
					System.out.println("** found LicenseLiability link in License : " + l.getLicenseNumber());
					l.setLicenseLiability(null);
					found = true;
				}

				if (found) {
					licenseFacade.edit(l);
				}
			}
		}

		// Un Reference Liability from Advisor.LicenseList
		List<LicenseLiability> liabilities = agency.getLicenseLiabilityList();
		liabilities.remove(liability);
		agency.setLicenseLiabilityList(liabilities);

		// persit change
		edit(agency);

		// Company link
		liability.setCompany(null);

		// remove license
		liabilityFacade.remove(liability);

		// NetStorage files must be removed as well
		// remember
	}

	public void deleteContractEft(ContractEft eft) {

		// Remove EFT from Agency
		Agency agency = eft.getAgency();

		agency.getContractEftList().remove(eft);
		edit(agency);

		// remove EFT from Contract from License
		if (agency.getContractSetupList() != null && !agency.getContractSetupList().isEmpty()) {

			List<ContractSetup> css = agency.getContractSetupList();

			for (ContractSetup cs : css) {

				Contract c = cs.getContract();

				if (c == null) {
					continue;
				}

				if (c.getContractEft() == null) {
					continue;
				}

				if (c.getContractEft().equals(eft)) {
					c.setContractEft(null);

					contractFacade.edit(c);
				}

			}
		}

		eft.setAdvisor(null);
		eft.setCompany(null);

		// remove license
		contractEftFacade.remove(eft);

		// NetStorage files must be removed as well
		// remember
	}

	public List<Agency> findByDescription(String name) {

		name = name.toUpperCase().trim() + "%";
		List<Agency> agency = new ArrayList<>();

		System.out.println("482 AgencyFacade name=" + name);
		try {
			TypedQuery<Agency> nq = em.createQuery(
					"SELECT a FROM Agency a WHERE (UPPER(a.agencyDescEn) like :name or UPPER(a.agencyDescFr) like :name)",
					Agency.class);
			nq.setParameter("name", name);

			agency = nq.getResultList();

		} catch (NoResultException e) {
//            e.printStackTrace();
			return agency;
		}

		return agency;
	}

	public List<Agency> findByEmailAddress(String email) {

		email = "%" + email + "%";

		List<Agency> agency = new ArrayList<>();

		try {
			TypedQuery<Email> nq = em.createQuery("SELECT a FROM Email a WHERE LOWER(a.emailAddress) like  :email", Email.class);
			nq.setParameter("email", email);

			List<Email> emails = nq.getResultList();

			if (emails != null && !emails.isEmpty()) {

				for (Email e : emails) {

					for (Contact c : e.getContactList()) {
						for (Agency a : findAll()) {
							if (a.getContact().equals(c)) {
								agency.add(a);
							}
						}
					}

				}
			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return agency;
	}

	public List<Agency> findByPhoneNumber(String area, String number) {

		List<Agency> agency = new ArrayList<>();

		if (area != null && area.isEmpty()) {
			area = null;
		}

		if (number != null && number.isEmpty()) {
			number = null;
		}

//        System.out.println("findByPhoneNumber " + masterCode + " type="+ type);
		try {
			TypedQuery<Phone> nq = null;

			if (area != null && number == null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code", Phone.class);
				nq.setParameter("code", area);
			}

			if (area == null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.phoneNumber like :number", Phone.class);
				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (area != null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code AND  a.phoneNumber like :number",
						Phone.class);
				nq.setParameter("code", area.trim());

				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (nq == null) {
				return agency;
			}

			List<Phone> phones = nq.getResultList();

			if (phones == null || phones.isEmpty()) {
				return agency;
			}

			for (Phone p : phones) {

				for (Contact c : p.getContactList()) {
					for (Agency a : findAll()) {
						if (a.getContact().equals(c)) {
							agency.add(a);
						}
					}
				}

			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return agency;
	}

}
