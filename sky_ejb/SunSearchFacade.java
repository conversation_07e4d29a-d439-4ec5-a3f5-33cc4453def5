/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.SunCompanyProduct;
import com.insurfact.skynet.SunTerminated;
import com.insurfact.skynet.entity.Branch;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class SunSearchFacade {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Resource(lookup = "jdbc/Skytest")
    private DataSource ds;

    protected EntityManager getEntityManager() {
        return em;
    }

    public SunSearchFacade() {

    }

    private Connection getConnection() {

        try {

            return ds.getConnection();
        } catch (Exception e) {

            e.printStackTrace();
        }
        return null;
    }

    public List<SunTerminated> findAllCasesByAgentDets(String name, String advNum) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();

        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select distinct a.AGENTID,    "
                    + "        a.ADVISORNUMBER,       "
                    + "        a.OLD_AGENT_FIRSTNAME,    "
                    +//3
                    "        a.OLD_AGENT_LASTNAME,     "
                    +//4
                    "        a.SUNTERMDATE, a.TOTPOLS, "
                    +//5-6
                    "        a.TOTCLI, a.TOTPREM,      "
                    +//7-8
                    "        a.BRANCH_NAME_EN, a.BRANCH_NAME_FR, "
                    +//9-10
                    "        a.MARKETING_REG_NAME_EN, a.MARKETING_REG_NAME_FR, a.POLICYSTATUS "
                    +//11-13
                    "   from SUN_ALL_POLICY_TABLE a      ";
            if (!name.equals("")) {
                sql += "  WHERE (UPPER(a.OLD_AGENT_FIRSTNAME) LIKE substr('%" + name + "%',  instr('%" + name + "%', ' '))  OR"
                        + " UPPER(a.OLD_AGENT_FIRSTNAME) LIKE substr('%" + name + "%', 1, instr('%" + name + "%', ' ') - 1) OR "
                        + "   UPPER(a.OLD_AGENT_LASTNAME) LIKE substr('%" + name + "%', instr('%" + name + "%', ' ') + 1)) ";
            }

            if (!advNum.equals("")) {
                sql += "  WHERE a.ADVISORNUMBER LIKE '%" + advNum + "%' ";
            }

            sql += "  ORDER BY 4, 3, 7 ";
            System.out.println(" 93 SunSearchFacade sql=" + sql);
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {
                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));
                sunTerm.setPolType(rst.getString(13));

                terminatedSunList.add(sunTerm);

            }

            String sql2 = " select distinct a.AGENTID,    "
                    + "        a.ADVISORNUMBER,       "
                    + "        a.OLD_AGENT_FIRSTNAME,    "
                    +//3
                    "        a.OLD_AGENT_LASTNAME,     "
                    +//4
                    "        a.SUNTERMDATE, a.TOTPOLS, "
                    +//5-6
                    "        a.TOTCLI, a.TOTPREM,      "
                    +//7-8
                    "        a.BRANCH_NAME_EN, a.BRANCH_NAME_FR, "
                    +//9-10
                    "        a.MARKETING_REG_NAME_EN, a.MARKETING_REG_NAME_FR, a.POLICYSTATUS "
                    +//11-13
                    "   from SUN_ALL_POLICY_TABLEX a      ";
            if (!name.equals("")) {
                sql2 += "  WHERE (UPPER(a.OLD_AGENT_FIRSTNAME) LIKE substr('%" + name + "%',  instr('%" + name + "%', ' '))  OR"
                        + " UPPER(a.OLD_AGENT_FIRSTNAME) LIKE substr('%" + name + "%', 1, instr('%" + name + "%', ' ') - 1) OR "
                        + "   UPPER(a.OLD_AGENT_LASTNAME) LIKE substr('%" + name + "%', instr('%" + name + "%', ' ') + 1)) ";
            }

            if (!advNum.equals("")) {
                sql2 += "  WHERE a.ADVISORNUMBER LIKE '%" + advNum + "%' ";
            }

            sql2 += "  ORDER BY 4, 3, 7 ";
            rst = ptStmt.executeQuery(sql2);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {
                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));
                sunTerm.setPolType(rst.getString(13));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findAllCasesByClientDets(String name, String polNum) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select FIRSTNAME, LASTNAME,    "
                    + "        NAMEENGLISH, NAMEFRENCH,   "
                    + "        ISSUEDATE, STATUSDATE,     "
                    + "        FACEAMT,                   "
                    +//7
                    "        ANNPREM, POLICYNUMBER,     "
                    + "        ENGLISH_NAME, FRENCH_NAME, "
                    + "        EXPIRYDATE,                "
                    +//12
                    "        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)), "
                    +//13
                    "OLD_AGENT_FIRSTNAME,\n"
                    + "	OLD_AGENT_LASTNAME,\n"
                    + "	AGENT_FIRSTNAME,\n"
                    + "	AGENT_LASTNAME,\n"
                    + "	ADVISORNUMBER, POLICYSTATUS "
                    + "   from SUN_ALL_POLICY_WITH_ADVISORT ";
            if (!name.equals("")) {
                sql += "  WHERE (UPPER(FIRSTNAME) LIKE substr('%" + name + "%', instr('%" + name + "%', ' ')) OR "
                        + " UPPER(FIRSTNAME) LIKE substr('%" + name + "%', 1, instr('%" + name + "%', ' ') - 1) OR "
                        + "   UPPER(LASTNAME) LIKE substr('%" + name + "%', instr('%" + name + "%', ' ') + 1)) ";
            }

            if (!polNum.equals("")) {
                sql += "  WHERE UPPER(POLICYNUMBER) LIKE '%" + polNum + "%' ";
            }

            sql += "  ORDER BY 4, 3, 7 ";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setClientFirstName(rst.getString(1));
                sunTerm.setClientLastName(rst.getString(2));

                sunTerm.setCompNameEn(rst.getString(3));
                sunTerm.setCompNameFr(rst.getString(4));

                sunTerm.setIssDate(rst.getDate(5));

                sunTerm.setFaceAmnt(rst.getDouble(7));
                sunTerm.setAnnPrem(rst.getDouble(8));

                sunTerm.setPolNum(rst.getString(9));

                sunTerm.setProdNameEn(rst.getString(10));
                sunTerm.setProdNameFr(rst.getString(11));

                sunTerm.setExpiryDate(rst.getDate(12));
                sunTerm.setExpiryMonths(rst.getInt(13));
                sunTerm.setSunAdvNo(rst.getString(18));
                sunTerm.setOldAgentFirstName(rst.getString(14));
                sunTerm.setOldAgentLastName(rst.getString(15));
                sunTerm.setPolType(rst.getString(19));

                sunTerm.setPaidToDate(rst.getDate(6));

                terminatedSunList.add(sunTerm);
            }

            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public double findTotalPremByBranch(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        double totPrem = 0;
        if (connection == null) {
            return totPrem;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select sum(ANNPREM) from SUN_ALL_POLICY_WITH_ADVISORT  "
                    + "  where AGENTID=AGENTID and POLICYSTATUS IN (6, 36, 43, 44 ) and (BRANCH= " + branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null)";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totPrem;
            }

            if (rst.next()) {
                totPrem = rst.getDouble(1);
            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totPrem;
        } // return the connection to the pool

        return totPrem;
    }

    public Integer findTotalAdvByBranch(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totAdv = 0;
        if (connection == null) {
            return totAdv;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT AGENTID FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE (POLICYSTATUS IN (6, 36, 43, 44 )) and (BRANCH= " + branch.getBranchIntId() + " )  and (NO_AGENT_FLAG is null) GROUP BY AGENTID ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totAdv;
            }

            while (rst.next()) {
                totAdv = totAdv + 1;
            }

            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totAdv;
        } // return the connection to the pool

        return totAdv;
    }

    public double findTotalPremByMarkReg(Integer markReg) {

        ResultSet rst = null;
        Connection connection = getConnection();

        double totPrem = 0;
        if (connection == null) {
            return totPrem;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select sum(ANNPREM) from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID=AGENTID and POLICYSTATUS IN (6, 36, 43, 44 ) and (MARKETING_REG = " + markReg + " ) "
                    + "    and (NO_AGENT_FLAG is null)";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totPrem;
            }

            if (rst.next()) {

                totPrem = rst.getDouble(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totPrem;
        } // return the connection to the pool

        return totPrem;
    }

    public Integer findTotalAdvByMarkReg(Integer markReg) {

        ResultSet rst = null;

        Connection connection = getConnection();
        Integer totAdv = 0;
        if (connection == null) {
            return totAdv;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT AGENTID FROM SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  WHERE (POLICYSTATUS IN (6, 36, 43, 44 )) "
                    + "    and (MARKETING_REG = " + markReg + " ) and (NO_AGENT_FLAG is null) GROUP BY AGENTID ";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return totAdv;
            }

            while (rst.next()) {
                totAdv = totAdv + 1;
            }

            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totAdv;
        } // return the connection to the pool

        return totAdv;
    }

    public double findTotalPrem() {

        ResultSet rst = null;

        Connection connection = getConnection();

        double totPrem = 0;
        if (connection == null) {
            return totPrem;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select sum(ANNPREM) from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID=AGENTID and POLICYSTATUS IN (6, 36, 43, 44 ) "
                    + "    and (NO_AGENT_FLAG is null)";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totPrem;
            }

            if (rst.next()) {

                totPrem = rst.getDouble(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totPrem;
        } // return the connection to the pool

        return totPrem;
    }

    public Integer findTotalAdv() {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totAdv = 0;
        if (connection == null) {
            return totAdv;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT AGENTID FROM SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  WHERE POLICYSTATUS IN (6, 36, 43, 44 )  "
                    + "    and (NO_AGENT_FLAG is null) GROUP BY AGENTID  ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totAdv;
            }

            while (rst.next()) {
                totAdv = totAdv + 1;
            }

            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totAdv;
        } // return the connection to the pool

        return totAdv;
    }

    //////////////New code
    public Integer findTotPaidToDate() {

        ResultSet rst = null;
        Connection connection = getConnection();

        Integer totPaid = 0;
        if (connection == null) {
            return totPaid;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID = AGENTID  "
                    + "    and POLICYSTATUS='6' and (NO_AGENT_FLAG is null) ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totPaid;
            }

            if (rst.next()) {

                totPaid = rst.getInt(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totPaid;
        } // return the connection to the pool

        return totPaid;
    }

    public Integer findTotMissedPremium() {

        ResultSet rst = null;
        Connection connection = getConnection();

        Integer totMissedPremium = 0;
        if (connection == null) {
            return totMissedPremium;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID = AGENTID       "
                    + "    and POLICYSTATUS='36'       "
                    + "    and (NO_AGENT_FLAG is null) ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totMissedPremium;
            }

            if (rst.next()) {

                totMissedPremium = rst.getInt(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totMissedPremium;
        } // return the connection to the pool

        return totMissedPremium;
    }

    public Integer findTotLapseTime() {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totLapseTime = 0;
        if (connection == null) {
            return totLapseTime;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID = AGENTID       "
                    + "    and POLICYSTATUS='43'       "
                    + "    and (NO_AGENT_FLAG is null) ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totLapseTime;
            }

            if (rst.next()) {

                totLapseTime = rst.getInt(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totLapseTime;
        } // return the connection to the pool

        return totLapseTime;
    }

    public Integer findTotLapsePending() {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totLapsePending = 0;
        if (connection == null) {
            return totLapsePending;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT  "
                    + "  where AGENTID = AGENTID       "
                    + "    and POLICYSTATUS='44'       "
                    + "    and (NO_AGENT_FLAG is null) ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totLapsePending;
            }

            if (rst.next()) {

                totLapsePending = rst.getInt(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totLapsePending;
        } // return the connection to the pool

        return totLapsePending;
    }

    //by markReg
    public Integer findTotPaidToDateByMarkReg(Integer markReg) {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totPaid = 0;
        if (connection == null) {
            return totPaid;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID = AGENTID and POLICYSTATUS='6'     "
                    + "    and (MARKETING_REG = " + markReg + ") and (NO_AGENT_FLAG is null) ";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totPaid;
            }

            if (rst.next()) {

                totPaid = rst.getInt(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totPaid;
        } // return the connection to the pool

        return totPaid;
    }

    public Integer findTotMissedPremiumByMarkReg(Integer markReg) {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totMissedPremium = 0;
        if (connection == null) {
            return totMissedPremium;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='36' and (MARKETING_REG = " + markReg + " ) and (NO_AGENT_FLAG is null) ";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totMissedPremium;
            }

            if (rst.next()) {
                totMissedPremium = rst.getInt(1);
            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totMissedPremium;
        } // return the connection to the pool

        return totMissedPremium;
    }

    public Integer findTotLapseTimeByMarkReg(Integer markReg) {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totLapseTime = 0;
        if (connection == null) {
            return totLapseTime;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='43' and (MARKETING_REG = " + markReg + " ) and (NO_AGENT_FLAG is null) ";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totLapseTime;
            }

            if (rst.next()) {
                totLapseTime = rst.getInt(1);
            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totLapseTime;
        } // return the connection to the pool

        return totLapseTime;
    }

    public Integer findTotLapsePendingByMarkReg(Integer markReg) {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totLapsePending = 0;
        if (connection == null) {
            return totLapsePending;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='44' and (MARKETING_REG = " + markReg + " ) and (NO_AGENT_FLAG is null) ";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totLapsePending;
            }

            if (rst.next()) {

                totLapsePending = rst.getInt(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totLapsePending;
        } // return the connection to the pool

        return totLapsePending;
    }

    //////// by branch
    public Integer findTotPaidToDateByBranch(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totPaid = 0;
        if (connection == null) {
            return totPaid;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID = AGENTID and POLICYSTATUS='6' and (BRANCH= " + branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null) ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totPaid;
            }

            if (rst.next()) {
                totPaid = rst.getInt(1);
            }

            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totPaid;
        } // return the connection to the pool

        return totPaid;
    }

    public Integer findTotMissedPremiumByBranch(Branch branch) {

        ResultSet rst = null;
        Connection connection = getConnection();
        Integer totMissedPremium = 0;
        if (connection == null) {
            return totMissedPremium;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='36' and (BRANCH= " + branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null) ";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totMissedPremium;
            }

            if (rst.next()) {
                totMissedPremium = rst.getInt(1);
            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totMissedPremium;
        } // return the connection to the pool

        return totMissedPremium;
    }

    public Integer findTotLapseTimeByBranch(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totLapseTime = 0;
        if (connection == null) {
            return totLapseTime;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID = AGENTID and POLICYSTATUS='43'    "
                    + "    and (BRANCH= " + branch.getBranchIntId() + ") and (NO_AGENT_FLAG is null) ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totLapseTime;
            }

            if (rst.next()) {

                totLapseTime = rst.getInt(1);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totLapseTime;
        } // return the connection to the pool

        return totLapseTime;
    }

    public Integer findTotLapsePendingByBranch(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        Integer totLapsePending = 0;
        if (connection == null) {
            return totLapsePending;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='44' and (BRANCH= " + branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null) ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return totLapsePending;
            }

            if (rst.next()) {
                totLapsePending = rst.getInt(1);
            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return totLapsePending;
        } // return the connection to the pool

        return totLapsePending;
    }

    public List<SunTerminated> findAdvisorsByBranchPaidToDate(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE"
                    + "  WHERE BRANCH=" + branch.getBranchIntId() + " and POLICYSTATUS='6'";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findByMarkRegCodePaidToDate(Integer markRegCode) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE"
                    + "  WHERE MARKETING_REG=" + markRegCode + " and POLICYSTATUS='6'";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findInforceCasesPaidToDate() {

        ResultSet rst = null;
        Connection connection = getConnection();
        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='6'";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    ////////////////////////
    public List<SunTerminated> findAdvisorsByBranchLapseTime(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE"
                    + "  WHERE BRANCH=" + branch.getBranchIntId() + " and POLICYSTATUS='43'";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findByMarkRegCodeLapseTime(Integer markRegCode) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE"
                    + "  WHERE MARKETING_REG=" + markRegCode + " and POLICYSTATUS='43'";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findInforceCasesLapseTime() {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='43'";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    /////////////////
    public List<SunTerminated> findAdvisorsByBranchLapsePending(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE"
                    + "  WHERE BRANCH=" + branch.getBranchIntId() + " and POLICYSTATUS='44'";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findByMarkRegCodeLapsePending(Integer markRegCode) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE"
                    + "  WHERE MARKETING_REG=" + markRegCode + " and POLICYSTATUS='44'";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findInforceCasesLapsePending() {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='44'";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findAdvisorsByBranchMissedPremium(Branch branch) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE"
                    + "  WHERE BRANCH=" + branch.getBranchIntId() + " and POLICYSTATUS='36'";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findByMarkRegCodeMissedPremium(Integer markRegCode) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE"
                    + "  WHERE MARKETING_REG=" + markRegCode + " and POLICYSTATUS='36'";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findInforceCasesMissedPremium() {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='36'";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));
                sunTerm.setSunTermDate(rst.getDate(5));

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                sunTerm.setBranchNameEn(rst.getString(9));
                sunTerm.setBranchNameFr(rst.getString(10));
                sunTerm.setMarketNameEn(rst.getString(11));
                sunTerm.setMarketNameFr(rst.getString(12));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findAdvisorDetails(int agentID) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select FIRSTNAME, LASTNAME,       "
                    + "        NAMEENGLISH, NAMEFRENCH,   "
                    + "        ISSUEDATE, STATUSDATE,     "
                    + "        FACEAMT,                   "
                    +//7
                    "        ANNPREM, POLICYNUMBER,     "
                    + "        ENGLISH_NAME, FRENCH_NAME, "
                    + "        EXPIRYDATE,                "
                    +//12
                    "        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)) "
                    +//13
                    "   from SUN_ALL_POLICY_WITH_ADVISORT "
                    + "  where AGENTID=" + agentID
                    + "  ORDER BY 2,1 ";
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setClientFirstName(rst.getString(1));
                sunTerm.setClientLastName(rst.getString(2));

                sunTerm.setCompNameEn(rst.getString(3));
                sunTerm.setCompNameFr(rst.getString(4));

                sunTerm.setIssDate(rst.getDate(5));

                sunTerm.setFaceAmnt(rst.getDouble(7));
                sunTerm.setAnnPrem(rst.getDouble(8));

                sunTerm.setPolNum(rst.getString(9));

                sunTerm.setProdNameEn(rst.getString(10));
                sunTerm.setProdNameFr(rst.getString(11));

                sunTerm.setExpiryDate(rst.getDate(12));
                sunTerm.setExpiryMonths(rst.getInt(13));

                sunTerm.setPaidToDate(rst.getDate(6));

                terminatedSunList.add(sunTerm);
            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    ////////////////////////////// The house /////////////////////////
    public List<SunTerminated> findCasesTheHouse(int markReg, int branch, int company, int productType, int expire, int house) {

        ResultSet rst = null;

        Connection connection = getConnection();
        String markRegSQL, branchSQL, companySQL, productTypeSQL, expireSQL;

        if (markReg == 0) {
            markRegSQL = "NOT IN (-1) or MARKETING_REG is null ";
        } else {
            markRegSQL = "IN (" + markReg + ")";
        }

        if (branch == 0) {
            branchSQL = "NOT IN (-1) or BRANCH is null ";
        } else {
            branchSQL = "IN (" + branch + ")";
        }

        if (company == 0) {
            companySQL = "NOT IN (-1) or COMPANYID is null ";
        } else {
            companySQL = "IN (" + company + ")";
        }

        switch (productType) {
            case 0:
                productTypeSQL = "NOT IN ('A') ";
                break;
            case 1:
                productTypeSQL = "= 'T' ";
                break;
            default:
                productTypeSQL = "= 'P' ";
                break;
        }

        switch (expire) {
            case 0:
                expireSQL = "";
                break;
            case 1:
                expireSQL = "  )  AND ADD_MONTHS(EXPIRYDATE, -" + expire + ") <= CURRENT_DATE ";
                break;
            default:
                expireSQL = "  )  AND CURRENT_DATE BETWEEN  ADD_MONTHS(EXPIRYDATE, -" + expire + ") and ADD_MONTHS(EXPIRYDATE, -" + (expire - 2) + ") ";
                break;
        }

        /*if (house == 0) {
            houseSQL = "";
        } else {
            houseSQL = "IN (" + house + ")";
        }*/

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT a.POLICYID ,"
                    //1
                    + " a.FIRSTNAME, a.LASTNAME, "
                    // 2-3
                    + "a.NAMEENGLISH, a.NAMEFRENCH,"
                    //4-5
                    + " a.FACEAMT,"
                    //6
                    + " a.ANNPREM, "
                    //7
                    + " a.ISSUEDATE,  "
                    //8
                    + " a.NO_AGENT_FLAG, "
                    //9
                    + " a.POLICYNUMBER, "
                    //10
                    //data for the new hose T...
                    + " a.BIRTHDAY, a.SEX , a.SMOKER, b.PROVINCE_CODE "
                    // 11             12     13          14
                    + " , a.EXPIRYDATE "
                    + " FROM SUN_ALL_POLICY_WITH_ADVISORT a inner join PROVINCE b on a.PROVSTATE = b.OLD_CODE  WHERE"
                    + " ( MARKETING_REG " + markRegSQL + " ) and "
                    + " ( BRANCH " + branchSQL + " ) and "
                    + " POLICYSTATUS in (6, 36, 43, 44) and "
                    + " ( COMPANYID " + companySQL + " ) and "
                    + " ( TERM_OR_PERM " + productTypeSQL + expireSQL;
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {
                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setPolicyId(rst.getInt(1));

                sunTerm.setClientFirstName(rst.getString(2));
                sunTerm.setClientLastName(rst.getString(3));

                sunTerm.setCompNameEn(rst.getString(4));
                sunTerm.setCompNameFr(rst.getString(5));

                sunTerm.setFaceAmnt(rst.getDouble(6));

                sunTerm.setAnnPrem(rst.getDouble(7));

                sunTerm.setIssDate(rst.getDate(8));
                sunTerm.setFlag(rst.getString(9));

                sunTerm.setPolNum(rst.getString(10));

                // For the new elementes of House
                sunTerm.setBirthDay(rst.getDate(11));
                sunTerm.setSex(rst.getString(12));
                sunTerm.setSmoker(rst.getString(13));
                sunTerm.setProvCode(rst.getString(14));
                sunTerm.setExpiryDate(rst.getDate(15));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool 

        return terminatedSunList;
    }

    public List<SunTerminated> findCasesTheHouse2(int markReg, int branch, int company, int productType, int expire, int house) {

        ResultSet rst = null;

        Connection connection = getConnection();
        String markRegSQL, branchSQL, companySQL, productTypeSQL, expireSQL;

        if (markReg == 0) {
            markRegSQL = "NOT IN (-1) or MARKETING_REG is null ";
        } else {
            markRegSQL = "IN (" + markReg + ")";
        }

        if (branch == 0) {
            branchSQL = "NOT IN (-1) or BRANCH is null ";
        } else {
            branchSQL = "IN (" + branch + ")";
        }

        if (company == 0) {
            companySQL = "NOT IN (-1) or COMPANYID is null ";
        } else {
            companySQL = "IN (" + company + ")";
        }

        switch (productType) {
            case 0:
                productTypeSQL = "NOT IN ('A')";
                break;
            case 1:
                productTypeSQL = "= 'T' ";
                break;
            default:
                productTypeSQL = "= 'P' ";
                break;
        }

        switch (expire) {
            case 0:
                expireSQL = "";
                break;
            case 6:
                expireSQL = "  )  AND ADD_MONTHS(EXPIRYDATE, -" + expire + ") <= STATUSDATE ";
                break;
            case 12:
                expireSQL = "  )  AND STATUSDATE BETWEEN ADD_MONTHS(CURRENT_DATE, -" + expire + ") and ADD_MONTHS(CURRENT_DATE, -" + (expire - 6) + ") ";
                break;
            default:
                expireSQL = "  )  AND STATUSDATE BETWEEN ADD_MONTHS(CURRENT_DATE, -" + expire + ") and ADD_MONTHS(CURRENT_DATE, -" + (expire - 12) + ") ";
                break;
        }

        /*if (house == 0) {
            houseSQL = "";
        } else {

            houseSQL = "IN (" + house + ")";
        }*/

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT a.POLICYID ,"
                    //1
                    + " a.FIRSTNAME, a.LASTNAME, "
                    // 2-3
                    + "a.NAMEENGLISH, a.NAMEFRENCH,"
                    //4-5
                    + " a.FACEAMT,"
                    //6
                    + " a.ANNPREM, "
                    //7
                    + " a.ISSUEDATE,  "
                    //8
                    + " a.NO_AGENT_FLAG, "
                    //9
                    + " a.POLICYNUMBER, "
                    //10
                    //data for the new hose T...
                    + " a.BIRTHDAY, a.SEX , a.SMOKER, b.PROVINCE_CODE "
                    // 11             12     13          14
                    + " , a.STATUSDATE "
                    + " FROM SUN_ALL_POLICY_WITH_ADVISORT a inner join PROVINCE b on a.PROVSTATE = b.OLD_CODE  WHERE"
                    + " ( MARKETING_REG " + markRegSQL + " ) and "
                    + " ( BRANCH " + branchSQL + " ) and "
                    + " POLICYSTATUS in (5, 12, 21) and "
                    + " ( COMPANYID " + companySQL + " ) and "
                    + " ( TERM_OR_PERM " + productTypeSQL + expireSQL;
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {
                System.out.println(" 105 SunSearchFacade ");
                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setPolicyId(rst.getInt(1));

                sunTerm.setClientFirstName(rst.getString(2));
                sunTerm.setClientLastName(rst.getString(3));

                sunTerm.setCompNameEn(rst.getString(4));
                sunTerm.setCompNameFr(rst.getString(5));

                sunTerm.setFaceAmnt(rst.getDouble(6));

                sunTerm.setAnnPrem(rst.getDouble(7));

                sunTerm.setIssDate(rst.getDate(8));
                sunTerm.setFlag(rst.getString(9));

                sunTerm.setPolNum(rst.getString(10));

                // For the new elementes of House
                sunTerm.setBirthDay(rst.getDate(11));
                sunTerm.setSex(rst.getString(12));
                sunTerm.setSmoker(rst.getString(13));
                sunTerm.setProvCode(rst.getString(14));
                sunTerm.setExpiryDate(rst.getDate(15));

                terminatedSunList.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunCompanyProduct> findActiveCompany() {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunCompanyProduct> sunCompanyProduct = new ArrayList<>();
        if (connection == null) {
            return sunCompanyProduct;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT DISTINCT COMPANYID , NAMEENGLISH, NAMEFRENCH FROM SUN_ALL_POLICY_WITH_ADVISORT ";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return sunCompanyProduct;
            }

            while (rst.next()) {
                SunCompanyProduct sunTerm = new SunCompanyProduct();

                sunTerm.setCompanyId(rst.getInt(1));

                sunTerm.setNameEnglish(rst.getString(2));
                sunTerm.setNameFrench(rst.getString(3));
                sunCompanyProduct.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return sunCompanyProduct;
        } // return the connection to the pool

        return sunCompanyProduct;
    }

    public List<SunCompanyProduct> findActiveProductType() {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunCompanyProduct> sunCompanyProduct = new ArrayList<>();
        if (connection == null) {
            return sunCompanyProduct;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " SELECT DISTINCT PRODUCTTYPE , DESCENGLISH, DESCFRENCH FROM SUN_ALL_POLICY_WITH_ADVISORT ";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return sunCompanyProduct;
            }

            while (rst.next()) {
                SunCompanyProduct sunTerm = new SunCompanyProduct();

                sunTerm.setProductType(rst.getInt(1));

                sunTerm.setDescEnlish(rst.getString(2));
                sunTerm.setDescFrench(rst.getString(3));
                sunCompanyProduct.add(sunTerm);

            }
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return sunCompanyProduct;
        } // return the connection to the pool

        return sunCompanyProduct;
    }

}
