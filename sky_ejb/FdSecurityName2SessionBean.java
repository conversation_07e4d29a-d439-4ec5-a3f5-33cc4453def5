/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdSecurityName2;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdSecurityName2SessionBean extends AbstractFacade<FdSecurityName2> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdSecurityName2SessionBean() {
		super(FdSecurityName2.class);
	}

	@Override
	public List<FdSecurityName2> findAll() {

		TypedQuery<FdSecurityName2> nq = em.createNamedQuery("FdSecurityName2.findAll", FdSecurityName2.class);

		nq.setHint("eclipselink.read-only", "true");

		// nq.setParameter("noSearch", noSearch);

		return nq.getResultList();

	}

}
