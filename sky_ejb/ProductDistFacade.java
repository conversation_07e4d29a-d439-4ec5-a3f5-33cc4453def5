/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Email;
import com.insurfact.skynet.entity.Phone;
import com.insurfact.skynet.entity.ProductDistributor;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ProductDistFacade extends AbstractFacade<ProductDistributor> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ProductDistFacade() {
		super(ProductDistributor.class);
	}

	public List<ProductDistributor> findByDescription(String name) {

		name = name.toUpperCase().trim() + "%";
		List<ProductDistributor> agency = new ArrayList<>();

		System.out.println("482 ProductDistFacade name=" + name);
		try {
			TypedQuery<ProductDistributor> nq = em.createQuery(
					"SELECT a FROM ProductDistributor a WHERE (UPPER(a.nameEn) like :name or UPPER(a.nameFr) like :name)",
					ProductDistributor.class);
			nq.setParameter("name", name);

			agency = nq.getResultList();

		} catch (NoResultException e) {
//            e.printStackTrace();
			return agency;
		}

		return agency;
	}

	public List<ProductDistributor> findByEmailAddress(String email) {

		email = "%" + email + "%";

		List<ProductDistributor> agency = new ArrayList<>();

		try {
			TypedQuery<Email> nq = em.createQuery("SELECT a FROM Email a WHERE LOWER(a.emailAddress) like  :email",
					Email.class);
			nq.setParameter("email", email);

			List<Email> emails = nq.getResultList();

			if (emails != null && !emails.isEmpty()) {

				for (Email e : emails) {

					for (Contact c : e.getContactList()) {
						for (ProductDistributor a : findAll()) {
							if (a.getContact().equals(c)) {
								agency.add(a);
							}
						}
					}

				}
			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return agency;
	}

	public List<ProductDistributor> findByPhoneNumber(String area, String number) {

		List<ProductDistributor> agency = new ArrayList<>();

		if (area != null && area.isEmpty()) {
			area = null;
		}

		if (number != null && number.isEmpty()) {
			number = null;
		}

//        System.out.println("findByPhoneNumber " + masterCode + " type="+ type);
		try {
			TypedQuery<Phone> nq = null;

			if (area != null && number == null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code", Phone.class);
				nq.setParameter("code", area);
			}

			if (area == null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.phoneNumber like :number", Phone.class);
				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (area != null && number != null) {
				nq = em.createQuery("SELECT a FROM Phone a WHERE a.areaCode = :code AND  a.phoneNumber like :number",
						Phone.class);
				nq.setParameter("code", area.trim());

				String n = number.trim() + "%";
				nq.setParameter("number", n);
			}

			if (nq == null) {
				return agency;
			}

			List<Phone> phones = nq.getResultList();

			if (phones == null || phones.isEmpty()) {
				return agency;
			}

			for (Phone p : phones) {

				for (Contact c : p.getContactList()) {
					for (ProductDistributor a : findAll()) {
						if (a.getContact().equals(c)) {
							agency.add(a);
						}
					}
				}

			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return agency;
	}

}
