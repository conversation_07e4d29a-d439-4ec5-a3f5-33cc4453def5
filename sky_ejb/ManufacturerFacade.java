/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.Manufacturer;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ManufacturerFacade extends AbstractFacade<Manufacturer> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public ManufacturerFacade() {
        super(Manufacturer.class);
    }
    
    public Manufacturer findManufacturerByCode(String code) {
        EntityManager em = getEntityManager();
        Manufacturer result = null;
         try {      
            Query nq = em.createNamedQuery("Manufacturer.findByMgmtCompanyCode",Manufacturer.class);
            nq.setParameter("mgmtCompanyCode", code);
           
            result = (Manufacturer) nq.getSingleResult();
            return result;

        } catch(NoResultException e){
            return null;
}
        
    }    
    
    public List<Manufacturer> findAllSorter() {
        EntityManager em = getEntityManager();

        try {
        	TypedQuery<Manufacturer> nq = em.createNamedQuery("Manufacturer.findAll", Manufacturer.class);

            return nq.getResultList();
        } catch (NoResultException e) {
            return null;
        } 
    }
        
    public List<Manufacturer> findByActiveStatus(char activeStatus) {
        EntityManager em = getEntityManager();

        try {
        	TypedQuery<Manufacturer> nq = em.createNamedQuery("Manufacturer.findByActive", Manufacturer.class);
            nq.setParameter("active", activeStatus);

            return nq.getResultList();
        } catch (NoResultException e) {
            return null;
        } 
    }
    
    public List<Manufacturer> findByName(String name) {
//        System.out.println("EJB>>  ManufacturerFacade.findByName with:" + name );
         List<Manufacturer> manus = null;
        try {
        	TypedQuery<Manufacturer> nq = getEntityManager().createNamedQuery("Manufacturer.findByName", Manufacturer.class);
            nq.setParameter("name", "%"+name.toUpperCase()+"%");
            
            manus =  nq.getResultList();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return manus;
    }  
    
    public List<Manufacturer> findByCode(String name) {
//        System.out.println("EJB>>  ManufacturerFacade.findByName with:" + name );
         List<Manufacturer> manus = null;
        try {
        	TypedQuery<Manufacturer> nq = getEntityManager().createNamedQuery("Manufacturer.findByCode", Manufacturer.class);
            nq.setParameter("code", "%"+name.toUpperCase()+"%");
            
            manus =  nq.getResultList();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return manus;
    } 
}
