/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2018 All Right Reserved, https://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdBestCategory2;
import com.insurfact.fundata.entity.FdCategory2;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdBestCategory2Facade extends AbstractFacade<FdBestCategory2> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdBestCategory2Facade() {
        super(FdBestCategory2.class);
    }
    
    public FdBestCategory2 getBestCategory2(FdCategory2 category){
        
        Query nq = em.createQuery("SELECT b FROM FdBestCategory2 b WHERE b.fdCategory2 = :category ",FdCategory2.class);
        nq.setParameter("category", category);
            
        return (FdBestCategory2) nq.getSingleResult();
    }
}
