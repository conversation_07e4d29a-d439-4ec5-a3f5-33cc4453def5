/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.fundata.entity.FdPerfBestOverall; 
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFundBestSessionBean extends AbstractFacade<FdPerfBestOverall> {


    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    public FdFundBestSessionBean() {
        super(FdPerfBestOverall.class);
    }

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

}
