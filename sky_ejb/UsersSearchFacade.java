/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.UserSearch;
import com.insurfact.skynet.entity.Users; 

import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class UsersSearchFacade extends AbstractFacade<UserSearch> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public UsersSearchFacade() {
        super(UserSearch.class);
    }
    
    public List<UserSearch> findByUsers(Users users){
        
    	TypedQuery<UserSearch> query = em.createQuery("select q from UserSearch q where q.users = :users", UserSearch.class);
        query.setParameter("users", users);
        
        return query.getResultList();
        
    }
    
}
