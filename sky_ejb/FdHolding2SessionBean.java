/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdHolding2;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdHolding2SessionBean extends AbstractFacade<FdHolding2> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdHolding2SessionBean() {
		super(FdHolding2.class);
	}

	@SuppressWarnings("finally")
	public List<FdHolding2> getByFundataKey(Long fundatakey) {
		List<FdHolding2> holdings = new ArrayList<>();

		EntityManager em = getEntityManager();

		try {
			TypedQuery<FdHolding2> nq = em.createNamedQuery("FdHolding2.findByHoldingFundatakey", FdHolding2.class);
			nq.setParameter("holdingFundatakey", fundatakey);

			holdings = nq.getResultList();
		} catch (NoResultException e) {
			//
		} finally {
			return holdings;
		}
	}

	public List<FdHolding2> findByHoldingIdName(Integer securityId, String holdingName, boolean market, boolean asset) {

		String sql = "";

		if (securityId != null)
			sql += "SELECT h FROM FdHolding2 h WHERE h.securityNameId = :holdingNameId ";
		else
			sql += "SELECT h FROM FdHolding2 h WHERE h.holdingSecurityName = :holdingName ";

		if (!market)
			sql += " AND (h.holdingMarketPercent IS NOT NULL)";

		if (!asset)
			sql += " AND (h.fdFund.fundTotalAssets IS NOT NULL)";

		sql += " ORDER BY h.holdingSecurityName ASC";

		TypedQuery<FdHolding2> nq = em.createQuery(sql, FdHolding2.class);

		if (securityId != null)
			nq.setParameter("holdingNameId", securityId);
		else
			nq.setParameter("holdingName", holdingName);

		return nq.getResultList();

	}
}
