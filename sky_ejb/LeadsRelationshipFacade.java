/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Lead;
import com.insurfact.skynet.entity.LeadsRelationship;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class LeadsRelationshipFacade extends AbstractFacade<LeadsRelationship> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @EJB
    private LeadFacade leadFacade;
    
    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public LeadsRelationshipFacade() {
        super(LeadsRelationship.class);
    }
    
    public LeadsRelationship findSingleRelationship(Lead lead1, Advisor advisor){
        LeadsRelationship lr = null;
        
        System.out.println("findSingleRelationship : " + lead1);
        
        try {
            Query nq = getEntityManager().createQuery("SELECT c FROM LeadsRelationship c WHERE  c.advisor = :advisor AND c.firstLeadIntId = :lead1 AND c.type = 'S'", LeadsRelationship.class);
            nq.setParameter("contact1", lead1.getLeadIntId());
            nq.setParameter("advisor", advisor);

            lr = (LeadsRelationship) nq.getSingleResult();
            
            Lead lead = leadFacade.find(lr.getFirstLeadIntId());
            lr.setFirst(lead);
      

        } catch (Exception e) {
          
            return null;
        }
        
        return lr;
    }
    
    public LeadsRelationship findCoupleRelationship(Lead lead1, Lead lead2, Advisor advisor){
        
        System.out.println("findCoupleRelationship : " + lead1 + " " +lead2);
        
        List<LeadsRelationship> list  =  new ArrayList<>();
        
        try {
            TypedQuery<LeadsRelationship> nq = getEntityManager().createQuery("SELECT c FROM LeadsRelationship c WHERE  c.advisor = :advisor AND  c.firstLeadIntId  IN (:lead1, :lead2)  AND c.secondLeadIntId IN (:lead1, :lead2) AND c.type = 'C'", LeadsRelationship.class);
            nq.setParameter("lead1", lead1.getLeadIntId());
            nq.setParameter("lead2", lead2.getLeadIntId());
            nq.setParameter("advisor", advisor);
            nq.setMaxResults(1);
            
            list = nq.getResultList();

            if(list != null && !list.isEmpty()) {
                
                LeadsRelationship lr = list.get(0);
                
                Lead lead = leadFacade.find(lr.getFirstLeadIntId());
                lr.setFirst(lead);
                
                lead = leadFacade.find(lr.getSecondLeadIntId());
                lr.setSecond(lead);
                
                
                return lr;
            }
        } catch (Exception e) {
          
         
        }
        
        return null;
    }
    
    public LeadsRelationship modify(LeadsRelationship lr){
        
        if(lr == null)
            return lr;
        
        if(lr.getFirst() != null)
            lr.setFirstLeadIntId(lr.getFirst().getLeadIntId());
        
        if(lr.getSecond() != null)
            lr.setSecondLeadIntId(lr.getSecond().getLeadIntId());
        
        // save changes
        em.merge(lr);
        
        return lr;
    }
    
    public LeadsRelationship save(LeadsRelationship lr){
        
        if(lr == null)
            return lr;
        
        if(lr.getFirst() != null)
            lr.setFirstLeadIntId(lr.getFirst().getLeadIntId());
        
        if(lr.getSecond() != null)
            lr.setSecondLeadIntId(lr.getSecond().getLeadIntId());
        
        // create new Entity
        em.persist(lr);
        
        return lr;
    }
    
    
}
