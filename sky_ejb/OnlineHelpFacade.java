/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.OnlineHelp;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class OnlineHelpFacade extends AbstractFacade<OnlineHelp> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public OnlineHelpFacade() {
		super(OnlineHelp.class);
	}

	public OnlineHelp findOnlineHelpById(Integer id) {
		OnlineHelp content = null;
		if (id != null) {
			content = em.find(OnlineHelp.class, id);
		}
		return content;
	}

	public OnlineHelp findByContentName(String name) {

		TypedQuery<OnlineHelp> query = em.createNamedQuery("OnlineHelp.findByName", OnlineHelp.class);
		query.setParameter("name", name);

		List<OnlineHelp> contents = query.getResultList();

		if (contents != null && !contents.isEmpty())
			return contents.get(0);

		return null;
	}

	public List<OnlineHelp> findByCategory(Integer categoryId) {
		TypedQuery<OnlineHelp> query = em.createQuery("SELECT w FROM OnlineHelp w WHERE w.onlineCategory = :category",
				OnlineHelp.class);
		query.setParameter("category", categoryId);

		return query.getResultList();
	}

	public List<OnlineHelp> findByName(String name) {

		name = "%" + name.toUpperCase() + "%";

		TypedQuery<OnlineHelp> nq = em.createQuery("SELECT a FROM OnlineHelp a WHERE   UPPER(a.name) like :name",
				OnlineHelp.class);
		nq.setParameter("name", name);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<OnlineHelp>
		 * criteriaQuery = cBuilder.createQuery(OnlineHelp.class); Root<OnlineHelp> p =
		 * criteriaQuery.from(OnlineHelp.class);
		 * 
		 * Predicate englishName =
		 * cBuilder.like(cBuilder.upper(p.get(OnlineHelp_.name)), name);
		 * 
		 * criteriaQuery.where(englishName); TypedQuery<OnlineHelp> query =
		 * em.createQuery(criteriaQuery); return query.getResultList();
		 */
	}

}
