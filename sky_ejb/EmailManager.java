/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.constant.Constants;
import com.insurfact.sdk.mail.Attachment;
import com.insurfact.sdk.mail.MailMngr;
import com.insurfact.skynet.Recipient;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.mail.Address;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.AddressException;
import jakarta.mail.internet.InternetAddress;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class EmailManager {

    /*
     * sending email using InsurfactSDK
     */
    public void sendEmail(String to, String from, String contactName, String subject, String message) throws AddressException, MessagingException, IOException {

        MailMngr mail = new MailMngr(Constants.MAIL_SERVER_IP);
        String hostname = Constants.HOST;

        mail.setHtml(false);
        mail.setDebug(false);
        mail.setLocalhost(hostname);

        mail.sendEmail(to, from, contactName, subject, message);

    }

    /*
     * sending email using InsurfactSDK
     */
    public void sendEmailAsHtml(String to, String from, String contactName, String subject, String message) throws AddressException, MessagingException, IOException {


        System.out.println("HTML Email sent to: " + to + " from: " + from);
        
        MailMngr mail = new MailMngr(Constants.MAIL_SERVER_IP);
        String hostname = Constants.HOST;

        mail.setHtml(true);
        mail.setDebug(false);
        mail.setLocalhost(hostname);

        mail.sendEmail(to, from, contactName, subject, message);

    }

    /*
     * sending email using InsurfactSDK
     */
    public void sendEmailWithAttachments(String to, String from, String subject, String message, List<Attachment> attachmentList) throws AddressException, MessagingException, IOException {

        MailMngr mail = new MailMngr(Constants.MAIL_SERVER_IP);
        String hostname = Constants.HOST;
        mail.setUsername(Constants.USER_NAME_EMAIL);
        mail.setPassword(Constants.PASSWORD_EMAIL);

        // get hostname
        try {

            InetAddress addr = InetAddress.getLocalHost();

            // Get hostname
            hostname = addr.getHostName();

        } catch (UnknownHostException e) {

            throw new MessagingException("The IP address of a host could not be determined.");
        }

        mail.setHtml(true);

        mail.setDebug(false);
        mail.setLocalhost(hostname);

        System.out.println("sending to: " + to + " from: " + from);

        mail.sendWithAttachments(to, from, subject, message, attachmentList);

    }
    
    /*
     * sending email using InsurfactSDK
     */
    public void sendEmailWithAttachmentsText(String to, String from, String subject, String message, List<Attachment> attachmentList) throws AddressException, MessagingException, IOException {

        MailMngr mail = new MailMngr(Constants.MAIL_SERVER_IP);
        String hostname = Constants.HOST;
        mail.setUsername(Constants.USER_NAME_EMAIL);
        mail.setPassword(Constants.PASSWORD_EMAIL);

        // get hostname
        try {

            InetAddress addr = InetAddress.getLocalHost();

            // Get hostname
            hostname = addr.getHostName();

        } catch (UnknownHostException e) {

            throw new MessagingException("The IP address of a host could not be determined.");
        }

        mail.setHtml(false);

        mail.setDebug(false);
        mail.setLocalhost(hostname);

        System.out.println("sending to: " + to + " from: " + from);

        mail.sendWithAttachments(to, from, subject, message, attachmentList);

    }

    public void sendEmailWithAttachments(List<Recipient> recipients, String from, String subject, String message, List<Attachment> attachmentList) throws AddressException, MessagingException, IOException {

        MailMngr mail = new MailMngr(Constants.MAIL_SERVER_IP);
        String hostname = Constants.HOST;
        mail.setUsername(Constants.USER_NAME_EMAIL);
        mail.setPassword(Constants.PASSWORD_EMAIL);

        // get hostname
        try {

            InetAddress addr = InetAddress.getLocalHost();

            // Get hostname
            hostname = addr.getHostName();

        } catch (UnknownHostException e) {

            throw new MessagingException("The IP address of a host could not be determined.");
        }

        mail.setHtml(true);

        mail.setDebug(false);
        mail.setLocalhost(hostname);
        Address[] toAddress = new Address[recipients.size()];

        //System.out.println("sending to: " + to + " from: " + from);
        for (int i = 0; i < recipients.size(); i++) {
            toAddress[i] = new InternetAddress(recipients.get(i).getAddress());
        }

        mail.sendWithAttachments(toAddress, from, subject, message, attachmentList);

    }
}
