/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdCategory;
import com.insurfact.fundata.entity.FdFundserv; 

import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFundServSessionBean extends AbstractFacade<FdFundserv> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdFundServSessionBean() {
		super(FdFundserv.class);
	}

	public FdFundserv getByFundServCode(String fundservCode) {

		//FdFundserv fdFundserv = null;

		if (fundservCode.startsWith("SSQG")) {

			// SSQG094
			String tmp = "SSQ";

			tmp += fundservCode.substring(4);

			fundservCode = tmp;
		}
		
		TypedQuery<FdFundserv> nq = em.createQuery(
				"SELECT a FROM FdFundserv a WHERE  a.servCode = :fundservCode", FdFundserv.class);
		nq.setParameter("fundservCode", fundservCode);
		return nq.getSingleResult();


		/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<FdFundserv> criteriaQuery = cBuilder.createQuery(FdFundserv.class);
		Root<FdFundserv> p = criteriaQuery.from(FdFundserv.class);

		Predicate codePredicate = cBuilder.equal(p.get(FdFundserv_.servCode), fundservCode);
		criteriaQuery.where(codePredicate);

		TypedQuery<FdFundserv> typeCodeQuery = em.createQuery(criteriaQuery);

		List<FdFundserv> results = typeCodeQuery.getResultList();
		if (results != null && !results.isEmpty()) {
			fdFundserv = results.get(0);
		}

		return fdFundserv;*/
	}

	public List<Integer> findDataKeysByFundServCode(String fundservCode) {

		//List<Integer> resultList = new ArrayList<>();

		if (fundservCode != null && !fundservCode.isEmpty()) {
			
			TypedQuery<Integer> nq = em.createQuery(
					"SELECT a FROM FdFundserv a.servFundatakey WHERE  a.servCode = :fundservCode", Integer.class);
			nq.setParameter("fundservCode", fundservCode);
			return nq.getResultList();

			/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
			CriteriaQuery<Integer> criteriaQuery = cBuilder.createQuery(Integer.class);
			Root<FdFundserv> p = criteriaQuery.from(FdFundserv.class);

			fundservCode = "%" + fundservCode.toUpperCase() + "%";

			Predicate codePredicate = cBuilder.like(cBuilder.upper(p.get(FdFundserv_.servCode)), fundservCode);
			criteriaQuery.where(codePredicate).select(p.get(FdFundserv_.servFundatakey));

			TypedQuery<Integer> typeCodeQuery = em.createQuery(criteriaQuery);
			resultList = typeCodeQuery.getResultList();*/
		}

		return new ArrayList<>();
	}

	public FdFundserv getByFundataKey(Integer fundatakey) {
		
		TypedQuery<FdFundserv> nq = em.createQuery(
				"SELECT a FROM FdFundserv a WHERE  a.servFundatakey = :fundatakey", FdFundserv.class);
		nq.setParameter("fundservCode", fundatakey);
		return nq.getSingleResult();

		/*FdFundserv fdFundserv = null;

		CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<FdFundserv> criteriaQuery = cBuilder.createQuery(FdFundserv.class);
		Root<FdFundserv> p = criteriaQuery.from(FdFundserv.class);

		Predicate codePredicate = cBuilder.equal(p.get(FdFundserv_.servFundatakey), fundatakey);
		criteriaQuery.where(codePredicate);

		TypedQuery<FdFundserv> typeCodeQuery = em.createQuery(criteriaQuery);

		List<FdFundserv> results = typeCodeQuery.getResultList();
		if (results != null && !results.isEmpty()) {
			fdFundserv = results.get(0);
		}

		return fdFundserv;*/
	}

	public List<FdFundserv> findByMgmtCompanyCode(String code) {
		EntityManager em = getEntityManager();

		try {
			TypedQuery<FdFundserv> nq = em.createNamedQuery("FdFundserv.findStartWithServCode", FdFundserv.class);
			nq.setParameter("servCode", "%" + code + "%");

			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<FdFundserv> findFundservByCriterias(String fsrvCode, FdCategory category) {

		TypedQuery<FdFundserv> nq;

		if (fsrvCode != null && !fsrvCode.isEmpty()) {

			String code = "%" + fsrvCode.toUpperCase() + "%";

			if (category != null)
				nq = em.createQuery(
						"SELECT f FROM FdFundserv f WHERE f.servCode like :fsrvcode AND (f.fdFund.category = :category) AND (f.fdFund.fundGrade IS NOT NULL)",
						FdFundserv.class);
			else
				nq = em.createQuery(
						"SELECT f FROM FdFundserv f WHERE f.servCode like :fsrvcode AND (f.fdFund.fundGrade IS NOT NULL)",
						FdFundserv.class);

			nq.setParameter("fsrvcode", code);

			if (category != null)
				nq.setParameter("category", category);

			List<FdFundserv> listFSRV = nq.getResultList();

			return listFSRV;
		}

		return null;
	}
}
