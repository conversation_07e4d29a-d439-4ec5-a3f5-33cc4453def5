/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.constant.Constants;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.CommissionPayable;
import com.insurfact.skynet.entity.Company;
import com.insurfact.skynet.entity.Contract;
import com.insurfact.skynet.entity.ContractEft;
import com.insurfact.skynet.entity.ContractSetup;
import com.insurfact.skynet.entity.Organization;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ContractCommissionHelper {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;
    
    
    @EJB
    private ContractSetupFacade contractSetupFacade;
    
    @EJB
    private ContractFacade contractFacade;
    
    @EJB
    private AdvisorFacade advisorFacade;
    
    @EJB
    private CommissionSourceFacade commissionSourceFacade;
    
    @EJB
    private CommissionPayableFacade commissionPayableFacade;
    
    @EJB
    private CommissionPayableTypeFacade commissionPayableTypeFacade;

    public boolean isValidContractNumber(String number){
        
        return  contractFacade.validContractNumber(number);
    }
   
    
    /**
     * return a calculated value that represent the name under that contract...
     * Advisor's name or Company's name
     *
     * @param advisor
     * @return
     */
    public String getContractName(ContractSetup contractSetup, Advisor advisor) {

        String name = "-";
        
        if (contractSetup == null) {
            return name;
        }

        Contract contract = contractSetup.getContract();

        int type = contract.getContractType().intValue();

        // Personal
        if (type == Constants.CONTRACT_TYPE_PERSONAL) {

            if (advisor != null) {
                return advisor.getName();
            }
        }

        // Corporate or AGA
        if (type == Constants.CONTRACT_TYPE_CORPORATE || type == Constants.CONTRACT_TYPE_AGA) {
            Company cie = contractSetup.getCompany();

            if (cie != null) {
//                System.out.println("*** getContractName Corp : "+  cie.getNameEn());
                return cie.getPrimaryName();
            }

        }

        return name;
    }
    
    public String getCommissionPayTo(ContractSetup contractSetup, Advisor advisor) {

        String payTo = "-";
        
        if (contractSetup == null) {
            return payTo;
        }

        Contract contract = contractSetup.getContract();
        
        ContractEft contractEft = contract.getContractEft();
      
        int paymentMethod = contract.getCommissionPaymentType().intValue();
        int type = contract.getContractType().intValue();

        // EFT
        if (paymentMethod == Constants.COMMISSION_PAYMENT_TYPE_EFT) {

            //Personal
            if (type == Constants.CONTRACT_TYPE_PERSONAL) {
                if (contractEft != null) {
                    return contractEft.getBankHolder();
                }
            }
            //Corporate
            if (type == Constants.CONTRACT_TYPE_CORPORATE || type == Constants.CONTRACT_TYPE_AGA) {
                if (contractEft != null) {
                    return contractEft.getBankHolder();
                }
            }

        }

        // CHEQUE
        if (paymentMethod == Constants.COMMISSION_PAYMENT_TYPE_CHEQUE) {
            return this.getContractName(contractSetup, advisor);
        }
        
        // TRANSFER ID
        if (paymentMethod == Constants.COMMISSION_PAYMENT_TYPE_TRANSFER) {
            
            String transferContractNumber = contract.getTransferId();
            
//            System.out.println("TransferId :"+transferContractNumber);
            
            if(isValidContractNumber(transferContractNumber)){
                
//                System.out.println("is valid TransferId :"+transferContractNumber);
                
                List<Contract> transferContracts = contractFacade.findByContractNumber(transferContractNumber);
                
                if(transferContracts != null && !transferContracts.isEmpty()){
                    
                    // take the first one, just in case...
                    Contract transferContract = transferContracts.get(0);
                    
                    Agency subAgency  = contract.getContractSetup().getSubAgency();
                    
                    if(subAgency !=null)
                    {
                        
                        System.out.println("CommissionPayTo with TransferId - SubAgency :"+subAgency);
                        return getCommissionPayTo(transferContract.getContractSetup(), subAgency);
                    }
                    else {
                         System.out.println("CommissionPayTo with TransferId :"+advisor);
                         
                        return getCommissionPayTo(transferContract.getContractSetup(), advisor);
                    }
                }
            }
            
            else{
                
                System.out.println("not valid TransferId :"+transferContractNumber);
            
                return "? no valid transfer contract #"+contract.getContractNumber();
            }
            
        }// transfer id
        
        
        // PRODUCER ON NO PAY
        if (paymentMethod == Constants.COMMISSION_PAYMENT_TYPE_NO_PAY) {
            
            String tmp = getContractName(contractSetup, advisor);
            
            return tmp + " (NO PAY)";     
        }
      
        return "N/A";
    }

    
    
    /**
     * return a calculated value that represent the name under that contract...
     * Agency's name 
     *
     * @param Agency
     * @return
     */
    public String getContractName(ContractSetup contractSetup, Agency agency) {

        String name = "-";
        
        if (contractSetup == null) {
            return name;
        }

        if(agency != null){
            Organization organization = agency.getOrganization();
            name = organization.getName();
        }
        
        return name;
    }    
    
    public String getCommissionPayTo(ContractSetup contractSetup, Agency agency) {

        String payTo = "-";
        
        if (contractSetup == null) {
            return payTo;
        }

        Contract contract = contractSetup.getContract();
        
        if(contract == null){
            return "-";
        }
        
        ContractEft contractEft = contract.getContractEft();
      
        int paymentMethod = contract.getCommissionPaymentType().intValue();
        //int type = contract.getContractType().intValue();
          
//        Organization organization = agency.getOrganization();
       
        // EFT
        if (paymentMethod == Constants.COMMISSION_PAYMENT_TYPE_EFT) {

            if (contractEft != null) {
                return contractEft.getBankHolder();
            }
        }
       

        // CHEQUE
        if (paymentMethod == Constants.COMMISSION_PAYMENT_TYPE_CHEQUE) {
            return this.getContractName(contractSetup, agency);
        }
        
        // TRANSFER ID
        if (paymentMethod == Constants.COMMISSION_PAYMENT_TYPE_TRANSFER) {
            return "? no valid transfer contract #";  
        }
        
        
        // PRODUCER ON NO PAY
        if (paymentMethod == Constants.COMMISSION_PAYMENT_TYPE_NO_PAY) {
            
        }
        
        return payTo;
     
    }    

    /**
     * process CommissionPayable calculation from Investment policy type=FYC
     * @param payable
     * @param selectedContractSetup
     * @return 
     */
    public CommissionPayable processAdvisorFYCInvest(CommissionPayable payable, ContractSetup selectedContractSetup){
        
        double mgaCommission = payable.getCommissionSource().getCommissionPaid();

        double commRate = selectedContractSetup.getContract().getInvestmentFYCRate();

        payable.setFormualType(selectedContractSetup.getContract().getInvestmentFYCCalc());

        payable.setCommissionBase(mgaCommission);

        double toPay = (mgaCommission * commRate)/100;

        payable.setInitAmount(toPay);
        payable.setInitRate(commRate);

        payable.setCalcRate(commRate);
        payable.setCalcAmount(toPay);     
        

        
        return payable;
    }
    
    public CommissionPayable processAdvisorServiceFee(CommissionPayable payable, ContractSetup selectedContractSetup){

        double mgaCommission = payable.getCommissionSource().getCommissionPaid();
        
        double commRate = selectedContractSetup.getContract().getInvestmentFeeRate();

        payable.setFormualType(selectedContractSetup.getContract().getInvestmentFeeCalc());

        payable.setCommissionBase(mgaCommission);

        double toPay = (mgaCommission * commRate)/100;

        payable.setInitAmount(toPay);
        payable.setInitRate(commRate);

        payable.setCalcRate(commRate);
        payable.setCalcAmount(toPay);        
        
        return payable;
    }
}
