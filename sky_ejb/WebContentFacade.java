/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2012 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.WebContent; 
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class WebContentFacade extends AbstractFacade<WebContent> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }
    
    public WebContentFacade() {
        super(WebContent.class);
    }
    
    public List<WebContent> findByName(String name){
        
        name = "%" + name.toUpperCase() + "%";
        
        TypedQuery<WebContent> nq = em.createQuery(
				"SELECT a FROM WebContent a WHERE   UPPER(a.contentName) like :name",
				WebContent.class);
		nq.setParameter("name", name);
		return nq.getResultList();
        
        /*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
        CriteriaQuery<WebContent> criteriaQuery = cBuilder.createQuery(WebContent.class);
        Root<WebContent> p = criteriaQuery.from(WebContent.class);

        Predicate englishName = cBuilder.like(cBuilder.upper(p.get(WebContent_.contentName)), name);

        criteriaQuery.where(englishName);
        TypedQuery<WebContent> query = em.createQuery(criteriaQuery);
        return query.getResultList();*/
    }
    
    public WebContent findByContentName(String name){
        
    	TypedQuery<WebContent> query = em.createNamedQuery("WebContent.findByContentName", WebContent.class); 
        query.setParameter("contentName", name);
        
        List<WebContent> contents =  query.getResultList();
        
        if(contents != null && !contents.isEmpty())
            return contents.get(0);
        
        return null;
    }
    
    public WebContent findWebContentById(Integer id){
        WebContent content = null;
        if(id != null){
            content = em.find(WebContent.class, id);
        }
        return content;
    }
    
    public WebContent findAdvisorSendDocTemplate(){
        
        WebContent content = em.find(WebContent.class, 1003);
        return content;
    }
    
    public List<WebContent> findByCategory(Integer categoryId) {
    	TypedQuery<WebContent> query = em.createQuery("SELECT w FROM WebContent w WHERE w.contentCategory = :categoryId", WebContent.class); 
        query.setParameter("categoryId", categoryId);
        
        return query.getResultList();
    }
}
