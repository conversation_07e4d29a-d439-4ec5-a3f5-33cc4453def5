/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.TypeClass;

import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.*;
import org.apache.poi.ss.formula.functions.T;

/**
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Stateless
public class TypeClassEntity extends AbstractFacade<TypeClass> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public TypeClassEntity() {
        super(TypeClass.class);
    }

    @SuppressWarnings("unchecked")
	public List<TypeClass> findAllCategoriesForDocuments(String listOfProfiles, String listOfTypes) {
        List<TypeClass> typeClasses;

        try {
            Query nq;
            if (listOfTypes == null) {
                nq = getEntityManager().createNativeQuery("select * from TYPE_CLASS where TYPE_CLASS_INT_ID in (SELECT TYPE_CLASS from "
                        + "TYPES where TYPE_INT_ID in (select sf.FILE_TYPE from STORED_FILE sf INNER JOIN STORED_FILE_PROFILE sfp on "
                        + "sf.STORED_FILE_INT_ID = sfp.STORED_FILE where sf.TYPE_ = 9 and sfp.PROFILE in (" + listOfProfiles + "))) ", TypeClass.class);
            } else {
                nq = getEntityManager().createNativeQuery("select * from TYPE_CLASS where TYPE_CLASS_INT_ID in (SELECT TYPE_CLASS from "
                        + "TYPES where TYPE_INT_ID in (select sf.FILE_TYPE from STORED_FILE sf where sf.TYPE_ = 9 and sf.FILE_TYPE "
                        + "in (" + listOfTypes + ")))", TypeClass.class);
            }
            typeClasses = nq.getResultList();

        } catch (NoResultException e) {
            return null;
        }

        return typeClasses;
    }

    public List<TypeClass> findAllByPartialTypeName(String partialName) {
        List<TypeClass> results = new ArrayList<>();
        try {
            jakarta.persistence.criteria.CriteriaBuilder cb = getEntityManager().getCriteriaBuilder();
            jakarta.persistence.criteria.CriteriaQuery<TypeClass> cq = cb.createQuery(TypeClass.class);
            jakarta.persistence.criteria.Root<TypeClass> root = cq.from(TypeClass.class);
            jakarta.persistence.criteria.Predicate predicate = cb.like(cb.lower(root.get("typeName")),
                    "%" + partialName.toLowerCase() + "%");
            cq.where(predicate);
            results = getEntityManager().createQuery(cq).getResultList();
        } catch (NoResultException e) {
            System.out.println("No matching TypeClass found for partialName: " + partialName);
        } catch (Exception e) {
            System.err.println("An error occurred during database query: " + e.getMessage());
            e.printStackTrace();
        }
        return results;
    }

    public List<TypeClass> findByName(String name) {
        List<TypeClass> results = new ArrayList<>();
        try {
            TypedQuery<TypeClass> query = getEntityManager().createQuery(
                    "SELECT tc FROM TypeClass tc WHERE tc.typeName = :typeName", TypeClass.class);
            query.setParameter("typeName", name);
            results = query.getResultList();
        } catch (NoResultException e) {
            System.out.println("No TypeClass found with name: " + name);
        } catch (Exception e) {
            System.err.println("Error querying database: " + e.getMessage());
            e.printStackTrace();
        }
        return results;
    }
}
