/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.Company; 
import com.insurfact.skynet.entity.ContractSetup;
import com.insurfact.skynet.entity.ProductSupplier;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

import org.eclipse.persistence.config.HintValues;
import org.eclipse.persistence.config.QueryHints;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ContractSetupFacade extends AbstractFacade<ContractSetup> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ContractSetupFacade() {
		super(ContractSetup.class);
	}

	public ContractSetup populateTransients(ContractSetup contractSetup) {

		Integer ref = null;

		// Agency's CS
		ref = contractSetup.getOtherContractSetupRef();
		if (ref != null) {
			ContractSetup cs = find(ref);
			contractSetup.setAgencyContractSetup(cs);
			contractSetup.getContract().setTransferId(cs.getContract().getContractNumber());
		}

		return contractSetup;

	}

	public List<ContractSetup> findAllByLicensee(Company company) {

		List<ContractSetup> list;

		TypedQuery<ContractSetup> query = em.createQuery("SELECT c FROM ContractSetup c WHERE c.company = :company",
				ContractSetup.class);
		query.setParameter("company", company);

		query.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

		list = query.getResultList();
		return list;
	}

	public List<ContractSetup> findAllAGAContractSetup(Agency agency, ProductSupplier ps) {

		List<ContractSetup> list;

		TypedQuery<ContractSetup> query = em.createQuery(
				"SELECT c FROM ContractSetup c WHERE c.productSupplier = :productSupplier AND c.servicingAgency = :agency",
				ContractSetup.class);
		query.setParameter("productSupplier", ps);
		query.setParameter("agency", agency);

		query.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

		list = query.getResultList();
		return list;
	}

	public List<ContractSetup> findAllContractSetupProductSuppOrMga(ProductSupplier ps) {
		TypedQuery<ContractSetup> query = em.createNamedQuery("ContractSetup.findByProductSupOrMgaSup",
				ContractSetup.class);
		query.setParameter("productSupplier", ps);
		return query.getResultList();
	}

	public List<ContractSetup> findAllContractSetupMga(ProductSupplier ps) {
		TypedQuery<ContractSetup> query = em.createNamedQuery("ContractSetup.findByMgaSup", ContractSetup.class);
		query.setParameter("productSupplier", ps);
		return query.getResultList();
	}

	public List<ContractSetup> findByContractNumber(String number, Agency agency) {

		number = "%" + number + "%";
		List<ContractSetup> list;

		TypedQuery<ContractSetup> query = em.createQuery(
				"SELECT c FROM ContractSetup c WHERE c.contract.contractNumber like :number AND (c.servicingAgency = :agency OR c.agency = :agency) ORDER BY c.company.primaryName",
				ContractSetup.class);
		query.setParameter("number", number);
		query.setParameter("agency", agency);

		query.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

		list = query.getResultList();
		return list;
	}

	public List<ContractSetup> findByCompanyStatusAgency(Integer status, ProductSupplier ps, Agency agency) {

		List<ContractSetup> list;

		TypedQuery<ContractSetup> query = em.createQuery(
				"SELECT c FROM ContractSetup c WHERE c.contract.contractStatus = :status AND c.productSupplier = :ps AND (c.servicingAgency = :agency OR c.agency = :agency) ORDER BY c.company.primaryName",
				ContractSetup.class);
		query.setParameter("status", status);
		query.setParameter("ps", ps);
		query.setParameter("agency", agency);

		query.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

		list = query.getResultList();
		return list;
	}

	public List<ContractSetup> findByContractNumber(String number, Advisor aga) {

		number = "%" + number + "%";
		List<ContractSetup> list;

		TypedQuery<ContractSetup> query = em.createQuery(
				"SELECT c FROM ContractSetup c WHERE c.contract.contractNumber like :number AND  c.agaAdvisor = :aga ORDER BY c.company.primaryName",
				ContractSetup.class);
		query.setParameter("number", number);
		query.setParameter("aga", aga);

		query.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

		list = query.getResultList();
		return list;
	}

	public List<ContractSetup> findByCompanyStatusAdvisor(Integer status, ProductSupplier ps, Advisor aga) {

		List<ContractSetup> list;

		TypedQuery<ContractSetup> query = em.createQuery(
				"SELECT c FROM ContractSetup c WHERE c.contract.contractStatus = :status AND c.productSupplier = :ps AND c.agaAdvisor = :aga ORDER BY c.company.primaryName",
				ContractSetup.class);
		query.setParameter("status", status);
		query.setParameter("ps", ps);
		query.setParameter("aga", aga);

		query.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

		list = query.getResultList();
		return list;
	}

}
