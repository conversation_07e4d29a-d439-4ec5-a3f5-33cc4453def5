/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Activity;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.CalendarEvent;
import com.insurfact.skynet.entity.HouseholdLead;
import com.insurfact.skynet.entity.Lead;
import com.insurfact.skynet.entity.Opportunity;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class LeadFacade extends AbstractFacade<Lead> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public LeadFacade() {
		super(Lead.class);
	}

	public Lead getLeadByLeadIntId(int leadIntId) {

		try {
			Query nq = em.createNamedQuery("Lead.findByLeadIntId", Lead.class);
			nq.setParameter("leadIntId", leadIntId);
			return (Lead) nq.getSingleResult();

		} catch (NoResultException e) {
			System.out.println("LeadFacade 134 NoResultException or NonUniqueResultException " + e.getMessage());
			return null;
		}

	}

	@SuppressWarnings("unchecked")
	public List<Lead> findByUsers(Users users) {

		List<Lead> list = new ArrayList<>();

		try {
			Query nq = em.createNativeQuery("SELECT * FROM LEAD where type != 19 and USERS = " + users.getUserIntId(),
					Lead.class);

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;
	}

	@SuppressWarnings("unchecked")
	public List<Lead> findByAdvisor(Advisor advisor) {

		List<Lead> list = new ArrayList<>();

		try {
			Query nq = em.createNativeQuery("SELECT * FROM LEAD where ADVISOR = " + advisor.getAdvisorIntId(),
					Lead.class);
			System.out.println("sql: " + "SELECT * FROM LEAD where ADVISOR = " + advisor.getAdvisorIntId());

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;
	}

	public Lead searchExistingContact(String leadName, String lastName, Date birthDate, Advisor advisor, int type) {
		System.out.println("com.insurfact.skynet.ejb.ContactFacade.searchExistingContact()");
		Lead result;
		Query query;
		// String[] names = leadName.split(" ");
		Calendar bday = Calendar.getInstance();
		bday.setTime(birthDate);
		String year = String.valueOf(bday.get(Calendar.YEAR) + 1900);
		String month = String.valueOf(bday.get(Calendar.MONTH) + 1);
		String day = String.valueOf(bday.get(Calendar.DATE));

		if (month.length() == 1) {
			month = "0" + month;
		}
		if (day.length() == 1) {
			day = "0" + day;
		}

		System.out.println("sql: " + "SELECT * from LEAD where ADVISOR = " + advisor.getAdvisorIntId() + " and TYPE = "
				+ type + " and  UPPER(FIRSTNAME) like UPPER('" + leadName.toUpperCase()
				+ "') and UPPER(LASTNAME) like UPPER('" + lastName.toUpperCase()
				+ "') and EXTRACT(YEAR FROM BIRTHDATE) = '" + year + "' and EXTRACT(MONTH FROM BIRTHDATE) = '" + month
				+ "' and EXTRACT(DAY FROM BIRTHDATE) = '" + day + "' ");

		query = em.createNativeQuery(" SELECT * from LEAD where ADVISOR = " + advisor.getAdvisorIntId() + " and TYPE = "
				+ type + " and  UPPER(FIRSTNAME) like UPPER('" + leadName.toUpperCase()
				+ "') and UPPER(LASTNAME) like UPPER('" + lastName.toUpperCase()
				+ "') and EXTRACT(YEAR FROM BIRTHDATE) = '" + year + "' and EXTRACT(MONTH FROM BIRTHDATE) = '" + month
				+ "' and EXTRACT(DAY FROM BIRTHDATE) = '" + day + "' ", Lead.class);
		query.setMaxResults(1);
		if (!query.getResultList().isEmpty()) {
			result = (Lead) query.getSingleResult();
			return result;
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	public void deleteLead(Lead lead) {
		// delete all oportunities for that lead
		List<Opportunity> oList = new ArrayList<>();
		List<Activity> aList = new ArrayList<>();
		List<HouseholdLead> houseList = new ArrayList<>();
		Query nq;

		try {
			nq = em.createNativeQuery(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN LEADS_RELATIONSHIP lr ON o.LEADS_RELATIONSHIP = lr.LEADS_RELATIONSHIP_INT_ID where lr.FIRST_LEAD_INT_ID  = "
							+ lead.getLeadIntId(),
					Opportunity.class);
			oList = nq.getResultList();

		} catch (NoResultException e) {
		}
		for (Opportunity o : oList) {
			// remove all oportunity activities
			try {
				nq = getEntityManager().createNativeQuery(
						"SELECT * FROM ACTIVITY WHERE TYPE = 1 and TYPE_ID = " + o.getOpportunityIntId(),
						Activity.class);
				aList = nq.getResultList();

			} catch (NoResultException e) {
			}
			for (Activity a : aList) {

				em.remove(a);

			}

			// remove all leadRelationship
			em.remove(o.getLeadsRelationship());

			// remove the oportunity
			em.remove(o);

		}

		// remove all lead activities
		try {
			nq = getEntityManager().createNativeQuery(
					"SELECT * FROM ACTIVITY WHERE TYPE = 2 and TYPE_ID = " + lead.getLeadIntId(), Activity.class);
			aList = nq.getResultList();

		} catch (NoResultException e) {
		}
		for (Activity a : aList) {

			em.remove(a);

		}

		// remove it from hosehold
		try {
			nq = getEntityManager().createNativeQuery("SELECT * FROM HOUSEHOLD_LEAD where LEAD1 = "
					+ lead.getLeadIntId() + " or LEAD2 = " + lead.getLeadIntId(), HouseholdLead.class);
			houseList = nq.getResultList();

		} catch (NoResultException e) {
		}
		for (HouseholdLead h : houseList) {

			if (h.getLead1().equals(lead) && lead.getHouseholdType() == 0) {
				h.getLead2().setHouseholdType(0);
				em.merge(h.getLead2());
			} else if (h.getLead2().equals(lead) && lead.getHouseholdType() == 0) {
				h.getLead1().setHouseholdType(0);
				em.merge(h.getLead1());
			}
			em.remove(h);

		}

		// remove the lead
		
		
		em.remove(getEntityManager().merge(lead));

	}

	@SuppressWarnings("unchecked")
	public void deleteLeadAll(List<Lead> leads) {
		// delete all oportunities for that lead
		List<Opportunity> oList = new ArrayList<>();
		List<Activity> aList = new ArrayList<>();
		List<HouseholdLead> houseList = new ArrayList<>();
		Query nq;

		for (Lead lead : leads) {

			try {
				nq = em.createNativeQuery(
						"SELECT o.* FROM OPPORTUNITY o INNER JOIN LEADS_RELATIONSHIP lr ON o.LEADS_RELATIONSHIP = lr.LEADS_RELATIONSHIP_INT_ID where lr.FIRST_LEAD_INT_ID  = "
								+ lead.getLeadIntId(),
						Opportunity.class);
				oList = nq.getResultList();

			} catch (NoResultException e) {
			}
			for (Opportunity o : oList) {
				// remove all oportunity activities
				try {
					nq = getEntityManager().createNativeQuery(
							"SELECT * FROM ACTIVITY WHERE TYPE = 1 and TYPE_ID = " + o.getOpportunityIntId(),
							Activity.class);
					aList = nq.getResultList();

				} catch (NoResultException e) {
				}
				for (Activity a : aList) {

					em.remove(a);

				}

				// remove all leadRelationship
				em.remove(o.getLeadsRelationship());

				// remove the oportunity
				em.remove(o);

			}

			// remove all lead activities
			try {
				nq = getEntityManager().createNativeQuery(
						"SELECT * FROM ACTIVITY WHERE TYPE = 2 and TYPE_ID = " + lead.getLeadIntId(), Activity.class);
				aList = nq.getResultList();

			} catch (NoResultException e) {
			}
			for (Activity a : aList) {

				em.remove(a);

			}

			// remove it from hosehold
			try {
				nq = getEntityManager().createNativeQuery("SELECT * FROM HOUSEHOLD_LEAD where LEAD1 = "
						+ lead.getLeadIntId() + " or LEAD2 = " + lead.getLeadIntId(), HouseholdLead.class);
				houseList = nq.getResultList();

			} catch (NoResultException e) {
			}
			for (HouseholdLead h : houseList) {

				em.remove(h);

			}

			// remove the lead
			em.remove(getEntityManager().merge(lead));

		}

	}

	public void editLead(Lead lead) {
		em.merge(lead);
	}

}
