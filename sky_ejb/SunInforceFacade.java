/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.SunTerminated;
import com.insurfact.skynet.entity.Branch;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class SunInforceFacade {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Resource(lookup = "jdbc/Skytest")
	private DataSource ds;

	protected EntityManager getEntityManager() {
		return em;
	}

	public SunInforceFacade() {

	}

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (Exception e) {

			e.printStackTrace();
		}
		return null;
	}

	public List<SunTerminated> findInforceCases() {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm2 = new SunTerminated();

				sunTerm2.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm2.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm2.setSunAdvNo("n/a");
				}

				sunTerm2.setAgentFirstName(rst2.getString(3));
				sunTerm2.setAgentLastName(rst2.getString(4));
				sunTerm2.setSunTermDate(rst2.getDate(5));

				sunTerm2.setTotalPolicies(rst2.getInt(6));
				sunTerm2.setTotalClients(rst2.getInt(7));
				sunTerm2.setTotalPremium(rst2.getDouble(8));

				sunTerm2.setBranchNameEn(rst2.getString(11));
				sunTerm2.setBranchNameFr(rst2.getString(12));
				sunTerm2.setMarketNameEn(rst2.getString(13));
				sunTerm2.setMarketNameFr(rst2.getString(14));
				sunTerm2.setFlag("X");

				terminatedSunList.add(sunTerm2);

			}
			rst.close();
			rst2.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAllCasesByAgentDets(String name, String advNum) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select distinct a.AGENTID,    " + "        a.ADVISORNUMBER,       "
					+ "        a.OLD_AGENT_FIRSTNAME,    " + // 3
					"        a.OLD_AGENT_LASTNAME,     " + // 4
					"        a.SUNTERMDATE, a.TOTPOLS, " + // 5-6
					"        a.TOTCLI, a.TOTPREM,      " + // 7-8
					"        a.BRANCH_NAME_EN, a.BRANCH_NAME_FR, " + // 9-10
					"        a.MARKETING_REG_NAME_EN, a.MARKETING_REG_NAME_FR " + // 11-12
					"   from SUN_ALL_POLICY_TABLE a      ";
			if (!name.equals("")) {
				sql += "  WHERE (UPPER(a.OLD_AGENT_FIRSTNAME) LIKE '%" + name + "%' OR "
						+ "   UPPER(a.OLD_AGENT_LASTNAME) LIKE '%" + name + "%') ";
			}

			if (!advNum.equals("")) {
				sql += "  WHERE a.ADVISORNUMBER LIKE '%" + advNum + "%' ";
			}

			sql += "  ORDER BY 4, 3, 7 ";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {
				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			ptStmt.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAllCasesByClientDets(String name, String polNum) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();

		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select FIRSTNAME, LASTNAME,       " + "        NAMEENGLISH, NAMEFRENCH,   "
					+ "        ISSUEDATE, STATUSDATE,     " + "        FACEAMT,                   " + // 7
					"        ANNPREM, POLICYNUMBER,     " + "        ENGLISH_NAME, FRENCH_NAME, "
					+ "        EXPIRYDATE,                " + // 12
					"        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)) " + // 13
					"   from SUN_ALL_POLICY_WITH_ADVISORT ";
			if (!name.equals("")) {
				sql += "  WHERE (UPPER(FIRSTNAME) LIKE '%" + name + "%' OR " + "   UPPER(LASTNAME) LIKE '%" + name
						+ "%') ";
			}

			if (!polNum.equals("")) {
				sql += "  WHERE UPPER(POLICYNUMBER) LIKE '%" + polNum + "%' ";
			}

			sql += "  ORDER BY 4, 3, 7 ";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setClientFirstName(rst.getString(1));
				sunTerm.setClientLastName(rst.getString(2));

				sunTerm.setCompNameEn(rst.getString(3));
				sunTerm.setCompNameFr(rst.getString(4));

				sunTerm.setIssDate(rst.getDate(5));

				sunTerm.setFaceAmnt(rst.getDouble(7));
				sunTerm.setAnnPrem(rst.getDouble(8));

				sunTerm.setPolNum(rst.getString(9));

				sunTerm.setProdNameEn(rst.getString(10));
				sunTerm.setProdNameFr(rst.getString(11));

				sunTerm.setExpiryDate(rst.getDate(12));
				sunTerm.setExpiryMonths(rst.getInt(13));

				sunTerm.setPaidToDate(rst.getDate(6));

				terminatedSunList.add(sunTerm);
			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(SunInforceFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAllCasesByClientDets(String advisorNumber) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();

		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select FIRSTNAME, LASTNAME,       " + "        NAMEENGLISH, NAMEFRENCH,   "
					+ "        ISSUEDATE, STATUSDATE,     " + "        FACEAMT,                   " + // 7
					"        ANNPREM, POLICYNUMBER,     " + "        ENGLISH_NAME, FRENCH_NAME, "
					+ "        EXPIRYDATE,                " + // 12
					"        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)) " + // 13
					"   from SUN_ALL_POLICY_WITH_ADVISORT " + "   WHERE ADVISORNUMBER = '" + advisorNumber + "'";

			sql += "  ORDER BY 4, 3, 7 ";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setClientFirstName(rst.getString(1));
				sunTerm.setClientLastName(rst.getString(2));

				sunTerm.setCompNameEn(rst.getString(3));
				sunTerm.setCompNameFr(rst.getString(4));

				sunTerm.setIssDate(rst.getDate(5));

				sunTerm.setFaceAmnt(rst.getDouble(7));
				sunTerm.setAnnPrem(rst.getDouble(8));

				sunTerm.setPolNum(rst.getString(9));

				sunTerm.setProdNameEn(rst.getString(10));
				sunTerm.setProdNameFr(rst.getString(11));

				sunTerm.setExpiryDate(rst.getDate(12));
				sunTerm.setExpiryMonths(rst.getInt(13));

				sunTerm.setPaidToDate(rst.getDate(6));

				terminatedSunList.add(sunTerm);
			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAdvisorsByBranch(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();

		if (connection == null) {
			return terminatedSunList;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId();
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(SunInforceFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCode(Integer markRegCode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();

		if (connection == null) {
			return terminatedSunList;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode;
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(SunInforceFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public double findTotalPremByBranch(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return 0;
		}
		double totPrem = 0;

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select sum(ANNPREM) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID=AGENTID and POLICYSTATUS IN (6, 36, 43, 44 ) and (BRANCH= "
					+ branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null)";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPrem;
			}

			if (rst.next()) {

				totPrem = rst.getDouble(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			e.printStackTrace();
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			return totPrem;
		} // return the connection to the pool

		return totPrem;
	}

	public Integer findTotalAdvByBranch(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totAdv = 0;
		if (connection == null) {
			return 0;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT AGENTID FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE (POLICYSTATUS IN (6, 36, 43, 44 )) and (BRANCH= "
					+ branch.getBranchIntId() + " )  and (NO_AGENT_FLAG is null) GROUP BY AGENTID ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totAdv;
			}

			while (rst.next()) {
				totAdv = totAdv + 1;
			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totAdv;
		} // return the connection to the pool

		return totAdv;
	}

	public Integer findTotalPolsByBranch(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totPol = 0;
		if (connection == null) {
			return totPol;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select TOTALPOLICE              " + "   from MARKETING_BRANCH_INFORCE  " + "  WHERE BRANCH="
					+ branch.getBranchIntId();
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPol;
			}

			if (rst.next()) {

				totPol = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPol;
		} // return the connection to the pool

		return totPol;
	}

	public List<SunTerminated> findTermAdvisorDetails(int agentID, String policyStatus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select FIRSTNAME, LASTNAME,       " + "        NAMEENGLISH, NAMEFRENCH,   "
					+ "        ISSUEDATE, STATUSDATE,     " + "        FACEAMT,                   " + // 7
					"        ANNPREM, POLICYNUMBER,     " + "        ENGLISH_NAME, FRENCH_NAME, "
					+ "        EXPIRYDATE,                " + // 12
					"        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)) " + // 13
					"   from SUN_ALL_POLICY_WITH_ADVISORT " + "  where AGENTID=" + agentID + " and POLICYSTATUS in ( "
					+ policyStatus + " ) " + "  ORDER BY 2,1 ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setClientFirstName(rst.getString(1));
				sunTerm.setClientLastName(rst.getString(2));

				sunTerm.setCompNameEn(rst.getString(3));
				sunTerm.setCompNameFr(rst.getString(4));

				sunTerm.setIssDate(rst.getDate(5));

				sunTerm.setFaceAmnt(rst.getDouble(7));
				sunTerm.setAnnPrem(rst.getDouble(8));

				sunTerm.setPolNum(rst.getString(9));

				sunTerm.setProdNameEn(rst.getString(10));
				sunTerm.setProdNameFr(rst.getString(11));

				sunTerm.setExpiryDate(rst.getDate(12));
				sunTerm.setExpiryMonths(rst.getInt(13));

				sunTerm.setPaidToDate(rst.getDate(6));

				terminatedSunList.add(sunTerm);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public double findTotalPremByMarkReg(Integer markReg) {

		ResultSet rst = null;

		Connection connection = getConnection();

		double totPrem = 0;
		if (connection == null) {
			return totPrem;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select sum(ANNPREM) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID=AGENTID and POLICYSTATUS IN (6, 36, 43, 44 ) and (MARKETING_REG = "
					+ markReg + " ) and (NO_AGENT_FLAG is null)";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPrem;
			}

			if (rst.next()) {

				totPrem = rst.getDouble(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPrem;
		} // return the connection to the pool

		return totPrem;
	}

	public Integer findTotalAdvByMarkReg(Integer markReg) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totAdv = 0;
		if (connection == null) {
			return totAdv;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT AGENTID FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE (POLICYSTATUS IN (6, 36, 43, 44 )) and (MARKETING_REG = "
					+ markReg + " ) and (NO_AGENT_FLAG is null) GROUP BY AGENTID ";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return totAdv;
			}

			while (rst.next()) {
				totAdv = totAdv + 1;
			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totAdv;
		} // return the connection to the pool

		return totAdv;
	}

	public Integer findTotalPolsByMarkReg(Integer markReg) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totPol = 0;
		if (connection == null) {
			return totPol;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select TOTALPOLICE              " + "   from MARKETING_INFORCE_TOTALS  "
					+ "  WHERE MARKETING=" + markReg;
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return totPol;
			}

			if (rst.next()) {

				totPol = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPol;
		} // return the connection to the pool

		return totPol;
	}

	public double findTotalPrem() {

		ResultSet rst = null;

		Connection connection = getConnection();

		double totPrem = 0;
		if (connection == null) {
			return totPrem;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select sum(ANNPREM) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID=AGENTID and POLICYSTATUS IN (6, 36, 43, 44 ) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPrem;
			}

			if (rst.next()) {

				totPrem = rst.getDouble(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPrem;
		} // return the connection to the pool

		return totPrem;
	}

	public Integer findTotalAdv() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totAdv = 0;
		if (connection == null) {
			return totAdv;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT AGENTID FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE POLICYSTATUS IN (6, 36, 43, 44 ) GROUP BY AGENTID  ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totAdv;
			}

			while (rst.next()) {
				totAdv = totAdv + 1;
			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totAdv;
		} // return the connection to the pool

		return totAdv;
	}

	public Integer findTotalPols() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totPol = 0;
		if (connection == null) {
			return totPol;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select SUM(TOTALPOLICE)         " + "   from MARKETING_INFORCE_TOTALS ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPol;
			}

			if (rst.next()) {

				totPol = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPol;
		} // return the connection to the pool

		return totPol;
	}
	////////////// New code

	public Integer findTotPaidToDate() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totPaid = 0;
		if (connection == null) {
			return totPaid;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='6' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPaid;
			}

			if (rst.next()) {

				totPaid = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPaid;
		} // return the connection to the pool

		return totPaid;
	}

	public Integer findTotMissedPremium() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totMissedPremium = 0;
		if (connection == null) {
			return totMissedPremium;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='36' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totMissedPremium;
			}

			if (rst.next()) {
				totMissedPremium = rst.getInt(1);
			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totMissedPremium;
		} // return the connection to the pool

		return totMissedPremium;
	}

	public Integer findTotLapseTime() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totLapseTime = 0;
		if (connection == null) {
			return totLapseTime;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='43' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapseTime;
			}

			if (rst.next()) {

				totLapseTime = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapseTime;
		} // return the connection to the pool

		return totLapseTime;
	}

	public Integer findTotLapsePending() {

		ResultSet rst = null;
		Connection connection = getConnection();

		Integer totLapsePending = 0;
		if (connection == null) {
			return totLapsePending;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='44'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapsePending;
			}

			if (rst.next()) {
				totLapsePending = rst.getInt(1);
			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapsePending;
		} // return the connection to the pool

		return totLapsePending;
	}

	// by markReg
	public Integer findTotPaidToDateByMarkReg(Integer markReg) {

		ResultSet rst = null;
		Connection connection = getConnection();

		Integer totPaid = 0;
		if (connection == null) {
			return totPaid;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='6' and (MARKETING_REG = "
					+ markReg + " ) and (NO_AGENT_FLAG is null) ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPaid;
			}

			if (rst.next()) {
				totPaid = rst.getInt(1);
			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPaid;
		} // return the connection to the pool

		return totPaid;
	}

	public Integer findTotMissedPremiumByMarkReg(Integer markReg) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totMissedPremium = 0;
		if (connection == null) {
			return totMissedPremium;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='36' and (MARKETING_REG = "
					+ markReg + " ) and (NO_AGENT_FLAG is null) ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totMissedPremium;
			}

			if (rst.next()) {

				totMissedPremium = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totMissedPremium;
		} // return the connection to the pool

		return totMissedPremium;
	}

	public Integer findTotLapseTimeByMarkReg(Integer markReg) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totLapseTime = 0;
		if (connection == null) {
			return totLapseTime;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='43' and (MARKETING_REG = "
					+ markReg + " ) and (NO_AGENT_FLAG is null) ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapseTime;
			}

			if (rst.next()) {

				totLapseTime = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapseTime;
		} // return the connection to the pool

		return totLapseTime;
	}

	public Integer findTotLapsePendingByMarkReg(Integer markReg) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totLapsePending = 0;

		if (connection == null) {
			return totLapsePending;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='44' and (MARKETING_REG = "
					+ markReg + " ) and (NO_AGENT_FLAG is null) ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapsePending;
			}

			if (rst.next()) {

				totLapsePending = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapsePending;
		} // return the connection to the pool

		return totLapsePending;
	}

	//////// by branch
	public Integer findTotPaidToDateByBranch(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totPaid = 0;
		if (connection == null) {
			return totPaid;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='6' and (BRANCH= "
					+ branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPaid;
			}

			if (rst.next()) {
				totPaid = rst.getInt(1);
			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPaid;
		} // return the connection to the pool

		return totPaid;
	}

	public Integer findTotMissedPremiumByBranch(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totMissedPremium = 0;
		if (connection == null) {
			return totMissedPremium;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='36' and (BRANCH= "
					+ branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null) ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totMissedPremium;
			}

			if (rst.next()) {
				totMissedPremium = rst.getInt(1);
			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totMissedPremium;
		} // return the connection to the pool

		return totMissedPremium;
	}

	public Integer findTotLapseTimeByBranch(Branch branch) {

		ResultSet rst = null;
		Connection connection = getConnection();

		Integer totLapseTime = 0;
		if (connection == null) {
			return totLapseTime;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='43' and (BRANCH= "
					+ branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapseTime;
			}

			if (rst.next()) {

				totLapseTime = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapseTime;
		} // return the connection to the pool

		return totLapseTime;
	}

	public Integer findTotLapsePendingByBranch(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totLapsePending = 0;
		if (connection == null) {
			return totLapsePending;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='44' and (BRANCH= "
					+ branch.getBranchIntId() + " ) and (NO_AGENT_FLAG is null) ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapsePending;
			}

			if (rst.next()) {
				totLapsePending = rst.getInt(1);
			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapsePending;
		} // return the connection to the pool

		return totLapsePending;
	}

	public List<SunTerminated> findAdvisorsByBranchPaidToDate(Branch branch) {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='6'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='6'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCodePaidToDate(Integer markRegCode) {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();

		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='6'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX " + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='6'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesPaidToDate() {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='6'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = "  SELECT * FROM SUN_ALL_POLICY_TABLEX WHERE POLICYSTATUS='6'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm2 = new SunTerminated();

				sunTerm2.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm2.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm2.setSunAdvNo("n/a");
				}

				sunTerm2.setAgentFirstName(rst2.getString(3));
				sunTerm2.setAgentLastName(rst2.getString(4));
				sunTerm2.setSunTermDate(rst2.getDate(5));

				sunTerm2.setTotalPolicies(rst2.getInt(6));
				sunTerm2.setTotalClients(rst2.getInt(7));
				sunTerm2.setTotalPremium(rst2.getDouble(8));

				sunTerm2.setBranchNameEn(rst2.getString(11));
				sunTerm2.setBranchNameFr(rst2.getString(12));
				sunTerm2.setMarketNameEn(rst2.getString(13));
				sunTerm2.setMarketNameFr(rst2.getString(14));
				sunTerm2.setFlag("X");

				terminatedSunList.add(sunTerm2);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	////////////////////////
	public List<SunTerminated> findAdvisorsByBranchLapseTime(Branch branch) {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='43'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='43'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCodeLapseTime(Integer markRegCode) {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='43'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='43'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesLapseTime() {

		ResultSet rst = null;
		ResultSet rst2 = null;
		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='43'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX WHERE POLICYSTATUS='43'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	/////////////////
	public List<SunTerminated> findAdvisorsByBranchLapsePending(Branch branch) {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='44'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='44'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCodeLapsePending(Integer markRegCode) {

		ResultSet rst = null;
		ResultSet rst2 = null;
		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='44'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='44'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesLapsePending() {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='44'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX WHERE POLICYSTATUS='44'";
			rst2 = ptStmt.executeQuery(sql2);
			// add counter

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAdvisorsByBranchMissedPremium(Branch branch) {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='36'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='36'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCodeMissedPremium(Integer markRegCode) {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='36'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='36'";
			rst2 = ptStmt.executeQuery(sql2);

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesMissedPremium() {

		ResultSet rst = null;
		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return terminatedSunList;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='36'";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX WHERE POLICYSTATUS='36'";
			rst2 = ptStmt.executeQuery(sql2);
			// add counter - int

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	// ********************************* added selected radio button
	// **************************************************
	//// whit no x
	public Integer findTotalAdvNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totAdv = 0;
		if (connection == null) {
			return totAdv;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT AGENTID FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE POLICYSTATUS IN (6, 36, 43, 44 ) and NO_AGENT_FLAG is null GROUP BY AGENTID  ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totAdv;
			}

			while (rst.next()) {
				totAdv = totAdv + 1;
			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totAdv;
		} // return the connection to the pool

		return totAdv;
	}

	public double findTotalPremNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		double totPrem = 0;
		if (connection == null) {
			return totPrem;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select sum(ANNPREM) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID=AGENTID and POLICYSTATUS IN (6, 36, 43, 44 ) and NO_AGENT_FLAG is null ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPrem;
			}

			if (rst.next()) {
				totPrem = rst.getDouble(1);
			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPrem;
		} // return the connection to the pool

		return totPrem;
	}

	public Integer findTotPaidToDateNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totPaid = 0;
		if (connection == null) {
			return totPaid;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='6' and NO_AGENT_FLAG is null ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPaid;
			}

			if (rst.next()) {

				totPaid = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPaid;
		} // return the connection to the pool

		return totPaid;
	}

	public Integer findTotMissedPremiumNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totMissedPremium = 0;
		if (connection == null) {
			return totMissedPremium;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='36' and NO_AGENT_FLAG is null ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totMissedPremium;
			}

			if (rst.next()) {
				totMissedPremium = rst.getInt(1);
			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totMissedPremium;
		} // return the connection to the pool

		return totMissedPremium;
	}

	public Integer findTotLapseTimeNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totLapseTime = 0;
		if (connection == null) {
			return totLapseTime;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='43' and NO_AGENT_FLAG is null ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapseTime;
			}

			if (rst.next()) {

				totLapseTime = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapseTime;
		} // return the connection to the pool

		return totLapseTime;
	}

	public Integer findTotLapsePendingNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totLapsePending = 0;
		if (connection == null) {
			return totLapsePending;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='44' and NO_AGENT_FLAG is null";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapsePending;
			}

			if (rst.next()) {

				totLapsePending = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapsePending;
		} // return the connection to the pool

		return totLapsePending;
	}

	/// whit x
	public Integer findTotalAdvX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totAdv = 0;
		if (connection == null) {
			return totAdv;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT AGENTID FROM SUN_ALL_POLICY_WITH_ADVISORT WHERE POLICYSTATUS IN (6, 36, 43, 44 ) and NO_AGENT_FLAG = 'X' GROUP BY AGENTID  ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totAdv;
			}

			while (rst.next()) {
				totAdv = totAdv + 1;
			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totAdv;
		} // return the connection to the pool

		return totAdv;
	}

	public double findTotalPremX() {

		ResultSet rst = null;
		Connection connection = getConnection();

		double totPrem = 0;
		if (connection == null) {
			return totPrem;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select sum(ANNPREM) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID=AGENTID and POLICYSTATUS IN (6, 36, 43, 44 ) and NO_AGENT_FLAG = 'X' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPrem;
			}

			if (rst.next()) {
				totPrem = rst.getDouble(1);
			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPrem;
		} // return the connection to the pool

		return totPrem;
	}

	public Integer findTotPaidToDateX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totPaid = 0;
		if (connection == null) {
			return totPaid;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='6' and NO_AGENT_FLAG = 'X' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totPaid;
			}

			if (rst.next()) {

				totPaid = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totPaid;
		} // return the connection to the pool

		return totPaid;
	}

	public Integer findTotMissedPremiumX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totMissedPremium = 0;
		if (connection == null) {
			return totMissedPremium;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='36' and NO_AGENT_FLAG = 'X' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totMissedPremium;
			}

			if (rst.next()) {

				totMissedPremium = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totMissedPremium;
		} // return the connection to the pool

		return totMissedPremium;
	}

	public Integer findTotLapseTimeX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totLapseTime = 0;
		if (connection == null) {
			return totLapseTime;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='43' and NO_AGENT_FLAG = 'X' ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapseTime;
			}

			if (rst.next()) {

				totLapseTime = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapseTime;
		} // return the connection to the pool

		return totLapseTime;
	}

	public Integer findTotLapsePendingX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		Integer totLapsePending = 0;
		if (connection == null) {
			return totLapsePending;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select count(*) from SUN_ALL_POLICY_WITH_ADVISORT where AGENTID = AGENTID and POLICYSTATUS='44' and NO_AGENT_FLAG = 'X'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return totLapsePending;
			}

			if (rst.next()) {

				totLapsePending = rst.getInt(1);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return totLapsePending;
		} // return the connection to the pool

		return totLapsePending;
	}

	/// added the on click filter , using the string 1 or 2 (update the init for
	/// show all and the onclick action)
	//// no X cases
	public List<SunTerminated> findInforceNoXCases() {

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return new ArrayList<>();
		}
		List<SunTerminated> terminatedSunList = new ArrayList<>();
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesPaidToDateNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='6'";
			rst = ptStmt.executeQuery(sql);
			// add counter - int
			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();

			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCodePaidToDateNoX(Integer markRegCode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='6'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAdvisorsByBranchPaidToDateNoX(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='6'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCodeLapseTimeNoX(Integer markRegCode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='43'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesLapseTimeNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='43'";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAdvisorsByBranchLapseTimeNoX(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='43'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCodeLapsePendingNoX(Integer markRegCode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='44'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesLapsePendingNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='44'";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAdvisorsByBranchLapsePendingNoX(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='44'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findByMarkRegCodeMissedPremiumNoX(Integer markRegCode) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE MARKETING_REG=" + markRegCode
					+ " and POLICYSTATUS='36'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesMissedPremiumNoX() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE WHERE POLICYSTATUS='36'";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAdvisorsByBranchMissedPremiumNoX(Branch branch) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='36'";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	/// X cases
	public List<SunTerminated> findInforceXCases() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLEX";
			rst = ptStmt.executeQuery(sql);
			// add counter - int

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesPaidToDateX() {

		ResultSet rst2 = null;
		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql2 = "  SELECT * FROM SUN_ALL_POLICY_TABLEX WHERE POLICYSTATUS='6'";
			rst2 = ptStmt.executeQuery(sql2);
			// add counter - int

			if (rst2 == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst2.next()) {

				SunTerminated sunTerm2 = new SunTerminated();

				sunTerm2.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm2.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm2.setSunAdvNo("n/a");
				}

				sunTerm2.setAgentFirstName(rst2.getString(3));
				sunTerm2.setAgentLastName(rst2.getString(4));
				sunTerm2.setSunTermDate(rst2.getDate(5));

				sunTerm2.setTotalPolicies(rst2.getInt(6));
				sunTerm2.setTotalClients(rst2.getInt(7));
				sunTerm2.setTotalPremium(rst2.getDouble(8));

				sunTerm2.setBranchNameEn(rst2.getString(11));
				sunTerm2.setBranchNameFr(rst2.getString(12));
				sunTerm2.setMarketNameEn(rst2.getString(13));
				sunTerm2.setMarketNameFr(rst2.getString(14));
				sunTerm2.setFlag("X");

				terminatedSunList.add(sunTerm2);

			}

			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findAdvisorsByBranchLapseTimeX(Branch branch) {

		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX" + "  WHERE BRANCH=" + branch.getBranchIntId()
					+ " and POLICYSTATUS='43'";
			rst2 = ptStmt.executeQuery(sql2);

			if (rst2 == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}

			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesLapseTimeX() {

		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX WHERE POLICYSTATUS='43'";
			rst2 = ptStmt.executeQuery(sql2);
			// add counter - int

			if (rst2 == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}

			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesLapsePendingX() {

		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX WHERE POLICYSTATUS='44'";
			rst2 = ptStmt.executeQuery(sql2);
			// add counter - int

			if (rst2 == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}

			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCasesMissedPremiumX() {

		ResultSet rst2 = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql2 = " SELECT * FROM SUN_ALL_POLICY_TABLEX WHERE POLICYSTATUS='36'";
			rst2 = ptStmt.executeQuery(sql2);
			// add counter - int

			if (rst2 == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst2.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst2.getInt(1));

				if (rst2.getString(2) != null) {
					sunTerm.setSunAdvNo(rst2.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst2.getString(3));
				sunTerm.setAgentLastName(rst2.getString(4));
				sunTerm.setSunTermDate(rst2.getDate(5));

				sunTerm.setTotalPolicies(rst2.getInt(6));
				sunTerm.setTotalClients(rst2.getInt(7));
				sunTerm.setTotalPremium(rst2.getDouble(8));

				sunTerm.setBranchNameEn(rst2.getString(11));
				sunTerm.setBranchNameFr(rst2.getString(12));
				sunTerm.setMarketNameEn(rst2.getString(13));
				sunTerm.setMarketNameFr(rst2.getString(14));
				sunTerm.setFlag("X");

				terminatedSunList.add(sunTerm);

			}
			rst2.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findInforceCases2() {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT * FROM SUN_ALL_POLICY_TABLE";
			rst = ptStmt.executeQuery(sql);

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setAgentID(rst.getInt(1));

				if (rst.getString(2) != null) {
					sunTerm.setSunAdvNo(rst.getString(2));
				} else {
					sunTerm.setSunAdvNo("n/a");
				}

				sunTerm.setAgentFirstName(rst.getString(3));
				sunTerm.setAgentLastName(rst.getString(4));
				sunTerm.setSunTermDate(rst.getDate(5));

				sunTerm.setTotalPolicies(rst.getInt(6));
				sunTerm.setTotalClients(rst.getInt(7));
				sunTerm.setTotalPremium(rst.getDouble(8));

				sunTerm.setBranchNameEn(rst.getString(11));
				sunTerm.setBranchNameFr(rst.getString(12));
				sunTerm.setMarketNameEn(rst.getString(13));
				sunTerm.setMarketNameFr(rst.getString(14));
				sunTerm.setFlag(null);

				terminatedSunList.add(sunTerm);

			}

			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	//// show details of the advisor
	public List<SunTerminated> findTermAdvisorDetailsX(int agentID, String policyStatus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select FIRSTNAME, LASTNAME,       " + "        NAMEENGLISH, NAMEFRENCH,   "
					+ "        ISSUEDATE, STATUSDATE,     " + "        FACEAMT,                   " + // 7
					"        ANNPREM, POLICYNUMBER,     " + "        ENGLISH_NAME, FRENCH_NAME, "
					+ "        EXPIRYDATE,                " + // 12
					"        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)) " + // 13
					"   from SUN_ALL_POLICY_WITH_ADVISORT " + "  where AGENTID=" + agentID + " and POLICYSTATUS in ( "
					+ policyStatus + " ) and NO_AGENT_FLAG = 'X' " + "  ORDER BY 2,1 ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setClientFirstName(rst.getString(1));
				sunTerm.setClientLastName(rst.getString(2));

				sunTerm.setCompNameEn(rst.getString(3));
				sunTerm.setCompNameFr(rst.getString(4));

				sunTerm.setIssDate(rst.getDate(5));

				sunTerm.setFaceAmnt(rst.getDouble(7));
				sunTerm.setAnnPrem(rst.getDouble(8));

				sunTerm.setPolNum(rst.getString(9));

				sunTerm.setProdNameEn(rst.getString(10));
				sunTerm.setProdNameFr(rst.getString(11));

				sunTerm.setExpiryDate(rst.getDate(12));
				sunTerm.setExpiryMonths(rst.getInt(13));

				sunTerm.setPaidToDate(rst.getDate(6));

				terminatedSunList.add(sunTerm);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

	public List<SunTerminated> findTermAdvisorDetailsNoX(int agentID, String policyStatus) {

		ResultSet rst = null;

		Connection connection = getConnection();

		List<SunTerminated> terminatedSunList = new ArrayList<>();
		if (connection == null) {
			return new ArrayList<>();
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select FIRSTNAME, LASTNAME,       " + "        NAMEENGLISH, NAMEFRENCH,   "
					+ "        ISSUEDATE, STATUSDATE,     " + "        FACEAMT,                   " + // 7
					"        ANNPREM, POLICYNUMBER,     " + "        ENGLISH_NAME, FRENCH_NAME, "
					+ "        EXPIRYDATE,                " + // 12
					"        ROUND(MONTHS_BETWEEN (EXPIRYDATE, SYSDATE)) " + // 13
					"   from SUN_ALL_POLICY_WITH_ADVISORT " + "  where AGENTID=" + agentID + " and POLICYSTATUS in ( "
					+ policyStatus + " ) and NO_AGENT_FLAG is null " + "  ORDER BY 2,1 ";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return terminatedSunList;
			}

			while (rst.next()) {

				SunTerminated sunTerm = new SunTerminated();

				sunTerm.setClientFirstName(rst.getString(1));
				sunTerm.setClientLastName(rst.getString(2));

				sunTerm.setCompNameEn(rst.getString(3));
				sunTerm.setCompNameFr(rst.getString(4));

				sunTerm.setIssDate(rst.getDate(5));

				sunTerm.setFaceAmnt(rst.getDouble(7));
				sunTerm.setAnnPrem(rst.getDouble(8));

				sunTerm.setPolNum(rst.getString(9));

				sunTerm.setProdNameEn(rst.getString(10));
				sunTerm.setProdNameFr(rst.getString(11));

				sunTerm.setExpiryDate(rst.getDate(12));
				sunTerm.setExpiryMonths(rst.getInt(13));

				sunTerm.setPaidToDate(rst.getDate(6));

				terminatedSunList.add(sunTerm);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return terminatedSunList;
		} // return the connection to the pool

		return terminatedSunList;
	}

}
