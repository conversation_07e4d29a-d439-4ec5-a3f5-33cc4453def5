/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdBenchmark; 
import com.insurfact.fundata.entity.FdQuartiles;  
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdBenchmarkSessionBean extends AbstractFacade<FdBenchmark> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdBenchmarkSessionBean() {
        super(FdBenchmark.class);
    }
    
    /**
     * 
     * @param assocCanIndexId usually from the FdQuartile table
     * @return FdBenchmark or null
     */
    public FdBenchmark getByAssocCanIndexId(Long assocCanIndexId){
    	
    	TypedQuery<FdBenchmark> nq = em.createQuery(
				"SELECT a FROM FdBenchmark a WHERE  a.assocCanIndexId = :assocCanIndexId", FdBenchmark.class);
		nq.setParameter("assocCanIndexId", assocCanIndexId);
		return nq.getSingleResult();
        
        /*FdBenchmark benchmark = null;

        CriteriaBuilder cBuilder = em.getCriteriaBuilder();
        CriteriaQuery<FdBenchmark> criteriaQuery = cBuilder.createQuery(FdBenchmark.class);
        Root<FdBenchmark> p = criteriaQuery.from(FdBenchmark.class);
        
        Predicate codePredicate = cBuilder.equal(p.get(FdBenchmark_.assocCanIndexId), assocCanIndexId);
        criteriaQuery.where(codePredicate);

        TypedQuery<FdBenchmark> typeCodeQuery = em.createQuery(criteriaQuery);

        List<FdBenchmark> results = typeCodeQuery.getResultList();
        if (results != null && !results.isEmpty()) {
            benchmark = results.get(0);
        }

        return benchmark;*/
        
    }
    
    /**
     * FdQuartiles must be set before calling this method.
     * 
     * @param fundatakey 
     * @return FdBenchmark or null
     */
    public FdBenchmark getByFundataKey(Long fundatakey) {
    	FdQuartiles quartile = null;
    	
    	TypedQuery<FdQuartiles> nq = em.createQuery(
				"SELECT a FROM FdQuartiles a WHERE  a.fundatakey = :fundatakey", FdQuartiles.class);
		nq.setParameter("fundatakey", fundatakey);
		quartile = nq.getSingleResult();

		if (quartile == null)
			return null;

		return getByAssocCanIndexId(quartile.getAssocCanIndexKeyRaw());
        
        /*FdQuartiles quartile = null;

        CriteriaBuilder cBuilder = em.getCriteriaBuilder();
        CriteriaQuery<FdQuartiles> criteriaQuery = cBuilder.createQuery(FdQuartiles.class);
        Root<FdQuartiles> p = criteriaQuery.from(FdQuartiles.class);

        Predicate codePredicate = cBuilder.equal(p.get(FdQuartiles_.fundatakey), fundatakey);
        criteriaQuery.where(codePredicate);

        TypedQuery<FdQuartiles> typeCodeQuery = em.createQuery(criteriaQuery);

        List<FdQuartiles> results = typeCodeQuery.getResultList();
        if (results != null && !results.isEmpty()) {
            quartile = results.get(0);
        }
       
        if(quartile==null)return null;
       
        //FdBenchmark benchmark = quartile.getAssocCanIndexKey();
        
        long key = quartile.getAssocCanIndexKeyRaw();
        
        FdBenchmark benchmark = getByAssocCanIndexId(key);
        
        return benchmark;*/
    }
}
