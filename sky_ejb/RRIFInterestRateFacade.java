/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.cannex.entity.RRIFInterestRate;
import com.insurfact.cannex.entity.RrifProduct;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class RRIFInterestRateFacade extends AbstractFacade<RRIFInterestRate> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public RRIFInterestRateFacade() {
        super(RRIFInterestRate.class);
    }
    
        public List<RRIFInterestRate> findRrifRates(RrifProduct product, Integer termTypeId){
        
        TypedQuery<RRIFInterestRate> query = em.createQuery("SELECT r FROM RRIFInterestRate r WHERE r.rrifProduct = :product AND r.rrifProduct.termTypeId = :duration  ORDER BY r.interestRateValue ASC",RRIFInterestRate.class);
        query.setParameter("product", product);
        query.setParameter("duration", termTypeId);
        
        return query.getResultList();
    }
        
    
    public void deleteRates(RrifProduct termProduct,Integer termTypeId){
         List<RRIFInterestRate> rates = findRrifRates(termProduct, termTypeId);
         
         if(rates != null && !rates.isEmpty()) {
             
             for(RRIFInterestRate rate : rates){
                 em.remove(rate);
             }
         }
    }
              
}
