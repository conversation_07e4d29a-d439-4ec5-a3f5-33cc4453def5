/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Account;
import com.insurfact.skynet.entity.Client; 
import com.insurfact.skynet.entity.PriceDistribution;
import com.insurfact.skynet.entity.SegFund;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class PriceDistributionSessionBean extends AbstractFacade<PriceDistribution> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public PriceDistributionSessionBean() {
		super(PriceDistribution.class);
	}

	public PriceDistribution getDailyPrice(String recordType, String mgmtCode, String fundId) {
		EntityManager em = getEntityManager();
		PriceDistribution price = null;
		try {
			TypedQuery<PriceDistribution> nq = em.createNamedQuery("PriceDistribution.findLatestPrice",
					PriceDistribution.class);
			nq.setParameter("recordType", recordType);
			nq.setParameter("mgmtCompanyCode", mgmtCode);
			nq.setParameter("fundId", fundId);

			List<PriceDistribution> dailies = nq.getResultList();

			// deal with no price found...
			if ((dailies == null) || (dailies.size() == 0)) {
				System.out.println("********* Error, cannot find Price detail for :" + mgmtCode + "-" + fundId);
				return null;
			}

			// retreive the first one... the latest
			price = dailies.get(0);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return price;
	}

	public Account calculateAccountValue(Account account) {

		double totalFundsValue = 0.0;

		List<SegFund> tmpFunds = account.getSegFundList();

		totalFundsValue = 0.0;
		for (SegFund fund : tmpFunds) {
			// update all SegFund market values with today's prices
			fetchDailyPrice(fund);

			totalFundsValue += fund.getMarketValue();

		}

		account.setMarketValue(totalFundsValue);

		return account;
	}

	/**
	 * calculate totals and market values of accounts and total asset value
	 * 
	 */
	public double calculateAssets(Client client) {

		if (client == null)
			return 0.0f;

		double totalAssetsValue = 0.0;
		double totalFundsValue = 0.0;

		// update all SegFund market values with today's prices
		updateDailyPrices(client);

		List<Account> tmpAccounts = client.getAllAccounts();

//        tmpAccounts = selectedClient.getAccountList();
		if (tmpAccounts != null) {
			for (Account account : tmpAccounts) {
				List<SegFund> tmpFunds = account.getSegFundList();

				totalFundsValue = 0.0;
				for (SegFund fund : tmpFunds) {

					Double marketValue = fund.getMarketValue();
					if (marketValue != null) {
						totalFundsValue += marketValue;
					}
				}

				account.setMarketValue(totalFundsValue);
				totalAssetsValue += totalFundsValue;

			}
		}

		System.out.println("Market value : " + totalAssetsValue);

		return totalAssetsValue;

	}

	/**
	 * fetch daily prices for all funds of all accounts under the selectedClient
	 * 
	 */
	private void updateDailyPrices(Client client) {

		if (client == null) {
			return;
		}

		List<Account> tmpAccounts = client.getAllAccounts();

		if (tmpAccounts != null) {
			for (Account account : tmpAccounts) {
				List<SegFund> tmpFunds = account.getSegFundList();

				for (SegFund fund : tmpFunds) {

					// fetch price
					fetchDailyPrice(fund);
				}
			}
		}
	}

	/**
	 * get latest daily price
	 * 
	 * @param fund
	 */
	private void fetchDailyPrice(SegFund fund) {

		String mgmt = fund.getMgmtCompanyCode();
		String fundId = fund.getFundId();

//        System.out.println("getting price for:"+mgmt+"-"+fundId);

		PriceDistribution price = getDailyPrice("PRI", mgmt, fundId);

		if (price == null) {
			fund.setUnitPrice(0.0d);
			System.out.println("Unable to find price for: " + fund.getMgmtCompanyCode() + fund.getFundId());
		}

		fund.setUnitPrice(price.getPriceRate().doubleValue());
	}

	public List<PriceDistribution> findPricesForFund(SegFund fund) {

		List<PriceDistribution> result = new ArrayList<PriceDistribution>();

		if (fund != null) {

			result = findPricesForFund(fund.getMgmtCompanyCode(), fund.getFundId());

		}

		return result;
	}

	public List<PriceDistribution> findPricesForFund(String mgmtCompany, String fundId) {

		TypedQuery<PriceDistribution> nq = em.createQuery(
				"SELECT a FROM PriceDistribution a WHERE   a.recordType = 'PRI' and a.mgmtCompanyCode = :mgmtCompanyCode and a.fundId = :fundId",
				PriceDistribution.class);
		nq.setParameter("mgmtCompanyCode", mgmtCompany);
		nq.setParameter("fundId", fundId);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * CriteriaQuery<PriceDistribution> criteriaQuery =
		 * cBuilder.createQuery(PriceDistribution.class); Root<PriceDistribution> p =
		 * criteriaQuery.from(PriceDistribution.class);
		 * 
		 * Predicate recTypePredicate =
		 * cBuilder.equal(p.get(PriceDistribution_.recordType), "PRI"); Predicate
		 * companyCodePredicate =
		 * cBuilder.equal(p.get(PriceDistribution_.mgmtCompanyCode), mgmtCompany);
		 * Predicate fundIdPredicate = cBuilder.equal(p.get(PriceDistribution_.fundId),
		 * fundId);
		 * 
		 * Order order = new OrderImpl(p.get(PriceDistribution_.priceRecordDate), true);
		 * criteriaQuery.where(cBuilder.and(recTypePredicate, companyCodePredicate,
		 * fundIdPredicate)).orderBy(order); TypedQuery<PriceDistribution> pricesQuery =
		 * em.createQuery(criteriaQuery);
		 * 
		 * return pricesQuery.getResultList();
		 */
	}
}
