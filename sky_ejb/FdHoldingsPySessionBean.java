/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdHoldingsPy;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdHoldingsPySessionBean extends AbstractFacade<FdHoldingsPy> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdHoldingsPySessionBean() {
		super(FdHoldingsPy.class);
	}

	@SuppressWarnings("finally")
	public List<FdHoldingsPy> getByFundataKey(Long fundatakey) {
		List<FdHoldingsPy> holdings = new ArrayList<>();

		EntityManager em = getEntityManager();

		try {
			TypedQuery<FdHoldingsPy> nq = em.createNamedQuery("FdHoldingsPy.findByHoldingFundatakey",
					FdHoldingsPy.class);
			nq.setParameter("holdingFundatakey", fundatakey);

			holdings = nq.getResultList();
		} catch (NoResultException e) {
			//
		} finally {
			return holdings;
		}
	}

	public List<FdHoldingsPy> findByHoldingIdName(Integer securityId, String holdingName, boolean market,
			boolean asset) {

		String sql = "";

		if (securityId != null)
			sql += "SELECT h FROM FdHoldingsPy h WHERE h.securityNameId = :holdingNameId ";
		else
			sql += "SELECT h FROM FdHoldingsPy h WHERE h.holdingSecurityName = :holdingName ";

		if (!market)
			sql += " AND (h.holdingMarketPercent IS NOT NULL)";

		if (!asset)
			sql += " AND (h.fdFund.fundTotalAssets IS NOT NULL)";

		sql += " ORDER BY h.holdingSecurityName ASC";

		TypedQuery<FdHoldingsPy> nq = em.createQuery(sql, FdHoldingsPy.class);

		if (securityId != null)
			nq.setParameter("holdingNameId", securityId);
		else
			nq.setParameter("holdingName", holdingName);

		return nq.getResultList();
	}
}
