/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.MasterGroup;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class MasterGroupFacade extends AbstractFacade<MasterGroup> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public MasterGroupFacade() {
		super(MasterGroup.class);
	}

	public List<MasterGroup> findMasterGroupByCode(String code) {

		TypedQuery<MasterGroup> nq = em.createQuery(
				"SELECT a FROM MasterGroup a WHERE   a.masterGroupCode = :masterGroupCode", MasterGroup.class);
		nq.setParameter("masterGroupCode", code);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * CriteriaQuery<MasterGroup> criteriaQuery =
		 * cBuilder.createQuery(MasterGroup.class); Root<MasterGroup> p =
		 * criteriaQuery.from(MasterGroup.class);
		 * 
		 * Predicate codePredicate = cBuilder.equal(p.get(MasterGroup_.masterGroupCode),
		 * code);
		 * 
		 * criteriaQuery.where(codePredicate); TypedQuery<MasterGroup> codeQuery =
		 * em.createQuery(criteriaQuery);
		 * 
		 * return codeQuery.getResultList();
		 */
	}

	@SuppressWarnings("unchecked")
	public List<String> findMasterGroupNotAdvisor() {

		Query q = em.createNativeQuery(
				"SELECT MASTER_GROUP_CODE FROM MASTER_GROUP where MASTER_GROUP_CODE not like 'ADV%' and MASTER_GROUP_CODE not like '0%' and MASTER_GROUP_CODE not like '1%' and MASTER_GROUP_CODE not like '2%' and MASTER_GROUP_CODE not like '3%' and MASTER_GROUP_CODE not like '4%' and MASTER_GROUP_CODE not like '5%' and MASTER_GROUP_CODE not like '6%' and MASTER_GROUP_CODE not like '7%' and MASTER_GROUP_CODE not like '8%' and MASTER_GROUP_CODE not like '9%' ORDER BY CREATION_DATE DESC");
		List<String> result = new ArrayList<>();
		for (Object o : q.getResultList()) {
			result.add(o.toString());
		}
		return q.getResultList();
	}

	public MasterGroup getMasterGroupByCode(String code) {

		try {
			Query nq = getEntityManager().createNamedQuery("MasterGroup.getByMasterGroupCode", MasterGroup.class);
			nq.setParameter("masterGroupCode", code);

			return (MasterGroup) nq.getSingleResult();
		} catch (NoResultException e) {
//            System.out.println("No MasterGroup Found for : " + code );
			return null;
		}
	}

	public String getNextMasterGroup() {
		Query q = em.createNativeQuery(
				"SELECT MASTER_GROUP_CODE FROM MASTER_GROUP where MASTER_GROUP_CODE like 'MGA-%' ORDER BY CREATION_DATE DESC");
		String result = q.getResultList().get(0).toString();
		return result;
	}

}
