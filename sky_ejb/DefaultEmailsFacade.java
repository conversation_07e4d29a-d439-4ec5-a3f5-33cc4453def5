/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2012 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.DefaultEmails;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class DefaultEmailsFacade extends AbstractFacade<DefaultEmails> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public DefaultEmailsFacade() {
		super(DefaultEmails.class);
	}

	public DefaultEmails findByType(Integer type) {

		TypedQuery<DefaultEmails> query = em.createNamedQuery("DefaultEmails.findByType", DefaultEmails.class);
		query.setParameter("type", type);

		List<DefaultEmails> contents = query.getResultList();

		if (contents != null && !contents.isEmpty())
			return contents.get(0);

		return null;
	}

	public List<DefaultEmails> findByTypeList(Integer type) {

		TypedQuery<DefaultEmails> query = em.createNamedQuery("DefaultEmails.findByType", DefaultEmails.class);
		query.setParameter("type", type);

		List<DefaultEmails> contents = query.getResultList();

		if (contents != null && !contents.isEmpty())
			return contents;

		return null;
	}

}
