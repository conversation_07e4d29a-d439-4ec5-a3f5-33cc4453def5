/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdMastQuartile;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdMastQuartileSessionBean extends AbstractFacade<FdMastQuartile> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdMastQuartileSessionBean() {
		super(FdMastQuartile.class);
	}

	public FdMastQuartile getByFundataKey(Integer fundatakey) {

		TypedQuery<FdMastQuartile> nq = em.createQuery(
				"SELECT a FROM FdMastQuartile a WHERE  a.mastFundatakey = :fundatakey", FdMastQuartile.class);
		nq.setParameter("fundatakey", fundatakey);
		return nq.getSingleResult();

		/*
		 * FdMastQuartile quartile = null;
		 * 
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * CriteriaQuery<FdMastQuartile> criteriaQuery =
		 * cBuilder.createQuery(FdMastQuartile.class); Root<FdMastQuartile> p =
		 * criteriaQuery.from(FdMastQuartile.class);
		 * 
		 * Predicate codePredicate =
		 * cBuilder.equal(p.get(FdMastQuartile_.mastFundatakey), fundatakey);
		 * criteriaQuery.where(codePredicate);
		 * 
		 * TypedQuery<FdMastQuartile> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * List<FdMastQuartile> results = typeCodeQuery.getResultList(); if (results !=
		 * null && !results.isEmpty()) { quartile = results.get(0); }
		 * 
		 * return quartile;
		 */
	}
}
