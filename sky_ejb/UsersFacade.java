/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.Branch;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.LoginAccess;
import com.insurfact.skynet.entity.MasterGroup;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class UsersFacade extends AbstractFacade<Users> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@EJB
	private ContactFacade contactFacade;

	@EJB
	private AdvisorFacade advisorFacade;

	@EJB
	private AgencyFacade agencyFacade;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public UsersFacade() {
		super(Users.class);
	}

	public Users refreshUsersEntity(Users users) {

		users = em.find(users.getClass(), users.getUserIntId());
		return users;
	}

	public void createUserProfile(Users users) {
		em.persist(users);
		Query nq = em.createNativeQuery("insert into profile_users values (" + users.getUserIntId() + " , "
				+ users.getProfile().getProfileIntId() + " )");
		nq.executeUpdate();
	}

	public List<Users> findAllPending() {
		TypedQuery<Users> query = em.createQuery("SELECT u FROM Users u WHERE u.sentIntroEmail = 'P'", Users.class);
		return query.getResultList();
	}

	public List<Contact> findAllOnlineRegistered() {
		TypedQuery<Contact> query = em.createQuery(
				"SELECT c FROM Contact c WHERE c.users is not null and c.contactSource = 7 and c.users.sentIntroEmail = 'P'",
				Contact.class);
		return query.getResultList();
	}

	public Users findByUsername(String username) {
		try {

			Query nq = getEntityManager().createQuery("SELECT u FROM Users u WHERE u.username = :username",
					Users.class);

			nq.setParameter("username", username);

			return (Users) nq.getSingleResult();

		} catch (NoResultException e) {
			return null;
		}
	}

	public Users findByUserEmail(String email) {
		try {
			TypedQuery<Users> query = em.createQuery(
					"SELECT u FROM Users u WHERE u.contact IN (SELECT c FROM Contact c JOIN c.emailList e WHERE e.emailAddress = :email)",
					Users.class);
			query.setParameter("email", email);
			return query.getSingleResult();
		} catch (NoResultException e) {
			// Log the exception if needed
			return null;
		}
	}

	public Users findByUserID(Integer userID) {
		try {

			Query nq = getEntityManager().createQuery("SELECT u FROM Users u WHERE u.userIntId = :userIntId",
					Users.class);

			nq.setParameter("userIntId", userID);

			return (Users) nq.getSingleResult();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Users> findUsersByUsernameForced(String username) {

		username = "%" + username.toUpperCase() + "%";

		TypedQuery<Users> nq = em.createQuery("SELECT a FROM Users a WHERE   UPPER(a.username) like :username",
				Users.class);
		nq.setParameter("username", username);
		return nq.getResultList();

		/*
		 * List<Users> userList;
		 * 
		 * username = "%" + username.toUpperCase() + "%";
		 * 
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<Users>
		 * criteriaQuery = cBuilder.createQuery(Users.class); Root<Users> p =
		 * criteriaQuery.from(Users.class);
		 * 
		 * Predicate userName = cBuilder.like(cBuilder.upper(p.get(Users_.username)),
		 * username);
		 * 
		 * criteriaQuery.where(userName); TypedQuery<Users> userNameQuery =
		 * em.createQuery(criteriaQuery);
		 * 
		 * userList = userNameQuery.getResultList();
		 * 
		 * return userList;
		 */
	}

	public List<Users> findAllUsersByNames(String nameSearch) {

		String[] names = nameSearch.split(" ");

		TypedQuery<Users> query;

		if (names.length == 1) {
			String name = nameSearch.toUpperCase() + "%";
			query = em.createQuery(
					"SELECT u from Users u WHERE u.profileType in(1,4,5)  AND (UPPER(u.contact.firstname) LIKE :name OR UPPER(u.contact.lastname) LIKE :name)",
					Users.class);
			query.setParameter("name", name);

		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			query = em.createQuery(
					"SELECT u from Users u WHERE u.profileType in(1,4,5) AND UPPER(u.contact.firstname) LIKE :firstname AND UPPER(u.contact.lastname) LIKE :lastname",
					Users.class);
			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);

//             
		}

		return query.getResultList();
	}

	public List<Users> findAllForAdvisorLock(Integer id) {
		List<Users> userList = new ArrayList<>();

		try {

			TypedQuery<Users> nq = getEntityManager().createNamedQuery("Users.findAllForAdvisorLock", Users.class);

			nq.setParameter("advisorIntId", id);

			userList = nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}

		return userList;
	}

	public List<Users> findByBranch(Branch branch) {

		List<Users> userList = new ArrayList<>();

		try {

			TypedQuery<Users> nq = getEntityManager().createQuery(
					"SELECT u FROM Users u WHERE u.contact.branch = :branch ORDER BY u.contact.firstname, u.contact.lastname",
					Users.class);

			nq.setParameter("branch", branch);

			userList = nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}

		return userList;
	}

	public List<Users> findToolmaster(ProductSupplier ps) {

		List<Users> userList = new ArrayList<>();

		try {

			TypedQuery<Users> nq = getEntityManager()
					.createQuery("select us.* from users us INNER JOIN CONTACT_CONTACT_ACTION cca on cca.CONTACT_ID = "
							+ " us.user_INT_ID INNER JOIN CONTACT_ACTION ca on cca.contact_action_id = ca.contact_action_int_id "
							+ " inner join PROFILE_USERS pu on pu.USERS = us.user_int_id INNER JOIN profile p on p.profile_int_id"
							+ " = pu.profile where ca.contact_action_type = 11 and p.PRODUCT_SUPPLIER =  "
							+ ps.getProductSupplierIntId(), Users.class);

			userList = nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}

		return userList;
	}

	public List<Users> findByMarketingRegion(Integer region, Integer advisorLock) {

		List<Users> userList = new ArrayList<>();

		try {

			TypedQuery<Users> nq = getEntityManager().createQuery(
					"SELECT u FROM Users u WHERE u.contact.branch IS NOT NULL AND u.contact.branch.marketingRegion = :region AND u.advisorLock = :advisorLock ORDER BY u.contact.firstname, u.contact.lastname",
					Users.class);

			nq.setParameter("region", region);
			nq.setParameter("advisorLock", advisorLock);

			userList = nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}

		return userList;
	}

	public Users findUsersByConfirmationKey(String key) {

		Users users = null;

		TypedQuery<Users> nq = getEntityManager().createNamedQuery("Users.fingByConfirmationKey", Users.class);
		nq.setParameter("key", key);

		try {
			users = (Users) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}

		return users;
	}

	public Users findUsersByUserName(String username) {

		Users users = null;

		TypedQuery<Users> nq = getEntityManager().createNamedQuery("Users.findByUserName", Users.class);
		nq.setParameter("username", username);

		try {
			users = (Users) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}

		return users;
	}

//    public List<Users> findUsersByContact(String username) {
//        
//        List<Users> userList = new ArrayList<>();
//
//        List<Contact> contacts = contactFacade.findContactByNames(username);
//        
//        if(contacts != null && !contacts.isEmpty()){
//            for(Contact contact: contacts){
//                Users user = contact.getUsers();
//                if(user != null){
//                    userList.add(user);
//                }
//            }
//        }
//        return userList;
//    }
	public LoginAccess getFirstLogin(Users users) {

		try {

			Query nq = getEntityManager().createQuery(
					"SELECT l FROM LoginAccess l WHERE l.users = :users ORDER BY l.timestamp ASC ", LoginAccess.class);

			nq.setParameter("users", users);
			nq.setMaxResults(1);

			return (LoginAccess) nq.getSingleResult();

		} catch (NoResultException e) {
			return null;
		}

	}

	public List<LoginAccess> getAllLoginAccess(Users users) {
		List<LoginAccess> allAccess = new ArrayList<>();
		try {

			TypedQuery<LoginAccess> nq = getEntityManager().createQuery(
					"SELECT l FROM LoginAccess l WHERE l.users = :users ORDER BY l.timestamp DESC ", LoginAccess.class);

			nq.setParameter("users", users);

			allAccess = nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}

		return allAccess;

	}

	public LoginAccess getLastLogin(Users users) {

		try {

			TypedQuery<LoginAccess> nq = getEntityManager().createQuery(
					"SELECT l FROM LoginAccess l WHERE l.users = :users ORDER BY l.timestamp DESC ", LoginAccess.class);

			nq.setParameter("users", users);
			nq.setMaxResults(1);

			return (LoginAccess) nq.getSingleResult();

		} catch (NoResultException e) {
			return null;
		}

	}

	public void deleteAllCompanySelection(Users user) {

		Query query = em.createQuery("DELETE FROM CompanySelection c WHERE c.users = :users");
		query.setParameter("users", user);

		query.executeUpdate();

//        System.out.println("deleted ["+result+"] for users : "+user.getUsername());
	}

	public void deleteAllProductSelection(Users user) {

		Query query = em.createQuery("DELETE FROM ProductSelection c WHERE c.users = :users");
		query.setParameter("users", user);

		query.executeUpdate();

//        System.out.println("deleted ["+result+"] for users : "+user.getUsername());
	}

	public MasterGroup getAdvisorMasterGroup(Users user) {

		Advisor advisor = user.getContact().getAdvisor();

//        System.out.println("*** getAdvisorMasterGroup : " + advisor);
		if (advisor != null) {
			return advisor.getMasterGroup();
		} else {
			return null;
		}
	}

	public MasterGroup getAgencyMasterGroup(Users user) {

		Agency agency = getLockAgency(user);

		if (agency != null) {
			return agency.getMasterGroup();
		} else {
			return null;
		}
	}

	public Advisor getLockAdvisor(Users user) {

		Integer id = user.getAdvisorLock();

		if (id != null) {
			return advisorFacade.find(id);
		} else {
			return null;
		}
	}

	public Advisor getAdvisorById(Users user) {

		Integer id = user.getUserIntId();

		if (id != null) {
			return advisorFacade.find(id);
		} else {
			return null;
		}
	}

	public Agency getLockAgency(Users user) {

		if (user == null) {
			return null; // smasse 2018-11-27
		}
		Integer id = user.getAgencyLock();

		if (id != null) {
			return agencyFacade.find(id);
		} else {
			return null;
		}

	}

}
