/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Activity;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Company;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.ContractEft;
import com.insurfact.skynet.entity.ContractSetup;
import com.insurfact.skynet.entity.Lead;
import com.insurfact.skynet.entity.License;
import com.insurfact.skynet.entity.LicenseLiability;
import com.insurfact.skynet.entity.OnboardingStatus;
import com.insurfact.skynet.entity.Opportunity;
import com.insurfact.skynet.entity.Organization;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.StoredFile;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class StoredFileFacade extends AbstractFacade<StoredFile> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public StoredFileFacade() {
		super(StoredFile.class);
	}

	public StoredFile findStoredFile(Object o) {
		String sql = "";
		if (o instanceof Opportunity) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 1 and TYPE_ID = " + ((Opportunity) o).getOpportunityIntId();
		} else if (o instanceof Users) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 2 and TYPE_ID = " + ((Users) o).getUserIntId();
		}
		try {
			Query nq = getEntityManager().createNativeQuery(sql, StoredFile.class);
			if (nq.getMaxResults() > 0) {
				return (StoredFile) nq.getResultList().get(0);
			} else {
				return null;
			}

		} catch (NoResultException | ArrayIndexOutOfBoundsException e) {

		}

		return null;
	}

	@SuppressWarnings("unchecked")
	public List<StoredFile> findAllDocuments() {
		String sql = "";

		sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 9 ";

		try {
			Query nq = getEntityManager().createNativeQuery(sql, StoredFile.class);
			return nq.getResultList();

		} catch (NoResultException | ArrayIndexOutOfBoundsException e) {

		}

		return new ArrayList<>();
	}

	@SuppressWarnings("unchecked")
	public List<StoredFile> findAllDashboards() {
		String sql = "";

		sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 10 ";

		try {
			Query nq = getEntityManager().createNativeQuery(sql, StoredFile.class);
			return nq.getResultList();

		} catch (NoResultException | ArrayIndexOutOfBoundsException e) {

		}

		return new ArrayList<>();
	}

	@SuppressWarnings("unchecked")
	public List<StoredFile> findStoredFileAll(Object o) {
		String sql = "";
		if (o instanceof Opportunity) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 1 and TYPE_ID = " + ((Opportunity) o).getOpportunityIntId();
		} else if (o instanceof Users) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 2 and TYPE_ID = " + ((Users) o).getUserIntId();
		} else if (o instanceof Company) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 3 and TYPE_ID = " + ((Company) o).getCompanyIntId();
		} else if (o instanceof License) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 4 and TYPE_ID = " + ((License) o).getLicenseIntId();
		} else if (o instanceof LicenseLiability) { // EandO
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 5 and TYPE_ID = "
					+ ((LicenseLiability) o).getLicenseLiabilityIntId();
		} else if (o instanceof ContractEft) { // banking
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 6 and TYPE_ID = " + ((ContractEft) o).getContractEftIntId();
		} else if (o instanceof ContractSetup) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 7 and TYPE_ID = "
					+ ((ContractSetup) o).getContractSetupIntId();
		} else if (o instanceof Activity) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 8 and TYPE_ID = " + ((Activity) o).getActivityIntId();
		} else if (o instanceof ProductSupplier) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 9 and TYPE_ID = "
					+ ((ProductSupplier) o).getProductSupplierIntId();
		} else if (o instanceof Organization) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 10 and TYPE_ID = "
					+ ((Organization) o).getOrganizationIntId();
		} else if (o instanceof Advisor) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 11 and TYPE_ID = " + ((Advisor) o).getAdvisorIntId();
		} else if (o instanceof Contact) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 12 and TYPE_ID = " + ((Contact) o).getContactIntId();
		} else if (o instanceof Lead) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 13 and TYPE_ID = " + ((Lead) o).getLeadIntId();
		} else if (o instanceof OnboardingStatus) {
			sql = "SELECT * FROM STORED_FILE WHERE TYPE_ = 16 and TYPE_ID = "
					+ ((OnboardingStatus) o).getOnboardingStatusIntId();
		}
		try {
			Query nq = getEntityManager().createNativeQuery(sql, StoredFile.class);
			return nq.getResultList();

		} catch (NoResultException | ArrayIndexOutOfBoundsException e) {
			return new ArrayList<>();
		}

	}

	public void editFile(StoredFile s) {
		em.merge(s);
	}

}
