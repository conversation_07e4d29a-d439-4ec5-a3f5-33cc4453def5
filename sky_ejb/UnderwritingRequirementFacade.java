/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.TypeClass;
import com.insurfact.skynet.entity.UnderwritingRequirement;  
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class UnderwritingRequirementFacade extends AbstractFacade<UnderwritingRequirement> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public UnderwritingRequirementFacade() {
        super(UnderwritingRequirement.class);
    }

    public void persist(UnderwritingRequirement selectedRequirement) {
        
        if(selectedRequirement != null){
            Integer reqId = selectedRequirement.getUwRequirementIntId();
            
            if(reqId != null){
                if(selectedRequirement.isDeleted()){
                    selectedRequirement = em.getReference(UnderwritingRequirement.class, reqId);
                    em.remove(selectedRequirement);
                } else {
                    
                    em.merge(selectedRequirement);
                }                
            } else {
                
                if(!selectedRequirement.isDeleted()){
                    em.persist(selectedRequirement);
                }                
            }
        }
    }
    
    public UnderwritingRequirement findByValue(String value) {
        
        if (value != null) {
        	
        	TypedQuery<UnderwritingRequirement> nq = em.createQuery(
    				"SELECT a FROM UnderwritingRequirement a WHERE a.value = :value",
    				UnderwritingRequirement.class);
    		nq.setParameter("value", value);
    		return nq.getSingleResult();
            
            /*CriteriaBuilder cBuilder = em.getCriteriaBuilder();

            CriteriaQuery<UnderwritingRequirement> criteriaQuery = cBuilder.createQuery(UnderwritingRequirement.class);
            Root<UnderwritingRequirement> p = criteriaQuery.from(UnderwritingRequirement.class);

            Predicate valuePredicate = cBuilder.equal(p.get(UnderwritingRequirement_.value), value);

            criteriaQuery.where(valuePredicate);

            TypedQuery<UnderwritingRequirement> typeCodeQuery = em.createQuery(criteriaQuery);

            List<UnderwritingRequirement> reqs = typeCodeQuery.getResultList();
            if(reqs != null && !reqs.isEmpty()){
                return reqs.get(0);
            }*/
        }

        return null;
    }
    
    public UnderwritingRequirement findByOldSystemValue(String value) {
        
        if (value != null) {
        	
        	TypedQuery<UnderwritingRequirement> nq = em.createQuery(
    				"SELECT a FROM UnderwritingRequirement a WHERE a.oldSystemCode = :value",
    				UnderwritingRequirement.class);
    		nq.setParameter("value", value);
    		return nq.getSingleResult();
            
            /*CriteriaBuilder cBuilder = em.getCriteriaBuilder();

            CriteriaQuery<UnderwritingRequirement> criteriaQuery = cBuilder.createQuery(UnderwritingRequirement.class);
            
            Root<UnderwritingRequirement> p = criteriaQuery.from(UnderwritingRequirement.class);

            Predicate valuePredicate = cBuilder.equal(p.get(UnderwritingRequirement_.oldSystemCode), value);

            criteriaQuery.where(valuePredicate);

            TypedQuery<UnderwritingRequirement> typeCodeQuery = em.createQuery(criteriaQuery);

            List<UnderwritingRequirement> reqs = typeCodeQuery.getResultList();
            if(reqs != null && !reqs.isEmpty()){
                return reqs.get(0);
            }*/
        }

        return null;
    }
    
    
    public UnderwritingRequirement findByValueAndTypeClassName(TypeClass typeClass, int value) {

    	TypedQuery<UnderwritingRequirement> nq = em.createQuery(
				"SELECT a FROM UnderwritingRequirement a WHERE a.typeClass = :typeClass and a.value = :value",
				UnderwritingRequirement.class);
		nq.setParameter("typeClass", typeClass);
		nq.setParameter("value", value);
		return nq.getSingleResult();
		/*
        CriteriaBuilder cBuilder = em.getCriteriaBuilder();

        CriteriaQuery<UnderwritingRequirement> criteriaQuery = cBuilder.createQuery(UnderwritingRequirement.class);
        Root<UnderwritingRequirement> p = criteriaQuery.from(UnderwritingRequirement.class);

        Predicate valuePredicate = cBuilder.equal(p.get(UnderwritingRequirement_.value), value);
        Predicate typeClassNamePredicate = cBuilder.equal(p.get(UnderwritingRequirement_.typeClass), typeClass);

        criteriaQuery.where(cBuilder.and(valuePredicate, typeClassNamePredicate));

        TypedQuery<UnderwritingRequirement> typeCodeQuery = em.createQuery(criteriaQuery);

        List<UnderwritingRequirement> reqs = typeCodeQuery.getResultList();
        if (reqs != null && !reqs.isEmpty()) {
            return reqs.get(0);
        }
        
        return null;*/
    }
}
