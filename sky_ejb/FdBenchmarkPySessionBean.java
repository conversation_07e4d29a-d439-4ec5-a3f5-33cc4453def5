/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdBenchmarkPy;
import com.insurfact.fundata.entity.FdFundQuartilesPy;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdBenchmarkPySessionBean extends AbstractFacade<FdBenchmarkPy> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdBenchmarkPySessionBean() {
		super(FdBenchmarkPy.class);
	}

	/**
	 * 
	 * @param assocCanIndexId usually from the FdQuartile table
	 * @return FdBenchmarkPy or null
	 */
	public FdBenchmarkPy getByAssocCanIndexId(Long assocCanIndexId) {

		TypedQuery<FdBenchmarkPy> nq = em.createQuery(
				"SELECT a FROM FdBenchmarkPy a WHERE  a.assocCanIndexId = :assocCanIndexId", FdBenchmarkPy.class);
		nq.setParameter("assocCanIndexId", assocCanIndexId);
		return nq.getSingleResult();

		/*
		 * FdBenchmarkPy benchmark = null;
		 * 
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * CriteriaQuery<FdBenchmarkPy> criteriaQuery =
		 * cBuilder.createQuery(FdBenchmarkPy.class); Root<FdBenchmarkPy> p =
		 * criteriaQuery.from(FdBenchmarkPy.class);
		 * 
		 * Predicate codePredicate =
		 * cBuilder.equal(p.get(FdBenchmarkPy_.assocCanIndexId), assocCanIndexId);
		 * criteriaQuery.where(codePredicate);
		 * 
		 * TypedQuery<FdBenchmarkPy> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * List<FdBenchmarkPy> results = typeCodeQuery.getResultList(); if (results !=
		 * null && !results.isEmpty()) { benchmark = results.get(0); }
		 * 
		 * return benchmark;
		 */

	}

	/**
	 * FdFundQuartilesPy must be set before calling this method.
	 * 
	 * @param fundatakey
	 * @return FdBenchmarkPy or null
	 */
	public FdBenchmarkPy getByFundataKey(Long fundatakey) {
		if(fundatakey == null) {
			return null;
		}
		FdFundQuartilesPy quartile = null;

		TypedQuery<FdFundQuartilesPy> nq = em.createQuery(
				"SELECT a FROM FdFundQuartilesPy a WHERE  a.fundatakey = :fundatakey", FdFundQuartilesPy.class);
		nq.setParameter("fundatakey", fundatakey);
		quartile = nq.getSingleResult();

		if (quartile == null)
			return null;

		return getByAssocCanIndexId(quartile.getAssocCanIndexKey());

		/*
		 * FdFundQuartilesPy quartile = null;
		 * 
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * CriteriaQuery<FdFundQuartilesPy> criteriaQuery =
		 * cBuilder.createQuery(FdFundQuartilesPy.class); Root<FdFundQuartilesPy> p =
		 * criteriaQuery.from(FdFundQuartilesPy.class);
		 * 
		 * Predicate codePredicate =
		 * cBuilder.equal(p.get(FdFundQuartilesPy_.fundatakey), fundatakey);
		 * criteriaQuery.where(codePredicate);
		 * 
		 * TypedQuery<FdFundQuartilesPy> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * List<FdFundQuartilesPy> results = typeCodeQuery.getResultList(); if (results
		 * != null && !results.isEmpty()) { quartile = results.get(0); }
		 * 
		 * if (quartile == null) return null;
		 * 
		 * // FdBenchmark benchmark = quartile.getAssocCanIndexKey();
		 * 
		 * long key = quartile.getAssocCanIndexKey();
		 * 
		 * FdBenchmarkPy benchmark = getByAssocCanIndexId(key);
		 * 
		 * return benchmark;
		 */
	}
}
