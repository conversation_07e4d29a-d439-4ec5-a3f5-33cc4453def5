/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.AccountManager;
import com.insurfact.skynet.entity.ContractRoute;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AccountManagerFacade extends AbstractFacade<AccountManager> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public AccountManagerFacade() {
        super(AccountManager.class);
    }
    
    public void modifyAccountManager(AccountManager accountManager){
        
        Date now = Calendar.getInstance().getTime();
        
        if(accountManager != null){
            Integer managerID = accountManager.getAccountManagerIntId();

            if (accountManager.isDeleted()) {
                
                if (managerID != null) {
                    
                    List<ContractRoute> routes = accountManager.getContractRouteList();
                    if(routes != null && !routes.isEmpty()){
                        
                        Iterator<ContractRoute> routesIter = routes.iterator();
                        while(routesIter.hasNext()){
                            
                            ContractRoute route = routesIter.next();
                            route.setAccountManager(null);
                            em.merge(route);
                        }
                    }
                    
                    accountManager.setContractRouteList(null);
                    accountManager = em.getReference(AccountManager.class, managerID);
                    em.remove(accountManager);
                }
                
            } else {
                
                if (managerID != null) {
                    accountManager.setLastModificationDate(now);
                    em.merge(accountManager);
                    
                } else {
                    accountManager.setLastModificationDate(now);
                    accountManager.setCreationDate(now);
                    em.persist(accountManager);
                }
            }
        }
    }
}
