/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.Salesrep;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class SalesrepFacade extends AbstractFacade<Salesrep> {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public SalesrepFacade() {
        super(Salesrep.class);
    }

    public List<Salesrep> findSalesRepBySalesRepCode(String salesRepCode) {
        
        try {
        	TypedQuery<Salesrep>  nq = getEntityManager().createNamedQuery("Salesrep.findBySalesrepCode", Salesrep.class);
            nq.setParameter("salesrepCode", salesRepCode);
            
            return nq.getResultList();
            
        }
        catch (NoResultException e) {
            return null;
        } 
    }   
    
   
    public Salesrep getByDealerAndSalesrepCode(String dealerCode, String salesRepCode) {
        
        EntityManager em = getEntityManager();
        TypedQuery<Salesrep> nq = null;
        try {      
            nq = em.createNamedQuery("Salesrep.findByDealerAndSalesrepCode",Salesrep.class);
            
            nq.setParameter("dealerCode", dealerCode );
            nq.setParameter("salesrepCode", salesRepCode );
               
            return (Salesrep) nq.getSingleResult();
        }
        catch(NoResultException e){
            return null;
        }    
        catch (NonUniqueResultException e){
             List<Salesrep> salesreps = nq.getResultList();
            
            return (Salesrep)salesreps.get(0);
        }        
        
    }      
}
