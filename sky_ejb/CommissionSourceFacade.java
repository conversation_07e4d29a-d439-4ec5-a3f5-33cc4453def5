/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.CommissionSource; 
import com.insurfact.skynet.entity.ProductSupplier;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TemporalType;
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CommissionSourceFacade extends AbstractFacade<CommissionSource> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public CommissionSourceFacade() {
        super(CommissionSource.class);
    }

    public void persist(CommissionSource selectedCommSource) {

        if (selectedCommSource != null) {
            Date now = Calendar.getInstance().getTime();
            selectedCommSource.setLastModificationDate(now);

            Integer sourceId = selectedCommSource.getCommissionSourceIntId();

            if (sourceId != null) {
                if (selectedCommSource.isDeleted()) {

                    selectedCommSource = em.getReference(CommissionSource.class, sourceId);
                    em.remove(selectedCommSource);
                } else {

                    em.merge(selectedCommSource);
                }
            } else {

                if (!selectedCommSource.isDeleted()) {

                    selectedCommSource.setCreationDate(now);
                    em.persist(selectedCommSource);
                }
            }
        }
    }
    
    public List<CommissionSource> findBySupplierAndStartAndEndDates(ProductSupplier supplier, Date startDate, Date endDate){
         
    	TypedQuery<CommissionSource> nq = em.createQuery(
				"SELECT a FROM CommissionSource a WHERE  a.productSupplier = :supplier and a.startDate =:startDate and a.endDate = :endDate",
				CommissionSource.class);
    	if (supplier != null) { 
    		nq.setParameter("supplier", supplier);
        }
        
        if(startDate != null){
    		nq.setParameter("startDate", startDate, TemporalType.TIMESTAMP);
        }
        
        if(endDate != null){
    		nq.setParameter("endDate", endDate, TemporalType.TIMESTAMP);
        } 
         
		return nq.getResultList();
    	
        /*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
        CriteriaQuery<CommissionSource> criteriaQuery = cBuilder.createQuery(CommissionSource.class);
        Root<CommissionSource> p = criteriaQuery.from(CommissionSource.class);
        
        List<Predicate> allPredicates = new ArrayList<Predicate>();

        if (supplier != null) {
            Predicate supplierPredicate = cBuilder.equal(p.get(CommissionSource_.productSupplier), supplier);
            allPredicates.add(supplierPredicate);
        }
        
        if(startDate != null){
            Predicate startPredicate = cBuilder.greaterThanOrEqualTo(p.get(CommissionSource_.startDate), startDate);
            allPredicates.add(startPredicate);
        }
        
        if(endDate != null){
            Predicate endPredicate = cBuilder.lessThanOrEqualTo(p.get(CommissionSource_.endDate), endDate);
            allPredicates.add(endPredicate);
        }
        
        Predicate queryPredicate = null;
        
        queryPredicate = cBuilder.and(allPredicates.toArray(new Predicate[]{}));

        if (queryPredicate != null) {
            criteriaQuery.where(queryPredicate).distinct(true);
        }

        TypedQuery<CommissionSource> clientsQuery = em.createQuery(criteriaQuery);

        return clientsQuery.getResultList();*/
    }
    
    
    public List<CommissionSource> findByStartAndEndDates(Date start, Date end, ProductSupplier prodSupp, int type, int level){
        
        String query = null;
        
        if(prodSupp == null) {
            query = "SELECT c FROM CommissionSource c WHERE c.startDate >= :startDate AND c.endDate <= :endDate AND c.commissionLevel = :level";
        }else {
             query = "SELECT c FROM CommissionSource c WHERE c.startDate >= :startDate AND c.endDate <= :endDate AND c.productSupplier = :productSupplier AND c.commissionLevel = :level";
        }
        
        if(type != 0){
            query += " AND c.commissionType = :type";
        }
        
        List <CommissionSource>commSources = new ArrayList<CommissionSource>();
        
        try {
        	TypedQuery<CommissionSource> nq = getEntityManager().createQuery(query, CommissionSource.class);
            nq.setParameter("startDate", start);
            nq.setParameter("endDate", end);
            nq.setParameter("level", level);
            
            if(prodSupp != null) {
                nq.setParameter("productSupplier", prodSupp);
            }
            
            // FYC
            if(type == 1){
                 nq.setParameter("type", "F");
            }
            
            // Service Fees
            if(type == 2){
                  nq.setParameter("type", "S");
            }
            
            // Renewalls 
            if(type == 3){
                  nq.setParameter("type", "R");
            }
            
            
            
            commSources =  nq.getResultList();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return commSources;
            
    }
}
