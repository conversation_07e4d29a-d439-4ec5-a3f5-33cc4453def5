/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED " IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.AdvisorBusinessStat;
import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.ProductClass;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

import org.eclipse.persistence.config.HintValues;
import org.eclipse.persistence.config.QueryHints;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AdvisorBusinessStatFacade extends AbstractFacade<AdvisorBusinessStat> {
    
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public AdvisorBusinessStatFacade() {
        super(AdvisorBusinessStat.class);
    }
    
    public Date getMaxDate(Advisor advisor, ProductClass productClass, Integer type, Integer status){
        
        Date maxDate = null;
        try {
            Query nq = em.createQuery("Select max(a.startDate) FROM AdvisorBusinessStat a where a.advisor = :advisor AND a.productClass = :productClass AND a.type = :type AND a.status = :status", Date.class);
            nq.setParameter("advisor", advisor);
            nq.setParameter("productClass", productClass);
            nq.setParameter("type", type);
            nq.setParameter("status", status);
            nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
            
            maxDate = (Date) nq.getSingleResult();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return maxDate;
    }
    
    public Date getContractStatsMaxDate(Agency agency, Integer type){
        
        Date maxDate = null;
        try {
            Query nq = em.createQuery("Select max(a.endDate) FROM AdvisorBusinessStat a where a.agency = :agency AND a.type = :type", Date.class);
            nq.setParameter("agency", agency);

            nq.setParameter("type", type);
            nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
            
            maxDate = (Date) nq.getSingleResult();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return maxDate;
    }
        
    public Date getContractStatsMaxDate(Advisor advisor, Integer type){
        
        Date maxDate = null;
        try {
            Query nq = em.createQuery("Select max(a.endDate) FROM AdvisorBusinessStat a where a.advisor = :advisor AND a.type = :type", Date.class);
            nq.setParameter("advisor", advisor);

            nq.setParameter("type", type);
            nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
            
            maxDate = (Date) nq.getSingleResult();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return maxDate;
    }
        
    public List<AdvisorBusinessStat> findStatByAdvisorOrderByTotalNumber(Advisor advisor,  Integer type, Integer count) {
            
        Date endDate = getContractStatsMaxDate(advisor,type);
        
        List<AdvisorBusinessStat> stats = null;
        try {
            TypedQuery<AdvisorBusinessStat> nq = em.createQuery("SELECT a FROM AdvisorBusinessStat a WHERE a.advisor = :advisor AND a.type = :type AND a.endDate = :endDate ORDER BY a.totalNumber DESC", AdvisorBusinessStat.class);
            nq.setParameter("advisor", advisor);
            nq.setParameter("type", type);
            nq.setParameter("endDate", endDate);
            
            if(count!= null)
                nq.setMaxResults(count);
            
            nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
            
            stats =  nq.getResultList();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return stats;
    }  
    public List<AdvisorBusinessStat> findStatByAgencyOrderByTotalNumber(Agency agency,  Integer type, Integer count) {
            
        Date endDate = getContractStatsMaxDate(agency,type);
        
        List<AdvisorBusinessStat> stats = null;
        try {
        	TypedQuery<AdvisorBusinessStat> nq = em.createQuery("SELECT a FROM AdvisorBusinessStat a WHERE a.agency = :agency AND a.type = :type AND a.endDate = :endDate ORDER BY a.totalNumber DESC", AdvisorBusinessStat.class);
            nq.setParameter("agency", agency);
            nq.setParameter("type", type);
            nq.setParameter("endDate", endDate);
            
            if(count!= null)
                nq.setMaxResults(count);
            
            nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
            
            stats =  nq.getResultList();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return stats;
    }  
    
    public List<AdvisorBusinessStat> findStatByAdvisorOrderByProvince(Advisor advisor, ProductClass productClass, Integer type, Integer status, Date fromDate, Date toDate) {
            
        List<AdvisorBusinessStat> stats = null;
        try {
        	TypedQuery<AdvisorBusinessStat> nq = em.createQuery("SELECT a FROM AdvisorBusinessStat a WHERE a.advisor = :advisor AND a.productClass = :productClass AND a.type = :type AND a.status = :status AND a.startDate >= :fromDate AND a.endDate <= :toDate ORDER BY a.province", AdvisorBusinessStat.class);
            nq.setParameter("advisor", advisor);
            nq.setParameter("startDate", fromDate);
            nq.setParameter("endDate", toDate);
            nq.setParameter("type", type);
            nq.setParameter("status", status);
            nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
            
            stats =  nq.getResultList();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return stats;
    }     
    
    public List<AdvisorBusinessStat> findStatByAdvisorOrderByProvince(Advisor advisor, Integer type, Integer status, Date fromDate, Date toDate) {
            
        List<AdvisorBusinessStat> stats = null;
        try {
        	TypedQuery<AdvisorBusinessStat> nq = em.createQuery("SELECT a FROM AdvisorBusinessStat  a WHERE a.advisor = :advisor AND a.type = :type AND a.status = :status AND a.startDate >= :fromDate AND a.endDate <= :toDate ORDER BY a.province", AdvisorBusinessStat.class);
            nq.setParameter("advisor", advisor);
            nq.setParameter("startDate", fromDate);
            nq.setParameter("endDate", toDate);
            nq.setParameter("type", type);
            nq.setParameter("status", status);
            nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
            
            stats =  nq.getResultList();
        }
        catch (NoResultException e) {
            System.out.println("No  results found...");
            return null;
        } 
        
        return stats;
    }
    
    public List<AdvisorBusinessStat> findLatestStatsByAdvisorAllProvinces(Advisor advisor, Integer type) {
            
        String[] provs = {"AB","BC","MB","NB","NL","NS","NT","NU","ON","PE","QC","SK","YT"};
        
        List<AdvisorBusinessStat> stats = new ArrayList<>();
        
        TypedQuery<AdvisorBusinessStat> nq = em.createQuery("SELECT a FROM AdvisorBusinessStat  a WHERE a.advisor = :advisor AND a.type = :type AND a.province = :province ORDER BY a.endDate DESC", AdvisorBusinessStat.class);
        nq.setParameter("advisor", advisor);
        nq.setParameter("type", type);
//        nq.setParameter("status", status);
        nq.setMaxResults(1);
        nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);

        for(String prov : provs) {

            try {
                nq.setParameter("province", prov);
                AdvisorBusinessStat stat  = (AdvisorBusinessStat) nq.getSingleResult();

                stats.add(stat);
            }
            catch (NoResultException | NonUniqueResultException e) {} 
        }
        
        return stats;
    }
    
    /**
     * retrieve the latest by Type  By Province.   One record per Province
     * @param agency
     * @param type
     * @return 
     */
    public List<AdvisorBusinessStat> findLatestStatsByAgencyAllProvinces(Agency agency, Integer type) {
            
        String[] provs = {"AB","BC","MB","NB","NL","NS","NT","NU","ON","PE","QC","SK","YT"};
        
        List<AdvisorBusinessStat> stats = new ArrayList<>();
        
        TypedQuery<AdvisorBusinessStat> nq = em.createQuery("SELECT a FROM AdvisorBusinessStat  a WHERE a.agency = :agency AND a.type = :type AND a.province = :province ORDER BY a.endDate DESC", AdvisorBusinessStat.class);
        nq.setParameter("agency", agency);
        nq.setParameter("type", type);
//        nq.setParameter("status", status);
        nq.setMaxResults(1);
        nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);
   
        
//        return nq.getResultList();
        
//        System.out.println(agency + " " + type + " " + status);
        
        for(String prov : provs) {

            try {
//                 System.out.println("Looking stat for : " + prov);
                      nq.setParameter("province", prov);
                
//                System.out.println(nq.toString());
                
                AdvisorBusinessStat stat  = (AdvisorBusinessStat) nq.getSingleResult();

//                System.out.println("adding stat for : " + stat.getProvince());
                stats.add(stat);
            }
            catch (NoResultException | NonUniqueResultException e) {

            } 
        }
        
        return stats;
    }
    
    public AdvisorBusinessStat findLatestStatsByAgency(Agency agency, Integer type) {
            
        AdvisorBusinessStat stats =null;
        
        TypedQuery<AdvisorBusinessStat> nq = em.createQuery("SELECT a FROM AdvisorBusinessStat  a WHERE a.agency = :agency AND a.type = :type ORDER BY a.endDate DESC", AdvisorBusinessStat.class);
        nq.setParameter("agency", agency);
        nq.setParameter("type", type);

        nq.setMaxResults(1);
        nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);


        try {
            stats  = (AdvisorBusinessStat) nq.getSingleResult();
        }
        catch (NoResultException | NonUniqueResultException e) {} 
        
        
        return stats;
    }
    
    public AdvisorBusinessStat findLatestStatsByAdvisor(Advisor advisor, Integer type) {
            
        AdvisorBusinessStat stats =null;
        
        TypedQuery<AdvisorBusinessStat> nq = em.createQuery("SELECT a FROM AdvisorBusinessStat  a WHERE a.advisor = :advisor AND a.type = :type ORDER BY a.endDate DESC", AdvisorBusinessStat.class);
        nq.setParameter("advisor", advisor);
        nq.setParameter("type", type);

        nq.setMaxResults(1);
        nq.setHint(QueryHints.READ_ONLY, HintValues.TRUE);


        try {
            stats  = (AdvisorBusinessStat) nq.getSingleResult();
        }
        catch (NoResultException | NonUniqueResultException e) {} 
        
        
        return stats;
    }    
}
