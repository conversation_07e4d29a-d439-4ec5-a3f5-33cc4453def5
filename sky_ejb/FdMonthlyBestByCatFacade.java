/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdBestCategory;
import com.insurfact.fundata.entity.FdMonthlyBestByCat;
import com.insurfact.fundata.entity.FdPerformance;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 * smasse 2017-4-21 washere washere todo
 * 
 * insert into FD_MONTHLY_BEST_BY_CAT (SEQ_NUMBER) (select ytd_return_fund FROM
 * FD_BEST_CATEGORY where FD_CATEGORY in (1019,1020,1022,1031,1043));
 * 
 * UPDATE FD_MONTHLY_BEST_BY_CAT a SET a.CREATION_DATE=SYSDATE,
 * a.PERF_SEQ=(SELECT b.PERf_SEQ FROM FD_PERFORMANCE b where
 * a.seq_number=b.PERF_FUNDATAKEY);
 *
 * <AUTHOR>
 */
@Stateless
public class FdMonthlyBestByCatFacade extends AbstractFacade<FdMonthlyBestByCat> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdMonthlyBestByCatFacade() {
		super(FdMonthlyBestByCat.class);
	}

	public List<FdMonthlyBestByCat> findAllBests() {
		try {
			TypedQuery<FdMonthlyBestByCat> nq = getEntityManager().createNamedQuery("FdMonthlyBestByCat.findAll",
					FdMonthlyBestByCat.class);
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No results found...");
			return null;
		}
	}

	/**
	 * Used by admin user to reset the dashboard. Performs the equivalent of these
	 * SQL statements:
	 * 
	 * <p/>
	 * insert into FD_MONTHLY_BEST_BY_CAT (SEQ_NUMBER) (select ytd_return_fund FROM
	 * FD_BEST_CATEGORY where FD_CATEGORY in (1019,1020,1022,1031,1043));
	 *
	 * <p/>
	 * UPDATE FD_MONTHLY_BEST_BY_CAT a SET a.CREATION_DATE=SYSDATE,
	 * a.PERF_SEQ=(SELECT b.PERf_SEQ FROM FD_PERFORMANCE b where
	 * a.seq_number=b.PERF_FUNDATAKEY);
	 *
	 * <!-- Ref: http://www.objectdb.com/java/jpa/query/jpql/update -->
	 *
	 * @param bestCats List of FdBestCategory instances where category id is one of
	 *                 (1019,1020,1022,1031,1043)
	 */
	public void resetTable(List<FdBestCategory> bestCats) {
		try {
			EntityManager em = getEntityManager();

			// technique jpa 2.0 ejb automated

			// bestCats is equivalent to FROM FD_BEST_CATEGORY where FD_CATEGORY in
			// (1019,1020,1022,1031,1043)

			for (FdBestCategory best : bestCats) {
				Integer ytdReturnFund = best.getYtdReturnFund(); // int
				FdMonthlyBestByCat fdMonthlyBestByCat = new FdMonthlyBestByCat(ytdReturnFund);
				fdMonthlyBestByCat.setCreationDate(new Date());

				// a.PERF_SEQ=(SELECT b.PERf_SEQ FROM FD_PERFORMANCE b where
				// a.seq_number=b.PERF_FUNDATAKEY)

				// FdPerformance.findByPerfFundatakey

				FdPerformance perf = getByFundataKey(fdMonthlyBestByCat.getSeqNumber());

				fdMonthlyBestByCat.setPerfSeq(perf.getPerfSeq());
				em.persist(fdMonthlyBestByCat);
			}
			System.out.println("FdMonthlyBestByCatFacade.resetTable: exiting normally");
		} catch (Exception e) {
			// PersistenceException possible?
			System.out.println("FdMonthlyBestByCatFacade.resetTable: " + e);
			e.printStackTrace();
		}
	}

	private FdPerformance getByFundataKey(Integer fundatakey) {

		EntityManager em = getEntityManager();

		try {
			Query nq = em.createNamedQuery("FdPerformance.findByPerfFundatakey", FdPerformance.class);
			nq.setParameter("perfFundatakey", fundatakey);
			FdPerformance fdPerf = (FdPerformance) nq.getSingleResult();

			return fdPerf;

		} catch (NoResultException e) {
			return null;
		}
	}

}
