/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.AccountManager;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Agency;
import com.insurfact.skynet.entity.ContractSetup;
import com.insurfact.skynet.entity.Payee;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class PayeeFacade extends AbstractFacade<Payee> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public PayeeFacade() {
		super(Payee.class);
	}

	public List<Payee> getPayeeByAdvisor(Advisor advisor) {

		try {
			TypedQuery<Payee> nq = em.createNamedQuery("Payee.findByAdvisor", Payee.class);

			nq.setParameter("advisor", advisor);

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Payee> getPayeeByAgency(Agency agency) {

		try {
			TypedQuery<Payee> nq = em.createNamedQuery("Payee.findByAgency", Payee.class);

			nq.setParameter("agency", agency);

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Payee> getPayeeByAccountManager(AccountManager accountManager) {

		try {
			TypedQuery<Payee> nq = em.createNamedQuery("Payee.findByAccountManager", Payee.class);

			nq.setParameter("accountManager", accountManager);

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	public Payee getPayeeByPayToAndAdvisor(Advisor advisor, String payTo) {

		try {
			TypedQuery<Payee> nq = em.createNamedQuery("Payee.findByAdvisorAndPayTo", Payee.class);

			nq.setParameter("advisor", advisor);
			nq.setParameter("payto", payTo);

			return (Payee) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}

	}

	public Payee getPayeeByPayToAndContractSetup(Advisor advisor, ContractSetup contract) {

		try {
			Query nq = em.createNamedQuery("Payee.findByAdvisorAndContractSetup", Payee.class);

			nq.setParameter("advisor", advisor);
			nq.setParameter("contractSetup", contract);

			return (Payee) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}
}
