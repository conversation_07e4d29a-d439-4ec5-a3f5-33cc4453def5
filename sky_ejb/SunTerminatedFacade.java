/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

/**
 *
 * <AUTHOR>
 */
import com.insurfact.skynet.SunTerminated;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import javax.sql.DataSource;

@Stateless
public class SunTerminatedFacade {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Resource(lookup = "jdbc/Skytest")
    private DataSource ds;

    protected EntityManager getEntityManager() {
        return em;
    }

    public SunTerminatedFacade() {

    }

    private Connection getConnection() {

        try {

            return ds.getConnection();
        } catch (Exception e) {

            e.printStackTrace();
        }
        return null;
    }

    /*
    SUN_TERMINATED_TOTALS
    AGENTID
ADVISORNUMBER
OLD_AGENT_FIRSTNAME
OLD_AGENT_LASTNAME
SUNTERMDATE
TOTPOLS
TOTCLI
TOTPREM
     */
    public List<SunTerminated> findTermAdvisors() {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select distinct a.AGENTID,      "
                    + "        a.ADVISORNUMBER,         "
                    + "        a.OLD_AGENT_FIRSTNAME,   "
                    + "        a.OLD_AGENT_LASTNAME,    "
                    + "        a.SUNTERMDATE,           "
                    + "        a.TOTPOLS,               "
                    + "        a.TOTCLI,                "
                    + "        a.TOTPREM                "
                    + "   from SUN_TERMINATED_TOTALS a  "
                    + "  ORDER BY 4, 3, 2, 6 ";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setAgentID(rst.getInt(1));

                if (rst.getString(2) != null) {
                    sunTerm.setSunAdvNo(rst.getString(2));
                } else {
                    sunTerm.setSunAdvNo("n/a");
                }

                sunTerm.setAgentFirstName(rst.getString(3));
                sunTerm.setAgentLastName(rst.getString(4));

                if (rst.getString(5) != null) {
                    sunTerm.setSunTermDate(rst.getDate(5));
                }

                sunTerm.setTotalPolicies(rst.getInt(6));
                sunTerm.setTotalClients(rst.getInt(7));
                sunTerm.setTotalPremium(rst.getDouble(8));

                terminatedSunList.add(sunTerm);

            }
            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

    public List<SunTerminated> findTermAdvisorDetails(int agentID) {

        ResultSet rst = null;

        Connection connection = getConnection();

        List<SunTerminated> terminatedSunList = new ArrayList<>();
        if (connection == null) {
            return terminatedSunList;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select FIRSTNAME, LASTNAME,         "
                    + "        NAMEENGLISH, NAMEFRENCH,     "
                    + "        ISSUEDATE, STATUSDATE,       "
                    + "        FACEAMT,                  "
                    + "        ANNPREM, POLICYNUMBER, "
                    + "        ENGLISH_NAME, FRENCH_NAME    "
                    + "   from ADVISOR_CLIENTS_LC_POLICIES  "
                    + "  where AGENTID=" + agentID
                    + "  ORDER BY 2,1 ";
            rst = ptStmt.executeQuery(sql);
            //add counter - int

            if (rst == null) {
                connection.close();
                return terminatedSunList;
            }

            while (rst.next()) {

                SunTerminated sunTerm = new SunTerminated();

                sunTerm.setClientFirstName(rst.getString(1));
                sunTerm.setClientLastName(rst.getString(2));

                sunTerm.setCompNameEn(rst.getString(3));
                sunTerm.setCompNameFr(rst.getString(4));

                sunTerm.setIssDate(rst.getDate(5));

                sunTerm.setFaceAmnt(rst.getDouble(7));
                sunTerm.setAnnPrem(rst.getDouble(8));

                sunTerm.setPolNum(rst.getString(9));

                sunTerm.setProdNameEn(rst.getString(10));
                sunTerm.setProdNameFr(rst.getString(11));

                sunTerm.setPaidToDate(rst.getDate(6));

                terminatedSunList.add(sunTerm);
            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            e.printStackTrace();
            return terminatedSunList;
        } // return the connection to the pool

        return terminatedSunList;
    }

}
