/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdFundCompany; 
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFundCompanyFacade extends AbstractFacade<FdFundCompany> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdFundCompanyFacade() {
        super(FdFundCompany.class);
    }
    
    
   public FdFundCompany getByFundataKey(Long fundatakey) {
       
        FdFundCompany fundCompany = null;
       
        List<FdFundCompany> fundCompanies = new ArrayList<>();
       
        EntityManager emLocal = getEntityManager();
        
        try {      
        	TypedQuery<FdFundCompany> nq = emLocal.createNamedQuery("FdFundCompany.findByGroupKey",FdFundCompany.class);           
            nq.setParameter("groupKey", fundatakey );
            
            fundCompanies = nq.getResultList();
        }
        catch(NoResultException e){
            //
        } finally {
            if(fundCompanies!=null && ! fundCompanies.isEmpty()){
                fundCompany = fundCompanies.get(0);
            }
        }    
        return fundCompany;
    }     
    
//    public FdFundCompany getByFundataKey(Long fundatakey) {
//
//        FdFundCompany quartile = null;
//
//        CriteriaBuilder cBuilder = em.getCriteriaBuilder();
//        CriteriaQuery<FdFundCompany> criteriaQuery = cBuilder.createQuery(FdFundCompany.class);
//        Root<FdFundCompany> p = criteriaQuery.from(FdFundCompany.class);
//
//        Predicate codePredicate = cBuilder.equal(p.get(FdFundCompany_.fundatakey), fundatakey);
//        criteriaQuery.where(codePredicate);
//
//        TypedQuery typeCodeQuery = em.createQuery(criteriaQuery);
//
//        List<FdFundCompany> results = typeCodeQuery.getResultList();
//        if (results != null && !results.isEmpty()) {
//            quartile = results.get(0);
//        }
//
//        return quartile;
//    }
}
