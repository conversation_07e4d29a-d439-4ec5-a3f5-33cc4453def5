package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.CarouselInfo; 
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Stateless
public class CarouselInfoFacade extends AbstractFacade<CarouselInfo> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    private static final Logger LOGGER = Logger.getLogger(CarouselInfoFacade.class.getName());

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public CarouselInfoFacade() {
        super(CarouselInfo.class);
    }

    @Transactional
    public void editFile(CarouselInfo d) {
        try {
            em.merge(d);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error editing CarouselInfo: {0}", e.getMessage());
            throw new RuntimeException("Failed to edit CarouselInfo", e);
        }
    }

    public List<CarouselInfo> findByTitleEn(String titleEn) {
        TypedQuery<CarouselInfo> query = em.createNamedQuery("CarouselInfo.findByTitleEn", CarouselInfo.class);
        query.setParameter("titleEn", titleEn);
        return query.getResultList();
    }

    public List<CarouselInfo> findByTitleFr(String titleFr) {
        TypedQuery<CarouselInfo> query = em.createNamedQuery("CarouselInfo.findByTitleFr", CarouselInfo.class);
        query.setParameter("titleFr", titleFr);
        return query.getResultList();
    }

    public List<CarouselInfo> findByDescription1(String description1) {
        TypedQuery<CarouselInfo> query = em.createNamedQuery("CarouselInfo.findByDescription1", CarouselInfo.class);
        query.setParameter("description1", description1);
        return query.getResultList();
    }

    public List<CarouselInfo> findByDescription2(String description2) {
        TypedQuery<CarouselInfo> query = em.createNamedQuery("CarouselInfo.findByDescription2", CarouselInfo.class);
        query.setParameter("description2", description2);
        return query.getResultList();
    }

    public List<CarouselInfo> findByType(int type) {
        TypedQuery<CarouselInfo> query = em.createNamedQuery("CarouselInfo.findByType", CarouselInfo.class);
        query.setParameter("type", type);
        return query.getResultList();
    }
}
