/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import jakarta.ejb.Stateless;
import com.insurfact.skynet.entity.ReportSaved;
import com.insurfact.skynet.entity.Users;
import java.util.List;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ReportSavedFacade extends AbstractFacade<ReportSaved>{
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public ReportSavedFacade() {
        super(ReportSaved.class);
    }
    
    public List<ReportSaved> findByUser(Users user){
        
        TypedQuery<ReportSaved> nq = em.createNamedQuery("ReportSaved.findByUsers", ReportSaved.class);
        nq.setParameter("users", user);

        return nq.getResultList();
    }
    
    
    
}
