/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdManager2;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdManager2SessionBean extends AbstractFacade<FdManager2> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdManager2SessionBean() {
		super(FdManager2.class);
	}

	@SuppressWarnings("finally")
	public List<FdManager2> getByFundataKey(Long fundatakey) {

		List<FdManager2> managers = new ArrayList<FdManager2>();

		EntityManager em = getEntityManager();

		try {
			TypedQuery<FdManager2> nq = em.createNamedQuery("FdManager2.findByManagerFundatakey", FdManager2.class);
			nq.setParameter("managerFundatakey", fundatakey);

			managers = nq.getResultList();
		} catch (NoResultException e) {
			//
		} finally {
			return managers;
		}
	}
}
