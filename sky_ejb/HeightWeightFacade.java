/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.isurvey.HeightWeight;
import com.insurfact.skynet.entity.Profile;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext; 
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class HeightWeightFacade extends AbstractFacade<HeightWeight> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public HeightWeightFacade() {
		super(HeightWeight.class);
	}

	public List<HeightWeight> getHeightWeightByPublic(int public1) {

		try {
			TypedQuery<HeightWeight> nq = em.createNamedQuery("HeightWeight.findByPublic1", HeightWeight.class);
			nq.setParameter("public1", public1);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}

	}

	public List<HeightWeight> findByOwner(Profile profile) {

		try {
			TypedQuery<HeightWeight> nq = em.createNamedQuery("HeightWeight.findByOwner", HeightWeight.class);
			nq.setParameter("owner", profile);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

}
