/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.HouseholdContact;  
import java.util.ArrayList; 
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class HouseholdContactFacade extends AbstractFacade<HouseholdContact> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    @Resource(lookup = "jdbc/Orains")
    private DataSource ds;

     

    public HouseholdContactFacade() {
        super(HouseholdContact.class);
    }

    @SuppressWarnings("unchecked")
	public List<Contact> findHouseholdByContact(Contact contact) {

        List<Contact> list = new ArrayList<>();
        List<HouseholdContact> listDirty = new ArrayList<>();

        try {
            Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId(), HouseholdContact.class);
            //System.out.println("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId());

            listDirty = nq.getResultList();

            list.add(contact);
            for (HouseholdContact h : listDirty) {
                if (!list.contains(h.getContact1())) {
                    if (h.getRelationship() != null) {
                        h.getContact1().setRelationship(h.getRelationship());
                    } else {
                        h.getContact1().setRelationship(0);
                    }
                    findHouseholdByContact(list, h.getContact1());
                    // list.add(h.getLead1());
                }
                if (!list.contains(h.getContact2())) {
                    if (h.getRelationship() != null) {
                        h.getContact2().setRelationship(h.getRelationship());
                    } else {
                        h.getContact2().setRelationship(0);
                    }
                    findHouseholdByContact(list, h.getContact2());
                    //list.add(h.getLead2());
                }
            }
            list.remove(0);

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
            return list;
        }

        return list;
    }
    
    @SuppressWarnings("unchecked")
	public List<Contact> findHouseholdByContact1(Contact contact) {

        List<Contact> list = new ArrayList<>();
        List<HouseholdContact> listDirty = new ArrayList<>();

        try {
            Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId(), HouseholdContact.class);
            //System.out.println("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId());

            listDirty = nq.getResultList();
 
            for (HouseholdContact h : listDirty) {
                 
                if (!list.contains(h.getContact2())) {
                    if (h.getRelationship() != null) {
                        h.getContact2().setRelationship(h.getRelationship());
                    } else {
                        h.getContact2().setRelationship(0);
                    } 
                    list.add(h.getContact2());
                }
            } 

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
            return list;
        }

        return list;
    }
    
    @SuppressWarnings("unchecked")
	public List<Contact> findHouseholdByContact2(Contact contact) {

        List<Contact> list = new ArrayList<>();
        List<HouseholdContact> listDirty = new ArrayList<>();

        try {
            Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT2 = " + contact.getContactIntId(), HouseholdContact.class);
            //System.out.println("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT2 = " + contact.getContactIntId());

            listDirty = nq.getResultList();
 
            for (HouseholdContact h : listDirty) {
                 
                if (!list.contains(h.getContact1())) {
                    if (h.getRelationship() != null) {
                        h.getContact1().setRelationship(h.getRelationship());
                    } else {
                        h.getContact1().setRelationship(0);
                    } 
                    list.add(h.getContact1());
                }
            } 

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
            return list;
        }

        return list;
    }

    @SuppressWarnings("unchecked")
	public List<Contact> findHouseholdByProviderType(int type) {

        List<Contact> list = new ArrayList<>();
        List<HouseholdContact> listDirty = new ArrayList<>();

        try {
            Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where RELATIONSHIP = " + type, HouseholdContact.class);
            //System.out.println("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId());

            listDirty = nq.getResultList();

            for (HouseholdContact h : listDirty) {
                if (!list.contains(h.getContact2()) && h.getContact2().getMasterCode().equalsIgnoreCase("BUILDINGS")) {
                    list.add(h.getContact2());
                }
            }
            list.remove(0);

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
            return list;
        }

        return list;
    }

    @SuppressWarnings("unchecked")
	public List<HouseholdContact> findHouseholdContact(Contact contact) {

        List<HouseholdContact> list = new ArrayList<>();

        try {
            Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId(), HouseholdContact.class);
            //System.out.println("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId());

            list = nq.getResultList();

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
            return list;
        }

        return list;
    }

    public HouseholdContact findHousehold(Contact contact1, Contact contact2) {

        try {
            Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where ( CONTACT1 = " + contact1.getContactIntId() + " and CONTACT2 = " + contact2.getContactIntId() + ") or (CONTACT1 = " + contact2.getContactIntId() + " and CONTACT2 = " + contact1.getContactIntId() + " )", HouseholdContact.class);
            //System.out.println("SELECT * FROM HOUSEHOLD_CONTACT where ( CONTACT1 = " + contact1.getContactIntId() + " and CONTACT2 = " + contact2.getContactIntId() + ") or (CONTACT1 = " + contact2.getContactIntId() + " and CONTACT2 = " + contact1.getContactIntId() + " )");

            if (nq.getMaxResults() > 0) {
                return (HouseholdContact) nq.getResultList().get(0);
            } else {
                return null;
            }

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
        }

        return null;
    }

    @SuppressWarnings("unchecked")
	public void findHouseholdByContact(List<Contact> toFill, Contact contact) {

        //List<Lead> list = new ArrayList<>();
        List<HouseholdContact> listDirty = new ArrayList<>();

        try {
            Query nq = em.createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId(), HouseholdContact.class);
            //System.out.println("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = " + contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId());

            listDirty = nq.getResultList();

            toFill.add(contact);
            System.out.println(listDirty.get(0));

            if (listDirty.get(0).getHouseholdIntId() == null) {
                //System.out.println("*******exiting*******");
                return;
            } else {
                for (HouseholdContact h : listDirty) {
                    if (!toFill.contains(h.getContact1())) {
                        findHouseholdByContact(toFill, h.getContact1());
                        // toFill.add(h.getLead1());
                    }
                    if (!toFill.contains(h.getContact2())) {
                        findHouseholdByContact(toFill, h.getContact2());
                        //toFill.add(h.getLead2());
                    }
                }
            }

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
            return;
        }

        return;
    }


    /*
     public List<Integer> findHouseholdByLead(Lead lead) {

        List<Integer> list = new ArrayList<>();
        ResultSet rst = null;
        Statement ptStmt;
        Connection connection = null;
        
         try {
            
            connection = getConnection();
            
            ptStmt = connection.createStatement();

            String sql = "SELECT m.ROW_KEY, m.AGE, m.RATE_1, m.POLICY_FEE_1 FROM IM_LIFE_PREMIUM_MATRIX m WHERE m.ROW_KEY ="+rowkey + " ORDER BY  m.age ASC";

            rst = ptStmt.executeQuery(sql);
            

            if(rst == null){
                System.err.println("***** Some error occured - unable to find any BandRates with rowKey="+rowkey );
                ptStmt.close();
                connection.close();
                return null;
            }
            while (rst.next() ) {
                
            }
             rst.close();
            ptStmt.close();
            
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
        // return the connection to the pool
        finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
            }
        }

        return list;
    } 
     */
}
