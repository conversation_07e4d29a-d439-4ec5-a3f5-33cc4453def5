/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2015 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.cannex.entity.CannexProductNew; 
import com.insurfact.cannex.entity.CannexInterestRateNewTerm;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class TermsInterestRateFacadeNew extends AbstractFacade<CannexInterestRateNewTerm> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public TermsInterestRateFacadeNew() {
        super(CannexInterestRateNewTerm.class);
    }

    public List<CannexInterestRateNewTerm> findTermRates(CannexProductNew product, Integer termTypeId) {

    	TypedQuery<CannexInterestRateNewTerm> query = em.createQuery("SELECT r FROM CannexInterestRateNewTerm r WHERE r.termProduct = :product AND r.termProduct.termTypeId = :duration  ORDER BY r.interestRateValue ASC", CannexInterestRateNewTerm.class);
        query.setParameter("product", product);
        query.setParameter("duration", termTypeId);

        return query.getResultList();
    }

    public void deleteRates(CannexProductNew termProduct, Integer termTypeId) {
        List<CannexInterestRateNewTerm> rates = findTermRates(termProduct, termTypeId);

        if (rates != null && !rates.isEmpty()) {

            for (CannexInterestRateNewTerm rate : rates) {
                em.remove(rate);
            }
        }

    }
    
    public void safeManualProduct(CannexInterestRateNewTerm rate, Integer product){
        Query query = em.createNativeQuery("update CANNEX_INTEREST_RATE_NEW_TERM set CANNEX_PRODUCT = "+product+" WHERE INTEREST_RATE_INT_ID = "+rate.getInterestRateIntId()) ;  
        query.executeUpdate();
    }
}
