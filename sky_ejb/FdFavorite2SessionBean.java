/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdFund2;
import com.insurfact.fundata.entity.FundFavorite2;
import com.insurfact.skynet.entity.Users;
import java.util.Calendar;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFavorite2SessionBean extends AbstractFacade<FundFavorite2> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdFavorite2SessionBean() {
		super(FundFavorite2.class);
	}

	public FundFavorite2 getFavoriteByFundAndUsers(FdFund2 fund, Users users) {

		try {
			Query nq = getEntityManager().createNamedQuery("FundFavorite2.findByFdFundAndUsers", FundFavorite2.class);
			nq.setParameter("fdFund", fund);
			nq.setParameter("users", users);

			return (FundFavorite2) nq.getSingleResult();

		} catch (Exception e) {

			return null;
		}
	}

	public List<FundFavorite2> findFavoriteByUsers(Users users) {

		try {
			TypedQuery<FundFavorite2> nq = getEntityManager().createNamedQuery("FundFavorite2.findByUsers",
					FundFavorite2.class);
			nq.setParameter("users", users);

			return nq.getResultList();

		} catch (Exception e) {

			return null;
		}
	}

	public List<FundFavorite2> findFavoriteByFund(FdFund2 fund) {

		try {
			TypedQuery<FundFavorite2> nq = getEntityManager().createNamedQuery("FundFavorite2.findByFdFund",
					FundFavorite2.class);
			nq.setParameter("fdFund", fund);

			return nq.getResultList();

		} catch (Exception e) {

			return null;
		}
	}

	public FundFavorite2 addToFavorite(Users users, FdFund2 fund) {

		FundFavorite2 fav = getFavoriteByFundAndUsers(fund, users);

		if (fav != null) {

			return null;
		}

		fav = new FundFavorite2();

		fav.setUsers(users);
		fav.setFdFund(fund);
		Calendar now = Calendar.getInstance();

		fav.setCreationDate(now.getTime());

		create(fav);

		return fav;
	}

	public boolean removeFavorite(FundFavorite2 fav) {

		remove(fav);

		return true;
	}

}
