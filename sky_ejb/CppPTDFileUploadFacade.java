/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.avue.navigation.ApplicationMainBean;
import com.insurfact.skynet.entity.Policy;
import com.insurfact.skynet.entity.PolicySettling;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.inject.Inject; 
import org.primefaces.model.file.UploadedFile;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CppPTDFileUploadFacade {

    @EJB
    private CppPTDfileEntity cppPTDfileEntity;

    @EJB
    private PolicySettlingFacade policySettlingFacade;

    @Inject
    private ApplicationMainBean applicationBean;

    private int numUpdated;

    private int numRejected;

    private List<String> invalidStatus = new ArrayList<>();

    String filepath = "tmp/"; // production 'live' version

    public List<String> parseAndUpdate(UploadedFile upFile) throws Exception {
        //read file
        applicationBean.showProgressDialog();

        List<String> rejectedList = new ArrayList<>();

        boolean hasDate;

        InputStreamReader isr = new InputStreamReader(upFile.getInputStream());

        try (BufferedReader input = new BufferedReader(isr);) {

            String line = null;
            String[] contents = null;

            String polNum = null;
            java.sql.Date paidToDate = null;
            int polStatus = 0;

            line = input.readLine(); //read first line and move to next - first line are column headers
            //int count = 0;

            applicationBean.showProgressDialog();
            while ((line = input.readLine()) != null) {
                //if empty line
                if (line.trim().equals("")) {
                    continue;
                }
                //split line on commas, into an array
                contents = line.split(",");

                //Ensure that we have 5 columns. If not 5, then the format has changed or there is an empty line in the file.
                //The 2nd column is the policy number
                //The 4th column is paid to date
                //The 5th column is billing status
                if (contents == null || contents.length < 5) {
                    continue;
                }

                polNum = contents[1].trim();
                polNum = polNum.replaceAll("^\"$", ""); //"^\"|\"$", ""
                polNum = polNum.replace("\"\"", "\"");

                if (!contents[3].trim().equals("")) {
                    paidToDate = formatDate(contents[3].trim());
                    hasDate = true;
                } else {
                    hasDate = false;
                }

                String stat = contents[4].replaceAll("^\"$", "");
                stat = stat.replace("\"\"", "\"");
                polStatus = getPolicyStatus(stat, hasDate);
                //count++;

                PolicySettling policySettling;
                Policy policy;

                //update database
                if (polNum != null && paidToDate != null && polStatus > 0) {

                        policy = cppPTDfileEntity.getPolicyByNumber(polNum);

                        if (policy != null) {
                            policySettling = policy.getPolicySettling();
                            update(paidToDate, polStatus, policy, policySettling);
                        } 
                        
                        if (policy == null && polStatus == 1) {
                            rejectedList.add(polNum + " with Status : " + polStatus);
                        }
                    
                }
                else if (polNum != null && paidToDate != null && polStatus == -1) { 
                        invalidStatus.add(polNum); 
                }
            }
            applicationBean.hideProgressDialog();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return rejectedList;

    }

    /**
     * Update the paid to date/status of policies
     *
     * @param polNum
     * @param lapseDate
     */
    private void update(java.sql.Date filePTD, int newPolStat, Policy policy, PolicySettling policySettling) throws Exception {

        int existPolStat = policy.getPolicyStatus();

        boolean settUpdated = false;

        boolean polUpdated = false;

        if (policySettling != null) {

            if (filePTD != null) {

                if (policySettling.getPaidToDate() != null) {

                    if (policySettling.getPaidToDate().compareTo(filePTD) != 0) {

                        policySettling.setPaidToDate(filePTD);

                        settUpdated = true;

                    }
                } else {

                    policySettling.setPaidToDate(filePTD);

                    settUpdated = true;

                }

                //if lapsed or cancelled store the file PTD in the lapse date field
                if (newPolStat == 7 || newPolStat == 8) { // 7=lapse 8=cancellation

                    if (policySettling.getLapseDate() == null) {

                        policySettling.setLapseDate(filePTD);
                        //also put the date in last paid to date
                        policySettling.setPaidToDate(filePTD);

                        settUpdated = true;
                    }

                }//if(polStatus == 7 || polStatus == 8) {
            }

            if (settUpdated) {
                policySettling.setLastChangedUpld(now());
            }

            policySettling.setLastUser("SYSTEM (CPP IMPORT)");
            policySettling.setLastUpdate(now());
            policySettlingFacade.edit(policySettling);

            System.out.println(" -- UPDATED policy settling: " + policySettling.toString());

        }

        //update only if not already set to latest CPP status
        if (existPolStat != newPolStat) {

            policy.setPolicyStatus(newPolStat);

            policy.setLastChangedUpld(now());
        }//if (existPolStat != newPolStat ) {

        policy.setLastUpdateDate(now());
        policy.setLastUpdateUser("SYSTEM (CPP IMPORT)");

//          polUpdated = true;
        //   if(polUpdated){
        cppPTDfileEntity.edit(policy);

        System.out.println("-- UPDATED policy number: " + policy.getPolicyNumber());

        // }
        if (settUpdated || polUpdated) {

            ++numUpdated;

        }

    }

    private java.sql.Date formatDate(String date) throws Exception {
        java.sql.Date paidToDate = null;

        if (date != null && !date.equals("0")) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            java.util.Date temp = formatter.parse(date);
            paidToDate = new java.sql.Date(temp.getTime());  //covert from jdbc.Date to sql.Date
        }

        return paidToDate;
    }

    /**
     * Maps CPP billing status code to one of insurfact's policy status codes
     *
     * @param billingStatus
     * @return
     */
    private int getPolicyStatus(String billingStatus, boolean hasDate) {
        //billingStatus 2 = premium due
        //billingStatus 3 = overdue premium
        //billingStatus 4 = NSF

        int stat = 0;
        //TODO   Redo this using a switch case statement

        if (billingStatus.equals("0"))//pending issue -- this basically a new busines app and will not have a PTD, no need to update
        {
            stat = 0;//ignore
        }
        if (billingStatus.equals("1"))//1=active 
        {
            stat = 1;//active
        }
        if (billingStatus.equals("2"))// 2=premium due
        {
            stat = 5;////Lapse Pending
        }
        if (billingStatus.equals("3")) //overdue premium
        {
            stat = 42;//Grace Period
        }
        if (billingStatus.equals("4")) //NSF
        {
            stat = 42;//Grace Period
        }
        if (billingStatus.equals("5")) //paid up policy
        {
            stat = 3;//Paid-up
        }
        if (billingStatus.equals("6"))//premium waived due to disability
        {
            stat = 15;//Under disability
        }
        if (billingStatus.equals("7"))//lapsed
        {
            stat = 4;//lapsed
        }
        if (billingStatus.equals("8") && hasDate)//cancellation
        {
            stat = 13;//cancelled
        }
        if (billingStatus.equals("8") && !hasDate)//cancellation
        {
            stat = 39;//Canceled - Customer canceled new business app
        }
        if (billingStatus.equals("9"))//claim
        {
            stat = 11;//death claim paid
        }
        if (billingStatus.equals("S"))//surrender 'S' on file
        {
            stat = 6;//surrender
        }
        return stat;

        /*   OLD!   0 = 9
                 1 = 6 or 11
                 2 = 36   3 = 43
                 4 = 44
                 5 = 18   6=30   7=12
                 8 = 5    9=26   S=21   */
 /*
     if (billingStatus.equals("0"))//pending issue -- this basically a new busines app and will not have a PTD, no need to update
       stat = 9;//new business

     if(billingStatus.equals("1"))//active
       stat = 6;//paid to date

     if (billingStatus.equals("2")) //lapse pending
       stat = 36;//Missed last premium - Inforce = 36

     if (billingStatus.equals("3")) //heading towards lapse
       stat = 43;//heading towards lapse

     if (billingStatus.equals("4")) //lapse pending
       stat = 44;//Inforce - Policy In Grace Period

     if (billingStatus.equals("5"))//paid up policy
       stat = 18;//Paid-Up Life

     if (billingStatus.equals("6"))//premium waived due to disability
       stat = 30;//inforce  - premium paying waived

     if (billingStatus.equals("7"))//lapsed
       stat = 12;//lapsed

     if (billingStatus.equals("8"))//cancellation
       stat = 5;//cancelled

     if (billingStatus.equals("9"))//claim
       stat = 26;//death claim

     if (billingStatus.equals("S"))//Surrender
       stat = 21;//Surrender

         */
    }

    /**
     * List of policy numbers with invalid billing addresses.
     *
     * @return
     */
    public List<String> getInvalidStatus() {

        // TODO no null and empty list verification
        if (this.invalidStatus.size() == 0) {
            return null;
        }
        return invalidStatus;
    }

    /**
     * Number of updated records
     *
     * @return
     */
    public int getNumUpdated() {
        return numUpdated;
    }

    /**
     * Number of rejected records
     *
     * @return
     */
    public int getNumRejected() {
        return numRejected;
    }

    public void setNumRejected(int numRejected) {

        this.numRejected = numRejected;

    }

    public final Date now() {
        return Calendar.getInstance().getTime();
    }

}
