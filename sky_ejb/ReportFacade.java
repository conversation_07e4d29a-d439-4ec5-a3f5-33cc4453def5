/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import jakarta.ejb.Stateless;
import com.insurfact.skynet.entity.Report;
import com.insurfact.skynet.entity.Users;
import java.util.List;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ReportFacade extends AbstractFacade<Report> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ReportFacade() {
		super(Report.class);
	}

	public List<Report> findByUser(Users user) {

		TypedQuery<Report> nq = em.createNamedQuery("Report.findByUsers", Report.class);
		nq.setParameter("users", user);

		return nq.getResultList();
	}

}
