/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import java.util.List;

import com.insurfact.skynet.entity.Account;
import com.insurfact.skynet.entity.AccountType;  

import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query; 
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AccountFacade extends AbstractFacade<Account> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public AccountFacade() {
		super(Account.class);
	}

	public List<Account> findByAccountNumber(String number) {
//        System.out.println("EJB>>  ClientFacade.findByName with:" + name );
		List<Account> clients = null;
		try {
			TypedQuery<Account> nq = getEntityManager().createNamedQuery("Account.findByAccountNumber", Account.class);
			nq.setParameter("accountNumber", "%" + number + "%");

			clients = nq.getResultList();
		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return clients;
	}

	public Account getByDetail(String mgmt, String accountNum) {

		// EntityManager em = getEntityManager();

		try {
			Query nq = getEntityManager().createNamedQuery("Account.getAccountByDetail", Account.class);
			nq.setParameter("accountNumber", accountNum);
			nq.setParameter("mgmtCompanyCode", mgmt);
			return (Account) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<AccountType> findInvestmentAccountTypes() {

		TypedQuery<AccountType> nq = em.createQuery("SELECT a FROM AccountType a WHERE  a.accountTypeIntId <= 20",
				AccountType.class);
		return nq.getResultList();

		/*
		 * CriteriaQuery<AccountType> criteriaQuery =
		 * em.getCriteriaBuilder().createQuery(AccountType.class); Root<AccountType> p =
		 * criteriaQuery.from(AccountType.class);
		 * 
		 * Predicate idPredicate =
		 * em.getCriteriaBuilder().lessThanOrEqualTo(p.get(AccountType_.accountTypeIntId
		 * ), 20);
		 * 
		 * return findAccountTypes(criteriaQuery, idPredicate);
		 */
	}

	public List<AccountType> findInsuranceAccountTypes() {

		TypedQuery<AccountType> nq = em.createQuery("SELECT a FROM AccountType a WHERE  a.accountTypeIntId = 60",
				AccountType.class);
		return nq.getResultList();

		/*
		 * CriteriaQuery<AccountType> criteriaQuery =
		 * em.getCriteriaBuilder().createQuery(AccountType.class); Root<AccountType> p =
		 * criteriaQuery.from(AccountType.class);
		 * 
		 * Predicate idPredicate =
		 * em.getCriteriaBuilder().equal(p.get(AccountType_.accountTypeIntId), 60);
		 * 
		 * return findAccountTypes(criteriaQuery, idPredicate);
		 */
	}

	public List<AccountType> findGICAccountTypes() {

		TypedQuery<AccountType> nq = em.createQuery("SELECT a FROM AccountType a WHERE  a.accountTypeIntId = 70",
				AccountType.class);
		return nq.getResultList();

		/*
		 * CriteriaQuery<AccountType> criteriaQuery =
		 * em.getCriteriaBuilder().createQuery(AccountType.class); Root<AccountType> p =
		 * criteriaQuery.from(AccountType.class);
		 * 
		 * Predicate idPredicate =
		 * em.getCriteriaBuilder().equal(p.get(AccountType_.accountTypeIntId), 70);
		 * 
		 * return findAccountTypes(criteriaQuery, idPredicate);
		 */
	}

	public List<AccountType> findAnnuityAccountTypes() {

		TypedQuery<AccountType> nq = em.createQuery("SELECT a FROM AccountType a WHERE  a.accountTypeIntId = 80",
				AccountType.class);
		return nq.getResultList();

		/*
		 * CriteriaQuery<AccountType> criteriaQuery =
		 * em.getCriteriaBuilder().createQuery(AccountType.class); Root<AccountType> p =
		 * criteriaQuery.from(AccountType.class);
		 * 
		 * Predicate idPredicate =
		 * em.getCriteriaBuilder().equal(p.get(AccountType_.accountTypeIntId), 80);
		 * 
		 * return findAccountTypes(criteriaQuery, idPredicate);
		 */
	}

	public List<AccountType> findMortgageAccountTypes() {

		TypedQuery<AccountType> nq = em.createQuery("SELECT a FROM AccountType a WHERE  a.accountTypeIntId = 90",
				AccountType.class);
		return nq.getResultList();

		/*
		 * CriteriaQuery<AccountType> criteriaQuery =
		 * em.getCriteriaBuilder().createQuery(AccountType.class); Root<AccountType> p =
		 * criteriaQuery.from(AccountType.class);
		 * 
		 * Predicate idPredicate =
		 * em.getCriteriaBuilder().equal(p.get(AccountType_.accountTypeIntId), 90);
		 * 
		 * return findAccountTypes(criteriaQuery, idPredicate);
		 */
	}

	public List<AccountType> findTravelAccountTypes() {

		TypedQuery<AccountType> nq = em.createQuery("SELECT a FROM AccountType a WHERE  a.accountTypeIntId = 50",
				AccountType.class);
		return nq.getResultList();

		/*
		 * CriteriaQuery<AccountType> criteriaQuery =
		 * em.getCriteriaBuilder().createQuery(AccountType.class); Root<AccountType> p =
		 * criteriaQuery.from(AccountType.class);
		 * 
		 * Predicate idPredicate =
		 * em.getCriteriaBuilder().equal(p.get(AccountType_.accountTypeIntId), 50);
		 * 
		 * return findAccountTypes(criteriaQuery, idPredicate);
		 */
	}

	/*private List<AccountType> findAccountTypes(CriteriaQuery<AccountType> criteriaQuery, Predicate idPredicate) {

		if (idPredicate != null && criteriaQuery != null) {
			criteriaQuery.where(idPredicate).distinct(true);
		}

		TypedQuery<AccountType> query = em.createQuery(criteriaQuery);

		return query.getResultList();
	}*/
}
