/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.im.IMProductCommission;
import com.insurfact.im.IMRiderFYCCommission;
import com.insurfact.skynet.entity.Product;
import com.insurfact.skynet.entity.ProductCommission;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ProductCommissionFacade extends AbstractFacade<ProductCommission> {

    @Resource(lookup = "jdbc/Orains")
    private DataSource ds;

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public ProductCommissionFacade() {
        super(ProductCommission.class);
    }

    private Connection getConnection() {

        try {

            return ds.getConnection();
        } catch (Exception e) {

            e.printStackTrace();
        }
        return null;
    }

    public List<ProductCommission> findProductCommissionByProduct(Product product) {

        List<ProductCommission> productCommissions = new ArrayList<ProductCommission>();
        List<IMProductCommission> imProductCommissions = new ArrayList<IMProductCommission>();

        if (product == null) {
            System.err.println("#######  ProductCommissionFacade.findProductCommissionByProduct() recieved NULL");
            return null;
        }

        if (product.isProxy()) {
            imProductCommissions = findIMProductCommission(product.getProductId());

            // not IM Product Commissions
            if (imProductCommissions != null && !imProductCommissions.isEmpty()) {

                for (IMProductCommission imCommission : imProductCommissions) {

                    ProductCommission commission = new ProductCommission();

                    commission.setProductCommissionId(imCommission.getProdcommissionid());
                    commission.setCommissionPercent(imCommission.getCommissionpercent());
                    commission.setFromYear(imCommission.getFromyear());
                    commission.setToYear(imCommission.getToyear());
                    commission.setFromDollar((int) imCommission.getFromdollar());
                    commission.setToDollar((int) imCommission.getTodollar());
                    commission.setLastModificationDate(imCommission.getLastupdatedate());
                    commission.setLastUpdateUser(imCommission.getLastupdateuser());
                    commission.setMinage(imCommission.getMinage());
                    commission.setMaxage(imCommission.getMaxage());
                    commission.setPolicyFee(imCommission.getPolicyfee());
                    commission.setMinFace(imCommission.getMinface());
                    commission.setMaxFace(imCommission.getMaxface());
                    commission.setCalcByPremOrComm(imCommission.getCalcbypremorcomm());
                    commission.setStartDate(imCommission.getStartdate());
                    commission.setEndDate(imCommission.getEnddate());
                    commission.setCalcByModal(imCommission.getCalcbymodal());
                    commission.setFormulaCode(imCommission.getFormulacode());
                    commission.setModFactMonth(imCommission.getModfact_month());
                    commission.setModFactSemi(imCommission.getModfact_semi());
                    commission.setModFactQuart(imCommission.getModfact_quart());

                    productCommissions.add(commission);
                }
            }
        } //  NOT PROXY
        else {
            //    
        }

        return productCommissions;
    }

    public List<ProductCommission> findProductCommissionForProduct(int prodId) {
        List<ProductCommission> prodComms = new ArrayList<>();

        return prodComms;
    }

    public List<IMProductCommission> findIMProductCommission(int prodId) {

        IMProductCommission prodComm = null;
        List<IMProductCommission> prodComms = new ArrayList<>();

        Connection connection = getConnection();

        ResultSet rst = null;
        if (connection == null) {
            return prodComms;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select PRODCOMMISSIONID, "
                    + " COMMISSIONPERCENT,"
                    + " FROMYEAR, "
                    + " TOYEAR, "
                    + " FROMDOLLAR,       "
                    + " TODOLLAR,         "
                    + " LASTUPDATEDATE,   "
                    + " LASTUPDATEUSER,   "
                    + " MINAGE,           "
                    + " MAXAGE,           "
                    + " POLICYFEE,        "
                    + " MINFACE,          "
                    + " MAXFACE,          "
                    + " CALCBYPREMORCOMM, "
                    + " STARTDATE,        "
                    + " ENDDATE,          "
                    + "        CALCBYMODAL,      "
                    + "        FORMULACODE,      "
                    + "        MODFACT_MONTH,    "
                    + "        MODFACT_SEMI,     "
                    + "        MODFACT_QUART     "
                    + "   FROM IM_PRODUCTCOMMISSION "
                    + "  WHERE PRODUCTID=\'" + prodId + "\' ORDER BY FROMYEAR";
            System.out.println("sql: " + sql);
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return prodComms;
            }

            while (rst.next()) {

                prodComm = new IMProductCommission();

                prodComm.setProdcommissionid(rst.getInt("PRODCOMMISSIONID"));
                prodComm.setProductid(prodId);

                prodComm.setCommissionpercent(rst.getDouble("COMMISSIONPERCENT"));

                prodComm.setFromyear(rst.getInt("FROMYEAR"));
                prodComm.setToyear(rst.getInt("TOYEAR"));

                prodComm.setFromdollar(rst.getDouble("FROMDOLLAR"));
                prodComm.setTodollar(rst.getDouble("TODOLLAR"));

                prodComm.setLastupdatedate(rst.getDate("LASTUPDATEDATE"));
                prodComm.setLastupdateuser(rst.getString("LASTUPDATEUSER"));

                prodComm.setMinage(rst.getInt("MINAGE"));
                prodComm.setMaxage(rst.getInt("MAXAGE"));

                prodComm.setPolicyfee(rst.getDouble("POLICYFEE"));

                prodComm.setMinface(rst.getDouble("MINFACE"));
                prodComm.setMaxface(rst.getDouble("MAXFACE"));

                prodComm.setCalcbypremorcomm(rst.getString("CALCBYPREMORCOMM"));

                prodComm.setStartdate(rst.getDate("STARTDATE"));
                prodComm.setEnddate(rst.getDate("STARTDATE"));

                prodComm.setCalcbymodal(rst.getString("CALCBYMODAL"));
                prodComm.setFormulacode(rst.getString("FORMULACODE"));

                prodComm.setModfact_month(rst.getDouble("MODFACT_MONTH"));
                prodComm.setModfact_semi(rst.getDouble("MODFACT_SEMI"));
                prodComm.setModfact_quart(rst.getDouble("MODFACT_QUART"));

                prodComms.add(prodComm);

            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (SQLException ex) {
                Logger.getLogger(ProductCommissionFacade.class.getName()).log(Level.SEVERE, null, ex);
            }
            e.printStackTrace();
            return prodComms;
        } // return the connection to the pool

        return prodComms;
    }

    public List<IMProductCommission> findIMProductCommission(int prodId, int issueAge) {

        IMProductCommission prodComm = null;
        List<IMProductCommission> prodComms = new ArrayList<>();

        Connection connection = getConnection();

        ResultSet rst = null;
        if (connection == null) {
            return prodComms;
        }
        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select PRODCOMMISSIONID, "
                    + " COMMISSIONPERCENT,"
                    + " FROMYEAR, "
                    + " TOYEAR, "
                    + " FROMDOLLAR,       "
                    + " TODOLLAR,         "
                    + " LASTUPDATEDATE,   "
                    + " LASTUPDATEUSER,   "
                    + " MINAGE,           "
                    + " MAXAGE,           "
                    + " POLICYFEE,        "
                    + " MINFACE,          "
                    + " MAXFACE,          "
                    + " CALCBYPREMORCOMM, "
                    + " STARTDATE,        "
                    + " ENDDATE,          "
                    + "        CALCBYMODAL,      "
                    + "        FORMULACODE,      "
                    + "        MODFACT_MONTH,    "
                    + "        MODFACT_SEMI,     "
                    + "        MODFACT_QUART     "
                    + "   FROM IM_PRODUCTCOMMISSION "
                    + "  WHERE PRODUCTID=\'" + prodId + "\' ORDER BY FROMYEAR";
            System.out.println("sql: " + sql);
            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return prodComms;
            }

            while (rst.next()) {

                prodComm = new IMProductCommission();

                prodComm.setProdcommissionid(rst.getInt("PRODCOMMISSIONID"));
                prodComm.setProductid(prodId);

                prodComm.setCommissionpercent(rst.getDouble("COMMISSIONPERCENT"));

                prodComm.setFromyear(rst.getInt("FROMYEAR"));
                prodComm.setToyear(rst.getInt("TOYEAR"));

                prodComm.setFromdollar(rst.getDouble("FROMDOLLAR"));
                prodComm.setTodollar(rst.getDouble("TODOLLAR"));

                prodComm.setLastupdatedate(rst.getDate("LASTUPDATEDATE"));
                prodComm.setLastupdateuser(rst.getString("LASTUPDATEUSER"));

                prodComm.setMinage(rst.getInt("MINAGE"));
                prodComm.setMaxage(rst.getInt("MAXAGE"));

                prodComm.setPolicyfee(rst.getDouble("POLICYFEE"));

                prodComm.setMinface(rst.getDouble("MINFACE"));
                prodComm.setMaxface(rst.getDouble("MAXFACE"));

                prodComm.setCalcbypremorcomm(rst.getString("CALCBYPREMORCOMM"));

                prodComm.setStartdate(rst.getDate("STARTDATE"));
                prodComm.setEnddate(rst.getDate("STARTDATE"));

                prodComm.setCalcbymodal(rst.getString("CALCBYMODAL"));
                prodComm.setFormulacode(rst.getString("FORMULACODE"));

                prodComm.setModfact_month(rst.getDouble("MODFACT_MONTH"));
                prodComm.setModfact_semi(rst.getDouble("MODFACT_SEMI"));
                prodComm.setModfact_quart(rst.getDouble("MODFACT_QUART"));

                System.out.println("prodComm.getMinage(): " + prodComm.getMinage());
                System.out.println("prodComm.getMaxage(): " + prodComm.getMaxage());
                System.out.println("issueAge: " + issueAge);

                if (issueAge >= prodComm.getMinage() && issueAge <= prodComm.getMaxage()) {
                    prodComms.add(prodComm);
                } else if (prodComm.getMinage() == 0 && prodComm.getMaxage() == 0) {
                    prodComms.add(prodComm);
                }

            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (SQLException ex) {
                Logger.getLogger(ProductCommissionFacade.class.getName()).log(Level.SEVERE, null, ex);
            }
            e.printStackTrace();
            return prodComms;
        } // return the connection to the pool

        return prodComms;
    }

    public IMProductCommission getIMProductFYCCommission(int prodId) {

        IMProductCommission prodComm = null;
        List<IMProductCommission> prodComms = new ArrayList<>();

        Connection connection = getConnection();

        ResultSet rst = null;

        if (connection == null) {
            return prodComm;
        }

        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select PRODCOMMISSIONID, "
                    + "        COMMISSIONPERCENT,"
                    + "        FROMYEAR,         "
                    + "        TOYEAR,           "
                    + "        FROMDOLLAR,       "
                    + "        TODOLLAR,         "
                    + "        LASTUPDATEDATE,   "
                    + "        LASTUPDATEUSER,   "
                    + "        MINAGE,           "
                    + "        MAXAGE,           "
                    + "        POLICYFEE,        "
                    + "        MINFACE,          "
                    + "        MAXFACE,          "
                    + "        CALCBYPREMORCOMM, "
                    + "        STARTDATE,        "
                    + "        ENDDATE,          "
                    + "        CALCBYMODAL,      "
                    + "        FORMULACODE,      "
                    + "        MODFACT_MONTH,    "
                    + "        MODFACT_SEMI,     "
                    + "        MODFACT_QUART     "
                    + "   FROM IM_PRODUCTCOMMISSION "
                    + "   WHERE PRODUCTID=\'" + prodId + "\' AND FROMYEAR=1 AND TOYEAR=1 ORDER BY FROMYEAR";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return null;
            }

            while (rst.next()) {

                prodComm = new IMProductCommission();

                prodComm.setProdcommissionid(rst.getInt("PRODCOMMISSIONID"));
                prodComm.setProductid(prodId);

                prodComm.setCommissionpercent(rst.getDouble("COMMISSIONPERCENT"));

                prodComm.setFromyear(rst.getInt("FROMYEAR"));
                prodComm.setToyear(rst.getInt("TOYEAR"));

                prodComm.setFromdollar(rst.getDouble("FROMDOLLAR"));
                prodComm.setTodollar(rst.getDouble("TODOLLAR"));

                prodComm.setLastupdatedate(rst.getDate("LASTUPDATEDATE"));
                prodComm.setLastupdateuser(rst.getString("LASTUPDATEUSER"));

                prodComm.setMinage(rst.getInt("MINAGE"));
                prodComm.setMaxage(rst.getInt("MAXAGE"));

                prodComm.setPolicyfee(rst.getDouble("POLICYFEE"));

                prodComm.setMinface(rst.getDouble("MINFACE"));
                prodComm.setMaxface(rst.getDouble("MAXFACE"));

                prodComm.setCalcbypremorcomm(rst.getString("CALCBYPREMORCOMM"));

                prodComm.setStartdate(rst.getDate("STARTDATE"));
                prodComm.setEnddate(rst.getDate("STARTDATE"));

                prodComm.setCalcbymodal(rst.getString("CALCBYMODAL"));
                prodComm.setFormulacode(rst.getString("FORMULACODE"));

                prodComm.setModfact_month(rst.getDouble("MODFACT_MONTH"));
                prodComm.setModfact_semi(rst.getDouble("MODFACT_SEMI"));
                prodComm.setModfact_quart(rst.getDouble("MODFACT_QUART"));

                prodComms.add(prodComm);

            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (SQLException ex) {
                Logger.getLogger(ProductCommissionFacade.class.getName()).log(Level.SEVERE, null, ex);
            }
            e.printStackTrace();
            return null;
        } // return the connection to the pool

        if (prodComms.size() >= 1) {
            return prodComms.get(0);
        }

        return null;
    }

    public IMRiderFYCCommission getIMRiderFYCCommission(int prodId, int riderId) {

        IMRiderFYCCommission riderComm = null;
        List<IMRiderFYCCommission> riderComms = new ArrayList<IMRiderFYCCommission>();

        Connection connection = getConnection();

        ResultSet rst = null;
        if (connection == null) {
            return null;
        }

        try (Statement ptStmt = connection.createStatement();) {

            String sql = " select RIDCOMMISSIONID, "
                    + "        COMMISSIONPERCENT,"
                    + "        FROMYEAR,         "
                    + "        TOYEAR,           "
                    + "        FROMDOLLAR,       "
                    + "        TODOLLAR,         "
                    + "        LASTUPDATEDATE,   "
                    + "        LASTUPDATEUSER,   "
                    + "        MINAGE,           "
                    + "        MAXAGE,           "
                    + "        POLICYFEE,        "
                    + "        MINFACE,          "
                    + "        MAXFACE,          "
                    + "        CALCBYPREMORCOMM, "
                    + "        STARTDATE,        "
                    + "        ENDDATE,          "
                    + "        FORMULACODE      "
                    + "   FROM IM_RIDERCOMMISSION "
                    + "  WHERE PRODUCTID=\'" + prodId + "\' AND RIDERID=\'" + riderId + "\' AND FROMYEAR=1 AND TOYEAR=1 ORDER BY FROMYEAR";

            rst = ptStmt.executeQuery(sql);

            if (rst == null) {
                connection.close();
                return null;
            }

            while (rst.next()) {

                riderComm = new IMRiderFYCCommission();

                riderComm.setRidcommissionid(rst.getInt("RIDCOMMISSIONID"));
                riderComm.setProductid(prodId);
                riderComm.setRiderid(riderId);

                riderComm.setCommissionpercent(rst.getDouble("COMMISSIONPERCENT"));

                riderComm.setFromyear(rst.getInt("FROMYEAR"));
                riderComm.setToyear(rst.getInt("TOYEAR"));

                riderComm.setFromdollar(rst.getInt("FROMDOLLAR"));
                riderComm.setTodollar(rst.getInt("TODOLLAR"));

                riderComm.setLastupdatedate(rst.getDate("LASTUPDATEDATE"));
                riderComm.setLastupdateuser(rst.getString("LASTUPDATEUSER"));

                riderComm.setMinage(rst.getInt("MINAGE"));
                riderComm.setMaxage(rst.getInt("MAXAGE"));

                riderComm.setPolicyfee(rst.getDouble("POLICYFEE"));

                riderComm.setMinface(rst.getDouble("MINFACE"));
                riderComm.setMaxface(rst.getDouble("MAXFACE"));

                riderComm.setCalcbypremorcomm(rst.getString("CALCBYPREMORCOMM"));

                riderComm.setStartdate(rst.getDate("STARTDATE"));
                riderComm.setEnddate(rst.getDate("STARTDATE"));

                riderComm.setFormulacode(rst.getString("FORMULACODE"));

                riderComms.add(riderComm);

            }

            // clean close of the resultset 
            rst.close();
            connection.close();

        } catch (SQLException e) {
            try {
                connection.close();
            } catch (SQLException ex) {
                Logger.getLogger(ProductCommissionFacade.class.getName()).log(Level.SEVERE, null, ex);
            }
            e.printStackTrace();
            return null;
        } // return the connection to the pool

        if (riderComms.size() >= 1) {
            return riderComms.get(0);
        }

        return null;

    }
}
