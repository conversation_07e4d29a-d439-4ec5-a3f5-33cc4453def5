/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Activity;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Client; 
import com.insurfact.skynet.entity.Contact; 
import com.insurfact.skynet.entity.HouseholdContact;
import com.insurfact.skynet.entity.Opportunity;

import com.insurfact.skynet.entity.Organization;

import com.insurfact.skynet.entity.Users;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.*; 
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ContactFacade extends AbstractFacade<Contact> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Resource(lookup = "jdbc/Skytest")
	private DataSource ds;

	public ContactFacade() {
		super(Contact.class);
	}

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

//   
	public List<Contact> findClientAdvisors() {

		TypedQuery<Contact> query;
		query = em.createQuery(
				"SELECT c from Contact c WHERE c.client IS NOT NULL AND c.masterCode = :masterCode AND c.advisor IS NOT NULL",
				Contact.class);
		query.setParameter("masterCode", "MGA-001");
		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Contact> findContactThatCanRecieveDocuments(Organization org) {
		Query query;
		query = em.createNativeQuery(
				"select c.* from contact c INNER JOIN CONTACT_CONTACT_ACTION cca on c.CONTACT_INT_ID = cca.contact_id INNER JOIN CONTACT_ACTION ca on cca.CONTACT_ACTION_ID = ca.CONTACT_ACTION_INT_ID where ca.CONTACT_ACTION_TYPE = 13 and c.ORGANIZATION = "
						+ org.getOrganizationIntId(),
				Contact.class);
		return query.getResultList();
	}

	public void createProfile(Users users) {
		Query nq = em.createNativeQuery("insert into profile_users values (" + users.getUserIntId() + " , "
				+ users.getProfile().getProfileIntId() + " )");
		nq.executeUpdate();
	}

	public List<Contact> findClients() {

		TypedQuery<Contact> query;
		query = em.createQuery(
				"SELECT c from Contact c WHERE c.client IS NOT NULL AND c.masterCode = :masterCode AND c.advisor IS NULL ORDER BY c.firstname, c.lastname",
				Contact.class);
		query.setParameter("masterCode", "MGA-001");
		return query.getResultList();
	}

	public Contact quickEdit(Contact entity) {

//        Address address = entity.getDefaultAddress();
//        Phone phone = entity.getDefaultPhone();
//        Email email = entity.getDefaultEmail();
//        if(address != null){
//            System.out.println("> Address : "+ address.getDescription());
//        }else {
//             System.out.println("> Address : IS NULL");
//        }
//        
//        if(phone != null){
//            System.out.println("> Phone : "+ phone.getDescription());
//        }else {
//             System.out.println("> Phone : IS NULL");
//        }
//        
//        if(email != null){
//            System.out.println("> Email : "+ email.getDescription());
//        }else {
//             System.out.println("> Email : IS NULL");
//        }
		try {
			em.merge(entity);

		} catch (Exception ex) {
			String msg = ex.getLocalizedMessage();
			System.err.println(msg);
		}

		return refresh(entity);
	}

	public Integer resetContactImportFlag(String masterCode) {

		String queryUpdateContact = "UPDATE Contact c SET c.importFlag = 'T' "
				+ " WHERE c.contactType = 1 AND c.masterCode = :master " + "   AND c.customField2Value IS NOT NULL ";

		try {
			Query nq = em.createQuery(queryUpdateContact);
			nq.setParameter("master", masterCode);
			return nq.executeUpdate();

		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	public Contact getSunlifeAdvisorContact(String number) {

		// Contact contact;

		List<Contact> contacts;
		try {
			/*
			 * Query nq = em.createNamedQuery("Contact.findBySunlifeNumber", Contact.class);
			 * System.out.println("line 145 ContactFacade adv number='"+number+"'");
			 * nq.setParameter("sunlifeNumber", number);
			 */

			TypedQuery<Contact> nq = em.createNamedQuery("Contact.findBySunlifeAdvisorId", Contact.class);

			nq.setParameter("sunlifeAdvisor", number);
			// nq.setParameter("org", 1054);

			contacts = nq.getResultList();

			if (contacts != null && !contacts.isEmpty()) {

				int size = contacts.size();

				if (size > 1) {

					System.out.println("$$$$ Duplicated Sunlife Contact Entry : " + number);

					for (Contact c : contacts) {
						if (c.getUsers() != null && size > 1) {
							System.out.println("### Duplicated Sunlife Contact Entry | TAKING VALID USERS : " + number);
							return c;
						}
					}

					System.out.println("### Duplicated Sunlife Contact Entry | TAKING FIRST : " + number);

					return contacts.get(0);
				}

				if (size == 1) {
					return contacts.get(0);
				}
			}

		} catch (NoResultException e) {
			return null;
		}

		return null;
	}

	public List<Contact> findAllForOrganization(Organization org) {

		List<Contact> contacts;
		try {
			TypedQuery<Contact> nq = getEntityManager().createNamedQuery("Contact.findAllForOrganization",
					Contact.class);

			nq.setParameter("org", org);

			contacts = nq.getResultList();

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	/**
	 * Find all Contacts for a given MasterCode with type, Lead, Prospect, Applicant
	 * and Client
	 *
	 * @param code
	 * @return
	 */
	public List<Contact> findByMasterCode(String code) {

		ResultSet rst = null;

		Connection connection = getConnection();

		if (connection == null) {
			return new ArrayList<>();
		}

		List<Contact> contacts = new ArrayList<>();

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select c.CONTACT_INT_ID, c.BIRTH_DATE, c.CONTACT_TYPE, c.CUSTOM_FIELD2_VALUE, c.FIRSTNAME, c.LASTNAME, c.PUBLIC_NAME, c.GENDER, c.CONTACT_SOURCE "
					+ "from Contact c where  c.MASTER_CODE='" + code + "' and c.contact_type in (10,15,16,17)";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return contacts;
			}

			while (rst.next()) {

				Contact contact = new Contact();

				contact.setContactIntId(rst.getInt(1));
				contact.setBirthDate(rst.getDate(2));
				contact.setContactType(rst.getInt(3));

				contact.setCustomField2Value(rst.getString(4));

				contact.setFirstname(rst.getString(5));
				contact.setLastname(rst.getString(6));
				contact.setPublicName(rst.getString(7));
				contact.setGender(rst.getInt(8));
				contact.setContactSource(rst.getInt(9));

				contacts.add(contact);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ContactFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return contacts;
		} // return the connection to the pool

		return contacts;
	}

	public Contact getContactByContactIntId(int contactIntId) {

		try {
			Query nq = em.createNamedQuery("Contact.findByContactIntId", Contact.class);
			nq.setParameter("contactIntId", contactIntId);
			return (Contact) nq.getSingleResult();

		} catch (NoResultException e) {
			return null;
		}

	}

	public List<Contact> getContactByCode(String masterCode) {

		try {
			TypedQuery<Contact> nq = em.createNamedQuery("Contact.findByCode", Contact.class);
			nq.setParameter("masterCode", masterCode);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}

	}

	@SuppressWarnings("unchecked")
	public List<Contact> findContactsByBranch(String financialCenters) {

		try {
			Query nq = em.createNativeQuery("select * from contact where branch in " + financialCenters, Contact.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}

	}

	@SuppressWarnings("unchecked")
	public List<Contact> findContactsByBranchInforce(String financialCenters) {

		try {
			Query nq = em.createNativeQuery(
					"SELECT DISTINCT adv.* FROM contact adv inner join ADVISOR_CLIENT advCli on adv.CONTACT_INT_ID = advCli.ADVISOR INNER JOIN client cli on advCli.CLIENT = cli.CLIENT_INT_ID INNER JOIN ACCOUNT_CLIENT_OWNERSHIP accCliOwn on cli.CLIENT_INT_ID = accCliOwn.client INNER JOIN POLICY pol on pol.POLICY_INT_ID = accCliOwn.POLICY where pol.POLICY_STATUS in (3,6,11,14,18,30,32,36,40,43,44,45) and adv.branch in "
							+ financialCenters,
					Contact.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}

	}

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (Exception e) {

			e.printStackTrace();
		}
		return null;
	}

	public List<Contact> findByLastContactedPeriod(Date fromDate, Date toDate) {
		
		TypedQuery<Contact> nq = em.createQuery("SELECT distinct a FROM Contact a WHERE  a.firstContacted between :fromDate and :toDate",
				Contact.class);
		nq.setParameter("fromDate", fromDate, TemporalType.TIMESTAMP);
		nq.setParameter("toDate", toDate, TemporalType.TIMESTAMP);
		return nq.getResultList();

		/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<Contact> criteriaQuery = cBuilder.createQuery(Contact.class);
		Root<Contact> p = criteriaQuery.from(Contact.class);

		Predicate lastContactedPredicate = cBuilder.between(p.get(Contact_.firstContacted), fromDate, toDate);

		criteriaQuery.where(lastContactedPredicate).distinct(true);
		TypedQuery<Contact> query = em.createQuery(criteriaQuery);

		return query.getResultList();*/
	}

	public Contact refresh(Contact contact) {
		return find(contact.getContactIntId());
	}

	/**
	 * Search Contact by primary key
	 *
	 * @param contactIntId
	 * @return
	 */
	public Contact findByContactId(Integer contactIntId) {
		try {
			Query nq = getEntityManager().createNamedQuery("Contact.findByContactIntId", Contact.class);
			nq.setParameter("contactIntId", contactIntId);

			Contact contact = (Contact) nq.getSingleResult();

			return contact;

		} catch (Exception e) {

			return null;
		}
	}

	public List<Contact> findActiveUsersContactByTypeRestricted(Integer type, String masterCode) {

		List<Contact> contacts;
		try {
			TypedQuery<Contact> nq = em.createNamedQuery("Contact.findByTypeRestricted", Contact.class);
			nq.setParameter("type", type);
			nq.setParameter("masterCode", masterCode);

			contacts = nq.getResultList();

			Iterator<Contact> iterator = contacts.iterator();

			while (iterator.hasNext()) {
				Contact contact = iterator.next();

				Users users = contact.getUsers();

				if (users == null) {
					continue;
				}

				// filter out inactive Users
				if (!users.isActive()) {
					iterator.remove();
				}
			}

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	public List<Contact> findSunlifeContacts() {

		List<Contact> contacts;

		try {
			TypedQuery<Contact> nq = getEntityManager().createNamedQuery("Contact.findSunlifeContacts", Contact.class);

			contacts = nq.getResultList();

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	public List<Contact> findInactivatedSunlifeContactsRestricted(String masterCode) {

		List<Contact> contacts;
		try {
			TypedQuery<Contact> nq = getEntityManager()
					.createNamedQuery("Contact.findInactivatedSunlifeContactsRestricted", Contact.class);

			nq.setParameter("masterCode", masterCode);

			contacts = nq.getResultList();

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	public List<Contact> findContactByFlagRestricted(String flag, String masterCode) {

		List<Contact> contacts;
		try {
			TypedQuery<Contact> nq = getEntityManager().createQuery(
					"SELECT c FROM Contact c WHERE c.importFlag = :flag AND c.masterCode = :masterCode", Contact.class);
			nq.setParameter("flag", flag);
			nq.setParameter("masterCode", masterCode);

			contacts = nq.getResultList();

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	/**
	 * find all contacts for a given MasterCode and type
	 *
	 * @param type
	 * @param masterCode
	 * @return
	 */
	public List<Contact> findContactByTypeRestricted(Integer type, String masterCode) {

		List<Contact> contacts;
		try {
			TypedQuery<Contact> nq = getEntityManager().createNamedQuery("Contact.findByTypeRestricted", Contact.class);
			nq.setParameter("type", type);
			nq.setParameter("masterCode", masterCode);

			contacts = nq.getResultList();

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	/**
	 * find all contacts for a given MasterCode and type
	 *
	 * @param type
	 * @return
	 */
	public List<Contact> findContactByType(Integer type) {

		List<Contact> contacts;
		try {
			TypedQuery<Contact> nq = getEntityManager().createNamedQuery("Contact.findByType", Contact.class);
			nq.setParameter("type", type);

			contacts = nq.getResultList();

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	public List<Contact> findProvidersByType(Integer type) {

		List<Contact> contacts;
		try {
			TypedQuery<Contact> nq = getEntityManager().createNamedQuery("Contact.findByProviderType", Contact.class);
			nq.setParameter("type", type);

			contacts = nq.getResultList();

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	/**
	 * find all contacts for a given MasterCode and type
	 *
	 * @param masterCode
	 * @param types
	 * @return
	 */
	public List<Contact> findContactByTypesRestricted(String masterCode, List<Integer> types) {

		List<Contact> contacts;
		try {
			TypedQuery<Contact> nq = getEntityManager().createQuery(
					"SELECT c from Contact c WHERE c.masterCode = :masterCode ORDER BY c.firstname, c.lastname",
					Contact.class);
			nq.setParameter("masterCode", masterCode);

			contacts = nq.getResultList();

			Iterator<Contact> iterator = contacts.iterator();

			// filter out
			while (iterator.hasNext()) {

				Contact c = iterator.next();

				if (c.getContactType() == null || !types.contains(c.getContactType())) {
					iterator.remove();
				}

			}

		} catch (Exception e) {

			return new ArrayList<>();
		}

		return contacts;
	}

	public List<Contact> findContactUsersByTypeAndEmployeeNumberRestricted(String number, String masterCode,
			Integer type) {

		// long now = System.nanoTime();

		String name = "%" + number.toUpperCase() + "%";

		TypedQuery<Contact> query = em.createQuery(
				"SELECT c from Contact c WHERE c.masterCode = :masterCode AND c.contactType = :type AND c.customField2Value like :number",
				Contact.class);
		query.setParameter("masterCode", masterCode);
		query.setParameter("number", name);
		query.setParameter("type", type);

		List<Contact> contacts = query.getResultList();

		return contacts;
	}

	public List<Contact> findContactUsersByTypeAndNamesRestricted(String contactName, String masterCode, Integer type,
			Advisor aga) {

		// long now = System.nanoTime();

		String[] names = contactName.split(" ");

		TypedQuery<Contact> query;

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";
			query = em.createQuery("SELECT c from Contact c WHERE c.masterCode = :masterCode "
					+ "   AND c.advisor IS NOT NULL AND c.advisor.status = 1    "
					+ "   AND c.advisor in (select a.advisor from AgaAdvisors a where a.aga= :agaID) "
					+ "   AND (UPPER(c.firstname) LIKE :name OR UPPER(c.lastname) LIKE :name) "
					+ "   AND c.customField2Value != null ", Contact.class);

			query.setParameter("masterCode", masterCode);
			query.setParameter("name", name);
			query.setParameter("agaID", aga);
		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			query = em.createQuery("SELECT c from Contact c WHERE  c.masterCode = :masterCode "
					+ "   AND c.advisor IS NOT NULL AND c.advisor.status = 1    "
					+ "   AND c.advisor in (select a.advisor from AgaAdvisors a where a.aga= :agaID) "
					+ "   AND UPPER(c.firstname) LIKE :firstname AND UPPER(c.lastname) LIKE :lastname "
					+ "   AND c.customField2Value != null ", Contact.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);
			query.setParameter("agaID", aga);
		}

		List<Contact> contacts = query.getResultList();

		return contacts;
	}

	/**
	 * find all Contacts for a MasterCode, type and name(s)
	 *
	 * @param contactName
	 * @param masterCode
	 * @param type
	 * @return
	 */
	public List<Contact> findContactByTypeAndNamesRestricted(String contactName, String masterCode, Integer type) {

		//long now = System.nanoTime();

		String[] names = contactName.split(" ");

		TypedQuery<Contact> query;

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE c.advisor IS NOT NULL AND c.advisor.status = 1 AND c.masterCode = :masterCode AND c.contactType = :type AND (UPPER(c.firstname) LIKE :name OR UPPER(c.lastname) LIKE :name)",
					Contact.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("name", name);
			query.setParameter("type", type);
		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE c.advisor IS NOT NULL AND c.advisor.status = 1 AND c.masterCode = :masterCode AND c.contactType = :type AND UPPER(c.firstname) LIKE :firstname AND UPPER(c.lastname) LIKE :lastname",
					Contact.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);
			query.setParameter("type", type);

		}

		List<Contact> contacts = query.getResultList();

		return contacts;
	}

	public List<Contact> findContactByTypesAndNamesRestricted(String contactName, String masterCode,
			List<Integer> types) {

		//long now = System.nanoTime();

		String[] names = contactName.split(" ");

		TypedQuery<Contact> query;

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE  c.masterCode = :masterCode AND (UPPER(c.firstname) LIKE :name OR UPPER(c.lastname) LIKE :name)",
					Contact.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("name", name);

		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE c.masterCode = :masterCode AND UPPER(c.firstname) LIKE :firstname AND UPPER(c.lastname) LIKE :lastname",
					Contact.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);

		}

		List<Contact> contacts = query.getResultList();

		Iterator<Contact> iterator = contacts.iterator();

		while (iterator.hasNext()) {

			Contact c = iterator.next();

			if (c.getContactType() == null || !types.contains(c.getContactType())) {
				iterator.remove();
			}

		}

		return contacts;
	}

	public List<Contact> findContactByTypesAndNames(String contactName, List<Integer> types) {

		//long now = System.nanoTime();

		String[] names = contactName.split(" ");

		TypedQuery<Contact> query;

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE  c.masterCode != 'TO_BE_DELETED' AND (UPPER(c.firstname) LIKE :name OR UPPER(c.lastname) LIKE :name)",
					Contact.class);
			query.setParameter("name", name);

		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE c.masterCode != 'TO_BE_DELETED'  AND UPPER(c.firstname) LIKE :firstname AND UPPER(c.lastname) LIKE :lastname",
					Contact.class);

			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);

		}

		List<Contact> contacts = query.getResultList();

		Iterator<Contact> iterator = contacts.iterator();

		while (iterator.hasNext()) {

			Contact c = iterator.next();

			if (c.getContactType() == null || !types.contains(c.getContactType())) {
				iterator.remove();
			}

		}

		return contacts;
	}

	public List<Contact> findContactByNames(String contactName) {

		//long now = System.nanoTime();

		String[] names = contactName.split(" ");

		TypedQuery<Contact> query;

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE  c.masterCode != 'TO_BE_DELETED' AND (UPPER(c.firstname) LIKE :name OR UPPER(c.lastname) LIKE :name)",
					Contact.class);

			query.setParameter("name", name);

		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE c.masterCode != 'TO_BE_DELETED'  AND UPPER(c.firstname) LIKE :firstname AND UPPER(c.lastname) LIKE :lastname",
					Contact.class);

			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);

		}

		List<Contact> contacts = query.getResultList();

		return contacts;
	}

	public List<Contact> findContactByNamesRestricted(String contactName, String masterCode) {

		//long now = System.nanoTime();

		String[] names = contactName.split(" ");

		TypedQuery<Contact> query;

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE c.masterCode = :masterCode AND UPPER(c.firstname) LIKE :name OR UPPER(c.lastname) LIKE :name",
					Contact.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("name", name);
		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Contact c WHERE c.masterCode = :masterCode AND (UPPER(c.firstname) LIKE :firstname AND UPPER(c.lastname) LIKE :lastname)",
					Contact.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);
		}

		List<Contact> contacts = query.getResultList();

		return contacts;
	}

	public List<Client> findClientsByNamesRestricted(String contactName, String masterCode) {

		//long now = System.nanoTime();

		String[] names = contactName.split(" ");

		TypedQuery<Client> query;

		if (names.length == 1) {
			String name = contactName.toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Client c WHERE c.contact.masterCode = :masterCode AND UPPER(c.contact.firstname) LIKE :name OR UPPER(c.contact.lastname) LIKE :name",
					Client.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("name", name);
		} else {
			String firstname = names[0].toUpperCase() + "%";
			String lastname = names[1].toUpperCase() + "%";
			query = em.createQuery(
					"SELECT c from Client c WHERE c.contact.masterCode = :masterCode AND (UPPER(c.contact.firstname) LIKE :firstname AND UPPER(c.contact.lastname) LIKE :lastname)",
					Client.class);
			query.setParameter("masterCode", masterCode);
			query.setParameter("firstname", firstname);
			query.setParameter("lastname", lastname);
		}

		List<Client> clients = query.getResultList();

		return clients;
	}

	public Contact searchExistingContact(String contactName, String lastName, Date birthDate) {
		Contact result;
		Query query;
		//String[] names = contactName.split(" ");
		Calendar bday = Calendar.getInstance();
		bday.setTime(birthDate);
		String year = String.valueOf(bday.get(Calendar.YEAR) + 1900);
		String month = String.valueOf(bday.get(Calendar.MONTH) + 1);
		String day = String.valueOf(bday.get(Calendar.DATE));

		if (month.length() == 1) {
			month = "0" + month;
		}
		if (day.length() == 1) {
			day = "0" + day;
		}

		query = em.createNativeQuery(" SELECT * from CONTACT where UPPER(FIRSTNAME) like UPPER('"
				+ contactName.toUpperCase() + "') and UPPER(LASTNAME) like UPPER('" + lastName.toUpperCase()
				+ "') and EXTRACT(YEAR FROM BIRTH_DATE) = '" + year + "' and EXTRACT(MONTH FROM BIRTH_DATE) = '" + month
				+ "' and EXTRACT(DAY FROM BIRTH_DATE) = '" + day + "' ", Contact.class);
		query.setMaxResults(1);
		if (!query.getResultList().isEmpty()) {
			result = (Contact) query.getSingleResult();
			return result;
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	public void deleteContact(Contact contact) {
		// delete all oportunities for that contact
		List<Opportunity> oList = new ArrayList<>();
		List<Activity> aList = new ArrayList<>();
		List<HouseholdContact> houseList = new ArrayList<>();
		Query nq;

		try {
			nq = em.createNativeQuery(
					"SELECT o.* FROM OPPORTUNITY o INNER JOIN CONTACTS_RELATIONSHIP cr ON o.CONTACTS_RELATIONSHIP = cr.CONTACTS_RELATIONSHIP_INT_ID where cr.FIRST_CONTACT_INT_ID  = "
							+ contact.getContactIntId(),
					Opportunity.class);
			oList = nq.getResultList();

		} catch (NoResultException e) {
		}
		for (Opportunity o : oList) {
			// remove all oportunity activities
			try {
				nq = getEntityManager().createNativeQuery(
						"SELECT * FROM ACTIVITY WHERE TYPE = 1 and TYPE_ID = " + o.getOpportunityIntId(),
						Activity.class);
				aList = nq.getResultList();

			} catch (NoResultException e) {
			}
			for (Activity a : aList) {

				em.remove(a);

			}

			// remove all contactRelationship
			em.remove(o.getContactsRelationship());

			// remove the oportunity
			em.remove(o);

		}

		// remove all contact activities
		try {
			nq = getEntityManager().createNativeQuery(
					"SELECT * FROM ACTIVITY WHERE TYPE = 3 and TYPE_ID = " + contact.getContactIntId(), Activity.class);
			aList = nq.getResultList();

		} catch (NoResultException e) {
		}
		for (Activity a : aList) {

			em.remove(a);

		}

		// remove it from hosehold
		try {
			nq = getEntityManager().createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = "
					+ contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId(),
					HouseholdContact.class);
			houseList = nq.getResultList();

		} catch (NoResultException e) {
		}
		for (HouseholdContact h : houseList) {

			if (h.getContact1().equals(contact) && contact.getHouseholdType() == 0) {
				h.getContact2().setHouseholdType(0);
				em.merge(h.getContact2());
			} else if (h.getContact2().equals(contact) && contact.getHouseholdType() == 0) {
				h.getContact1().setHouseholdType(0);
				em.merge(h.getContact1());
			}
			em.remove(h);

		}

		// remove the contact
		em.remove(getEntityManager().merge(contact));

	}

	@SuppressWarnings("unchecked")
	public void deleteContactAll(List<Contact> contacts) {
		// delete all oportunities for that contact
		List<Opportunity> oList = new ArrayList<>();
		List<Activity> aList = new ArrayList<>();
		List<HouseholdContact> houseList = new ArrayList<>();
		Query nq;

		for (Contact contact : contacts) {

			try {
				nq = em.createNativeQuery(
						"SELECT o.* FROM OPPORTUNITY o INNER JOIN CONTACTS_RELATIONSHIP cr ON o.CONTACTS_RELATIONSHIP = cr.CONTACTS_RELATIONSHIP_INT_ID where cr.FIRST_CONTACT_INT_ID  = "
								+ contact.getContactIntId(),
						Opportunity.class);
				oList = nq.getResultList();

			} catch (NoResultException e) {
			}
			for (Opportunity o : oList) {
				// remove all oportunity activities
				try {
					nq = getEntityManager().createNativeQuery(
							"SELECT * FROM ACTIVITY WHERE TYPE = 1 and TYPE_ID = " + o.getOpportunityIntId(),
							Activity.class);
					aList = nq.getResultList();

				} catch (NoResultException e) {
				}
				for (Activity a : aList) {

					em.remove(a);

				}

				// remove all contactRelationship
				em.remove(o.getContactsRelationship());

				// remove the oportunity
				em.remove(o);

			}

			// remove all contact activities
			try {
				nq = getEntityManager().createNativeQuery(
						"SELECT * FROM ACTIVITY WHERE TYPE = 3 and TYPE_ID = " + contact.getContactIntId(),
						Activity.class);
				aList = nq.getResultList();

			} catch (NoResultException e) {
			}
			for (Activity a : aList) {

				em.remove(a);

			}

			// remove it from hosehold
			try {
				nq = getEntityManager().createNativeQuery("SELECT * FROM HOUSEHOLD_CONTACT where CONTACT1 = "
						+ contact.getContactIntId() + " or CONTACT2 = " + contact.getContactIntId(),
						HouseholdContact.class);
				houseList = nq.getResultList();

			} catch (NoResultException e) {
			}
			for (HouseholdContact h : houseList) {

				em.remove(h);

			}

			// remove the contact
			em.remove(getEntityManager().merge(contact));

		}
	}

}
