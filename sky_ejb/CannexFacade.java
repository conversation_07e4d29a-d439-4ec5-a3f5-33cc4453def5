/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.avue.navigation.ApplicationMainBean;
import com.insurfact.avue.navigation.shop4rates.Shop4RatesMortgageSearchParameter;
import com.insurfact.cannex.entity.CannexCompany;
import com.insurfact.cannex.entity.MortProduct;
import com.insurfact.cannex.entity.RrifProduct;
import com.insurfact.cannex.entity.TermProduct;
import com.insurfact.cannex.entity.TermsInterestRate;
import com.insurfact.fundata.entity.BestCIG;
import com.insurfact.skynet.constant.Constants;
import com.insurfact.skynet.entity.CompanySelection;

import com.insurfact.skynet.entity.Province;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.sql.DataSource;

import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

import com.insurfact.skynet.entity.Users;
import jakarta.inject.Inject;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CannexFacade extends AbstractFacade<CannexCompany> {

	@Inject
	ApplicationMainBean applicationBean;

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Resource(lookup = "jdbc/Skytest")
	private DataSource ds;

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (SQLException e) {

			e.printStackTrace();
		}
		return null;
	}

	public CannexFacade() {
		super(CannexCompany.class);
	}

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public List<TermsInterestRate> find10BestGICS() {
		List<TermsInterestRate> rates = new ArrayList<>();

		TermsInterestRate result = null;

		// 30 days
		result = getBestGICInterestRate(0, 1000, "N", "N", "E", "30");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 30 days : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 30 days");
		}

		// 60 days
		result = getBestGICInterestRate(0, 1000, "N", "N", "E", "60");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 60 days : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 60 days");
		}

		// 90 days
		result = getBestGICInterestRate(0, 1000, "N", "N", "E", "90");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 90 days : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 90 days");
		}

		// 1 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "1");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 1 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 1 Year");
		}

		// 2 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "2");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 2 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 2 Year");
		}

		// 3 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "3");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 3 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 3 Year");
		}

		// 4 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "4");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 4 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 4 Year");
		}

		// 5 Year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "5");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 5 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 5 Year");
		}

		// 6 year
		result = getBestGICInterestRate(1, 1000, "N", "A", "E", "6");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 6 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 6 Year");
		}

		// 10 Year
		result = getBestGICInterestRate(4, 1000, "N", "A", "E", "10");

		if (result != null) {
			rates.add(result);
//            System.out.println("*** Found a Product for term 10 Year : " + result.getTermProduct().getCannexCompany().getCompanyDescEn());
		} else {
			System.out.println("### unable to find a Product for term 10 Year");
		}

		return rates;

	}

	public List<BestCIG> fetchBestCIGs(String provinceCode,String codeTax) {
		Connection connection = null;
		ResultSet rst = null;
		List<BestCIG> array = new ArrayList<>(); 
		
		String sql = "SELECT \n" +
			    "    company_desc_en,\n" +
			    "    company_desc_fr, \n" +
			    "    province,\n" +
			    "    term_value,\n" +
			    "    term_duration,\n" +
			    "    change,\n" +
			    "    change_date_String,\n" +
			    "    interest_rate_value,\n" +
			    "    tax_indicator, \n" +
			    "    compound_frequency, \n" +
			    "    payment_frequency, \n" +
			    "    redeemability,\n" +
			    "    min_amount, \n" +
			    "    company_website_rate, \n"+
			    "    max_amount \n" +
			    "FROM (\n" +
			    "    SELECT \n" +
			    "        c.company_desc_en,\n" +
			    "        c.company_desc_fr, \n" +
			    "        x.province,\n" +
			    "        i.term_value,\n" +
			    "        i.term_duration,\n" +
			    "        i.change,\n" +
			    "        i.change_date_String,\n" +
			    "        i.interest_rate_value,\n" +
			    "        p.tax_indicator, \n" +
			    "        p.compound_frequency, \n" +
			    "        p.payment_frequency, \n" +
			    "        p.redeemability,\n" +
			    "        p.min_amount, \n" +
			    "        c.company_website_rate, \n" +
			    "        p.max_amount,\n" +
			    "        ROW_NUMBER() OVER (PARTITION BY i.term_value ORDER BY i.interest_rate_value DESC) AS rn\n" +
			    "    FROM \n" +
			    "        CANNEX_PRODUCT_new p\n" +
			    "        JOIN CANNEX_INTEREST_RATE_new_TERM i ON p.prod_int_id = i.cannex_product\n" +
			    "        JOIN CANNEX_COMPANY_NEW c ON p.cannex_company = c.company_int_id\n" +
			    "        JOIN CANNEX_COMPANY_PROVINCE x ON c.IPNO = x.IPNO\n" +
			    "    WHERE \n" +
			    "        p.product_type = 'TERM' \n" +
			    "        AND p.tax_indicator = '" + codeTax + "' \n" +
			    "        AND p.redeemability IN ('R', 'N', 'C')\n" +
			    "        AND p.compound_frequency IN ('A', 'N')\n" +
			    "        AND p.payment_frequency IN ('A', 'E')\n" +
			    "        AND p.term_type IN ('0', '1', '4')\n" +
			    "        AND 5000 BETWEEN p.min_amount AND p.max_amount\n" +
			    "        AND x.province = '" + provinceCode + "'\n" +
			    "        AND i.term_value IN ('30', '60', '90', '180', '1', '2', '3', '4', '5', '10')\n" +
			    ") subquery\n" +
			    "WHERE rn = 1\n" +
			    "ORDER BY \n" +
			    "    company_desc_en,\n" +
			    "    term_duration,\n" +
			    "    term_value,\n" +
			    "    interest_rate_value,\n" +
			    "    min_amount, \n" +
			    "    max_amount";


		 

		connection = getConnection(); // Assume getConnection() method returns a valid database connection
		if (connection != null) {
			try (Statement ptStmt = connection.createStatement()) {
				rst = ptStmt.executeQuery(sql);

				while (rst.next()) {
					// Create a new instance of BestCIG for each row
					BestCIG bestCIG = new BestCIG();

					// Populate the BestCIG object with data from the ResultSet
					bestCIG.setCompanyDescEn(rst.getString("company_desc_en"));
					bestCIG.setCompanyDescFr(rst.getString("company_desc_fr"));
					bestCIG.setProvince(rst.getString("province"));
					bestCIG.setChange(rst.getDouble("change"));
					bestCIG.setChangeDateString(rst.getString("change_date_string"));
					bestCIG.setTermValue(rst.getInt("term_value"));
					bestCIG.setTermDuration(rst.getString("term_duration"));
					bestCIG.setInterestRateValue(rst.getDouble("interest_rate_value"));
					bestCIG.setTaxIndicator(rst.getString("tax_indicator"));
					bestCIG.setCompoundFrequency(rst.getString("compound_frequency"));
					bestCIG.setPaymentFrequency(rst.getString("payment_frequency"));
					bestCIG.setRedeemability(rst.getString("redeemability"));
					bestCIG.setMinAmount(rst.getDouble("min_amount"));
					bestCIG.setMaxAmount(rst.getDouble("max_amount"));
					bestCIG.setWebsite(rst.getString("company_website_rate"));

					// Add the populated BestCIG object to the list
					array.add(bestCIG);
				}
			} catch (SQLException e) {
				e.printStackTrace();
			} finally {
				try {
					if (connection != null) {
						connection.close();
					}
				} catch (SQLException ex) {
					Logger.getLogger(CompanyFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
			}
		}
		return array;
	}

	// productTypeId
	// GIC = 1 (TERM + TAX=N)
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years
	public TermsInterestRate getBestGICInterestRate(Integer termDuration, Integer amount, String redeem,
			String compound, String payment, String termValue) {

		TypedQuery<TermsInterestRate> query = null;

		System.out.println(
				"CannexFacade.getBestGICProduct() | duration=" + termDuration + " amount=" + amount + " redeem="
						+ redeem + " compound=" + compound + " payment_freq=" + payment + " termValue=" + termValue);

		query = em.createQuery("SELECT r FROM TermsInterestRate r " + "WHERE r.termValue = :termValue "
				+ "AND r.termProduct.productTypeId=1  " + "AND r.termProduct.termTypeId = :duration "
				+ "AND :deposit BETWEEN r.termProduct.minAmount AND r.termProduct.maxAmount "
				+ "AND r.termProduct.redeemability = :redeem " + "AND r.termProduct.compoundFrequency = :compound "
				+ "AND r.termProduct.paymentFrequency = :payment " + "ORDER BY r.interestRateValue DESC ",
				TermsInterestRate.class);

		query.setParameter("termValue", termValue);
		query.setParameter("duration", termDuration);
		query.setParameter("deposit", amount);
		query.setParameter("redeem", redeem);
		query.setParameter("compound", compound);
		query.setParameter("payment", payment);

		query.setMaxResults(1);

		List<TermsInterestRate> results = query.getResultList();
		TermsInterestRate best = null;

		if (results != null && !results.isEmpty()) {
			best = results.get(0);
		}

		return best;
	}

	public List<TermProduct> findTermProducts(CannexCompany company) {
		TypedQuery<TermProduct> query = null;

		query = em.createQuery(
				"SELECT p FROM TermProduct p WHERE p.cannexCompany = :company and p.productTypeId in(1,2) ORDER by p.termTypeId",
				TermProduct.class);
		query.setParameter("company", company);

		return query.getResultList();
	}

	public List<RrifProduct> findRrifProducts(CannexCompany company) {
		TypedQuery<RrifProduct> query = null;
		String sql = " SELECT p FROM RrifProduct p       " + "  WHERE p.cannexCompany = :company "
				+ "    and p.productTypeId = 3        " + "  ORDER by p.termTypeId ";
		query = em.createQuery(sql, RrifProduct.class);
		query.setParameter("company", company);

		return query.getResultList();
	}

	// productTypeId
	// GIC = 1 (TERM + TAX=N)
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years
	public List<TermProduct> findGICProducts(Integer cannexCompanyType, Province province, Integer termDuration,
			Integer amount, String redeem, String compound, String payment) {

		Users user = applicationBean.getUsers();

		TypedQuery<TermProduct> query = null;
		String sql = "";
//        System.out.println("CannexFacade.findGICProducts()  FULL  | cie_type="+ cannexCompanyType +" duration="+termDuration + " amount="+amount+" redeem="+redeem +" compound="+compound+ " payment_freq="+payment);

		if (cannexCompanyType != null) {
			sql = " SELECT p FROM TermProduct p                " + "  WHERE p.cannexCompany.companyType = :type "
					+ "    AND :province MEMBER OF p.cannexCompany.provinceList "
					+ "    AND p.productTypeId = 1 AND p.termTypeId = :duration "
					+ "    AND :deposit BETWEEN p.minAmount AND p.maxAmount     "
					+ "    AND p.redeemability = :redeem                        "
					+ "    AND p.compoundFrequency = :compound                  "
					+ "    AND p.paymentFrequency = :payment ";
			query = em.createQuery(sql, TermProduct.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", termDuration);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);
		} else {

			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents

				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null) {
						if (compSel.getShop4rates().equalsIgnoreCase("y")) {
							compIds.add(compSel.getCompanyId());
							System.out.println("line288: " + compSel.getCompanyId());
						}
					}
					System.out.println("line290: " + compIds.toString());
				}
				query = em.createQuery("SELECT p FROM TermProduct p "
						+ " WHERE :province MEMBER OF p.cannexCompany.provinceList "
						+ "   AND p.cannexCompany.companyId IN :compID  "
						+ "   AND p.productTypeId = 1  AND p.termTypeId = :duration "
						+ "   AND :deposit BETWEEN p.minAmount AND p.maxAmount " + "   AND p.redeemability = :redeem "
						+ "   AND p.compoundFrequency = :compound " + "   AND p.paymentFrequency = :payment",
						TermProduct.class);

				query.setParameter("compID", compIds);
				query.setParameter("province", province);
				query.setParameter("duration", termDuration);
				query.setParameter("deposit", amount);
				query.setParameter("redeem", redeem);
				query.setParameter("compound", compound);
				query.setParameter("payment", payment);

			} else {
				query = em.createQuery(
						"SELECT p FROM TermProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList "
								+ "AND p.productTypeId = 1  AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount "
								+ "AND p.redeemability = :redeem AND p.compoundFrequency = :compound "
								+ "AND p.paymentFrequency = :payment",
						TermProduct.class);

				query.setParameter("province", province);
				query.setParameter("duration", termDuration);
				query.setParameter("deposit", amount);
				query.setParameter("redeem", redeem);
				query.setParameter("compound", compound);
				query.setParameter("payment", payment);

			}
		}

		return query.getResultList();
	}

	// productTypeId
	// GIC = 1 (TERM + TAX=N)
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years
	public List<TermProduct> findGICProducts(Integer cannexCompanyType, Province province, Integer termDuration,
			Integer amount) {

		Users user = applicationBean.getUsers();

		TypedQuery<TermProduct> query = null;

//        System.out.println("CannexFacade.findGICProducts()  QUICK  | cie_type="+ cannexCompanyType +" duration="+termDuration +" amount="+amount );
		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM TermProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 1 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
					TermProduct.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", termDuration);
			query.setParameter("deposit", amount);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents

				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null) {
						if (compSel.getShop4rates().equalsIgnoreCase("y")) {
							compIds.add(compSel.getCompanyId());
							System.out.println("line 356: " + compSel.getCompanyId());
						}
					}

				}
				System.out.println("line 356: " + compIds.toString());
				query = em.createQuery(
						"SELECT p FROM TermProduct p WHERE p.productTypeId = 1 AND p.cannexCompany.companyId IN :compID AND :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						TermProduct.class);
				query.setParameter("compID", compIds);
				System.out.println("line 364: ");
			} else {
				query = em.createQuery(
						"SELECT p FROM TermProduct p WHERE p.productTypeId = 1 AND :province MEMBER OF p.cannexCompany.provinceList AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						TermProduct.class);
			}

			query.setParameter("duration", termDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
		}

		return query.getResultList();
	}

	// productTypeId
	// RRSP = 2 (TERM + TAX=R
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years
	public List<TermProduct> findRRSPProducts(Integer cannexCompanyType, Province province, Integer termDuration,
			Integer amount, String redeem, String compound, String payment) {

		Users user = applicationBean.getUsers();

		TypedQuery<TermProduct> query = null;

//        System.out.println("CannexFacade.findRRSPProducts()  FULL  | cie_type="+ cannexCompanyType +" duration="+termDuration + " amount="+amount+" redeem="+redeem +" compound="+compound+ " payment_freq="+payment);
		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM TermProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 2 AND p.termTypeId = :duration AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
					TermProduct.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", termDuration);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents

				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null) {
						if (compSel.getShop4rates().equalsIgnoreCase("y")) {
							compIds.add(compSel.getCompanyId());
							System.out.println("line415: " + compSel.getCompanyId());
						}
					}

				}

				query = em.createQuery(
						"SELECT p FROM TermProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 2 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
						TermProduct.class);
				query.setParameter("compID", compIds);
			} else {
				query = em.createQuery(
						"SELECT p FROM TermProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 2 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
						TermProduct.class);
			}

			query.setParameter("duration", termDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);
		}

		return query.getResultList();
	}

	public List<TermProduct> findRRSPProducts(Integer cannexCompanyType, Province province, Integer termDuration,
			Integer amount) {

		Users user = applicationBean.getUsers();

		TypedQuery<TermProduct> query = null;

//        System.out.println("CannexFacade.findRRSPProducts()  QUICK  | cie_type="+ cannexCompanyType +" duration="+termDuration + " amount="+amount);
		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM TermProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 2 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
					TermProduct.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", termDuration);
			query.setParameter("deposit", amount);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents

				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null) {
						if (compSel.getShop4rates().equalsIgnoreCase("y")) {
							compIds.add(compSel.getCompanyId());
							System.out.println("line468: " + compSel.getCompanyId());
						}
					}

				}
				query = em.createQuery(
						"SELECT p FROM TermProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 2 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						TermProduct.class);
				query.setParameter("compID", compIds);

			} else {
				query = em.createQuery(
						"SELECT p FROM TermProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 2 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						TermProduct.class);
			}

			query.setParameter("duration", termDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
		}

		return query.getResultList();
	}

	// productTypeId
	// RRIF = 3
	// Duration
	// 1 = 1 - 5 years (default)
	// 2 = 10 - 25 Years
	// 4 = 1.5 - 6.5 years
	public List<RrifProduct> findRRIFProductsFix(Integer cannexCompanyType, Province province, Integer rrifDuration,
			Integer amount) {

		Users user = applicationBean.getUsers();

		TypedQuery<RrifProduct> query = null;

		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM RrifProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 3 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
					RrifProduct.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", rrifDuration);
			query.setParameter("deposit", amount);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents
				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null && compSel.getShop4rates().equalsIgnoreCase("y")) {
						compIds.add(compSel.getCompanyId());
					}

				}
				query = em.createQuery(
						"SELECT p FROM RrifProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 3 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						RrifProduct.class);
				query.setParameter("compID", compIds);

			} else {
				query = em.createQuery(
						"SELECT p FROM RrifProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 3 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount",
						RrifProduct.class);
			}

			query.setParameter("duration", rrifDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
		}

		return query.getResultList();
	}

	public List<RrifProduct> findRRIFProductsFix(Integer cannexCompanyType, Province province, Integer rrifDuration,
			Integer amount, String redeem, String compound, String payment) {

		Users user = applicationBean.getUsers();

		TypedQuery<RrifProduct> query = null;
		if (cannexCompanyType != null) {

			query = em.createQuery(
					"SELECT p FROM RrifProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 3 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
					RrifProduct.class);

			query.setParameter("type", cannexCompanyType);
			query.setParameter("province", province);
			query.setParameter("duration", rrifDuration);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);

		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																							// agents
				List<CompanySelection> compSelList = user.getCompanySelectionList();
				List<Integer> compIds = new ArrayList<>();

				for (CompanySelection compSel : compSelList) {
					if (compSel.getShop4rates() != null) {
						if (compSel.getShop4rates().equalsIgnoreCase("y")) {
							compIds.add(compSel.getCompanyId());
							System.out.println("line577: " + compSel.getCompanyId());
						}
					}

				}

				query = em.createQuery(
						"SELECT p FROM RrifProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 3 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
						RrifProduct.class);
				query.setParameter("compID", compIds);
			} else {
				query = em.createQuery(
						"SELECT p FROM RrifProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 3 AND p.termTypeId = :duration AND :deposit BETWEEN p.minAmount AND p.maxAmount AND p.redeemability = :redeem AND p.compoundFrequency = :compound AND p.paymentFrequency = :payment",
						RrifProduct.class);
			}

			query.setParameter("duration", rrifDuration);
			query.setParameter("province", province);
			query.setParameter("deposit", amount);
			query.setParameter("redeem", redeem);
			query.setParameter("compound", compound);
			query.setParameter("payment", payment);
		}

		return query.getResultList();
	}

	public List<String> findDistinctTermTypes(String productType, String companyType) {

		TypedQuery<String> nq = em.createQuery(
				"SELECT distinct a.termType FROM CannexProduct a WHERE  a.companyType = :companyType  and a.productType = :productType",
				String.class);
		nq.setParameter("productType", productType);
		nq.setParameter("companyType", companyType);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<String>
		 * criteriaQuery = cBuilder.createQuery(String.class); Root<CannexProduct> p =
		 * criteriaQuery.from(CannexProduct.class);
		 * 
		 * Predicate companyPredicate =
		 * cBuilder.equal(p.get(CannexProduct_.cannexCompany).get(CannexCompany_.
		 * companyType), companyType); Predicate prodTypePredicate =
		 * cBuilder.equal(p.get(CannexProduct_.productType), productType);
		 * 
		 * criteriaQuery.where(cBuilder.and(companyPredicate,
		 * prodTypePredicate)).select(p.get(CannexProduct_.termType)) .distinct(true);
		 * 
		 * TypedQuery<String> query = em.createQuery(criteriaQuery);
		 * 
		 * List<String> results = query.getResultList();
		 * 
		 * return results;
		 */
	}

	public String findTermUnit(String termType, String productType) {

		TypedQuery<String> nq = em.createQuery(
				"SELECT distinct a.termType FROM CannexProduct a WHERE  a.termType = :termType  and a.productType = :productType",
				String.class);
		nq.setParameter("productType", productType);
		nq.setParameter("termType", termType);
		return nq.getSingleResult();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder(); CriteriaQuery<String>
		 * criteriaQuery = cBuilder.createQuery(String.class); Root<CannexProduct> p =
		 * criteriaQuery.from(CannexProduct.class);
		 * 
		 * Predicate prodTypePredicate =
		 * cBuilder.equal(p.get(CannexProduct_.productType), productType); Predicate
		 * termTypePredicate = cBuilder.equal(p.get(CannexProduct_.termType), termType);
		 * 
		 * criteriaQuery.where(cBuilder.and(prodTypePredicate,
		 * termTypePredicate)).select(p.get(CannexProduct_.termUnit)) .distinct(true);
		 * 
		 * TypedQuery<String> query = em.createQuery(criteriaQuery);
		 * 
		 * List<String> results = query.getResultList(); if (results != null &&
		 * !results.isEmpty()) { return results.get(0); }
		 * 
		 * return null;
		 */
	}

	public CannexCompany getCompanyByIPNO(String ipno) {

		Query query = em.createQuery("SELECT c FROM CannexCompany c WHERE c.ipno = :ipno", CannexCompany.class);
		query.setParameter("ipno", ipno);

		CannexCompany cie;

		try {
			cie = (CannexCompany) query.getSingleResult();
		} catch (jakarta.persistence.NoResultException e) {
			return null;
		}

		return cie;
	}

	public List<CannexCompany> findAllCannexComps() {

		try {
			TypedQuery<CannexCompany> nq = em.createNamedQuery("CannexCompany.findAll", CannexCompany.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			e.printStackTrace();
			return null;
		}

	}

	public List<CannexCompany> findAllCannexCompsNoSel(String comps) {

		try {
			TypedQuery<CannexCompany> query = em.createQuery(
					"SELECT p FROM CannexCompany p where p.companyId NOT IN ( :comps )", CannexCompany.class);
			query.setParameter("comps", comps);
			return query.getResultList();

		} catch (NoResultException e) {
			e.printStackTrace();
			return null;
		}

	}

	public CannexCompany getCompanyByCode(String code) {

		TypedQuery<CannexCompany> query = em.createQuery("SELECT c FROM CannexCompany c WHERE c.companyCode = :code",
				CannexCompany.class);
		query.setParameter("code", code);
		CannexCompany cie;

		try {
			cie = (CannexCompany) query.getSingleResult();
		} catch (jakarta.persistence.NoResultException e) {
			return null;
		}

		return cie;
	}

	// productTypeId
	// GIC = 1 (TERM + TAX=N)
	// Duration
	// 0 = 30 - 270 days
	// 1 = 1 - 6 years (default)
	// 2 = 1.5 - 6.5 years
	// 4 = 7 - 12 years (9, a, 1, "C");
	public List<MortProduct> findMORTProducts(Integer cannexCompanyType, Province province, Integer terms,
			String openClosed) {

		Users user = applicationBean.getUsers();

		List<CompanySelection> compSelList = user.getCompanySelectionList();
		List<Integer> compIds = new ArrayList<>();

		for (CompanySelection compSel : compSelList) {
			if (compSel.getShop4rates() != null) {
				if (compSel.getShop4rates().equalsIgnoreCase("y")) {
					compIds.add(compSel.getCompanyId());
				}
			}
		}

		TypedQuery<MortProduct> query = null;
		String sql = "";
		System.out.println("CannexFacade.findMORTProducts()  FULL  | cie_type=" + cannexCompanyType + " terms=" + terms
				+ " openClosed=" + openClosed);

		if (cannexCompanyType != null) {

			if (terms == 0) {
				if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																								// agents
					sql = " SELECT p FROM MortProduct p                "
							+ "  WHERE p.cannexCompany.companyType = :type "
							+ "    AND :province MEMBER OF p.cannexCompany.provinceList  "
							+ "    AND p.cannexCompany.companyId IN :compID              "
							+ "    AND p.productTypeId = 4                               "
							+ "    AND p.termTypeId = 0 AND p.rateType = 'V' ";
					query = em.createQuery(sql, MortProduct.class);
					System.out.println("CannexFacade line743:  sql=" + sql);
					query.setParameter("compID", compIds);
					query.setParameter("province", province);
					query.setParameter("type", cannexCompanyType);
				} else {
					sql = " SELECT p FROM MortProduct p                  "
							+ "  WHERE p.cannexCompany.companyType = :type   "
							+ "    AND :province MEMBER OF p.cannexCompany.provinceList "
							+ "    AND p.productTypeId = 4                   "
							+ "    AND p.termTypeId = 0 AND p.rateType = 'V' ";
					query = em.createQuery(sql, MortProduct.class);
					System.out.println("CannexFacade line755:  sql=" + sql);
					query.setParameter("province", province);
					query.setParameter("type", cannexCompanyType);
				}
			}
			if (terms == 1) {
				if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																								// agents
					query = em.createQuery("SELECT p FROM MortProduct p " + "WHERE p.cannexCompany.companyType = :type "
							+ "AND :province MEMBER OF p.cannexCompany.provinceList "
							+ "AND p.cannexCompany.companyId IN :compID "
							+ "AND p.productTypeId = 4 AND p.termTypeId = 0 "
							+ "AND p.openClosed = :openClosed AND p.rateType = 'F'", MortProduct.class);

					query.setParameter("compID", compIds);

					query.setParameter("openClosed", openClosed);
					query.setParameter("type", cannexCompanyType);
					query.setParameter("province", province);

				} else {
					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'",
							MortProduct.class);

					query.setParameter("openClosed", openClosed);
					query.setParameter("type", cannexCompanyType);
					query.setParameter("province", province);

				}
			}
			if (terms == 2) {
				if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// dashBoard for Sun Tool
																								// agents
					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4 AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
							MortProduct.class);

					query.setParameter("compID", compIds);
					query.setParameter("openClosed", openClosed);
					query.setParameter("type", cannexCompanyType);
					query.setParameter("province", province);

				} else {
					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE p.cannexCompany.companyType = :type AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4 AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
							MortProduct.class);

					query.setParameter("openClosed", openClosed);
					query.setParameter("type", cannexCompanyType);
					query.setParameter("province", province);

				}

			}
		} else {
			if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// for Sun Tool agents

				if (terms == 0) {
					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4  AND p.termTypeId = 0  AND p.rateType = 'V'",
							MortProduct.class);

					query.setParameter("compID", compIds);
					query.setParameter("province", province);
				}
				if (terms == 1) {

					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4  AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'",
							MortProduct.class);
					System.out.println("line 800 " + compIds.toString());
					query.setParameter("compID", compIds);
					query.setParameter("openClosed", openClosed);
					query.setParameter("province", province);
				}
				if (terms == 2) {
					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4  AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
							MortProduct.class);

					query.setParameter("compID", compIds);
					query.setParameter("openClosed", openClosed);
					query.setParameter("province", province);
				}

			} else {

				if (terms == 0) {
					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4  AND p.termTypeId = 0  AND p.rateType = 'V'",
							MortProduct.class);

					query.setParameter("province", province);
				}
				if (terms == 1) {
					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4  AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'",
							MortProduct.class);

					query.setParameter("openClosed", openClosed);
					query.setParameter("province", province);
				}
				if (terms == 2) {
					query = em.createQuery(
							"SELECT p FROM MortProduct p WHERE :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4  AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
							MortProduct.class);

					query.setParameter("openClosed", openClosed);
					query.setParameter("province", province);
				}

			}
		}

		return query.getResultList();
	}

	public List<MortProduct> findMORTProducts(Shop4RatesMortgageSearchParameter params) {

		Users user = applicationBean.getUsers();

		List<CompanySelection> compSelList = user.getCompanySelectionList();
		List<Integer> compIds = new ArrayList<>();

		for (CompanySelection compSel : compSelList) {
			if (compSel.getShop4rates() != null) {
				if (compSel.getShop4rates().equalsIgnoreCase("y")) {
					compIds.add(compSel.getCompanyId());
					System.out.println("line 850: " + compSel.getCompanyId());

				}
			}
		}

		TypedQuery<MortProduct> query = null;

		CannexCompany company = params.getSelectedCompany();
		Province province = params.getSelectedProvince();
		Integer terms = params.getTerms();
		String openClosed = params.getOpenClosed();

//        System.out.println("CannexFacade.findMORTProducts()  FULL  | cie_type="+ company.getCompanyDescEn() + " terms="+ terms +" openClosed="+openClosed);
		if (user.getProfileType() == Constants.Users_Profile_Type.Suntool.getCode()) {// for Sun Tool agents
			if (terms == 0) {
				query = em.createQuery(
						"SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.rateType = 'V'",
						MortProduct.class);

				query.setParameter("compID", compIds);
				query.setParameter("province", province);
				query.setParameter("company", company);
			}
			if (terms == 1) {
				query = em.createQuery(
						"SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'",
						MortProduct.class);

				query.setParameter("compID", compIds);
				query.setParameter("openClosed", openClosed);
				query.setParameter("company", company);
				query.setParameter("province", province);
			}
			if (terms == 2) {
				query = em.createQuery(
						"SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.cannexCompany.companyId IN :compID AND p.productTypeId = 4 AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
						MortProduct.class);

				query.setParameter("compID", compIds);
				query.setParameter("openClosed", openClosed);
				query.setParameter("company", company);
				query.setParameter("province", province);
			}
		} else {
			if (terms == 0) {
				query = em.createQuery(
						"SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.rateType = 'V'",
						MortProduct.class);
				query.setParameter("province", province);
				query.setParameter("company", company);
			}
			if (terms == 1) {
				query = em.createQuery(
						"SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4 AND p.termTypeId = 0 AND p.openClosed = :openClosed AND p.rateType = 'F'",
						MortProduct.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("company", company);
				query.setParameter("province", province);
			}
			if (terms == 2) {
				query = em.createQuery(
						"SELECT p FROM MortProduct p WHERE p.cannexCompany = :company AND :province MEMBER OF p.cannexCompany.provinceList AND p.productTypeId = 4 AND p.termTypeId = 1 AND p.openClosed = :openClosed AND p.rateType = 'F'",
						MortProduct.class);

				query.setParameter("openClosed", openClosed);
				query.setParameter("company", company);
				query.setParameter("province", province);
			}

		}

		return query.getResultList();
	}

	//
}
