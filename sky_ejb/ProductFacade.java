/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.avue.util.IMProductFormDateComparator;
import com.insurfact.im.IMLifeProductDetail;
import com.insurfact.im.IMProduct;
import com.insurfact.im.IMProductForm;
import com.insurfact.im.IMProductIllness;
import com.insurfact.im.IMUWClass;
import com.insurfact.iq.domain.Band;
import com.insurfact.iq.domain.BandRate;
import com.insurfact.iq.domain.Company;
import com.insurfact.iq.domain.MinMax;

import com.insurfact.skynet.entity.Product;
import com.insurfact.skynet.entity.ProductClass;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.ProductType;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ProductFacade extends AbstractFacade<Product> {

	@EJB
	private ProductCommissionFacade productCommissionFacade;

	@EJB
	private ProductTypeFacade productTypeFacade;

	@EJB
	private ProductClassFacade productClassFacade;

	@Resource(lookup = "jdbc/Orains")
	private DataSource ds;

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ProductFacade() {
		super(Product.class);
	}

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (SQLException e) {

			e.printStackTrace();
		}
		return null;
	}

	public Product refreshProduct(Product product) {
		Integer productID = product.getProductIntId();
		if (productID != null) {
			product = em.find(Product.class, productID);
		}

		return product;
	}

	@Override
	public Product find(Object id) {

		EntityManager em = getEntityManager();

		Product product = em.find(Product.class, id);

		if (product != null && product.isProxy()) {

			IMProduct im_product = getIMProduct(product.getProductId(), false);

			if (im_product == null) {
				System.err.println(
						"##### Unable to retrieve the matching IM_Product with prodId : " + product.getProductId());
			}
			product.setIm_product(im_product);
		}

		return product;
	}

	/**
	 * Retrieve all ProductType for a given ProductClass
	 *
	 * @param productClass if null returns all productType for all classed
	 * @return
	 */
	public List<ProductType> findProductTypeByClass(ProductClass productClass) {

		if (productClass == null) {
			return productTypeFacade.findAll();
		}

		return productTypeFacade.findTypesByProductClass(productClass);
	}

	/**
	 * retrieve a specific ProductClass
	 *
	 * @return
	 */
	public ProductClass getProductClassByCode(String productClassStr) {

		if (productClassStr == null || productClassStr.isEmpty()) {
			return null;
		}

		return productClassFacade.getProductClassByCode(productClassStr);
	}

	/**
	 * retrieve all ProductClass
	 *
	 * @return
	 */
	public List<ProductClass> findAllProductClass() {

		return productClassFacade.findAll();
	}

	/**
	 *
	 * @param productSupplier
	 * @param productClass
	 * @param withDetail
	 * @return
	 */
	/**
	 * return a Product based on the IQ productId
	 *
	 * @param prodId
	 * @return
	 */
	public Product getProduct(int prodId) {

		Product product = null;

		try {
			Query nq = em.createNamedQuery("Product.findByProductId", Product.class);

			nq.setParameter("productId", prodId);

			product = (Product) nq.getSingleResult();

		} catch (NoResultException e) {
			return null;
		}

		return product;
	}

	/**
	 * return ProductType based on productTypeId of IQ
	 *
	 * @param prodTypeId
	 * @return
	 */
	public ProductType getProductType(int prodTypeId) {

		ProductType productType = null;

		try {
			Query nq = em.createNamedQuery("ProductType.findByProductTypeid", ProductType.class);

			nq.setParameter("productTypeId", prodTypeId);

			productType = (ProductType) nq.getSingleResult();

		} catch (NoResultException e) {
			return null;
		}

		return productType;
	}

	public Blob getProductGuide(Integer productId, Integer masterId, boolean french) {

		Blob blob = null;
		ResultSet rst = null;
		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}
		try (Statement ptStmt = connection.createStatement();) {

			StringBuilder builder = new StringBuilder();
			builder.append("SELECT");

			if (!french) {
				builder.append(" FULL_ENGLISH_DESC");
			} else {
				builder.append(" FULL_FRENCH_DESC");
			}

			if (masterId != null && !masterId.equals(0)) {
				builder.append(" FROM IM_COMPANYPRODUCTDESC");
				builder.append(" WHERE PRODUCTID=").append(masterId);
			} else {
				builder.append(" FROM IM_COMPANYPRODUCTDESC");
				builder.append(" WHERE PRODUCTID=").append(productId);
			}

			rst = ptStmt.executeQuery(builder.toString());

			if (rst == null) {
				connection.close();
				return null;
			}

			if (rst.next()) {

				// get blob
				blob = rst.getBlob(1);

			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return blob;
	}

	/**
	 * return a list of IMProduct
	 *
	 * @param productSupplier
	 * @param productClass
	 * @return
	 */
	public List<IMProduct> findIMProductByProductSupplierAndClass(ProductSupplier productSupplier,
			ProductClass productClass) {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;
		IMProduct product = null;
		Connection connection = getConnection();

		int compId = productSupplier.getCompanyId();
		String pClass = productClass.getProductClass();

		if (connection == null) {
			return null;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT p.PRODUCTID,          " + "        p.COMPANYID,          "
					+ "        p.PRODUCTCLASS        " + "  FROM  IM_COMPANYPRODUCT   p, IM_LIFEPRODUCTDETAIL d  "
					+ "  WHERE COMPANYID=" + compId
					+ " AND p.PRODUCTID = d.productid AND SING_JO1_JOLAST IN (1,3,5,7)  AND "
					+ "        PRODUCTCLASS=\'" + pClass + "\'" + "  ORDER BY ENGLISH_NAME";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				int id = (int) rst.getLong("PRODUCTID");

				// fetch all the product information
				product = getIMProduct(id, true);

				products.add(product);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return products;
	}

	/**
	 * return a list of IMProduct
	 *
	 * @param compId - IMCompany ID
	 * @return
	 */
	public List<IMProduct> findIMProductByProductSupplierNoSel(int compId, String prods) {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;
		IMProduct product = null;
		Connection connection = getConnection();

		if (connection == null) {
			return products;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT PRODUCTID,          " + "        COMPANYID,          "
					+ "        PRODUCTCLASS        " + "   FROM IM_COMPANYPRODUCT   " + "  WHERE COMPANYID=" + compId
					+ "    AND AVAILABLE='Y'       ";
			if (!prods.equals("")) {
				sql += "        AND PRODUCTID NOT IN (" + prods + ")";
			}
			sql += "         ORDER BY ENGLISH_NAME";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				int id = (int) rst.getLong("PRODUCTID");

				// fetch all the product information
				product = getIMProduct(id, true);

				products.add(product);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return products;
	}

	/**
	 * return a list of IMProduct
	 *
	 * @param compId - IMCompany ID
	 * @return
	 */
	public List<IMProduct> findIMProductAvailDate(int compID, int lang) {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;

		IMProduct product = null;
		Connection connection = getConnection();

		String prodName = "ENGLISH_NAME";
		if (lang == 2) {
			prodName = "FRENCH_NAME";
		}

		if (connection == null) {
			return products;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT PRODUCTID,          " + "        COMPANYID,          "
					+ "        ACTIVE_DATE,        " + "        " + prodName + "        "
					+ "   FROM IM_COMPANYPRODUCT   " + "  WHERE AVAILABLE='Y'       "
					+ "    AND ACTIVE_DATE > ADD_MONTHS(SYSDATE, -9) ";
			if (compID != 0) {
				sql += "          AND COMPANYID=" + compID;
			}

			sql += "        ORDER BY ACTIVE_DATE DESC, COMPANYID, " + prodName;

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return products;
			}

			while (rst.next()) {
				int id = (int) rst.getLong("PRODUCTID");
				// fetch all the product information
				product = getIMProduct(id, true);
				if (product == null) {
					product = getDIProduct(id, true);
				}
				products.add(product);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return products;
		} // return the connection to the pool

		return products;
	}

	public List<IMProduct> findIMProductAvailDateView() {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;

		IMProduct product = null;
		Connection connection = getConnection();

		if (connection == null) {
			return products;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT DISTINCT	a.productid,	a.english_name,	a.french_name,	a.companyid,	b.nameenglish,	"
					+ "b.namefrench,	a.active_date,	a.short_english_desc,	a.short_french_desc,	a.master_desc_productid,"
					+ "	a.producttype,	c.descenglish,	c.descfrench FROM	IM_COMPANYPRODUCT a	INNER JOIN IM_COMPANY  b"
					+ " ON a.companyid = b.companyid	INNER JOIN IM_PRODUCTTYPE c ON a.producttype = c.producttype,	IM_LIFEPRODUCTDETAIL  d,"
					+ "	IM_DIPRODUCTDETAIL  e WHERE	(		( d.sing_jo1_jolast IN (1,3,7) AND a.productid = d.productid ) 	"
					+ "	OR ( e.sing_jo1_jolast = '1' AND a.productid = e.productid ) 	) 	AND a.AVAILABLE = 'Y' 	AND a.ACTIVE_DATE > "
					+ "ADD_MONTHS( SYSDATE, - 3 ) ORDER BY a.active_date DESC ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return products;
			}
			// List<Integer> checkList = new ArrayList<>();
			int productIdVal;
			// boolean passIt;

			while (rst.next()) {
				// filtering the products
				// passIt = false;
				/*
				 * productIdVal = (int) rst.getLong("master_desc_productid"); if (productIdVal
				 * != 0) { if (!checkList.contains(productIdVal)) { passIt = true;
				 * checkList.add(productIdVal); } } else { if (!checkList.contains((int)
				 * rst.getLong("PRODUCTID"))) { passIt = true; productIdVal = (int)
				 * rst.getLong("PRODUCTID"); checkList.add(productIdVal); } } if (passIt) {
				 */
				productIdVal = (int) rst.getLong("PRODUCTID");
				product = getIMProductAvailable(productIdVal, true);
				if (product == null) {
					product = getDIProductAvailable(productIdVal, true);
				}

				if (product != null) {
					product.setEnglish_type_desc(rst.getString("descenglish"));
					product.setFrench_type_desc(rst.getString("descfrench"));
					products.add(product);
				}
				// }
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return products;
		} // return the connection to the pool

		return products;
	}

	/**
	 * return a list of IMProduct
	 *
	 * @param compID - IMCompany ID
	 * @return
	 */
	public List<IMProduct> findAllIMProductByCompany(int compID) {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;

		IMProduct product = null;
		Connection connection = getConnection();
		if (connection == null) {
			return products;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT PRODUCTID,          " + "        COMPANYID,          "
					+ "        ACTIVE_DATE,        " + "        ENGLISH_NAME," + "        FRENCH_NAME        "
					+ "   FROM IM_COMPANYPRODUCT   " + "  WHERE AVAILABLE='Y'       ";
			if (compID != 0) {
				sql += "          and COMPANYID=" + compID;
			}

			sql += " and PRODUCTCLASS NOT LIKE 'INVEST' ";
			sql += "        ORDER BY ACTIVE_DATE DESC, COMPANYID ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {
				int id = (int) rst.getLong("PRODUCTID");
				// fetch all the product information
				product = getIMProduct(id, true);
				products.add(product);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return products;
	}

	public List<IMProduct> findAllIMProductByCompanyForManage(int compID) {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;
		IMProduct product = null;
		Connection connection = getConnection();
		if (connection == null) {
			return products;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT a.PRODUCTID,          " + "       a.COMPANYID,          " + " a.ACTIVE_DATE,        "
					+ "        a.ENGLISH_NAME, " + "         a.PRODUCTTYPE,"
					+ "        a.FRENCH_NAME, a.product_note        "
					+ "   FROM IM_COMPANYPRODUCT a, IM_LIFEPRODUCTDETAIL b  "
					+ "  WHERE (a.AVAILABLE='Y' or   (a.AVAILABLE='N' and a.active_date > sysdate)  )  ";
			if (compID != 0) {
				sql += "    and  b.SING_JO1_JOLAST != 0    and a.COMPANYID=" + compID;
			}

			sql += " and a.PRODUCTCLASS in ('LIFE', 'CRIT', 'DI')  AND a.PRODUCTID = b.productid";
			sql += "        ORDER BY a.ACTIVE_DATE DESC, a.COMPANYID ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return products;
			}

			while (rst.next()) {
				// fetch all the product information
				product = new IMProduct();

				product.setProductid(rst.getLong("PRODUCTID"));
				product.setCompanyid(rst.getLong("COMPANYID"));

				product.setEnglish_name(rst.getString("ENGLISH_NAME"));
				product.setFrench_name(rst.getString("FRENCH_NAME"));

				product.setActive_date(rst.getDate("ACTIVE_DATE"));
				product.setProducttype(rst.getString("PRODUCTTYPE"));

				products.add(product);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return products;
	}

	public List<IMProduct> findAllIMProductByCompanyImport(String compID) {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;
		IMProduct product = null;
		Connection connection = getConnection();
		if (connection == null) {
			return products;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT PRODUCTID,          " + "       COMPANYID,          " + " ACTIVE_DATE,        "
					+ "        ENGLISH_NAME, " + "         PRODUCTTYPE," + "        FRENCH_NAME, product_note        "
					+ "   FROM IM_COMPANYPRODUCT  where active_date > CURRENT_DATE and COMPANYID = " + compID;

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return products;
			}

			while (rst.next()) {
				// fetch all the product information
				product = new IMProduct();

				product.setProductid(rst.getLong("PRODUCTID"));
				product.setCompanyid(rst.getLong("COMPANYID"));

				product.setEnglish_name(rst.getString("ENGLISH_NAME"));
				product.setFrench_name(rst.getString("FRENCH_NAME"));

				product.setActive_date(rst.getDate("ACTIVE_DATE"));
				product.setProducttype(rst.getString("PRODUCTTYPE"));

				products.add(product);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return products;
	}

	public List<IMProduct> findAllIMProductRiderByCompanyImport(String compID) {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;
		IMProduct product = null;
		Connection connection = getConnection();
		if (connection == null) {
			return products;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT a.PRODUCTID,          " + "       a.COMPANYID,          " + " a.ACTIVE_DATE,        "
					+ "        a.ENGLISH_NAME, " + "         a.PRODUCTTYPE,"
					+ "        a.FRENCH_NAME, a.product_note        "
					+ "   FROM IM_COMPANYPRODUCT a, IM_LIFEPRODUCTDETAIL b where a.active_date > CURRENT_DATE and b.SING_JO1_JOLAST = 0 and a.PRODUCTID = b.productid and a.COMPANYID = "
					+ compID;

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return products;
			}

			while (rst.next()) {
				// fetch all the product information
				product = new IMProduct();

				product.setProductid(rst.getLong("PRODUCTID"));
				product.setCompanyid(rst.getLong("COMPANYID"));

				product.setEnglish_name(rst.getString("ENGLISH_NAME"));
				product.setFrench_name(rst.getString("FRENCH_NAME"));

				product.setActive_date(rst.getDate("ACTIVE_DATE"));
				product.setProducttype(rst.getString("PRODUCTTYPE"));

				products.add(product);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return products;
	}

	public List<IMProduct> findAllIMProductByCompanyForManage2(int compID) {

		List<IMProduct> products = new ArrayList<>();

		ResultSet rst = null;

		IMProduct product = null;
		Connection connection = getConnection();
		if (connection == null) {
			return products;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT distinct a.PRODUCTID,          " + "       a.COMPANYID,          "
					+ "        a.ACTIVE_DATE,        " + "        a.ENGLISH_NAME, " + "         a.PRODUCTTYPE,"
					+ "        a.FRENCH_NAME, a.product_note        "
					+ "   FROM IM_COMPANYPRODUCT a, IM_LIFEPRODUCTDETAIL b,  IM_DIPRODUCTDETAIL c  "
					+ "  WHERE (a.AVAILABLE='Y' or   (a.AVAILABLE='N' and a.active_date > sysdate)  )  ";
			if (compID != 0) {
				sql += "    and  (b.SING_JO1_JOLAST != 0 or c.SING_JO1_JOLAST != 0)    and a.COMPANYID=" + compID;
			}

			sql += " and a.PRODUCTCLASS in ('LIFE', 'CRIT', 'DI')  AND (a.PRODUCTID = b.productid or a.PRODUCTID = c.productid)";
			sql += "        ORDER BY a.ACTIVE_DATE DESC, a.COMPANYID ";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return products;
			}

			while (rst.next()) {
				// int id = (int) rst.getLong("PRODUCTID");
				// fetch all the product information
				product = new IMProduct();

				product.setProductid(rst.getLong("PRODUCTID"));
				product.setCompanyid(rst.getLong("COMPANYID"));

				product.setEnglish_name(rst.getString("ENGLISH_NAME"));
				product.setFrench_name(rst.getString("FRENCH_NAME"));

				product.setActive_date(rst.getDate("ACTIVE_DATE"));
				product.setProducttype(rst.getString("PRODUCTTYPE"));

				products.add(product);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return products;
	}

	/**
	 * return s IMProduct based on the IQ ProductId
	 *
	 * @param prodId
	 * @param withDetail This option retrieves the IM Product Commissions
	 * @return
	 */
	public IMProduct getIMProduct(int prodId, boolean withDetail) {

		IMProduct product = null;

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select a.PRODUCTID,          " + "        a.COMPANYID,          "
					+ "        a.PRODUCTCLASS,       " + "        a.PRODUCTTYPE,        "
					+ "        a.ENGLISH_NAME,       " + "        a.FRENCH_NAME,        "
					+ "        a.FRENCH_NAME,         " + "        a.LASTUPDATEDATE,     "
					+ "        a.LASTUPDATEUSER,     " + "        a.AVAILABLE,     " + "        a.USEPARENTCOMPANY,   "
					+ "        a.SHORT_ENGLISH_DESC, " + "        a.SHORT_FRENCH_DESC,  "
					+ "        a.MASTER_DESC_PRODUCTID, " + "        a.ACTIVE_DATE,        "
					+ "        a.INACTIVE_DATE,      " + "        a.SHAREPRODUCTTYPE,   "
					+ "        a.WITH_VALUES,        " + "        a.COMMISSION,         "
					+ "        a.MANAGEMENT_ONLY,    " + "        a.ADDT_ENGLISH_DESC,  "
					+ "        a.ADDT_FRENCH_DESC,   " + "        a.PROFILE_TYPE,       "
					+ "        b.SING_JO1_JOLAST,  " + "        a.EAPP_ID , a.product_note             "
					+ "   FROM IM_COMPANYPRODUCT a " + "   , IM_LIFEPRODUCTDETAIL b " + "  WHERE a.PRODUCTID=\'"
					+ prodId + "\' " + " AND a.PRODUCTID = b.productid";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				product = new IMProduct();

				product.setProductid(rst.getLong("PRODUCTID"));
				product.setCompanyid(rst.getLong("COMPANYID"));
				product.setProductclass(rst.getString("PRODUCTCLASS"));

				product.setEnglish_name(rst.getString("ENGLISH_NAME"));
				product.setFrench_name(rst.getString("FRENCH_NAME"));

				product.setAvailable(rst.getString("AVAILABLE"));

				product.setLastupdatedate(rst.getDate("LASTUPDATEDATE"));
				product.setLastupdateuser(rst.getString("LASTUPDATEUSER"));

				product.setUseparentcompany(rst.getString("USEPARENTCOMPANY"));

				product.setShort_english_desc(rst.getString("SHORT_ENGLISH_DESC"));
				product.setShort_french_desc(rst.getString("SHORT_FRENCH_DESC"));

				product.setMaster_desc_productid(rst.getLong("MASTER_DESC_PRODUCTID"));

				product.setActive_date(rst.getDate("ACTIVE_DATE"));
				product.setInactive_date(rst.getDate("INACTIVE_DATE"));
				product.setShareproducttype(rst.getString("SHAREPRODUCTTYPE"));

				product.setWith_values(rst.getString("WITH_VALUES"));
				product.setCommission(rst.getDouble("COMMISSION"));
				product.setManagement_only(sql);

				product.setAddt_english_desc(rst.getString("ADDT_ENGLISH_DESC"));
				product.setAddt_french_desc(rst.getString("ADDT_FRENCH_DESC"));

				product.setProfileType(rst.getInt("PROFILE_TYPE"));

				product.setJolast(rst.getInt("SING_JO1_JOLAST"));

				if (rst.getString("EAPP_ID") != null) {
					try (Statement eAppStmt = connection.createStatement()) {
						ResultSet eappRs = eAppStmt
								.executeQuery(" SELECT * FROM IM_EAPP " + "WHERE EAPP_ID=" + rst.getString("EAPP_ID"));
						if (eappRs.next()) {
							if (eappRs.getString("EAPP_URL_ENG") != null) {
								product.setEapp_url_eng(eappRs.getString("EAPP_URL_ENG"));
							}
							if (eappRs.getString("EAPP_URL_FRE") != null) {
								product.setEapp_url_fre(eappRs.getString("EAPP_URL_FRE"));
							}
						}
					}
				}

				if (withDetail) {
					// TODO
				}

				// Fetch Product Detail
				IMLifeProductDetail detail = getIMLifeProductDetail(prodId);
				product.setIm_lifeProductDetail(detail);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return product;
	}

	public IMProduct getIMProductAvailable(int prodId, boolean withDetail) {

		IMProduct product = null;

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select a.PRODUCTID,          " + "        a.COMPANYID,          "
					+ "        a.PRODUCTCLASS,       " + "        a.PRODUCTTYPE,        "
					+ "        a.ENGLISH_NAME,       " + "        a.FRENCH_NAME,        "
					+ "        a.FRENCH_NAME,         " + "        a.LASTUPDATEDATE,     "
					+ "        a.LASTUPDATEUSER,     " + "        a.AVAILABLE,     " + "        a.USEPARENTCOMPANY,   "
					+ "        a.SHORT_ENGLISH_DESC, " + "        a.SHORT_FRENCH_DESC,  "
					+ "        a.MASTER_DESC_PRODUCTID, " + "        a.ACTIVE_DATE,        "
					+ "        a.INACTIVE_DATE,      " + "        a.SHAREPRODUCTTYPE,   "
					+ "        a.WITH_VALUES,        " + "        a.COMMISSION,         "
					+ "        a.MANAGEMENT_ONLY,    " + "        a.ADDT_ENGLISH_DESC,  "
					+ "        a.ADDT_FRENCH_DESC,   " + "        a.PROFILE_TYPE,       "
					+ "        b.SING_JO1_JOLAST,  " + "        a.EAPP_ID , a.product_note             "
					+ "   FROM IM_COMPANYPRODUCT a " + "   , IM_LIFEPRODUCTDETAIL b " + "  WHERE a.PRODUCTID=\'"
					+ prodId + "\' " + " AND a.AVAILABLE = 'Y' AND a.ACTIVE_DATE > ADD_MONTHS( SYSDATE, - 3 ) "
					+ " AND a.PRODUCTID = b.productid";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				product = new IMProduct();

				product.setProductid(rst.getLong("PRODUCTID"));
				product.setCompanyid(rst.getLong("COMPANYID"));
				product.setProductclass(rst.getString("PRODUCTCLASS"));

				product.setEnglish_name(rst.getString("ENGLISH_NAME"));
				product.setFrench_name(rst.getString("FRENCH_NAME"));

				product.setAvailable(rst.getString("AVAILABLE"));

				product.setLastupdatedate(rst.getDate("LASTUPDATEDATE"));
				product.setLastupdateuser(rst.getString("LASTUPDATEUSER"));

				product.setUseparentcompany(rst.getString("USEPARENTCOMPANY"));

				product.setShort_english_desc(rst.getString("SHORT_ENGLISH_DESC"));
				product.setShort_french_desc(rst.getString("SHORT_FRENCH_DESC"));

				product.setMaster_desc_productid(rst.getLong("MASTER_DESC_PRODUCTID"));

				product.setActive_date(rst.getDate("ACTIVE_DATE"));
				product.setInactive_date(rst.getDate("INACTIVE_DATE"));
				product.setShareproducttype(rst.getString("SHAREPRODUCTTYPE"));

				product.setWith_values(rst.getString("WITH_VALUES"));
				product.setCommission(rst.getDouble("COMMISSION"));
				product.setManagement_only(sql);

				product.setAddt_english_desc(rst.getString("ADDT_ENGLISH_DESC"));
				product.setAddt_french_desc(rst.getString("ADDT_FRENCH_DESC"));

				product.setProfileType(rst.getInt("PROFILE_TYPE"));

				product.setJolast(rst.getInt("SING_JO1_JOLAST"));

				if (rst.getString("EAPP_ID") != null) {
					try (Statement eAppStmt = connection.createStatement()) {
						ResultSet eappRs = eAppStmt
								.executeQuery(" SELECT * FROM IM_EAPP " + "WHERE EAPP_ID=" + rst.getString("EAPP_ID"));
						if (eappRs.next()) {
							if (eappRs.getString("EAPP_URL_ENG") != null) {
								product.setEapp_url_eng(eappRs.getString("EAPP_URL_ENG"));
							}
							if (eappRs.getString("EAPP_URL_FRE") != null) {
								product.setEapp_url_fre(eappRs.getString("EAPP_URL_FRE"));
							}
						}
					}
				}

				if (withDetail) {
					// TODO
				}

				// Fetch Product Detail
				IMLifeProductDetail detail = getIMLifeProductDetail(prodId);
				product.setIm_lifeProductDetail(detail);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return product;
	}

	/**
	 * return s IMProduct based on the IQ ProductId
	 *
	 * @param prodId
	 * @param withDetail This option retrieves the IM Product Commissions
	 * @return
	 */
	public IMProduct getDIProduct(int prodId, boolean withDetail) {

		IMProduct product = null;

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select a.PRODUCTID,          " + "        a.COMPANYID,          "
					+ "        a.PRODUCTCLASS,       " + "        a.PRODUCTTYPE,        "
					+ "        a.ENGLISH_NAME,       " + "        a.FRENCH_NAME,        "
					+ "        a.AVAILABLE,          " + "        a.LASTUPDATEDATE,     "
					+ "        a.LASTUPDATEUSER,     " + "        a.USEPARENTCOMPANY,   "
					+ "        a.SHORT_ENGLISH_DESC, " + "        a.SHORT_FRENCH_DESC,  "
					+ "        a.MASTER_DESC_PRODUCTID, " + "        a.ACTIVE_DATE,        "
					+ "        a.INACTIVE_DATE,      " + "        a.SHAREPRODUCTTYPE,   "
					+ "        a.WITH_VALUES,        " + "        a.COMMISSION,         "
					+ "        a.MANAGEMENT_ONLY,    " + "        a.ADDT_ENGLISH_DESC,  "
					+ "        a.ADDT_FRENCH_DESC,   " + "        a.PROFILE_TYPE,       "
					+ "        b.SING_JO1_JOLAST,  " + "        a.EAPP_ID             " + "   FROM IM_COMPANYPRODUCT a "
					+ "   , IM_DIPRODUCTDETAIL b " + "  WHERE a.PRODUCTID=\'" + prodId + "\' "
					+ " AND a.PRODUCTID = b.productid";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				product = new IMProduct();

				product.setProductid(rst.getLong("PRODUCTID"));
				product.setCompanyid(rst.getLong("COMPANYID"));
				product.setProductclass(rst.getString("PRODUCTCLASS"));

				product.setEnglish_name(rst.getString("ENGLISH_NAME"));
				product.setFrench_name(rst.getString("FRENCH_NAME"));

				product.setAvailable(rst.getString("AVAILABLE"));

				product.setLastupdatedate(rst.getDate("LASTUPDATEDATE"));
				product.setLastupdateuser(rst.getString("LASTUPDATEUSER"));

				product.setUseparentcompany(rst.getString("USEPARENTCOMPANY"));

				product.setShort_english_desc(rst.getString("SHORT_ENGLISH_DESC"));
				product.setShort_french_desc(rst.getString("SHORT_FRENCH_DESC"));

				product.setMaster_desc_productid(rst.getLong("MASTER_DESC_PRODUCTID"));

				product.setActive_date(rst.getDate("ACTIVE_DATE"));
				product.setInactive_date(rst.getDate("INACTIVE_DATE"));
				product.setShareproducttype(rst.getString("SHAREPRODUCTTYPE"));

				product.setWith_values(rst.getString("WITH_VALUES"));
				product.setCommission(rst.getDouble("COMMISSION"));
				product.setManagement_only(sql);

				product.setAddt_english_desc(rst.getString("ADDT_ENGLISH_DESC"));
				product.setAddt_french_desc(rst.getString("ADDT_FRENCH_DESC"));

				product.setProfileType(rst.getInt("PROFILE_TYPE"));

				product.setJolast(rst.getInt("SING_JO1_JOLAST"));

				if (rst.getString("EAPP_ID") != null) {
					try (Statement eAppStmt = connection.createStatement()) {
						ResultSet eappRs = eAppStmt
								.executeQuery(" SELECT * FROM IM_EAPP " + "WHERE EAPP_ID=" + rst.getString("EAPP_ID"));
						if (eappRs.next()) {
							if (eappRs.getString("EAPP_URL_ENG") != null) {
								product.setEapp_url_eng(eappRs.getString("EAPP_URL_ENG"));
							}
							if (eappRs.getString("EAPP_URL_FRE") != null) {
								product.setEapp_url_fre(eappRs.getString("EAPP_URL_FRE"));
							}
						}
					}
				}

				if (withDetail) {
					// TODO
				}

				// Fetch Product Detail
				IMLifeProductDetail detail = getIMLifeProductDetail(prodId);
				product.setIm_lifeProductDetail(detail);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return product;
	}

	public IMProduct getDIProductAvailable(int prodId, boolean withDetail) {

		IMProduct product = null;

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select a.PRODUCTID,          " + "        a.COMPANYID,          "
					+ "        a.PRODUCTCLASS,       " + "        a.PRODUCTTYPE,        "
					+ "        a.ENGLISH_NAME,       " + "        a.FRENCH_NAME,        "
					+ "        a.AVAILABLE,          " + "        a.LASTUPDATEDATE,     "
					+ "        a.LASTUPDATEUSER,     " + "        a.USEPARENTCOMPANY,   "
					+ "        a.SHORT_ENGLISH_DESC, " + "        a.SHORT_FRENCH_DESC,  "
					+ "        a.MASTER_DESC_PRODUCTID, " + "        a.ACTIVE_DATE,        "
					+ "        a.INACTIVE_DATE,      " + "        a.SHAREPRODUCTTYPE,   "
					+ "        a.WITH_VALUES,        " + "        a.COMMISSION,         "
					+ "        a.MANAGEMENT_ONLY,    " + "        a.ADDT_ENGLISH_DESC,  "
					+ "        a.ADDT_FRENCH_DESC,   " + "        a.PROFILE_TYPE,       "
					+ "        b.SING_JO1_JOLAST,  " + "        a.EAPP_ID             " + "   FROM IM_COMPANYPRODUCT a "
					+ "   , IM_DIPRODUCTDETAIL b " + "  WHERE a.PRODUCTID=\'" + prodId + "\' "
					+ " AND a.AVAILABLE = 'Y' AND a.ACTIVE_DATE > ADD_MONTHS( SYSDATE, - 3 ) "
					+ " AND a.PRODUCTID = b.productid";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				product = new IMProduct();

				product.setProductid(rst.getLong("PRODUCTID"));
				product.setCompanyid(rst.getLong("COMPANYID"));
				product.setProductclass(rst.getString("PRODUCTCLASS"));

				product.setEnglish_name(rst.getString("ENGLISH_NAME"));
				product.setFrench_name(rst.getString("FRENCH_NAME"));

				product.setAvailable(rst.getString("AVAILABLE"));

				product.setLastupdatedate(rst.getDate("LASTUPDATEDATE"));
				product.setLastupdateuser(rst.getString("LASTUPDATEUSER"));

				product.setUseparentcompany(rst.getString("USEPARENTCOMPANY"));

				product.setShort_english_desc(rst.getString("SHORT_ENGLISH_DESC"));
				product.setShort_french_desc(rst.getString("SHORT_FRENCH_DESC"));

				product.setMaster_desc_productid(rst.getLong("MASTER_DESC_PRODUCTID"));

				product.setActive_date(rst.getDate("ACTIVE_DATE"));
				product.setInactive_date(rst.getDate("INACTIVE_DATE"));
				product.setShareproducttype(rst.getString("SHAREPRODUCTTYPE"));

				product.setWith_values(rst.getString("WITH_VALUES"));
				product.setCommission(rst.getDouble("COMMISSION"));
				product.setManagement_only(sql);

				product.setAddt_english_desc(rst.getString("ADDT_ENGLISH_DESC"));
				product.setAddt_french_desc(rst.getString("ADDT_FRENCH_DESC"));

				product.setProfileType(rst.getInt("PROFILE_TYPE"));

				product.setJolast(rst.getInt("SING_JO1_JOLAST"));

				if (rst.getString("EAPP_ID") != null) {
					try (Statement eAppStmt = connection.createStatement()) {
						ResultSet eappRs = eAppStmt
								.executeQuery(" SELECT * FROM IM_EAPP " + "WHERE EAPP_ID=" + rst.getString("EAPP_ID"));
						if (eappRs.next()) {
							if (eappRs.getString("EAPP_URL_ENG") != null) {
								product.setEapp_url_eng(eappRs.getString("EAPP_URL_ENG"));
							}
							if (eappRs.getString("EAPP_URL_FRE") != null) {
								product.setEapp_url_fre(eappRs.getString("EAPP_URL_FRE"));
							}
						}
					}
				}

				if (withDetail) {
					// TODO
				}

				// Fetch Product Detail
				IMLifeProductDetail detail = getIMLifeProductDetail(prodId);
				product.setIm_lifeProductDetail(detail);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return product;
	}

	/**
	 *
	 * @param prodId
	 * @return
	 */
	public IMLifeProductDetail getIMLifeProductDetail(int prodId) {

		IMLifeProductDetail product = null;

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT PRODUCTID, " + "        SING_JO1_JOLAST, " + "        LAST_RENEWABLE_AGE, "
					+ "        LAST_CONVERTABLE_AGE, " + "        CONVERTABLE_WAITING_PERIOD, "
					+ "        PAYABLE_TO_AGE, " + "        AGETYPE, " + "        MLOW_ISSUE_AGE, "
					+ "        MHIGH_ISSUE_AGE, " + "        FLOW_ISSUE_AGE, " + "        FHIGH_ISSUE_AGE, "
					+ "        LOW_WAIVER_ISSUE_AGE, " + "        HIGH_WAIVER_ISSUE_AGE, " + "        LAST_WAIVER_AGE, "
					+ "        MLOW_ISSUE_AGE_SMOKER, " + "        MHIGH_ISSUE_AGE_SMOKER, "
					+ "        FLOW_ISSUE_AGE_SMOKER, " + "        FHIGH_ISSUE_AGE_SMOKER, " + "        INIT_WAIVER_F, "
					+ "        INIT_WAIVER_M, " + "        RENEW_WAIVER_F, " + "        RENEW_WAIVER_M, "
					+ "        ROP_LOW_AGE, " + "        ROP_HIGH_AGE, " + "        MIN_BUS_FACE_AMOUNT, "
					+ "        MAX_BUS_FACE_AMOUNT, " + "        MULTI_LIFE, " + "        CI_NUM_ILL, "
					+ "        ROP_INCLUDED, " + "        ROP_MATRIX, " + "        BACKDATE, "
					+ "        DISCOUNTPRICE, " + "        ASRIDER, " + "        ONLY_ONE_PERIOD, " + "        UWTYPE, "
					+ "         FACE_AMOUNT_RECHECK " // new field added jun 28, 2021 -Argyro
					+ "   FROM IM_LIFEPRODUCTDETAIL   " + "  WHERE PRODUCTID=\'" + prodId + "\'";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {

				product = new IMLifeProductDetail();

				product.setProductid(rst.getLong("PRODUCTID"));

				product.setSING_JO1_JOLAST(rst.getInt("SING_JO1_JOLAST"));
				product.setLAST_RENEWABLE_AGE(rst.getInt("LAST_RENEWABLE_AGE"));
				product.setLAST_CONVERTABLE_AGE(rst.getInt("LAST_CONVERTABLE_AGE"));
				product.setCONVERTABLE_WAITING_PERIOD(rst.getInt("CONVERTABLE_WAITING_PERIOD"));
				product.setPAYABLE_TO_AGE(rst.getInt("PAYABLE_TO_AGE"));
				product.setAGETYPE(rst.getString("AGETYPE"));
				product.setMLOW_ISSUE_AGE(rst.getInt("MLOW_ISSUE_AGE"));
				product.setMHIGH_ISSUE_AGE(rst.getInt("MHIGH_ISSUE_AGE"));
				product.setFLOW_ISSUE_AGE(rst.getInt("FLOW_ISSUE_AGE"));
				product.setFHIGH_ISSUE_AGE(rst.getInt("FHIGH_ISSUE_AGE"));
				product.setLOW_WAIVER_ISSUE_AGE(rst.getInt("LOW_WAIVER_ISSUE_AGE"));
				product.setHIGH_WAIVER_ISSUE_AGE(rst.getInt("HIGH_WAIVER_ISSUE_AGE"));
				product.setLAST_WAIVER_AGE(rst.getInt("LAST_WAIVER_AGE"));
				product.setMLOW_ISSUE_AGE_SMOKER(rst.getInt("MLOW_ISSUE_AGE_SMOKER"));
				product.setMHIGH_ISSUE_AGE_SMOKER(rst.getInt("MHIGH_ISSUE_AGE_SMOKER"));
				product.setFLOW_ISSUE_AGE_SMOKER(rst.getInt("FLOW_ISSUE_AGE_SMOKER"));
				product.setFHIGH_ISSUE_AGE_SMOKER(rst.getInt("FHIGH_ISSUE_AGE_SMOKER"));
				product.setINIT_WAIVER_F(rst.getDouble("INIT_WAIVER_F"));
				product.setINIT_WAIVER_M(rst.getDouble(("INIT_WAIVER_M")));
				product.setRENEW_WAIVER_F(rst.getDouble("RENEW_WAIVER_F"));
				product.setRENEW_WAIVER_M(rst.getDouble(("RENEW_WAIVER_M")));
				product.setROP_LOW_AGE(rst.getInt("ROP_LOW_AGE"));
				product.setROP_HIGH_AGE(rst.getInt("ROP_HIGH_AGE"));
				product.setMIN_BUS_FACE_AMOUNT(rst.getDouble(("MIN_BUS_FACE_AMOUNT")));
				product.setMAX_BUS_FACE_AMOUNT(rst.getDouble("MAX_BUS_FACE_AMOUNT"));
				product.setMULTI_LIFE(rst.getString("MULTI_LIFE"));
				product.setCI_NUM_ILL(rst.getInt("CI_NUM_ILL"));
				product.setROP_INCLUDED(rst.getString("ROP_INCLUDED"));
				product.setROP_MATRIX(rst.getString("ROP_MATRIX"));
				product.setBACKDATE(rst.getInt("BACKDATE"));
				product.setDISCOUNTPRICE(rst.getString("DISCOUNTPRICE"));
				product.setASRIDER(rst.getString("ASRIDER"));
				product.setONLY_ONE_PERIOD(rst.getString("ONLY_ONE_PERIOD"));
				product.setUWTYPE(rst.getInt("UWTYPE"));

				product.setFACE_AMOUNT_RECHECK(rst.getDouble("FACE_AMOUNT_RECHECK"));

			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return product;
	}

	public com.insurfact.iq.domain.Product getIQ4LifeProductDetail2(int prodId, char gender) {

		com.insurfact.iq.domain.Product product = null;

		ResultSet rst = null;

		Connection connection = getConnection();

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT l.PRODUCTID, " + "        l.LAST_RENEWABLE_AGE, " + "        l.LAST_CONVERTABLE_AGE, "
					+ "        l.AGETYPE, " + "        l.CI_NUM_ILL, " + "        d.guaranteed, "
					+ "        r.payableage," + "        r.payableyears, "
					+ "        p.MASTER_DESC_PRODUCTID masterID,  "
					+ "        p.COMPANYID,  c.ABBRV_ENGLISH, c.ABBRV_FRENCH, c.NAMEENGLISH, c.NAMEFRENCH, "
					+ "        p.SHORT_ENGLISH_DESC, " + "        p.SHORT_FRENCH_DESC, " + "        p.EAPP_ID,  "
					+ "        d.PRODUCTTYPE, d.PRODUCTTYPEID, d.PRODUCTCLASS, d.TERM_OR_PERM, "
					+ "d.descenglish, d.DESCFRENCH, d.renewable, d.renew_years," + "        r.PLAN_MIN_ISSUE_AGE,"
					+ "        r.PLAN_MAX_ISSUE_AGE," + "        r.PREMIUM_MIN_FACE_AMOUNT,"
					+ "        r.MIN_PREMIUM_REQUIRED," + "        r.MIN_MONTH_PREMIUM,"
					+ "        r.MIN_QUART_PREMIUM," + "        r.MIN_SEMI_PREMIUM,"
					+ "        r.PREMIUM_MAX_FACE_AMOUNT," + "        r.SMOKER," + "        r.SMOKECODE,"
					+ "        s.GLOBALCLASS," + "        s.LOWMNTH_NS," + "        s.CIGARETTES, "
					+ "        s.SMOKEALL, " + "        l.UWTYPE," + "        r.SEX," + "        r.CLASSID,"
					+ "        r.INSURANCEBEN," + "        r.ROW_KEY," + "        r.UNDERWRITINGGROUP" + "   FROM  "
					+ "        IM_LIFEPRODUCTDETAIL l,  " + "        im_producttype d, im_company c, "
					+ "        im_companyproduct p, " + "        im_life_premium_rate r, " + "        SMOKEHABIT s "
					+ "  WHERE l.PRODUCTID=\'" + prodId + "\'" + "  AND d.producttype=p.producttype"
					+ "  AND p.companyid=c.companyid" + "  AND d.productclass=p.productclass"
					+ "  AND p.PRODUCTID = l.PRODUCTID " + "  AND r.SMOKECODE = s.CODE " + "  AND r.SMOKER = s.SMOKER"
					+ "  AND r.productid=l.productid" + "  AND r.sex='" + gender + "'"
					+ "  ORDER by r.classid, r.PREMIUM_MIN_FACE_AMOUNT";
			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			boolean first = true;

			while (rst.next()) {
				if (first) {
					product = new com.insurfact.iq.domain.Product();

					product.setID(rst.getInt("PRODUCTID"));
					product.setMasterDescProductID(rst.getInt("masterID"));

					product.setLastRenewableAge(rst.getInt("LAST_RENEWABLE_AGE"));
					product.setAgeToUse(rst.getString("AGETYPE"));

					product.setLastConvertibleAge(rst.getInt("LAST_CONVERTABLE_AGE"));

					product.setName(rst.getString("SHORT_ENGLISH_DESC"));
					product.setNameFr(rst.getString("SHORT_FRENCH_DESC"));

					product.setAgePayable(rst.getInt("payableage"));
					product.setYearsPayable(rst.getInt("payableyears"));
					product.setRenewYears(rst.getInt("renew_years"));
					String tmp = rst.getString("renewable");
					if (tmp == null || tmp.isEmpty()) {
						tmp = "N";
					}

					product.setRenewable(tmp.equalsIgnoreCase("Y") ? true : false);

					product.setGuaranteed(
							(rst.getString("guaranteed") != null && rst.getString("guaranteed").equalsIgnoreCase("Y")
									? true
									: false));

					product.setNumIllnesses(rst.getInt("CI_NUM_ILL"));
					product.setUwtype(rst.getInt("UWTYPE"));
					product.setInsuranceBen(rst.getInt("INSURANCEBEN"));
					product.setMinAge(rst.getInt("PLAN_MIN_ISSUE_AGE"));
					product.setMaxAge(rst.getInt("PLAN_MAX_ISSUE_AGE"));

					if (rst.getString("EAPP_ID") != null) {
						Statement eAppStmt = connection.createStatement();
						ResultSet eappRs = eAppStmt
								.executeQuery(" SELECT * FROM IM_EAPP " + "WHERE EAPP_ID=" + rst.getString("EAPP_ID"));
						if (eappRs.next()) {
							if (eappRs.getString("EAPP_URL_ENG") != null) {
								product.setEapp_url_eng(eappRs.getString("EAPP_URL_ENG"));
							}
							if (eappRs.getString("EAPP_URL_FRE") != null) {
								product.setEapp_url_fre(eappRs.getString("EAPP_URL_FRE"));
							}
						}
						eAppStmt.close();
					}

					// build the ProductType
					com.insurfact.iq.domain.ProductType prodType = new com.insurfact.iq.domain.ProductType();

					prodType.setName(rst.getString("descenglish"));
					prodType.setNameFr(rst.getString("descfrench"));
					prodType.setID(rst.getInt("PRODUCTTYPE"));
					prodType.setMasterID(rst.getInt("PRODUCTTYPEID"));
					prodType.setTermOrPerm(rst.getString("TERM_OR_PERM"));

					product.setProductType(prodType);

					Company cie = new Company();
					cie.setID(rst.getInt("COMPANYID"));
					cie.setName(rst.getString("ABBRV_ENGLISH"));
					cie.setNameFr(rst.getString("ABBRV_FRENCH"));
					cie.setLongName(rst.getString("NAMEENGLISH"));
					cie.setLongNameFr(rst.getString("NAMEFRENCH"));
					product.setCompany(cie);

					first = false;
				}

				Band band = new Band();

				band.setGender(rst.getString("SEX").charAt(0));
				band.setMinAge(rst.getInt("PLAN_MIN_ISSUE_AGE"));
				band.setMaxAge(rst.getInt("PLAN_MAX_ISSUE_AGE"));
				band.setGlobalHealthClass(rst.getString("GLOBALCLASS"));
				band.setSmoker(rst.getString("SMOKER").equalsIgnoreCase("Y"));
				band.setSmokeCode(rst.getString("SMOKECODE"));
				band.setMonths(rst.getInt("LOWMNTH_NS"));
				band.setUnderwritingGroup(rst.getInt("UNDERWRITINGGROUP"));

				band.setMinAnnualRequired(rst.getInt("MIN_PREMIUM_REQUIRED"));
				band.setMinMonthlyRequired(rst.getInt("MIN_MONTH_PREMIUM"));

				band.setJointMinAnnualRequired(rst.getInt("MIN_PREMIUM_REQUIRED"));
				band.setJointMinMonthlyRequired(rst.getInt("MIN_MONTH_PREMIUM"));

				if (rst.getInt("MIN_SEMI_PREMIUM") > 0) {
					band.setJointMinAnnualRequired(rst.getInt("MIN_SEMI_PREMIUM"));
				}

				if (rst.getInt("MIN_QUART_PREMIUM") > 0) {
					band.setJointMinMonthlyRequired(rst.getInt("MIN_QUART_PREMIUM"));
				}

				String tmp = rst.getString("SMOKEALL");
				if (tmp == null) {
					tmp = "N";
				}

				band.setSmokeAll(tmp.equalsIgnoreCase("Y"));

				tmp = rst.getString("CIGARETTES");
				if (tmp == null) {
					tmp = "N";
				}

				band.setCigarettes(tmp.equalsIgnoreCase("Y"));

				band.setRowrey(rst.getInt("ROW_KEY"));
				band.setClassId(rst.getInt("CLASSID"));
				band.setMinMax(
						new MinMax(rst.getInt("PREMIUM_MIN_FACE_AMOUNT"), rst.getInt("PREMIUM_MAX_FACE_AMOUNT")));

				if (product != null) {
					product.appendBand(band);
				}

			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return product;
	}

	public com.insurfact.iq.domain.Product getIQ4LifeProductDetail2(int prodId) {

		com.insurfact.iq.domain.Product product = null;

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = " select l.PRODUCTID, " + "        l.LAST_RENEWABLE_AGE, " + "        l.LAST_CONVERTABLE_AGE, "
					+ "        l.AGETYPE, " + "        l.CI_NUM_ILL, " + "        d.guaranteed, "
					+ "        r.payableage," + "        r.payableyears, "
					+ "        p.MASTER_DESC_PRODUCTID masterID,  "
					+ "        p.COMPANYID,  c.ABBRV_ENGLISH, c.ABBRV_FRENCH, c.NAMEENGLISH, c.NAMEFRENCH, "
					+ "        p.SHORT_ENGLISH_DESC, " + "        p.SHORT_FRENCH_DESC, "
					+ "        d.PRODUCTTYPE, d.PRODUCTTYPEID, d.PRODUCTCLASS, d.TERM_OR_PERM, d.descenglish, d.DESCFRENCH, d.renewable, d.renew_years,"
					+ "        r.PLAN_MIN_ISSUE_AGE," + "        r.PLAN_MAX_ISSUE_AGE,"
					+ "        r.PREMIUM_MIN_FACE_AMOUNT," + "        r.PREMIUM_MAX_FACE_AMOUNT,"
					+ "        r.MIN_PREMIUM_REQUIRED," + "        r.MIN_MONTH_PREMIUM,"
					+ "        r.MIN_QUART_PREMIUM," + "        r.MIN_SEMI_PREMIUM," + "        r.SMOKER,"
					+ "        r.SMOKECODE," + "        s.GLOBALCLASS," + "        s.LOWMNTH_NS,"
					+ "        s.CIGARETTES, " + "        s.SMOKEALL, " + "        l.UWTYPE," + "        r.SEX,"
					+ "        r.CLASSID," + "        r.INSURANCEBEN," + "        r.ROW_KEY,"
					+ "        r.UNDERWRITINGGROUP" + "   FROM  " + "        IM_LIFEPRODUCTDETAIL l,  "
					+ "        im_producttype d, " + "        im_companyproduct p, im_company c, "
					+ "        im_life_premium_rate r, " + "        SMOKEHABIT s " + "  WHERE l.PRODUCTID=\'" + prodId
					+ "\'" + "  AND d.producttype=p.producttype" + "  AND p.companyid=c.companyid"
					+ "  AND d.productclass=p.productclass" + "  AND p.PRODUCTID = l.PRODUCTID "
					+ "  AND r.SMOKECODE = s.CODE " + "  AND r.SMOKER = s.SMOKER" + "  AND r.productid=l.productid"
					+ "  ORDER by r.classid, r.PREMIUM_MIN_FACE_AMOUNT";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			boolean first = true;

			while (rst.next()) {
				if (first) {
					product = new com.insurfact.iq.domain.Product();

					product.setID(rst.getInt("PRODUCTID"));
					product.setMasterDescProductID(rst.getInt("masterID"));

					product.setLastRenewableAge(rst.getInt("LAST_RENEWABLE_AGE"));
					product.setAgeToUse(rst.getString("AGETYPE"));

					product.setLastConvertibleAge(rst.getInt("LAST_CONVERTABLE_AGE"));

					product.setName(rst.getString("SHORT_ENGLISH_DESC"));
					product.setNameFr(rst.getString("SHORT_FRENCH_DESC"));

					product.setAgePayable(rst.getInt("payableage"));
					product.setYearsPayable(rst.getInt("payableyears"));
					product.setRenewYears(rst.getInt("renew_years"));
					String tmp = rst.getString("renewable");
					if (tmp == null || tmp.isEmpty()) {
						tmp = "N";
					}

					product.setRenewable(tmp.equalsIgnoreCase("Y") ? true : false);

					product.setGuaranteed(
							(rst.getString("guaranteed") != null && rst.getString("guaranteed").equalsIgnoreCase("Y")
									? true
									: false));

					product.setNumIllnesses(rst.getInt("CI_NUM_ILL"));
					product.setUwtype(rst.getInt("UWTYPE"));
					product.setInsuranceBen(rst.getInt("INSURANCEBEN"));

					product.setMinAge(rst.getInt("PLAN_MIN_ISSUE_AGE"));
					product.setMaxAge(rst.getInt("PLAN_MAX_ISSUE_AGE"));

					// build the ProductType
					com.insurfact.iq.domain.ProductType prodType = new com.insurfact.iq.domain.ProductType();

					prodType.setName(rst.getString("descenglish"));
					prodType.setNameFr(rst.getString("descfrench"));
					prodType.setID(rst.getInt("PRODUCTTYPE"));
					prodType.setMasterID(rst.getInt("PRODUCTTYPEID"));
					prodType.setTermOrPerm(rst.getString("TERM_OR_PERM"));

					product.setProductType(prodType);

					Company cie = new Company();
					cie.setID(rst.getInt("COMPANYID"));
					cie.setName(rst.getString("ABBRV_ENGLISH"));
					cie.setNameFr(rst.getString("ABBRV_FRENCH"));
					cie.setLongName(rst.getString("NAMEENGLISH"));
					cie.setLongNameFr(rst.getString("NAMEFRENCH"));
					product.setCompany(cie);

					first = false;
				}

				Band band = new Band();

				band.setGender(rst.getString("SEX").charAt(0));
				band.setMinAge(rst.getInt("PLAN_MIN_ISSUE_AGE"));
				band.setMaxAge(rst.getInt("PLAN_MAX_ISSUE_AGE"));
				band.setGlobalHealthClass(rst.getString("GLOBALCLASS"));
				band.setSmoker(rst.getString("SMOKER").equalsIgnoreCase("Y"));
				band.setSmokeCode(rst.getString("SMOKECODE"));
				band.setMonths(rst.getInt("LOWMNTH_NS"));
				band.setUnderwritingGroup(rst.getInt("UNDERWRITINGGROUP"));

				band.setMinAnnualRequired(rst.getInt("MIN_PREMIUM_REQUIRED"));
				band.setMinMonthlyRequired(rst.getInt("MIN_MONTH_PREMIUM"));

				band.setJointMinAnnualRequired(rst.getInt("MIN_PREMIUM_REQUIRED"));
				band.setJointMinMonthlyRequired(rst.getInt("MIN_MONTH_PREMIUM"));

				if (rst.getInt("MIN_SEMI_PREMIUM") > 0) {
					band.setJointMinAnnualRequired(rst.getInt("MIN_SEMI_PREMIUM"));
				}

				if (rst.getInt("MIN_QUART_PREMIUM") > 0) {
					band.setJointMinMonthlyRequired(rst.getInt("MIN_QUART_PREMIUM"));
				}

				String tmp = rst.getString("SMOKEALL");
				if (tmp == null) {
					tmp = "N";
				}

				band.setSmokeAll(tmp.equalsIgnoreCase("Y"));

				tmp = rst.getString("CIGARETTES");
				if (tmp == null) {
					tmp = "N";
				}

				band.setCigarettes(tmp.equalsIgnoreCase("Y"));

				band.setRowrey(rst.getInt("ROW_KEY"));
				band.setClassId(rst.getInt("CLASSID"));
				band.setMinMax(
						new MinMax(rst.getInt("PREMIUM_MIN_FACE_AMOUNT"), rst.getInt("PREMIUM_MAX_FACE_AMOUNT")));

				if (product != null) {
					product.appendBand(band);
				}

			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return product;
	}

	public com.insurfact.iq.domain.Product getJointProductDetail2(com.insurfact.iq.domain.Product product,
			String jointType) {

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = " SELECT j.PRODUCTID, j.CALCMETHOD, j.RATES_FROM, j.FORMULA_METHOD, j.PERCENT_VALUE, j.POLICYFEE, j.AGE_DISPLAYED, j.MIN_AGE_DIFF, j.MAX_AGE_DIFF, j.JOINTTYPE, j.MIN_ANNUAL_PREMIUM, j.MIN_MONTHLY_PREMIUM, j.MIN_FACE_AMOUNT,  j.ANN_PREM_RATE_SUB, "
					+ "   m.methodjava annualbasemethod, m1.methodjava monthlybasemethod "
					+ " FROM im_method m, im_method m1, joint_header j" + " WHERE j.PRODUCTID=" + product.getID()
					+ "  AND j.jointtype=\'" + jointType + "\'" + "   AND j.max_lives=2"
					+ "   AND j.joint_method=m.methodid and j.joint_method_monthly =m1.methodid";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return product;
			}

			rst.next();

			product.setJointCalcMethod(rst.getString("CALCMETHOD"));
			product.setJointAgeToDisplay(rst.getString("AGE_DISPLAYED").charAt(0));
			product.setJointFormulaMethod(rst.getString("FORMULA_METHOD"));
			product.setJointType(rst.getString("JOINTTYPE").charAt(0));

			product.setJointPolicyFee(rst.getDouble("POLICYFEE"));

			product.setJointMinFaceAmount(rst.getDouble("MIN_FACE_AMOUNT"));
			product.setJointPercentValue(rst.getDouble("PERCENT_VALUE"));
			product.setJointAnnualMethod(rst.getString("annualbasemethod"));
			product.setJointMonthlyMethod(rst.getString("monthlybasemethod"));
			product.setJointRatesFrom(rst.getString("RATES_FROM").charAt(0));

			product.setJointAnnualRateSubstract(rst.getDouble("ANN_PREM_RATE_SUB"));

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException exp) {
				exp.printStackTrace();
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return product;
	}

	public List<ProductClass> findInsuranceProductClasses() {

		List<ProductClass> all = findAllProductClass();

		Iterator<ProductClass> iterator = all.iterator();

		while (iterator.hasNext()) {

			ProductClass prodClass = iterator.next();

			if ("INVEST".equalsIgnoreCase(prodClass.getProductClass())) {
				iterator.remove();
			}
			if ("GIC".equalsIgnoreCase(prodClass.getProductClass())) {
				iterator.remove();
			}
			if ("ANNUITY".equalsIgnoreCase(prodClass.getProductClass())) {
				iterator.remove();
			}
		}

		return all;
	}

	public List<ProductClass> findLifeProductClasses() {

		List<ProductClass> result = new ArrayList<>();

		result.add(productClassFacade.getProductClassByCode("LIFE"));

		return result;
	}

	public List<ProductClass> findCIProductClasses() {

		List<ProductClass> result = new ArrayList<>();

		result.add(productClassFacade.getProductClassByCode("CRIT"));

		return result;
	}

	public List<ProductClass> findDIProductClasses() {

		List<ProductClass> result = new ArrayList<>();

		result.add(productClassFacade.getProductClassByCode("DI"));

		return result;
	}

	public List<ProductClass> findTravelProductClasses() {

		List<ProductClass> result = new ArrayList<>();

		result.add(productClassFacade.getProductClassByCode("TRAV"));

		return result;
	}

	public List<ProductClass> findInvestmentProductClasses() {

		List<ProductClass> result = new ArrayList<>();

		result.add(productClassFacade.getProductClassByCode("INVEST"));

		return result;
	}

	public List<ProductClass> findAnnuityProductClasses() {

		List<ProductClass> result = new ArrayList<>();

		result.add(productClassFacade.getProductClassByCode("ANNUITY"));

		return result;
	}

	public List<ProductClass> findGICProductClasses() {

		List<ProductClass> result = new ArrayList<>();

		result.add(productClassFacade.getProductClassByCode("GIC"));

		return result;
	}

	/**
	 * return all the active Breaking News
	 *
	 * @return
	 */
	public List<IMProductForm> findActiveBreakingNews() {

		List<IMProductForm> activeNews = new ArrayList<>();

		ResultSet rst = null;
		Connection connection = getConnection();
		if (connection == null) {
			return activeNews;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "SELECT f.FORMID, f.PRODUCTID, f.COMPANYID, f.FORMTYPE, f.TITLE_ENG, f.TITLE_FRE, f.START_DATE, f.END_DATE, c.ABBRV_ENGLISH, c.ABBRV_FRENCH FROM IM_PRODUCT_FORMS f, im_company c WHERE  f.COMPANYID = c.COMPANYID AND  SYSDATE BETWEEN f.START_DATE and f.END_DATE ORDER BY 1";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return activeNews;
			}

			while (rst.next()) {

				IMProductForm news = new IMProductForm();

				news.setFormId(rst.getInt("FORMID"));
				news.setProductId(rst.getInt("PRODUCTID"));
				news.setCompanyId(rst.getInt("COMPANYID"));
				news.setFormtype(rst.getInt("FORMTYPE"));
				news.setCompanyForm(false);

				news.setStartDate(rst.getDate("START_DATE"));
				news.setEndDate(rst.getDate("END_DATE"));

				news.setTitleEng(rst.getString("TITLE_ENG"));
				news.setTitleFre(rst.getString("TITLE_FRE"));
				news.setCompanyNameEng(rst.getString("ABBRV_ENGLISH"));
				news.setCompanyNameFre(rst.getString("ABBRV_FRENCH"));

				activeNews.add(news);
			}

			// clean close of the resultset
			rst.close();

			sql = "SELECT f.FORMID, f.COMPANYID, f.FORMTYPE, f.TITLE_ENG, f.TITLE_FRE, f.START_DATE, f.END_DATE, c.ABBRV_ENGLISH, c.ABBRV_FRENCH FROM IM_COMPANY_FORMS f, im_company c WHERE  f.COMPANYID = c.COMPANYID AND  SYSDATE BETWEEN f.START_DATE and f.END_DATE ORDER BY 1";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return activeNews;
			}

			while (rst.next()) {

				IMProductForm news = new IMProductForm();

				news.setFormId(rst.getInt("FORMID"));
				news.setCompanyId(rst.getInt("COMPANYID"));
				news.setFormtype(rst.getInt("FORMTYPE"));
				news.setCompanyForm(true);
				news.setStartDate(rst.getDate("START_DATE"));
				news.setEndDate(rst.getDate("END_DATE"));

				news.setTitleEng(rst.getString("TITLE_ENG"));
				news.setTitleFre(rst.getString("TITLE_FRE"));
				news.setCompanyNameEng(rst.getString("ABBRV_ENGLISH"));
				news.setCompanyNameFre(rst.getString("ABBRV_FRENCH"));

				activeNews.add(news);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return activeNews;
		} // return the connection to the pool

		Collections.sort(activeNews, new IMProductFormDateComparator());

		return activeNews;
	}

	/**
	 * return all the active Breaking News
	 *
	 * @return
	 */
	public List<IMProductIllness> findProductIllnesses(int productId, int companyId) {

		List<IMProductIllness> illnesses = new ArrayList<>();

		ResultSet rst = null;
		Connection connection = getConnection();
		if (connection == null) {
			return illnesses;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "SELECT ILLNESS_ID, PRODUCT_ID, COMPANY_ID, TYPE_ID, ENGLISH_DESC, FRENCH_DESC, ENGLISH_LONG_DESC, FRENCH_DESC FROM IM_PRODUCT_ILLNESS WHERE PRODUCT_ID="
					+ productId + " AND COMPANY_ID=" + companyId;

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return illnesses;
			}

			while (rst.next()) {

				IMProductIllness illness = new IMProductIllness();

				illness.setIllnessId(rst.getInt("ILLNESS_ID"));
				illness.setProductId(rst.getInt("PRODUCTID"));
				illness.setCompanyId(rst.getInt("COMPANYID"));
				illness.setTypeId(rst.getInt("TYPE_ID"));

				illness.setEnglishDesc(rst.getString("ENGLISH_DESC"));
				illness.setFrenchDesc(rst.getString("FRENCH_DESC"));

				illness.setEnglishLongDesc(rst.getString("ENGLISH_LONG_DESC"));
				illness.setFrenchLongDesc(rst.getString("FRENCH_LONG_DESC"));

				illnesses.add(illness);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return illnesses;
		} // return the connection to the pool

		return illnesses;
	}

	public com.insurfact.iq.domain.ProductType populateGenericTypes(com.insurfact.iq.domain.ProductType productType) {

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return null;
		}

		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select language, DESCRIPTION from type where typeid=" + productType.getMasterID()
					+ " and typename='PRODUCTTYPE' order by language";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			while (rst.next()) {
				if (rst.getInt(1) == 1) {
					productType.setMasterName(rst.getString(2));
				}
				if (rst.getInt(1) == 2) {
					productType.setMasterNameFr(rst.getString(2));
				}
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return productType;
	}

	public IMUWClass getIMUWClass(int classId) {

		IMUWClass uwClass = null;

		if (classId != 0) {

			ResultSet rst = null;

			Connection connection = getConnection();
			if (connection == null) {
				return null;
			}
			try (Statement ptStmt = connection.createStatement();) {

				String sql = " select CLASSID,          " + "        PRODUCTID,       " + "        SMOKER,        "
						+ "        ENGDESC,       " + "        FREDESC        " + "   FROM UW_CLASS   "
						+ "  WHERE CLASSID=\'" + classId + "\'";

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return null;
				}

				while (rst.next()) {

					uwClass = new IMUWClass();
					uwClass.setClassid(rst.getInt("CLASSID"));
					uwClass.setProductid(rst.getInt("PRODUCTID"));
					uwClass.setSmoker(rst.getString("SMOKER"));
					uwClass.setEngdesc(rst.getString("ENGDESC"));
					uwClass.setFredesc(rst.getString("FREDESC"));
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return null;
			} // return the connection to the pool

		}

		return uwClass;
	}

	public List<IMUWClass> getIMUWClasses(int prodId, boolean smoker) {

		List<IMUWClass> uwClasses = new ArrayList<>();

		if (prodId != 0) {

			ResultSet rst = null;

			Connection connection = getConnection();
			if (connection == null) {
				return uwClasses;
			}
			try (Statement ptStmt = connection.createStatement();) {

				String sql = " select CLASSID,          " + "        PRODUCTID,       " + "        SMOKER,        "
						+ "        ENGDESC,       " + "        FREDESC        " + "   FROM UW_CLASS   "
						+ "   WHERE PRODUCTID=" + prodId;

				if (smoker) {
					sql += "  AND SMOKER='Y'";
				} else {
					sql += "  AND SMOKER='N'";
				}

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return uwClasses;
				}

				while (rst.next()) {

					IMUWClass uwClass = new IMUWClass();
					uwClass.setClassid(rst.getInt("CLASSID"));
					uwClass.setProductid(rst.getInt("PRODUCTID"));
					uwClass.setSmoker(rst.getString("SMOKER"));
					uwClass.setEngdesc(rst.getString("ENGDESC"));
					uwClass.setFredesc(rst.getString("FREDESC"));

					uwClasses.add(uwClass);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return uwClasses;
			} // return the connection to the pool

		}

		return uwClasses;
	}

	public List<IMUWClass> getIMUWClassesWithDetails(int prodId, boolean smoker, char sex, int age,
			Integer faceAmount) {

		List<IMUWClass> uwClasses = new ArrayList<>();

		if (prodId != 0) {

			ResultSet rst = null;

			Connection connection = getConnection();
			if (connection == null) {
				return uwClasses;
			}
			try (Statement ptStmt = connection.createStatement();) {

				String sql = " SELECT distinct r.classid, r.SMOKECODE, s.LOWMNTH_NS, s.cigarettes, s.GLOBALCLASS, r.INSURANCEBEN, l.uwtype "
						+ "FROM IM_LIFE_PREMIUM_RATE r, SMOKEHABIT s, im_lifeproductdetail l " + " WHERE l.PRODUCTID="
						+ prodId + " AND l.PRODUCTID=r.PRODUCTID" + " AND r.SMOKECODE = s.CODE AND s.SMOKER='"
						+ (smoker ? 'Y' : 'N') + "' " + " AND SEX='" + sex + "' " + " AND " + faceAmount
						+ " BETWEEN PREMIUM_MIN_FACE_AMOUNT AND PREMIUM_MAX_FACE_AMOUNT " + " AND " + age
						+ " BETWEEN PLAN_MIN_ISSUE_AGE AND PLAN_MAX_ISSUE_AGE";

				if (smoker) {
					sql += "  AND r.SMOKER='Y'";
				} else {
					sql += "  AND r.SMOKER='N'";
				}

				sql += " ORDER BY 1";

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return uwClasses;
				}

				while (rst.next()) {

					IMUWClass uwClass = getIMUWClass(rst.getInt("CLASSID"));
					uwClass.setSmokeCode(rst.getString("SMOKECODE"));
					uwClass.setGlobalClass(rst.getString("GLOBALCLASS"));
					uwClass.setMonthNonSmoker(rst.getInt("LOWMNTH_NS"));
					uwClass.setInsuranceBen(rst.getInt("INSURANCEBEN"));

					uwClass.setUwType(rst.getInt("uwtype"));
					String tmp = rst.getString("cigarettes");
					if (tmp == null) {
						tmp = "N";
					}

					uwClass.setCigarette(tmp.equalsIgnoreCase("Y"));
					uwClasses.add(uwClass);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return uwClasses;
			} // return the connection to the pool

		}

		return uwClasses;
	}

	public MinMax getProductRiderMinMax(int prodId, Integer age) {

		MinMax minMax = null;
		if (prodId != 0) {

			ResultSet rst = null;

			Connection connection = getConnection();
			if (connection == null) {
				return null;
			}

			try (Statement ptStmt = connection.createStatement();) {

				String sql = " SELECT min(r.PREMIUM_MIN_FACE_AMOUNT), max(r.PREMIUM_MAX_FACE_AMOUNT) FROM IM_LIFE_PREMIUM_RATE r, SMOKEHABIT s  "
						+ " WHERE PRODUCTID=" + prodId;

				if (age != null) {
					sql += " AND " + age + " BETWEEN r.PLAN_MIN_ISSUE_AGE AND r.PLAN_MAX_ISSUE_AGE";
				}

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return null;
				}

				while (rst.next()) {

					int min = rst.getInt(1);
					int max = rst.getInt(2);
					minMax = new MinMax(min, max);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return null;
			} // return the connection to the pool

		}

		return minMax;
	}

	public MinMax getProductRiderMinMax(int prodId, boolean smoker, char sex, int age) {

		MinMax minMax = null;
		if (prodId != 0) {

			ResultSet rst = null;

			Connection connection = getConnection();
			if (connection == null) {
				return null;
			}

			try (Statement ptStmt = connection.createStatement();) {

				String sql = " SELECT min(r.PREMIUM_MIN_FACE_AMOUNT), max(r.PREMIUM_MAX_FACE_AMOUNT) FROM IM_LIFE_PREMIUM_RATE r, SMOKEHABIT s  "
						+ " WHERE PRODUCTID=" + prodId + " AND r.SMOKECODE = s.CODE AND s.SMOKER='"
						+ (smoker ? 'Y' : 'N') + "' " + " AND SEX='" + sex + "' " + " AND " + age
						+ " BETWEEN r.PLAN_MIN_ISSUE_AGE AND r.PLAN_MAX_ISSUE_AGE";

				if (smoker) {
					sql += "  AND r.SMOKER='Y'";
				} else {
					sql += "  AND r.SMOKER='N'";
				}

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return null;
				}

				while (rst.next()) {

					int min = rst.getInt(1);
					int max = rst.getInt(2);
					minMax = new MinMax(min, max);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return null;
			} // return the connection to the pool

		}

		return minMax;
	}

	public MinMax getProductRiderMinMaxWithClassSpec(int prodId, boolean smoker, char sex, int age, String smokeCode) {

		MinMax minMax = null;
		if (prodId != 0) {

			ResultSet rst = null;

			Connection connection = getConnection();
			if (connection == null) {
				return null;
			}

			try (Statement ptStmt = connection.createStatement();) {

				String sql = " SELECT min(PREMIUM_MIN_FACE_AMOUNT), max(PREMIUM_MAX_FACE_AMOUNT) FROM IM_LIFE_PREMIUM_RATE r  "
						+ " WHERE PRODUCTID=" + prodId + " AND SEX='" + sex + "' " + " AND " + age
						+ " BETWEEN PLAN_MIN_ISSUE_AGE AND PLAN_MAX_ISSUE_AGE" + " AND SMOKECODE='" + smokeCode + "'";

				if (smoker) {
					sql += "  AND SMOKER='Y'";
				} else {
					sql += "  AND SMOKER='N'";
				}

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return null;
				}

				while (rst.next()) {

					int min = rst.getInt(1);
					int max = rst.getInt(2);
					minMax = new MinMax(min, max);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return null;
			} // return the connection to the pool

		}

		return minMax;
	}

	public MinMax getProductRiderMinMaxWithClassSpecWBcombo(int prodId, boolean smoker, char sex, int age,
			String smokeCode, int wbCombo) {

		MinMax minMax = null;
		if (prodId != 0) {

			ResultSet rst = null;

			Connection connection = getConnection();
			if (connection == null) {
				return null;
			}

			try (Statement ptStmt = connection.createStatement();) {

				String sql = " SELECT min(PREMIUM_MIN_FACE_AMOUNT), "
						+ "        max(PREMIUM_MAX_FACE_AMOUNT) FROM IM_LIFE_PREMIUM_RATE r  " + " WHERE PRODUCTID="
						+ prodId + " AND SEX='" + sex + "' " + " AND " + age
						+ " BETWEEN PLAN_MIN_ISSUE_AGE AND PLAN_MAX_ISSUE_AGE" + " AND SMOKECODE='" + smokeCode + "' "
						+ " AND WBCOMBO=" + wbCombo;

				if (smoker) {
					sql += "  AND SMOKER='Y'";
				} else {
					sql += "  AND SMOKER='N'";
				}

				rst = ptStmt.executeQuery(sql);

				if (rst == null) {
					connection.close();
					return null;
				}

				while (rst.next()) {

					int min = rst.getInt(1);
					int max = rst.getInt(2);
					minMax = new MinMax(min, max);
				}

				// clean close of the resultset
				rst.close();
				connection.close();

			} catch (SQLException e) {
				try {
					connection.close();
				} catch (SQLException ex) {
					Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
				}
				e.printStackTrace();
				return null;
			} // return the connection to the pool

		}

		return minMax;
	}

	public com.insurfact.iq.domain.Company getIQ4Company(Integer id) {

		ResultSet rst = null;
		Connection connection = getConnection();

		com.insurfact.iq.domain.Company company = null;
		if (connection == null) {
			return null;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "select NAMEENGLISH, NAMEFRENCH, ABBRV_ENGLISH, ABBRV_FRENCH COMPANYID from IM_company where companyid="
					+ id;

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return null;
			}

			rst.next();

			String english = rst.getString(1);
			String french = rst.getString(2);

			company = new com.insurfact.iq.domain.Company();

			company.setName(english);
			company.setNameFr(french);
			company.setAbbrv(rst.getString(3));
			company.setAbbrvFr(rst.getString(4));

			company.setID(id);

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		} // return the connection to the pool

		return company;
	}

	public List<BandRate> findIQ4BandRates(int rowkey, int classId) {

		List<BandRate> rates = new ArrayList<>();

		ResultSet rst = null;

		Connection connection = getConnection();
		if (connection == null) {
			return rates;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "SELECT m.ROW_KEY, m.AGE, m.RATE_1, m.POLICY_FEE_1 FROM IM_LIFE_PREMIUM_MATRIX m WHERE m.ROW_KEY ="
					+ rowkey + " ORDER BY  m.age ASC";

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return rates;
			}

			while (rst.next()) {

				BandRate rate = new BandRate();

				rate.setRowrey(rowkey);
				rate.setClassId(classId);
				rate.setAge(rst.getInt("AGE"));
				rate.setRate(rst.getDouble("RATE_1"));
				rate.setPolicyFee(rst.getDouble("POLICY_FEE_1"));

				rates.add(rate);
			}

			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return rates;
		} // return the connection to the pool

		return rates;
	}

	public void updateProductWithNote(int productId, int noteID) {
		ResultSet rst = null;
		Connection connection = getConnection();
		if (connection == null) {
			return;
		}
		try (Statement ptStmt = connection.createStatement();) {

			String sql = "update im_companyproduct set product_note = " + noteID + " where PRODUCTID = " + productId;

			rst = ptStmt.executeQuery(sql);

			if (rst == null) {
				connection.close();
				return;
			}
			// clean close of the resultset
			rst.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return;
		} // return the connection to the pool

	}

}
