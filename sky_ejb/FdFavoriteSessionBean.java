/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdFund;
import com.insurfact.skynet.entity.FundFavorite;
import com.insurfact.skynet.entity.Users;
import java.util.Calendar;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFavoriteSessionBean extends AbstractFacade<FundFavorite> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdFavoriteSessionBean() {
		super(FundFavorite.class);
	}

	public FundFavorite getFavoriteByFundAndUsers(FdFund fund, Users users) {

		try {
			Query nq = getEntityManager().createNamedQuery("FundFavorite.findByFdFundAndUsers", FundFavorite.class);
			nq.setParameter("fdFund", fund);
			nq.setParameter("users", users);

			return (FundFavorite) nq.getSingleResult();

		} catch (Exception e) {

			return null;
		}
	}

	public List<FundFavorite> findFavoriteByUsers(Users users) {

		try {
			TypedQuery<FundFavorite> nq = getEntityManager().createNamedQuery("FundFavorite.findByUsers",
					FundFavorite.class);
			nq.setParameter("users", users);

			return nq.getResultList();

		} catch (Exception e) {

			return null;
		}
	}

	public List<FundFavorite> findFavoriteByFund(FdFund fund) {

		try {
			TypedQuery<FundFavorite> nq = getEntityManager().createNamedQuery("FundFavorite.findByFdFund",
					FundFavorite.class);
			nq.setParameter("fdFund", fund);

			return nq.getResultList();

		} catch (Exception e) {

			return null;
		}
	}

	public FundFavorite addToFavorite(Users users, FdFund fund) {

		FundFavorite fav = getFavoriteByFundAndUsers(fund, users);

		if (fav != null) {

			return null;
		}

		fav = new FundFavorite();

		fav.setUsers(users);
		fav.setFdFund(fund);
		Calendar now = Calendar.getInstance();

		fav.setCreationDate(now.getTime());

		create(fav);

		return fav;
	}

	public boolean removeFavorite(FundFavorite fav) {

		remove(fav);

		return true;
	}

}
