/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdCategory2;
import com.insurfact.fundata.entity.FdPerformance2;
import java.util.List;
import java.util.Locale;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdPerformance2SessionBean extends AbstractFacade<FdPerformance2> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdPerformance2SessionBean() {
		super(FdPerformance2.class);
	}

	public List<FdPerformance2> findTopsByCategory(int rows, int highLow, int column, FdCategory2 category,
			Locale locale) {

		try {

			TypedQuery<FdPerformance2> nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "p.perfYtdReturn";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}
			}

			if (highLow == 0)
				sort = " DESC";
			else
				sort = " ASC";

//            String sql = "SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND p. ORDER BY " + order + sort ;
//            nq = em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdFund.class);

			nq = em.createQuery("SELECT p FROM FdPerformance2 p WHERE p.fdFund2.category = :category AND " + order
					+ " IS NOT NULL ORDER BY " + order + sort, FdPerformance2.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");
//            nq = em.createQuery("SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdPerformance.class);

			nq.setParameter("category", category);

			if (rows != 0)
				nq.setMaxResults(rows);

//            List<FdFund> funds = new ArrayList<>(); 
//         
//            List<FdPerformance> perfs = 
//            
//            for(FdPerformance performance: perfs){
//                FdFund fund = performance.getFdFund();
//                
//                fund.getPerformance();
//                
//                funds.add(fund);
//            }
			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

	public List<FdPerformance2> findTop10ByCategory(int highLow, int column, FdCategory2 category, Locale locale) {

		try {

			TypedQuery<FdPerformance2> nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "p.perfYtdReturn";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}
			}

			if (highLow == 0)
				sort = " DESC";
			else
				sort = " ASC";

//            String sql = "SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND p. ORDER BY " + order + sort ;
//            nq = em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdFund.class);

			nq = em.createQuery("SELECT p FROM FdPerformance2 p WHERE p.fdFund2.category = :category AND " + order
					+ " IS NOT NULL ORDER BY " + order + sort, FdPerformance2.class);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");
//            nq = em.createQuery("SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdPerformance.class);

			nq.setParameter("category", category);

			nq.setMaxResults(10);

//            List<FdFund> funds = new ArrayList<>(); 
//         
//            List<FdPerformance> perfs = 
//            
//            for(FdPerformance performance: perfs){
//                FdFund fund = performance.getFdFund();
//                
//                fund.getPerformance();
//                
//                funds.add(fund);
//            }
			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

	public List<FdPerformance2> findTopByCategory(int highLow, int column, FdCategory2 category) {

		try {

			TypedQuery<FdPerformance2> nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "p.perfYtdReturn";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 2: { // 2 year
				order = "p.perfTwoYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 4: { // 4 year
				order = "p.perfFourYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}

			case 10: { // Inception
				order = "p.perfInceptionReturn";
				break;
			}

			case 11: { // 1 Month
				order = "p.perfOneMonthReturn";
				break;
			}
			case 13: { // 3 Month
				order = "p.perfThreeMonthReturn";
				break;
			}

			}

			if (highLow == 0)
				sort = " DESC";
			else
				sort = " ASC";

//            String sql = "SELECT p FROM FdPerformance p WHERE p.fdFund.category = :category AND p. ORDER BY " + order + sort ;
//            nq = em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND "+ order + " IS NOT NULL ORDER BY " + order + sort,FdFund.class);

			nq = em.createQuery("SELECT p FROM FdPerformance2 p WHERE p.fdFund2.category = :category AND " + order
					+ " IS NOT NULL ORDER BY " + order + sort, FdPerformance2.class);
			nq.setParameter("category", category);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			nq.setMaxResults(1);

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

	public List<FdPerformance2> findTopOverall(int highLow, int column) {

		try {

			TypedQuery<FdPerformance2> nq = null;
			String order = "";
			String sort = "";

			switch (column) {

			case 0: { // YTD
				order = "p.perfYtdReturn";
				break;
			}
			case 1: { // 1 year
				order = "p.perfOneYrCompoundReturn";
				break;
			}
			case 2: { // 2 year
				order = "p.perfTwoYrCompoundReturn";
				break;
			}
			case 3: { // 3 year
				order = "p.perfThreeYrCompoundReturn";
				break;
			}
			case 4: { // 4 year
				order = "p.perfFourYrCompoundReturn";
				break;
			}
			case 5: { // 5 year
				order = "p.perfFiveYrCompoundReturn";
				break;
			}

			case 10: { // Inception
				order = "p.perfInceptionReturn";
				break;
			}

			case 11: { // 1 Month
				order = "p.perfOneMonthReturn";
				break;
			}
			case 13: { // 3 Month
				order = "p.perfThreeMonthReturn";
				break;
			}

			}

			if (highLow == 0)
				sort = " DESC";
			else
				sort = " ASC";

			nq = em.createQuery(
					"SELECT p FROM FdPerformance2 p WHERE " + order + " IS NOT NULL ORDER BY " + order + sort,
					FdPerformance2.class);
			nq.setMaxResults(1);
			// set when only read
			nq.setHint("eclipselink.read-only", "true");

			return nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

	}

	public FdPerformance2 getByFundataKey(Integer fundatakey) {

		EntityManager em = getEntityManager();

		try {
			TypedQuery<FdPerformance2> nq = em.createNamedQuery("FdPerformance2.findByPerfFundatakey",
					FdPerformance2.class);
			nq.setParameter("perfFundatakey", fundatakey);
			FdPerformance2 fdPerf = (FdPerformance2) nq.getSingleResult();

			return fdPerf;

		} catch (NoResultException e) {
			return null;
		}
	}

	public FdPerformance2 getByFundataKey(Long fundatakey) {

		EntityManager emLocal = getEntityManager();

		try {
			Query nq = emLocal.createNamedQuery("FdPerformance2.findByPerfFundatakey", FdPerformance2.class);
			nq.setParameter("perfFundatakey", fundatakey);
			FdPerformance2 fdPerf = (FdPerformance2) nq.getSingleResult();

			return fdPerf;

		} catch (NoResultException e) {
			return null;
		}
	}

	/**
	 * Return the count of all entries for FdPerformnce
	 * 
	 * @return
	 */
	@SuppressWarnings("unused")
	private Long getCountOfPerfEntries() {

		CriteriaBuilder builder = em.getCriteriaBuilder();
		CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
		countQuery.select(builder.count(countQuery.from(FdPerformance2.class)));
		return em.createQuery(countQuery).getSingleResult();
	}

}
