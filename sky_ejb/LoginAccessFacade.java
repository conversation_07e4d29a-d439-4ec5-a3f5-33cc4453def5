/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.LoginAccess;
import com.insurfact.skynet.entity.Users;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class LoginAccessFacade extends AbstractFacade<LoginAccess> {
	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public LoginAccessFacade() {
		super(LoginAccess.class);
	}

	public List<LoginAccess> findByUser(Users user) {

		TypedQuery<LoginAccess> nq = em.createNamedQuery("LoginAccess.findByUsers", LoginAccess.class);
		nq.setParameter("users", user);

		return nq.getResultList();
	}

	public LoginAccess findBySessionId(String id) {

		Query nq = em.createNamedQuery("LoginAccess.findBySessionId", LoginAccess.class);
		nq.setParameter("sessionId", id);

		LoginAccess access = null;

		try {
			access = (LoginAccess) nq.getSingleResult();
		} catch (NoResultException e) {
		}

		return access;
	}

	public List<LoginAccess> findByDetails(Date startDate, Date endDate, String appName) {

		List<LoginAccess> list = new ArrayList<>();

//        System.out.println(startDate);
//        System.out.println(endDate);

		TypedQuery<LoginAccess> nq;

		if (appName == null || appName.isEmpty()) {
			nq = em.createQuery(
					"SELECT l FROM LoginAccess l WHERE (l.timestamp >= :startDate) AND (l.timestamp <= :endDate) AND l.users IS NOT NULL ORDER BY l.timestamp DESC",
					LoginAccess.class);
			nq.setParameter("startDate", startDate);
			nq.setParameter("endDate", endDate);

		} else {
			nq = em.createQuery(
					"SELECT l FROM LoginAccess l WHERE l.applicationName = :appName AND l.timestamp >= :startDate AND l.timestamp <= :endDate AND l.users IS NOT NULL ORDER BY l.timestamp DESC",
					LoginAccess.class);
			nq.setParameter("startDate", startDate);
			nq.setParameter("endDate", endDate);
			nq.setParameter("appName", appName);
		}

		list = nq.getResultList();

		return list;
	}

	public List<LoginAccess> findByDetailsDistinctUsers(Date startDate, Date endDate, String appName) {

		List<LoginAccess> list = new ArrayList<>();

//        System.out.println(startDate);
//        System.out.println(endDate);

		TypedQuery<LoginAccess> nq;

		if (appName == null || appName.isEmpty()) {
			nq = em.createQuery(
					"SELECT l FROM LoginAccess l WHERE (l.timestamp >= :startDate) AND (l.timestamp <= :endDate) AND l.users IS NOT NULL ORDER BY l.timestamp DESC",
					LoginAccess.class);
			nq.setParameter("startDate", startDate);
			nq.setParameter("endDate", endDate);

		} else {
			nq = em.createQuery(
					"SELECT l FROM LoginAccess l WHERE l.applicationName = :appName AND l.timestamp >= :startDate AND l.timestamp <= :endDate AND l.users IS NOT NULL ORDER BY l.timestamp DESC",
					LoginAccess.class);
			nq.setParameter("startDate", startDate);
			nq.setParameter("endDate", endDate);
			nq.setParameter("appName", appName);
		}

		list = nq.getResultList();

		return list;
	}

}
