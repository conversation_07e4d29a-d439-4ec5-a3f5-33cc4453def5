/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Branch;
import com.insurfact.skynet.entity.Organization;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class BranchFacade extends AbstractFacade<Branch> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public BranchFacade() {
		super(Branch.class);
	}

	public List<Branch> findAllBranchesForOrganization(Organization org) {

		TypedQuery<Branch> nq = em.createNamedQuery("Branch.findByOrganization", Branch.class);
		nq.setParameter("organization", org);

		return nq.getResultList();
	}

	public List<Branch> findByMarketingRegion(Integer region, Organization org) {

		TypedQuery<Branch> nq = em.createNamedQuery("Branch.findByMarketingRegion", Branch.class);
		nq.setParameter("marketingRegion", region);
		nq.setParameter("organization", org);

		return nq.getResultList();
	}

	public Branch getByBranchCode(String code) {

		TypedQuery<Branch> nq = em.createNamedQuery("Branch.findByBranchCode", Branch.class);
		nq.setParameter("branchCode", code);

		Branch branch = null;

		try {
			branch = (Branch) nq.getSingleResult();
		} catch (Exception e) {
		}

		return branch;
	}

	public Branch getByBranchNumber(Integer number) {

		TypedQuery<Branch> nq = em.createNamedQuery("Branch.findByBranchNumber", Branch.class);
		nq.setParameter("branchNumber", number);

		Branch branch = null;

		try {
			branch = (Branch) nq.getSingleResult();
		} catch (Exception e) {
		}

		return branch;
	}

	public Branch getByBranchId(Integer ID) {

		TypedQuery<Branch> nq = em.createNamedQuery("Branch.findByBranchIntId", Branch.class);
		nq.setParameter("branchIntId", ID);

		Branch branch = null;

		try {
			branch = (Branch) nq.getSingleResult();
		} catch (Exception e) {
		}

		return branch;
	}

	public Branch getByBranchNumber(Organization org, Integer number) {

		Query nq = (Query) em.createQuery(
				"SELECT b FROM Branch b WHERE b.branchNumber = :number AND b.organization = :org", Branch.class);
		nq.setParameter("number", number);
		nq.setParameter("org", org);

		Branch branch = null;

		try {
			branch = (Branch) nq.getSingleResult();
		} catch (Exception e) {
		}

		return branch;
	}
}
