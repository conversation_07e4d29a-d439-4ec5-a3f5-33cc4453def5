/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;


import com.insurfact.skynet.entity.Door; 
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class DoorsFacade extends AbstractFacade<Door> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public DoorsFacade() {
        super(Door.class);
    }  
    
    public void editFile(Door d) {
        em.merge(d);
    }

}
