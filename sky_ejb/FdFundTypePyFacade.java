/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2018 All Right Reserved, https://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdFundTypePy;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFundTypePyFacade extends AbstractFacade<FdFundTypePy> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdFundTypePyFacade() {
		super(FdFundTypePy.class);
	}

	public List<FdFundTypePy> findAllSearchable() { // smasse

		return findNoSearch("N");

	}

	public List<FdFundTypePy> findNoSearch(String noSearch) { // smasse

		TypedQuery<FdFundTypePy> nq = em.createNamedQuery("FdFundTypePy.findByNoSearch", FdFundTypePy.class);

		nq.setHint("eclipselink.read-only", "true");

		nq.setParameter("noSearch", noSearch);

		return nq.getResultList();

	}

	public List<FdFundTypePy> findCategory(long typeKey) {
		TypedQuery<FdFundTypePy> nq = em.createNamedQuery("FdFundTypePy.findByTypeKey", FdFundTypePy.class);

		nq.setHint("eclipselink.read-only", "true");

		nq.setParameter("noSearch", typeKey);

		return nq.getResultList();
	}

//    @ Override
//    public List<FdCategory2> findAll(){  //smasse added 2018.8.21
//        
//        Query nq = em.createNamedQuery("FdCategory2.findAll");
//        
//        nq.setHint("eclipselink.read-only", "true");
//
//        return nq.getResultList();
//
//    }
}
