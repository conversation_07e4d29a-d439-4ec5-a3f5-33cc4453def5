/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Email;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class EmailFacade extends AbstractFacade<Email> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public EmailFacade() {
		super(Email.class);
	}

	public Email quickEdit(Email entity) {

		try {
			em.merge(entity);

		} catch (Exception ex) {
			String msg = ex.getLocalizedMessage();
			System.err.println(msg);
		}

		return entity;
	}

	public Email refresh(Email entity) {
		return find(entity.getEmailIntId());
	}

	public Email findByEMail(String email) {
	    try {
	        TypedQuery<Email> pq = em.createNamedQuery("Email.findByEmailAddress", Email.class);
	        pq.setParameter("emailAddress", email);
	        List<Email> results = pq.getResultList();
	        if (results.isEmpty()) {
	            return null;
	        } else {
	            // Handle the case where multiple results are found. For example, return the first one:
	            return results.get(0);
	        }
	    } catch (Exception e) { 
	        e.printStackTrace();
	        return null;
	    }
	}


	public Email findEmailByDetail(String emailAddress, int type, String primaryStr, String sendSolicatationStr) {

		List<Email> emails = null;

		/// convert to lower case
		emailAddress = emailAddress.toLowerCase();

		String queryStr = "SELECT e FROM Email e WHERE LOWER(e.emailAddress) = :emailAddress AND e.type = :type AND e.isPrimary = :isPrimary AND e.sendSolicitation = :sendSolicitation";

//        System.out.println("-----  Email Query = "+ QueryStr);
		try {
			TypedQuery<Email> pq = em.createQuery(queryStr, Email.class);

			pq.setParameter("emailAddress", emailAddress);
			pq.setParameter("type", type);

			pq.setParameter("isPrimary", primaryStr);
			pq.setParameter("sendSolicitation", sendSolicatationStr);

			// retrieve the Entity(es)
			emails = pq.getResultList();

			if ((emails != null) && (emails.size() > 0)) {
//                System.out.println("-----  EmailFacade, More than one Email was returned ="+emails.size());
				return emails.get(0);
			}
			return null;

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<Contact> findByEmailAddress(String email, String masterCode, Integer type) {

		email = "%" + email + "%";

		List<Contact> contacts = new ArrayList<>();

		try {
			TypedQuery<Email> nq = em.createQuery("SELECT a FROM Email a WHERE LOWER(a.emailAddress) like  :email",
					Email.class);
			nq.setParameter("email", email);

			List<Email> emails = nq.getResultList();

			if (emails != null && !emails.isEmpty()) {

				for (Email e : emails) {

					for (Contact c : e.getContactList()) {

						if (c.getMasterCode() == null || !c.getMasterCode().equalsIgnoreCase(masterCode)) {
							continue;
						}

						if (c.getContactType() == null || !c.getContactType().equals(type)) {
							continue;
						}

						contacts.add(c);
					}

				}
			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return contacts;
	}

	public List<Contact> findByEmailAddress(String email, String masterCode, List<Integer> types) {

		email = "%" + email + "%";

		List<Contact> contacts = new ArrayList<>();

		try {
			TypedQuery<Email> nq = em.createQuery("SELECT a FROM Email a WHERE LOWER(a.emailAddress) like  :email",
					Email.class);
			nq.setParameter("email", email);

			List<Email> emails = nq.getResultList();

			if (emails != null && !emails.isEmpty()) {

				for (Email e : emails) {

					for (Contact c : e.getContactList()) {

						if (c.getMasterCode() == null || !c.getMasterCode().equalsIgnoreCase(masterCode)) {
							continue;
						}

						if (c.getContactType() == null || !types.contains(c.getContactType())) {
							continue;
						}

						contacts.add(c);
					}

				}
			}

		} catch (NoResultException e) {
//            e.printStackTrace();
			return null;
		}

		return contacts;
	}
}
