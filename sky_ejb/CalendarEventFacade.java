/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import java.time.LocalDateTime;
import java.util.List;

import com.insurfact.skynet.entity.CalendarEvent;

import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class CalendarEventFacade extends AbstractFacade<CalendarEvent> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public CalendarEventFacade() {
		super(CalendarEvent.class);
	}

	public List<CalendarEvent> getEventsByTitle(String title) {
		return em.createNamedQuery("CalendarEvent.findByTitle", CalendarEvent.class).setParameter("eventTitle", title)
				.getResultList();
	}

	public List<CalendarEvent> getEventsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
		return em.createNamedQuery("CalendarEvent.findByDateRange", CalendarEvent.class)
				.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
	}
}
