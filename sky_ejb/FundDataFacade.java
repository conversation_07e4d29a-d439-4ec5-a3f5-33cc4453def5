/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdAllocation;
import com.insurfact.fundata.entity.FdAllocationPy;
import com.insurfact.fundata.entity.FdBenchmark;
import com.insurfact.fundata.entity.FdBenchmarkPy;
import com.insurfact.fundata.entity.FdBestCategory;
import com.insurfact.fundata.entity.FdBestCategory2;
import com.insurfact.fundata.entity.FdBestOverall;
import com.insurfact.fundata.entity.FdBestOverall2;
import com.insurfact.fundata.entity.FdCategory;
import com.insurfact.fundata.entity.FdCategory2;
import com.insurfact.fundata.entity.FdDaily;
import com.insurfact.fundata.entity.FdFund;
import com.insurfact.fundata.entity.FdFund2;
import com.insurfact.fundata.entity.FdFundCompany;
import com.insurfact.fundata.entity.FdFundCompanyPy;
import com.insurfact.fundata.entity.FdFundFavoritePy;
import com.insurfact.fundata.entity.FdFundFlexSearchPy;
import com.insurfact.fundata.entity.FdFundManagerPy;
import com.insurfact.fundata.entity.FdFundQuartilesPy;
import com.insurfact.fundata.entity.FdFundTypePy;
import com.insurfact.fundata.entity.FdFundserv;
import com.insurfact.fundata.entity.FdFundserv2;
import com.insurfact.fundata.entity.FdFundservePy;
import com.insurfact.fundata.entity.FdGenFundInfoPy;
import com.insurfact.fundata.entity.FdHolding;
import com.insurfact.fundata.entity.FdHolding2;
import com.insurfact.fundata.entity.FdHoldingsPy;
import com.insurfact.fundata.entity.FdManager2;
import com.insurfact.fundata.entity.FdMastQuartile;
import com.insurfact.fundata.entity.FdMaster;
import com.insurfact.fundata.entity.FdPerformance;
import com.insurfact.fundata.entity.FdPerformance2;
import com.insurfact.fundata.entity.FdPerformancePy;
import com.insurfact.fundata.entity.FdQuartiles;
import com.insurfact.fundata.entity.FdSecurityName;
import com.insurfact.fundata.entity.FdSecurityName2;
import com.insurfact.fundata.entity.FundFavorite2;
import com.insurfact.skynet.entity.FundFavorite;
import com.insurfact.skynet.entity.PriceDistribution;
import com.insurfact.skynet.entity.Users;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Stateless
public class FundDataFacade implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private final boolean DEBUG = true;

	@EJB
	private FdFundServSessionBean fdFundServSessionBean;

	@EJB
	private FdFundServ2SessionBean fdFundServ2SessionBean;

	@EJB
	private FdFundSessionBean fdFundSessionBean;

	@EJB
	private FdFund2SessionBean fdFund2SessionBean;

	@EJB
	private FdFundFlexSearchPyFacade fdFundPyFacade;// smasse 2019.3.29 ADDED

	// smasse 2019.4.12 ADDED
	@EJB
	private FdFundFavoritePyFacade fdFundFavoritePyFacade;

	@EJB
	private FdFavoriteSessionBean favoriteSessionBean;

	@EJB
	private FdFavorite2SessionBean favorite2SessionBean;

	@EJB
	private FdCategorySessionBean fdCategorySessionBean;

	@EJB
	private FdCategory2Facade fdCategory2Facade;// smasse 2018.8.21 ADDED

	@EJB
	private FdFundTypePyFacade fdFundTypePyFacade;// smasse 2019.3.29 ADDED

	@EJB
	private FdFundCompanyFacade fdFundCompanyFacade;// smasse 2018.8.21 ADDED

	@EJB
	private FdFundCompanyPyFacade fdFundCompanyPyFacade;// smasse 2019.3.29 ADDED

	@EJB
	private FdMonthlyBestByCatFacade fdMonthlyBestByCatFacade; // smasse 2017-4-21 ADDED

//    @ EJB
//    private FdFund2Facade fdFund2Facade;//smasse 2017-11-17 ADDED

	@EJB
	private FdQuartilesSessionBean fdQuartilesSessionBean;// smasse 2017 ADDED

	@EJB
	private FdBenchmarkSessionBean fdBenchmarkSessionBean;// smasse 2017 ADDED

	@EJB
	private FdBenchmarkPySessionBean fdBenchmarkPySessionBean;// smasse 2019 ADDED

	@EJB
	private FdGenFundInfoPySessionBean fdGenFundInfoPySessionBean;// smasse 2019 ADDED

	@EJB
	private FdMasterSessionBean fdMasterSessionBean;

	@EJB
	private FdManagerSessionBean fdManagerSessionBean;

	@EJB
	private FdManager2SessionBean fdManager2SessionBean;

	@EJB
	private FdFundManagerPySessionBean fdFundManagerPySessionBean;// smasse 2019 ADDED

	@EJB
	private FdHoldingsPySessionBean fdHoldingsPySessionBean;// smasse 2019 ADDED

	@EJB
	private FdAllocationSessionBean fdAllocationSessionBean;

	@EJB
	private FdAllocationPySessionBean fdAllocationPySessionBean;// smasse 2019 ADDED

	@EJB
	private FdSecurityNameSessionBean fdSecurityNameSessionBean;

	@EJB
	private FdSecurityName2SessionBean fdSecurityName2SessionBean;

	@EJB
	private FdHoldingSessionBean fdHoldingSessionBean;

	@EJB
	private FdHolding2SessionBean fdHolding2SessionBean;

	@EJB
	private FdFundQuartilesPySessionBean fdQuartilesPySessionBean;// smasse 2019 ADDED

//    @ EJB
//    private FdHoldingsPySessionBean fdHoldingsPySessionBean;

	@EJB
	private FdDailySessionBean fdDailySessionBean;

	@EJB
	private FdMastQuartileSessionBean fdQuartileSessionBean;

	@EJB
	private FdBestCategorySessionBean fdBestCategorySessionBean;

	@EJB
	private FdBestCategory2Facade fdBestCategory2Facade;

	@EJB
	private FdBestOverallSessionBean fdBestOverallSessionBean;

	@EJB
	private FdBestOverall2Facade fdBestOverall2Facade;

	@EJB
	private PriceDistributionSessionBean priceDistributionSessionBean;

	@EJB
	private FdPerformanceSessionBean fdPerformanceSessionBean;

	@EJB
	private FdPerformance2SessionBean fdPerformance2SessionBean;

	@EJB
	private FdPerformancePySessionBean fdPerformancePySessionBean;// smasse 2019 ADDED

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	public FundDataFacade() {
	}

	public FdMaster getMaster(Integer id) {
		return fdMasterSessionBean.find(id);
	}

	public FdFundCompany getFdFundCompany(Integer id) {
		return fdFundCompanyFacade.find(id);
	}

	/**
	 * return price info from FundSERV
	 *
	 * @param mgmtCompany
	 * @param fundId
	 * @return
	 */
	public List<PriceDistribution> findPricesForFund(String mgmtCompany, String fundId) {

		return priceDistributionSessionBean.findPricesForFund(mgmtCompany, fundId);
	}

	//////////////////////
	//
	// DAILYS
	//

	/**
	 * retrieves one year of data from Fundata
	 *
	 * @param datakey
	 * @return
	 */
	public List<FdDaily> findYearlyDailysByDataKey(Integer datakey) {

		return fdDailySessionBean.findDailysByDataKey(datakey);
	}

	public List<FdDaily> findYearlyDailysByDataKey(Long datakey) {

		return fdDailySessionBean.findDailysByDataKey(datakey);
	}

	/**
	 * retrieves all available price data from Fundata
	 *
	 * @param datakey
	 * @return
	 */
	public List<FdDaily> findAllDailysByDataKey(Integer datakey) {

		return fdDailySessionBean.findAllDailysByDataKey(datakey);
	}

	//////////////////////
	//
	// FUNDSERV
	//
	public FdFundserv getFundServByCode(String fundservCode) {
		return fdFundServSessionBean.getByFundServCode(fundservCode);
	}

	public FdFundserv2 getFundServ2ByCode(String fundservCode) { // smasse 2018.11.2
		return fdFundServ2SessionBean.getByFundServCode(fundservCode);
	}

	public FdFundservePy getFundServPyByCode(String fundservCode) { // smasse 2019.4.15 TODO washere washere
		return null;// fdFundServPySessionBean.getByFundServCode(fundservCode);
	}

	public List<FdFundserv> searchFundservByLookups(String fsrvCode, Integer categoryId) {

		FdCategory category = null;

		if (categoryId != null) {
			category = getCategory(categoryId);
		}

		return fdFundServSessionBean.findFundservByCriterias(fsrvCode, category);
	}

	public List<FdFundserv2> searchFundserv2ByLookups(String fsrvCode, Integer categoryId) {

		FdCategory2 category = null;

		if (categoryId != null) {
			category = getCategory2(categoryId);
		}

		return fdFundServ2SessionBean.findFundservByCriterias(fsrvCode, category);
	}

	//////////////////////
	//
	// BESTS
	//
	public FdBestCategory getBestCategory(FdCategory category) {

		return fdBestCategorySessionBean.getBestCategory(category);
	}

	public FdBestCategory2 getBestCategory2(FdCategory2 category) {

		return fdBestCategory2Facade.getBestCategory2(category);
	}

	public FdBestOverall getBestOverall() {

		return fdBestOverallSessionBean.getBestOverall();
	}

	public FdBestOverall2 getBestOverall2() {

		return fdBestOverall2Facade.getBestOverall();
	}

	//////////////////////
	//
	// FUNDS
	//

	public FdFund2 getByFundatakey2(Long key) {

		return fdFund2SessionBean.getByFundataKey(key);
	}

	/**
	 * smasse 2018.8.24 called by FundNavigatorBean:
	 * 
	 * @return List of FdFundCompany instances
	 */
	public List<FdFundCompany> findAllFundCompanies() {
		return fdFundCompanyFacade.findAll();
	}

	/**
	 * smasse 2018.8.24 called by FundNavigatorBean:
	 * 
	 * @return List of FdFundCompany instances
	 */
	public List<FdFundCompanyPy> findAllFundCompaniesPy() {
		return fdFundCompanyPyFacade.findAll();
	}

	/**
	 * smasse 2018.8.24
	 * 
	 * @return List of FdFund2 instances: all data from table FD_FUND2
	 */
	public List<FdFund2> getAllFunds() {

		return fdFund2SessionBean.getAllFunds(); // TODO if exception, then show Ooops screen
	}

	public List<FdFundFlexSearchPy> getAllFundsPy() {

		return fdFundPyFacade.getAllFundsEnabled(); // TODO if exception, then show Ooops screen
	}

	// ================

	public FdFund getByFundatakey(Integer key) {

		return fdFundSessionBean.getByFundataKey(key);
	}

	public FdFund2 getByFundatakey2(Integer key) {

		return fdFund2SessionBean.getByFundataKey(key);
	}

//    public FdFundFlexSearchPy getByFundatakeyPy(Integer key) {//smasse 2019.4.15
//
//        return fdFundPyFacade.getByFundataKey(key);
//    }
	public FdFundFlexSearchPy getByFundatakeyPy(Long key) {// smasse 2019.6.20

		return fdFundPyFacade.getByFundataKey(key);
	}

	public FdFund loadAllFundDatas(FdFund fdFund) {

		long start = System.currentTimeMillis();

		if (fdFund != null) {
			Integer dataKey = fdFund.getFundFundatakey();

			if (dataKey != null) {

//                FdPerformance fundPerf = fdFund.getPerformance();
//                if (fundPerf == null) {
//                    fundPerf = fdPerformanceFacade.getByFundataKey(dataKey);
//                    fdFund.setPerformance(fundPerf);
//                }
				List<FdAllocation> allocations = fdFund.getAllocations();
				if (allocations == null || allocations.isEmpty()) {
					allocations = fdAllocationSessionBean.getByFundataKey(dataKey);
					fdFund.setAllocations(allocations);
				}

//                List<FdHolding> holdings = fdFund.getHoldings();
//                if (holdings == null || holdings.isEmpty()) {
//                    holdings = fdHoldingFacade.getByFundataKey(dataKey);
//                    fdFund.setHoldings(holdings);
//                }
//                List<FdManager> managers = fdFund.getManagers();
//                if (managers == null || managers.isEmpty()) {
//                    managers = fdManagerFacade.getByFundataKey(dataKey);
//                    fdFund.setManagers(managers);
//                }
//                FdDaily latestDaily = fdFund.getFundDaily();
//                if (latestDaily == null) {
//                    latestDaily = fdDailySessionBean.getLatestByFundDataKey(dataKey);
//                    fdFund.setFundDaily(latestDaily);
//                }
				FdMastQuartile quartile = fdFund.getQuartile();
				if (quartile == null) {
					quartile = fdQuartileSessionBean.getByFundataKey(dataKey);
					fdFund.setQuartile(quartile);
				}

			} else {
				System.out.println(">>>>DATAKEY IS NULL!!");
			}
		} else {
			System.out.println(">>>>FUNDSERV IS NULL!!");
		}

		long end = System.currentTimeMillis();

		if (DEBUG)
			System.out.println("debug *** loadAllFundDatas :" + (end - start));

		return fdFund;
	}

	/**
	 * Called by FundViewerBean2.init2()
	 * 
	 * @param fdFund FdFund2
	 * @return FdFund2
	 */
	public FdFund2 loadAllFundDatas(FdFund2 fdFund) {

		long start = System.currentTimeMillis();

		if (fdFund != null) {
			Long dataKey = fdFund.getFundFundatakey();

			if (dataKey != null) {

//                FdPerformance fundPerf = fdFund.getPerformance();
//                if (fundPerf == null) {
//                    fundPerf = fdPerformanceFacade.getByFundataKey(dataKey);
//                    fdFund.setPerformance(fundPerf);
//                }
				List<FdAllocation> allocations = fdFund.getAllocations();
				if (allocations == null || allocations.isEmpty()) {
					allocations = fdAllocationSessionBean.getByFundataKey(dataKey);
					fdFund.setAllocations(allocations);
				}

				List<FdHolding2> holdings = fdFund.getHoldings();
				if (holdings == null || holdings.isEmpty()) {
					holdings = fdHolding2SessionBean.getByFundataKey(dataKey);
					fdFund.setHoldings(holdings);
				}

				List<FdManager2> managers = fdFund.getManagers();
				if (managers == null || managers.isEmpty()) {
					managers = fdManager2SessionBean.getByFundataKey(dataKey);
					fdFund.setManagers(managers);
				}

//                FdDaily latestDaily = fdFund.getFundDaily();
//                if (latestDaily == null) {
//                    latestDaily = fdDailySessionBean.getLatestByFundDataKey(dataKey);
//                    fdFund.setFundDaily(latestDaily);
//                }
//                FdMastQuartile quartile = fdFund.getQuartile();
//                if (quartile == null) {
//                    quartile = fdQuartileSessionBean.getByFundataKey(dataKey);
//                    fdFund.setQuartile(quartile);
//                }

				FdQuartiles quartiles = fdFund.getFdQuartiles();
				if (quartiles == null) {
					quartiles = fdQuartilesSessionBean.getByFundataKey(dataKey); // smasse 2018.11.2
					fdFund.setFdQuartiles(quartiles);
				}

				FdBenchmark benchmark = fdFund.getFdBenchmark();// smasse 2018.11.27
				if (benchmark == null) {
					benchmark = fdBenchmarkSessionBean.getByFundataKey(dataKey);
					fdFund.setFdBenchmark(benchmark);
				}

				FdFundCompany fundCompany = null;
				String fundCompanyNameEn = fdFund.getFundCompanyNameEn();
				if (fundCompanyNameEn == null || fundCompanyNameEn.isEmpty()) {
					fundCompany = fdFundCompanyFacade.getByFundataKey(dataKey);
					if (fundCompany != null) {
						fdFund.setFundCompanyNameEn(fundCompany.getFundCompanyEn());
					}
				}
				String fundCompanyNameFr = fdFund.getFundCompanyNameFr();
				if (fundCompanyNameFr == null || fundCompanyNameFr.isEmpty()) {
					if (fundCompany == null)
						fundCompany = fdFundCompanyFacade.getByFundataKey(dataKey);
					if (fundCompany != null) {
						fdFund.setFundCompanyNameFr(fundCompany.getFundCompanyFr());
					}
				}

				// stdDev and VolatilityRanking, both from fd_quartiles

				FdQuartiles q = null;

				BigDecimal stdDev = fdFund.getStdDev();
				if (stdDev == null || stdDev.equals(BigDecimal.ZERO)) {
					q = fdQuartilesSessionBean.getByFundataKey(dataKey);
					if (q != null) {
						fdFund.setStdDev(q.getYr3AnnualizedStdDev());
					} else {
						fdFund.setStdDev(BigDecimal.ZERO);
					}
				}

				int vr = fdFund.getVolatilityRanking();
				if (vr == 0) {
					if (q == null)
						q = fdQuartilesSessionBean.getByFundataKey(dataKey);
					if (q != null) {
						fdFund.setVolatilityRanking(q.getYr3VolatilityRanking());
					}
				}

			} else {
				System.out.println(">>>>DATAKEY IS NULL!!");
			}
		} else {
			System.out.println(">>>>FUNDSERV IS NULL!!");
		}

		long end = System.currentTimeMillis();

		if (DEBUG)
			System.out.println("debug *** loadAllFundDatas :" + (end - start));

		return fdFund;
	}

	/**
	 * Called by FundViewerBeanPy.init2()
	 * 
	 * @param fdFund FdFundFlexSearchPy
	 * @return FdFundFlexSearchPy
	 */
	public FdFundFlexSearchPy loadAllFundDatas(FdFundFlexSearchPy fdFund) { // TODO washere washere 2019.4.15

//        long start = System.currentTimeMillis(); 
//
//        if (fdFund != null) {
//            Long dataKey = fdFund.getFundFundatakey();
//
//            if (dataKey != null) {
//
////                FdPerformance fundPerf = fdFund.getPerformance();
////                if (fundPerf == null) {
////                    fundPerf = fdPerformanceFacade.getByFundataKey(dataKey);
////                    fdFund.setPerformance(fundPerf);
////                }
//                List<FdAllocation> allocations = fdFund.getAllocations();
//                if (allocations == null || allocations.isEmpty()) {
//                    allocations = fdAllocationSessionBean.getByFundataKey(dataKey);
//                    fdFund.setAllocations(allocations);
//                }
//
//                List<FdHoldingsPy> holdings = fdFund.getHoldings();
//                if (holdings == null || holdings.isEmpty()) {
//                    holdings = fdHolding2SessionBean.getByFundataKey(dataKey);
//                    fdFund.setHoldings(holdings);
//                }
//                
//                List<FdFundManagerPy> managers = fdFund.getManagers();
//                if (managers == null || managers.isEmpty()) {
//                    managers = fdManager2SessionBean.getByFundataKey(dataKey);
//                    fdFund.setManagers(managers);
//                }
//
//                FdQuartiles quartiles = fdFund.getFdQuartiles();
//                if (quartiles == null) {
//                    quartiles = fdQuartilesSessionBean.getByFundataKey(dataKey); //smasse 2018.11.2
//                    fdFund.setFdQuartiles(quartiles);
//                }
//                
//                FdBenchmark benchmark = fdFund.getFdBenchmark();//smasse 2018.11.27
//                if(benchmark==null){
//                    benchmark = fdBenchmarkSessionBean.getByFundataKey(dataKey);
//                    fdFund.setFdBenchmark(benchmark);
//                }
//                
//                FdFundCompany fundCompany = null;
//                String fundCompanyNameEn = fdFund.getFundCompanyNameEn();
//                if(fundCompanyNameEn==null || fundCompanyNameEn.isEmpty()){
//                    fundCompany = fdFundCompanyFacade.getByFundataKey(dataKey);
//                    if(fundCompany!=null){
//                        fdFund.setFundCompanyNameEn(fundCompany.getFundCompanyEn());
//                    }
//                }
//                String fundCompanyNameFr = fdFund.getFundCompanyNameFr();
//                if(fundCompanyNameFr==null || fundCompanyNameFr.isEmpty()){
//                    if(fundCompany==null) fundCompany = fdFundCompanyFacade.getByFundataKey(dataKey);
//                    if(fundCompany!=null){
//                        fdFund.setFundCompanyNameFr(fundCompany.getFundCompanyFr());
//                    }
//                }
//            
//                // stdDev and VolatilityRanking, both from fd_quartiles
//                
//                FdQuartiles q = null;
//                
//                BigDecimal stdDev = fdFund.getStdDev();
//                if(stdDev==null || stdDev.equals(BigDecimal.ZERO)){
//                    q = fdQuartilesSessionBean.getByFundataKey(dataKey);
//                    if(q!=null){
//                        fdFund.setStdDev(q.getYr3AnnualizedStdDev());
//                    }else{
//                        fdFund.setStdDev(BigDecimal.ZERO);
//                    }
//                }
//                
//                int vr = fdFund.getVolatilityRanking();
//                if( vr==0 ){
//                    if(q==null)q = fdQuartilesSessionBean.getByFundataKey(dataKey);
//                    if(q!=null){
//                        fdFund.setVolatilityRanking(q.getYr3VolatilityRanking());
//                    }
//                }
//                
//
//            } else {
//                System.out.println(">>>>DATAKEY IS NULL!!");
//            }
//        } else {
//            System.out.println(">>>>FUNDSERV IS NULL!!");
//        }
//
//        long end = System.currentTimeMillis();
//
//        if(DEBUG) System.out.println("debug *** loadAllFundDatas :" + (end - start));

		return fdFund;
	}

	public List<FdManager2> findManagers(Long key) {
		List<FdManager2> managers = fdManager2SessionBean.getByFundataKey(key);

		return managers;
	}

	public List<FdFundManagerPy> findManagerPys(Long key) {
		return fdFundManagerPySessionBean.getByFundataKey(key);
	}

	public List<FdHoldingsPy> findHoldingsPys(Long key) {
		return fdHoldingsPySessionBean.getByFundataKey(key);
	}

	public FdPerformancePy findPerformance(Long key) {
		return fdPerformancePySessionBean.getByFundataKey(key);
	}

	public FdBenchmark findBenchmark(Long key) {
		return fdBenchmarkSessionBean.getByFundataKey(key);
	}

	public FdBenchmarkPy findBenchmarkPy(Long key) {
		return fdBenchmarkPySessionBean.getByFundataKey(key);
	}

	public List<FdFundQuartilesPy> findFdFundQuartilesPy(Long key) {
		return fdQuartilesPySessionBean.getByFundataKey(key);
	}

	public List<FdAllocationPy> findAllocationPys(Long key) {
		return fdAllocationPySessionBean.getByFundataKey(key);
	}

	public FdGenFundInfoPy findFdGenFundInfoPy(Long key) {
		return fdGenFundInfoPySessionBean.getByFundataKey(key);
	}

	public List<FdFund> searchByAssets(int merRange, Integer categoryId, Locale locale) {

		FdCategory category = getCategory(categoryId);

		return fdFundSessionBean.findByAssets(merRange, category, locale);
	}

	public List<FdFund2> searchByAssets2(int merRange, Integer categoryId, Locale locale) {

		FdCategory2 category = getCategory2(categoryId);

		return fdFund2SessionBean.findByAssets(merRange, category, locale);
	}

	public List<FdFund> searchFundByLookups(Integer masterId, Integer categoryId, boolean french) {

		FdCategory category = null;
		FdMaster master = null;

		if (categoryId != null) {
			category = getCategory(categoryId);
		}

		if (masterId != null) {
			master = getMaster(masterId);
		}

//        System.out.println("*** searchFundByLookups : master="+master + " category="+category);
		return fdFundSessionBean.findFundByCriterias(master, category, french);

	}

	public List<FdFund2> searchFund2ByLookups(Integer masterId, Integer categoryId, boolean french) {

		FdCategory2 category = null;
		FdFundCompany master = null;

		if (categoryId != null) {
			category = getCategory2(categoryId);
		}

		if (masterId != null) {
			master = getFdFundCompany(masterId);
		}

//        System.out.println("*** searchFundByLookups : master="+master + " category="+category);
		return fdFund2SessionBean.findFundByCriterias(master, category, french);

	}

	// unused!
	public FdFund loadFundServData1(Integer dataKey) {

		FdFund fund = null;
		if (dataKey != null) {

			fund = fdFundSessionBean.getByFundataKey(dataKey);

			if (fund != null) {
//                fund.setFundServ(fundservFacade.getByFundataKey(dataKey));
//                fund.setPerformance(performanceFacade.getByFundataKey(dataKey));
				fund.setAllocations(fdAllocationSessionBean.getByFundataKey(dataKey));
//                fund.setHoldings(holdingFacade.getByFundataKey(dataKey));
//                fund.setManagers(fdManagerFacade.getByFundataKey(dataKey));
//                fund.setFundDaily(fdDailySessionBean.getLatestByFundDataKey(dataKey));
				fund.setQuartile(fdQuartileSessionBean.getByFundataKey(dataKey));
			}

		} else {
			System.out.println(">>>>DATAKEY IS NULL!!");
		}

		return fund;
	}

	public List<FdFund> findAllFundsByCategory(int categoryId) {

		FdCategory category = getCategory(categoryId);

		return fdFundSessionBean.findAllByCategory(category);
	}

	public List<FdFund2> findAllFund2sByCategory(FdCategory2 category) {

		// FdCategory2 category = getCategory2(categoryId);

		return fdFund2SessionBean.findAllByCategory(category);
	}

	public List<FdFund2> findAllFund2sByCategoryId(Long categoryId) {

		return fdFund2SessionBean.findAllByCategoryId(categoryId);
	}

	public List<FdFund2> findAllFund2sByCategoryId(Integer categoryId) {

		return fdFund2SessionBean.findAllByCategoryId(Long.valueOf(categoryId));
	}

	//////////////////////
	//
	// HOLDINGS
	//
	public List<FdHolding> searchByHolding(Integer holdingId, String holdingName, boolean market, boolean asset) {

		return fdHoldingSessionBean.findByHoldingIdName(holdingId, holdingName, market, asset);
	}

	public List<FdHolding2> searchByHolding2(Integer holdingId, String holdingName, boolean market, boolean asset) {

		return fdHolding2SessionBean.findByHoldingIdName(holdingId, holdingName, market, asset);
	}

	//////////////////////
	//
	// PERFORMANCE
	//
	public List<FdPerformance> searchTop10(int highLow, int column, Integer categoryId, Locale locale) {

		FdCategory category = getCategory(categoryId);

		return fdPerformanceSessionBean.findTop10ByCategory(highLow, column, category, locale);
	}

	public List<FdPerformance> searchTops(int rows, int highLow, int column, Integer categoryId, Locale locale) {

		FdCategory category = getCategory(categoryId);

		return fdPerformanceSessionBean.findTopsByCategory(rows, highLow, column, category, locale);
	}

	public List<FdPerformance2> searchTops2(int rows, int highLow, int column, Integer categoryId, Locale locale) {

		FdCategory2 category = getCategory2(categoryId);

		return fdPerformance2SessionBean.findTopsByCategory(rows, highLow, column, category, locale);
	}

	public List<FdPerformance2> getAllPerformances() {

		return fdPerformance2SessionBean.findAll();
	}

	public FdPerformance2 getPerformance(final long key) {

		return fdPerformance2SessionBean.getByFundataKey(key);
	}

	//////////////////////
	//
	// CATEGORIES
	//
	public List<FdCategory> findAllCategory() {
		return fdCategorySessionBean.findAll();
	}

	public List<FdCategory> findAllSearchableCategory() {
		return fdCategorySessionBean.findAllSearchable();
	}

	public FdCategory getCategory(Integer id) {
		return fdCategorySessionBean.find(id);
	}

	public List<FdCategory2> findAllCategory2() {
		return fdCategory2Facade.findAll();
	}

	public List<FdCategory2> findAllSearchableCategory2() {
		return fdCategory2Facade.findAllSearchable();// TODO select those with non-searchable = 'N' smasse 2018-8-24
	}

	public List<FdFundTypePy> findAllSearchableCategoryPy() {
		return fdFundTypePyFacade.findAllSearchable();// TODO select those with non-searchable = 'N' smasse 2018-8-24
	}

	public FdCategory2 getCategory2(Long id) {
		return fdCategory2Facade.find(id);
	}

	public FdCategory2 getCategory2(Integer id) {
		return fdCategory2Facade.find(Long.valueOf(id));
	}

	public FdFundTypePy getFdFundTypePy(Long id) {// smasse 2019.4.15
		return fdFundTypePyFacade.find(id);
	}

	/**
	 * Used by admin to reset the dashboard. Performs the equivalent of these SQL
	 * statements:
	 *
	 * <p/>
	 * insert into FD_MONTHLY_BEST_BY_CAT (SEQ_NUMBER) (select ytd_return_fund FROM
	 * FD_BEST_CATEGORY where FD_CATEGORY in (1019,1020,1022,1031,1043));
	 *
	 * <p/>
	 * UPDATE FD_MONTHLY_BEST_BY_CAT a SET a.CREATION_DATE=SYSDATE,
	 * a.PERF_SEQ=(SELECT b.PERf_SEQ FROM FD_PERFORMANCE b where
	 * a.seq_number=b.PERF_FUNDATAKEY);
	 *
	 * <p/>
	 * getCategory = @NamedQuery(name = "FdCategory.findByCategoryIntId"
	 *
	 * <p/>
	 * Called by FundNavigatorBean.
	 */
	public void resetTableFdMonthlyBestByCat() {

		List<FdBestCategory> bestCats = new ArrayList<>();

		bestCats.add(getBestCategory(getCategory(1019)));
		bestCats.add(getBestCategory(getCategory(1020)));
		bestCats.add(getBestCategory(getCategory(1022)));
		bestCats.add(getBestCategory(getCategory(1031)));
		bestCats.add(getBestCategory(getCategory(1043)));

		fdMonthlyBestByCatFacade.resetTable(bestCats); // smasse 2017-4-21 washere washere
	}

	//////////////////////
	//
	// MASTERS - Deprecated
	//
	public List<FdMaster> findAllMasters() {
		return fdMasterSessionBean.findAll();
	}

	public List<FdMaster> findAllMastersNoSel(String compIDs) {
		return fdMasterSessionBean.findAllMastersNoSel(compIDs);
	}

	//////////////////////
	//
	// SECURITIES
	//
	public List<FdSecurityName> findAllSecurityNames() {
		return fdSecurityNameSessionBean.findAll();
	}

	public List<FdSecurityName2> findAllSecurityName2s() {// smasse 2018.9.25 - TODO washere bug 2018-11-20
															// OutOfMemoryError
		return fdSecurityName2SessionBean.findAll();
	}

	//////////////////////
	//
	// FUND FAVORITES
	//
	public List<FundFavorite> findFavoritesByUsers(Users users) {

		return favoriteSessionBean.findFavoriteByUsers(users);
	}

	public List<FundFavorite2> findFavorites2ByUsers(Users users) {

		return favorite2SessionBean.findFavoriteByUsers(users);
	}

	public List<FdFundFavoritePy> findFavoritesPyByUsers(Users users) {

		return fdFundFavoritePyFacade.findFavoritesByUsers(users);
	}

	public List<FundFavorite> findFavoritesByFdFund(FdFund fund) {

		return favoriteSessionBean.findFavoriteByFund(fund);
	}

	public FdFundFavoritePy addToFavorite(Users users, FdFundFlexSearchPy fund) {// TODO 2019.6.18 washere washere
																					// complete - needed??? 2019.6.20

		return fdFundFavoritePyFacade.addToFavoritesInDatabase(users, fund); // smasse 2019.4.12
	}

	public boolean removeFavorite(FdFundFavoritePy fav) {// TODO 2019.6.18 washere washere complete - needed???
															// 2019.6.20

		return fdFundFavoritePyFacade.removeFavoriteFromDatabase(fav);
	}

	//////////////////////
	//
	// FIX AND FK REBUILD
	//
	public void fixBestOverall() {

		Calendar now = Calendar.getInstance();

		FdBestOverall best = new FdBestOverall();
		best.setCreationDate(now.getTime());

		int[] values = { 0, 1, 2, 3, 4, 5, 10, 11, 13 };

		for (int val : values) {
			List<FdPerformance> performances = fdPerformanceSessionBean.findTopOverall(0, val);

			if (performances != null && !performances.isEmpty()) {
				FdPerformance perf = performances.get(0);

				if (val == 11) {
					best.setOneMonthReturn(perf.getPerfOneMonthReturn());
					best.setOneMonthReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 13) {
					best.setThreeMonthReturn(perf.getPerfThreeMonthReturn());
					best.setThreeMonthReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 0) {
					best.setYtdReturn(perf.getPerfYtdReturn());
					best.setYtdReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 1) {
					best.setOneYrCompoundReturn(perf.getPerfOneYrCompoundReturn());
					best.setOneYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 2) {
					best.setTwoYrCompoundReturn(perf.getPerfTwoYrCompoundReturn());
					best.setTwoYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 3) {
					best.setThreeYrCompoundReturn(perf.getPerfThreeYrCompoundReturn());
					best.setThreeYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 4) {
					best.setFourYrCompoundReturn(perf.getPerfFourYrCompoundReturn());
					best.setFourYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 5) {
					best.setFiveYrCompoundReturn(perf.getPerfFiveYrCompoundReturn());
					best.setFiveYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 10) {
					best.setInceptionReturn(perf.getPerfInceptionReturn());
					best.setOneMonthReturnFund(perf.getFdFund().getFundFundatakey());
				}
			}
		}

		// save
		fdBestOverallSessionBean.create(best);

	}

	public List<FdFund2> findAllFund2sForFlexSearch(Long flexSearchSponsorId, Integer flexSearchCategoryId,
			int rrspElig, int loadType, int fundType, String grade, int merCondition, int merValue, int assetCondition,
			int assetValue) {

		List<FdFund2> funds = new ArrayList<>();

		// [[[TODO]]] smasse 2018-10-9

		return funds;
	}

}
