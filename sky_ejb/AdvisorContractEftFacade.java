/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import jakarta.ejb.Stateless;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class AdvisorContractEftFacade {


//    @Override
//    public void edit(ContractEft advisorContractEft) {
//
//        Date now = Calendar.getInstance().getTime();
//
//        ContractEft persistentContractEft = em.find(ContractEft.class, advisorContractEft.getContractEftIntId());
//        Advisor advisorOld = persistentContractEft.getAdvisor();
//        Advisor advisorNew = advisorContractEft.getAdvisor();
//        if (advisorNew != null) {
//            advisorNew = em.getReference(advisorNew.getClass(), advisorNew.getAdvisorIntId());
//            
//            advisorNew.setLastModificationDate(now);
//            advisorContractEft.setAdvisor(advisorNew);
//        }
//        
//        advisorContractEft.setLastModificationDate(now);
//        advisorContractEft = em.merge(advisorContractEft);
//        
//        if (advisorOld != null && !advisorOld.equals(advisorNew)) {
//            advisorOld.getContractEftList().remove(advisorContractEft);
//            
//            advisorOld.setLastModificationDate(now);
//            advisorOld = em.merge(advisorOld);
//        }
//        if (advisorNew != null && !advisorNew.equals(advisorOld)) {
//            advisorNew.getContractEftList().add(advisorContractEft);
//            
//            advisorNew.setLastModificationDate(now);
//            advisorNew = em.merge(advisorNew);
//        }
//    }

//    public void destroy(Integer id){
//
//        Date now = Calendar.getInstance().getTime();
//
//        ContractEft advisorContractEft;
//
//        advisorContractEft = em.getReference(ContractEft.class, id);
//        advisorContractEft.getContractEftIntId();
//
//        Advisor advisor = advisorContractEft.getAdvisor();
//        Integer advisorID = advisor.getAdvisorIntId();
//
//        if (advisor != null) {
//            advisor.getContractEftList().remove(advisorContractEft);
//            
//            advisor.setLastModificationDate(now);
//            advisor = em.merge(advisor);
//        }
//        
//        imageStoreFacade.destroy(id, 4, advisorID, 14);
//        em.remove(advisorContractEft);
//    }
}
