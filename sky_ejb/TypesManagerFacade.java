/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.TypeClass;
import com.insurfact.skynet.entity.Types;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class TypesManagerFacade {

    @EJB
    private TypeClassEntity typeClassEntity;

    @EJB
    private TypesEntity typesEntity;

    public List<TypeClass> findAllTypeClass() {

        return typeClassEntity.findAll();

    }

    public Types find(Integer id) {

        Types type = typesEntity.find(id);

        return type;
    }

    public List<Types> findTypesByTypeClass(TypeClass typeClass) {

        List<Types> types;

        types = typesEntity.findTypesByTypeClass(typeClass);

        return types;
    }

    public void saveTypeClass(TypeClass typeClass) {

        typeClass.setLastModificationDate(new Date());
        typeClassEntity.edit(typeClass);
    }

    public void createTypeClass(TypeClass typeClass) {

        Date now = Calendar.getInstance().getTime();

        typeClass.setCreationDate(now);

        Types types = new Types();
        types.setCreationDate(now);
        types.setTypeValue(0);
        types.setDescEn("None");
        types.setDescFr("Aucun(e)");

        types.setCanadaOnly("N");
        types.setUsOnly("N");
        types.setUsCanada("Y");

        types.setLifeAdminReq("N");
        types.setCritAdminReq("N");
        types.setDIAdminReq("N");
        types.setSegAdminReq("N");
        types.setGicAdminReq("N");

        types.setTypeClass(typeClass);

        types.getCoverage();

        List<Types> typesList = typeClass.getTypesList();

        if (typesList == null) {
            typesList = new ArrayList<>();
        }

        typesList.add(types);
        typeClass.setTypesList(typesList);

        typeClassEntity.create(typeClass);

    }

    public void saveTypes(Types types) {

        Date now = Calendar.getInstance().getTime();
        types.setLastModificationDate(now);
        typesEntity.edit(types);
    }

    public void createTypes(Types types) {

        Date now = Calendar.getInstance().getTime();
        types.setCreationDate(now);
        typesEntity.create(types);
    }

    public TypeClass findTypeClassByName(String typeClassName) {

        String queryStr = "SELECT t FROM TypeClass t WHERE t.typeName = :typeName";

        TypedQuery<TypeClass> query = typesEntity.getEntityManager().createQuery(queryStr, TypeClass.class);
        query.setParameter("typeName", typeClassName.trim());

        TypeClass typeClass = null;

        try {

            typeClass = (TypeClass) query.getSingleResult();

        } catch (Exception exp) {

            System.err.println("******** Unable to find TypeClass : " + typeClassName);
        }

        if (typeClass == null) {
            System.err.println("******** Unable to find TypeClass : " + typeClassName);
        }

        return typeClass;
    }

    public List<TypeClass> findAllCliedisTypeClass() {

        String queryStr = "SELECT t FROM TypeClass t WHERE t.cliedisExtension = 'Y'";

        TypedQuery<TypeClass> query = typesEntity.getEntityManager().createQuery(queryStr, TypeClass.class);

        return query.getResultList();

    }

    public List<Types> findByTypeClassName(String typeClassName) {

        TypeClass typeClass = findTypeClassByName(typeClassName);

        if (typeClass != null) {
            return typeClass.getTypesList();
        }

        System.err.println("******** Unable to find List<Types> for TypeClass : " + typeClassName);

        return new ArrayList<>();
    }

    //smasse 2025-1-13 not in old c2admin project
    public List<Types> findByTypeClassNameAndCategory(String typeClassName, String cat) {

        TypeClass typeClass = findTypeClassByName(typeClassName);

        if (typeClass != null) {
//            System.out.println(typeClass.getTypeName());

            List<Types> types = new ArrayList<>();
            for (Types t : typeClass.getTypesList()) {
                String category = t.getCategory();

                if (category != null && category.equalsIgnoreCase(cat)) {
                    types.add(t);
                }

            }
            return types;
        }

//        System.err.println("******** Unable to find List<Types> for TypeClass : "+ typeClassName);
        return new ArrayList<>();
    }

    public Types getTypesByDetails(String typeClassName, int value) {

        TypeClass typeClass = findTypeClassByName(typeClassName);

        if (typeClass == null) {
            System.err.println("******** Unable to find Types with value : " + value + " for TypeClass : " + typeClassName);
            return null;
        }

        for (Types types : typeClass.getTypesList()) { 

            if (types.getTypeValue() == value) {
                return types;
            }
        }

        System.err.println("******** Unable to find Types with value : " + value + " for TypeClass : " + typeClassName);
        return null;
    }

    //smasse 2025-1-13 not in old c2admin project
    public Types getTypesByDetailsId(String typeClassName, int value) {

        TypeClass typeClass = findTypeClassByName(typeClassName);

        if (typeClass == null) {
            System.err.println("******** Unable to find Types with value : " + value + " for TypeClass : " + typeClassName);
            return null;
        }

        for (Types types : typeClass.getTypesList()) {

            if (types.getTypeIntId()== value) {
                return types;
            }
        }

        System.err.println("******** Unable to find Types with value : " + value + " for TypeClass : " + typeClassName);
        return null;
    }

    public int getNextValueForTypeClass(TypeClass typeClass) {

        int val = 0;

        if (typeClass == null) {
            System.err.println("******** Unable to find TypeClass with null value!");
            return val;
        }

        val = typesEntity.getNextValue(typeClass);

        return val;
    }
}
