/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.im.IMCompany;
import com.insurfact.im.IMCompanyContact;
import com.insurfact.skynet.entity.ProductSupplier;
import com.insurfact.skynet.entity.ProductSupplierType;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaQuery;
import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ProductSupplierFacade extends AbstractFacade<ProductSupplier> {

	@Resource(lookup = "jdbc/Orains")
	private DataSource ds;

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ProductSupplierFacade() {
		super(ProductSupplier.class);
	}

	private Connection getConnection() {

		try {

			return ds.getConnection();
		} catch (Exception e) {

			e.printStackTrace();
		}
		return null;
	}

	public List<ProductSupplier> findActive() {
		try {
			TypedQuery<ProductSupplier> nq = getEntityManager().createNamedQuery("ProductSupplier.findActive",
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findAllStructure(ProductSupplier ps) {
		List<ProductSupplier> result = new ArrayList<>();

		try {
			Query nq = getEntityManager().createNativeQuery(
					"select DISTINCT ps.* from PRODUCT_SUPPLIER ps, PRODUCT_SUPPLIER_TREE tree where (tree.PRODUCT_SUPPLIER_IC = "
							+ ps.getProductSupplierIntId() + " or tree.PRODUCT_SUPPLIER_MGA = "
							+ ps.getProductSupplierIntId() + " or tree.PRODUCT_SUPPLIER_MGA_AGA = "
							+ ps.getProductSupplierIntId() + " or tree.PRODUCT_SUPPLIER_MGA_AGA_AGA = "
							+ ps.getProductSupplierIntId()
							+ " ) and (tree.PRODUCT_SUPPLIER_IC = ps.PRODUCT_SUPPLIER_INT_ID or tree.PRODUCT_SUPPLIER_MGA = ps.PRODUCT_SUPPLIER_INT_ID or tree.PRODUCT_SUPPLIER_MGA_AGA = ps.PRODUCT_SUPPLIER_INT_ID or tree.PRODUCT_SUPPLIER_MGA_AGA_AGA = ps.PRODUCT_SUPPLIER_INT_ID ) ",
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return result;
		}
	}

	// type 0 --- suntool type 1 --- cfiles type 2 --- both(always)
	public List<Integer> findProductsForCompany(int companyId, int type) {

		List<Integer> products = new ArrayList<>();

		StringBuilder buffer = new StringBuilder();

		buffer.append(
				"SELECT PRODUCTID from IM_COMPANYPRODUCT WHERE AVAILABLE='Y' and ACTIVE_DATE <= TRUNC(SYSDATE) and PRODUCTCLASS NOT LIKE 'INVEST' and PROFILE_TYPE in (");
		buffer.append(type);
		buffer.append(",2) and COMPANYID= ");
		buffer.append(companyId);

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return products;
		}

		ResultSet rs;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(buffer.toString());

			if (rs == null) {
				connection.close();
				return products;
			}

			// iterate through the resultset
			while (rs.next()) {
				products.add(rs.getInt("PRODUCTID"));
			}

			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductSupplierFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			return products;
		}

		return products;

	}

	// find all the Ic from a given MGA/AGA
	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findAllIcForMgaAga(ProductSupplier mgaAga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select DISTINCT ps.* from PRODUCT_SUPPLIER ps INNER JOIN PRODUCT_SUPPLIER_TREE pst on pst.PRODUCT_SUPPLIER_IC = ps.PRODUCT_SUPPLIER_INT_ID where pst.PRODUCT_SUPPLIER_MGA = "
							+ mgaAga.getProductSupplierIntId() + " or pst.PRODUCT_SUPPLIER_MGA_AGA = "
							+ mgaAga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	// find the list of son mga given a ic
	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findMgaIfIc(ProductSupplier ic) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct mga.* from product_supplier mga INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_MGA = mga.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_IC = "
							+ ic.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	// find the list of son mga/aga given an ic and mga
	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findAgaMgaIfIc(ProductSupplier ic, ProductSupplier mga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct mga.* from product_supplier mga INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_MGA_AGA = mga.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_IC = "
							+ ic.getProductSupplierIntId() + " and tree.PRODUCT_SUPPLIER_MGA = "
							+ mga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findAgaAgaMgaIfIc(ProductSupplier ic, ProductSupplier mga, ProductSupplier aga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct mga.* from product_supplier mga INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_MGA_AGA_AGA = mga.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_IC = "
							+ ic.getProductSupplierIntId() + " and tree.PRODUCT_SUPPLIER_MGA = "
							+ mga.getProductSupplierIntId() + " and tree.PRODUCT_SUPPLIER_MGA_AGA = "
							+ aga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	// find the list of fathers if given a mga
	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findIcIfMga(ProductSupplier mga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct ic.* from product_supplier ic INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_IC = ic.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_MGA_AGA is null and tree.PRODUCT_SUPPLIER_MGA = "
							+ mga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	// find the list of sons if given a mga
	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findMgaAgaIfMga(ProductSupplier mga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct aga.* from product_supplier aga INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_MGA_AGA = aga.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_MGA = "
							+ mga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findMgaAgaIfAga(ProductSupplier aga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct aga.* from product_supplier aga INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_MGA_AGA = aga.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_MGA_AGA_AGA = "
							+ aga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	// find the list of fathers mga if given a mga/aga
	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findMgaIfMgaAga(ProductSupplier mga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct mga.* from product_supplier mga INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_MGA = mga.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_MGA_AGA = "
							+ mga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	// find the list of fathers mga if given a mga/aga
	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findMgaIfAgaMga(ProductSupplier aga, ProductSupplier mgaAga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct mga.* from product_supplier mga INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_MGA = mga.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_MGA_AGA = "
							+ mgaAga.getProductSupplierIntId() + "and tree.PRODUCT_SUPPLIER_MGA_AGA_AGA = "
							+ aga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	// find the list of fathers ic if given a mga/aga
	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findIcIfMgaAga(ProductSupplier aga, ProductSupplier mga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct ic.* from product_supplier ic INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_IC = ic.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_MGA_AGA = "
							+ aga.getProductSupplierIntId() + " and tree.PRODUCT_SUPPLIER_MGA = "
							+ mga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	@SuppressWarnings("unchecked")
	public List<ProductSupplier> findIcIfMgaAgaAga(ProductSupplier aga2, ProductSupplier aga, ProductSupplier mga) {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"select distinct ic.* from product_supplier ic INNER JOIN PRODUCT_SUPPLIER_TREE tree on tree.PRODUCT_SUPPLIER_IC = ic.PRODUCT_SUPPLIER_INT_ID where tree.PRODUCT_SUPPLIER_MGA_AGA_AGA = "
							+ aga2.getProductSupplierIntId() + " and tree.PRODUCT_SUPPLIER_MGA_AGA = "
							+ aga.getProductSupplierIntId() + " and tree.PRODUCT_SUPPLIER_MGA = "
							+ mga.getProductSupplierIntId(),
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	public void fillManyToMany(ProductSupplier ic, ProductSupplier mga) {
		Query q;
		// System.out.println("sql: " + "insert into product_supplier_tree values (" +
		// ic.getProductSupplierIntId() + " , " + mga.getProductSupplierIntId() + ")");
		/*
		 * q = em.
		 * createNativeQuery("delete from product_supplier_tree where PRODUCT_SUPPLIER_IC = "
		 * + ic.getProductSupplierIntId() + " and PRODUCT_SUPPLIER_MGA = " +
		 * mga.getProductSupplierIntId()); q.executeUpdate();
		 */
		q = em.createNativeQuery("insert into product_supplier_tree values (" + ic.getProductSupplierIntId() + " , "
				+ mga.getProductSupplierIntId() + ", null, null)");
		q.executeUpdate();

	}

	public void fillManyToMany(ProductSupplier ic, ProductSupplier mga, ProductSupplier mgaAga) {
		Query q;
		/*
		 * q = em.
		 * createNativeQuery("delete from product_supplier_tree where PRODUCT_SUPPLIER_IC = "
		 * + ic.getProductSupplierIntId() + " and PRODUCT_SUPPLIER_MGA = " +
		 * mga.getProductSupplierIntId() + " and PRODUCT_SUPPLIER_MGA_AGA = " +
		 * mgaAga.getProductSupplierIntId()); q.executeUpdate();
		 */
		q = em.createNativeQuery("insert into product_supplier_tree values (" + ic.getProductSupplierIntId() + " , "
				+ mga.getProductSupplierIntId() + " , " + mgaAga.getProductSupplierIntId() + ", null)");
		q.executeUpdate();
	}

	public void fillManyToMany(ProductSupplier ic, ProductSupplier mga, ProductSupplier mgaAga, ProductSupplier aga) {
		Query q;
		/*
		 * q = em.
		 * createNativeQuery("delete from product_supplier_tree where PRODUCT_SUPPLIER_IC = "
		 * + ic.getProductSupplierIntId() + " and PRODUCT_SUPPLIER_MGA = " +
		 * mga.getProductSupplierIntId() + " and PRODUCT_SUPPLIER_MGA_AGA = " +
		 * mgaAga.getProductSupplierIntId()); q.executeUpdate();
		 */
		q = em.createNativeQuery("insert into product_supplier_tree values (" + ic.getProductSupplierIntId() + " , "
				+ mga.getProductSupplierIntId() + " , " + mgaAga.getProductSupplierIntId() + " , "
				+ aga.getProductSupplierIntId() + ")");
		q.executeUpdate();
	}

	// remove
	// remove if ic
	public void removeIc(ProductSupplier ic) {
		Query q;
		q = em.createNativeQuery(
				"delete from product_supplier_tree where PRODUCT_SUPPLIER_IC = " + ic.getProductSupplierIntId());
		q.executeUpdate();
	}

	// remove if mga
	public void removeMga(ProductSupplier mga) {
		Query q;
		q = em.createNativeQuery(
				"delete from product_supplier_tree where PRODUCT_SUPPLIER_MGA = " + mga.getProductSupplierIntId());
		q.executeUpdate();
	}

	// remove if mga-aga
	public void removeMgaAga(ProductSupplier mgaAga) {
		Query q;
		q = em.createNativeQuery(
				"update product_supplier_tree set PRODUCT_SUPPLIER_MGA_AGA = null where PRODUCT_SUPPLIER_MGA_AGA = "
						+ mgaAga.getProductSupplierIntId());
		q.executeUpdate();
	}

	// remove if mga-aga-aga
	public void removeMgaAgaAga(ProductSupplier aga) {
		Query q;
		q = em.createNativeQuery(
				"update product_supplier_tree set PRODUCT_SUPPLIER_MGA_AGA_AGA = null where PRODUCT_SUPPLIER_MGA_AGA_AGA = "
						+ aga.getProductSupplierIntId());
		q.executeUpdate();
	}

	public List<ProductSupplier> findAllMgaActive() {
		try {
			TypedQuery<ProductSupplier> nq = getEntityManager().createNamedQuery("ProductSupplier.findActiveMga",
					ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<ProductSupplier> findAllInsuranceCompanyActive() {
		try {
			TypedQuery<ProductSupplier> nq = getEntityManager()
					.createNamedQuery("ProductSupplier.findActiveInsuranceCompany", ProductSupplier.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return null;
		}
	}

	public List<ProductSupplier> findAllProductSupplierSorted() {
		TypedQuery<ProductSupplier> nq = getEntityManager().createNamedQuery("ProductSupplier.findAllSorted",
				ProductSupplier.class);
		return nq.getResultList();

		/*
		 * CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		 * 
		 * CriteriaQuery<ProductSupplier> criteriaQuery =
		 * cBuilder.createQuery(ProductSupplier.class); Root<ProductSupplier> p =
		 * criteriaQuery.from(ProductSupplier.class);
		 * 
		 * criteriaQuery.orderBy(cBuilder.asc(p.get(ProductSupplier_.organization).get(
		 * Organization_.organizationDescEn)));
		 * 
		 * TypedQuery<ProductSupplier> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * return typeCodeQuery.getResultList();
		 */
	}

	/*
	 * Returns all companyContacts by companyId from the legacy system
	 */
	public List<IMCompanyContact> findIMContactByCompanyId(Integer companyId) {

		List<IMCompanyContact> contacts = new ArrayList<>();

		StringBuilder buffer = new StringBuilder();

		buffer.append("select * from IM_CIECONTACT WHERE COMPANYID=\'").append(companyId).append('\'');

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return contacts;
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(buffer.toString());

			// iterate through the resultset
			while (rs.next()) {

				IMCompanyContact contact = new IMCompanyContact();

				contact.setCompanyId(rs.getInt("COMPANYID"));
				contact.setContactId(rs.getInt("CONTACTID"));
				contact.setOffice(rs.getString("OFFICE"));
				contact.setContactType(rs.getInt("CONTACTTYPE"));
				contact.setIsPrimary(rs.getString("ISPRIMARY"));
				contact.setFirstName(rs.getString("FIRSTNAME"));
				contact.setLastName(rs.getString("LASTNAME"));
				contact.setEmail(rs.getString("EMAIL"));
				contact.setTelephone(rs.getString("TELEPHONE"));
				contact.setFax(rs.getString("FAX"));
				contact.setPosition(rs.getString("POS"));
				contact.setTollFree(rs.getString("TOLLFREE"));

				// adding to the list
				contacts.add(contact);

			}

			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductSupplierFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return contacts;
		} // return the connection to the pool

		return contacts;
	}

	/*
	 * find all companies by type from legacy system
	 */
	public List<IMCompany> findIMCompanyByType(String type) {

		List<IMCompany> companies = new ArrayList<>();

		StringBuilder buffer = new StringBuilder();

		buffer.append("select * from IM_COMPANY WHERE COMPANYTYPE=\'").append(type).append('\'')
				.append(" ORDER BY NAMEENGLISH ");

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return companies;
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(buffer.toString());

			if (rs == null) {
				connection.close();
				return companies;
			}

			// iterate through the resultset
			while (rs.next()) {
				IMCompany cie = new IMCompany();
				cie.setCompanyId(rs.getInt("COMPANYID"));
				cie.setCompanyType(rs.getString("COMPANYTYPE"));
				cie.setDescriptionEN(rs.getString("NAMEENGLISH"));
				cie.setDescriptionFR(rs.getString("NAMEFRENCH"));
				cie.setParentCompany(rs.getInt("PARENTCOMPANY"));
				cie.setInstitutionNumber(rs.getString("INSTITUTIONNO"));

				// adding to the list
				companies.add(cie);

			}

			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductSupplierFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return companies;
		}

		return companies;

	}

	/*
	 * find all companies by type from legacy system
	 */
	public IMCompany findIMCompanyById(Integer id) {

		IMCompany cie = null;

		String sql = "select * from IM_COMPANY WHERE COMPANYID=" + id;

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return null;
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(sql);

			// iterate through the resultset
			while (rs.next()) {

				cie = new IMCompany();

				cie.setCompanyId(rs.getInt("COMPANYID"));
				cie.setCompanyType(rs.getString("COMPANYTYPE"));
				cie.setDescriptionEN(rs.getString("NAMEENGLISH"));
				cie.setDescriptionFR(rs.getString("NAMEFRENCH"));
				cie.setParentCompany(rs.getInt("PARENTCOMPANY"));
				cie.setInstitutionNumber(rs.getString("INSTITUTIONNO"));

			}

			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductSupplierFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		}

		return cie;

	}

	public List<ProductSupplier> findProductSupplierByType(ProductSupplierType type) {

		TypedQuery<ProductSupplier> nq = em.createNamedQuery("ProductSupplier.findByProductSupplierType",
				ProductSupplier.class);

		nq.setParameter("productSupplierType", type);

		return nq.getResultList();
	}

	public ProductSupplierType findProductSupplierType(int typeId) {

		Query nq = em.createNamedQuery("ProductSupplierType.findByProductSupplierType", ProductSupplier.class);

		nq.setParameter("productSupplierType", typeId);

		return (ProductSupplierType) nq.getResultList().get(0);
	}

	public ProductSupplier findProductSupplierByCompanyId(Integer id) {

		TypedQuery<ProductSupplier> nq = em.createQuery("SELECT a FROM ProductSupplier a WHERE   a.companyId = :companyId",
				ProductSupplier.class);
		nq.setParameter("companyId", id);
		return nq.getSingleResult();

		/*
		 * ProductSupplier ps = null; CriteriaBuilder cBuilder =
		 * em.getCriteriaBuilder();
		 * 
		 * CriteriaQuery<ProductSupplier> criteriaQuery =
		 * cBuilder.createQuery(ProductSupplier.class); Root<ProductSupplier> p =
		 * criteriaQuery.from(ProductSupplier.class);
		 * 
		 * Predicate typeCodePredicate =
		 * cBuilder.equal(p.get(ProductSupplier_.companyId), id);
		 * 
		 * criteriaQuery.where(typeCodePredicate);
		 * 
		 * TypedQuery<ProductSupplier> typeCodeQuery = em.createQuery(criteriaQuery);
		 * 
		 * try { ps = (ProductSupplier) typeCodeQuery.getSingleResult(); } catch
		 * (Exception e) { ps = null; }
		 * 
		 * return ps;
		 */
	}

	public List<ProductSupplierType> findAllProductSupplierTypes() {

		em = getEntityManager();

		CriteriaQuery<ProductSupplierType> cq = em.getCriteriaBuilder().createQuery(ProductSupplierType.class);

		cq.select(cq.from(ProductSupplierType.class));

		List<ProductSupplierType> types = null;

		TypedQuery<ProductSupplierType> tq = getEntityManager().createQuery(cq);

		try {
			types = tq.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return types;
	}

	@SuppressWarnings("unchecked")
	public List<ProductSupplierType> findAllProductSupplierTypesForGabinet() {
		try {
			Query nq = getEntityManager().createNativeQuery(
					"SELECT * FROM PRODUCT_SUPPLIER_TYPE where PRODUCT_SUPPLIER_TYPE in (1,5)",
					ProductSupplierType.class);
			return nq.getResultList();

		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	/*
	 * returns all companies from legacy system
	 */
	public List<IMCompany> findAllIMCompany() {

		List<IMCompany> companies = new ArrayList<IMCompany>();

		StringBuilder buffer = new StringBuilder();

		buffer.append("select * from IM_COMPANY");

		// get connection from the pool
		Connection connection = getConnection();

		if (connection == null) {
			return null;
		}

		ResultSet rs = null;

		try (Statement stmt = connection.createStatement();) {

			rs = stmt.executeQuery(buffer.toString());

			// iterate through the resultset
			while (rs.next()) {
				IMCompany cie = new IMCompany();
				cie.setCompanyId(rs.getInt("COMPANYID"));
				cie.setCompanyType(rs.getString("COMPANYTYPE"));
				cie.setDescriptionEN(rs.getString("NAMEENGLISH"));
				cie.setDescriptionFR(rs.getString("NAMEFRENCH"));
				cie.setParentCompany(rs.getInt("PARENTCOMPANY"));
				cie.setInstitutionNumber(rs.getString("INSTITUTIONNO"));

				// adding to the list
				companies.add(cie);
			}

			rs.close();
			connection.close();

		} catch (SQLException e) {
			try {
				connection.close();
			} catch (SQLException ex) {
				Logger.getLogger(ProductSupplierFacade.class.getName()).log(Level.SEVERE, null, ex);
			}
			e.printStackTrace();
			return null;
		}

		return companies;
	}
}
