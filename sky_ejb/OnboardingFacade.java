/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Onboarding;
import com.insurfact.skynet.entity.ProductSupplier;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class OnboardingFacade extends AbstractFacade<Onboarding> {

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public OnboardingFacade() {
		super(Onboarding.class);
	}

	public List<Onboarding> findByProductSupplier(ProductSupplier ps) {

		List<Onboarding> list = new ArrayList<>();

//        System.out.println("**** findByUsers() : users="+users.getUserIntId() );
		try {
			TypedQuery<Onboarding> nq = em.createNamedQuery("Onboarding.findByProductSupplier", Onboarding.class);

			nq.setParameter("productSupplier", ps);

			list = nq.getResultList();

		} catch (NoResultException e) {
			return list;
		}

		return list;

	}

	public Integer findNextOrderValue(ProductSupplier ps) {
		try {
			Query nq = em.createNativeQuery(
					"select max(ORDER_NO)+1 from ONBOARDING where PRODUCT_SUPPLIER = " + ps.getProductSupplierIntId());
			return ((BigDecimal) nq.getSingleResult()).intValue();

		} catch (NoResultException e) {
			return 99;
		}
	}

}
