/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.SegFund;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class SegFundFacade extends AbstractFacade<SegFund>  {
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public SegFundFacade() {
        super(SegFund.class);
    }
    
  public SegFund getSegFundByMgmtCompanyCodeAndFundId(String mgmtCode, String accountNumber, String fundId) {
       
        
        try {      
            Query nq = em.createNamedQuery("SegFund.findByDetail",SegFund.class);
     
            nq.setParameter("mgmtCompanyCode", mgmtCode );
            nq.setParameter("accountNumber", accountNumber );
            nq.setParameter("fundId", fundId );
            
            return (SegFund) nq.getSingleResult();
        }
        catch(NoResultException e){
            return null;
        } 
 
    }       
}
