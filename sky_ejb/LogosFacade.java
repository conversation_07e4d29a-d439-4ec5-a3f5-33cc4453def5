/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;


import com.insurfact.skynet.entity.Logos;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class LogosFacade extends AbstractFacade<Logos> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public LogosFacade() {
        super(Logos.class);
    }

    public Logos findStoredFile(int type, int id) {
        String sql = "SELECT * FROM LOGOS WHERE TYPE_ = " + type + " and TYPE_ID = " + id;

        try {
            Query nq = getEntityManager().createNativeQuery(sql, Logos.class);
           // System.out.println("results logos: " + nq.getMaxResults());
            if (nq.getMaxResults() > 0) {
                return (Logos) nq.getResultList().get(0);
            } else {
                return null;
            }

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
        }

        return null;
    }
    
    public Logos findStoredLogo(int type, int id) {
        String sql = "SELECT * FROM LOGOS WHERE TYPE_ = " + type + " and TYPE_ID = (select product_supplier_int_id from product_supplier where company_id = "+ id+")" ;

        try {
            Query nq = getEntityManager().createNativeQuery(sql, Logos.class);
           // System.out.println("results logos: " + nq.getMaxResults());
            if (nq.getMaxResults() > 0) {
                return (Logos) nq.getResultList().get(0);
            } else {
                return null;
            }

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
        }

        return null;
    }

    public void editFile(Logos l) {
        em.merge(l);
    }

}
