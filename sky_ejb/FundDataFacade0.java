/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdAllocation;
import com.insurfact.fundata.entity.FdBestCategory;
import com.insurfact.fundata.entity.FdBestOverall;
import com.insurfact.fundata.entity.FdCategory;
import com.insurfact.fundata.entity.FdDaily;
import com.insurfact.fundata.entity.FdFund;
import com.insurfact.fundata.entity.FdFundserv;
import com.insurfact.fundata.entity.FdHolding;
import com.insurfact.fundata.entity.FdMastQuartile;
import com.insurfact.fundata.entity.FdMaster;
import com.insurfact.fundata.entity.FdPerformance;
import com.insurfact.fundata.entity.FdSecurityName;
import com.insurfact.skynet.entity.FundFavorite;
import com.insurfact.skynet.entity.PriceDistribution;
import com.insurfact.skynet.entity.Users;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FundDataFacade0 implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@EJB
	private FdFundServSessionBean fdFundServSessionBean;

	@EJB
	private FdFundSessionBean fdFundSessionBean;

	@EJB
	private FdFavoriteSessionBean favoriteSessionBean;

	@EJB
	private FdCategorySessionBean fdCategorySessionBean;

	@EJB
	private FdMonthlyBestByCatFacade fdMonthlyBestByCatFacade; // smasse 2017-4-21 ADDED

	@EJB
	private FdMasterSessionBean fdMasterSessionBean;

	@EJB
	private FdManagerSessionBean fdManagerSessionBean;

	@EJB
	private FdAllocationSessionBean_0 fdAllocationSessionBean;

	@EJB
	private FdSecurityNameSessionBean fdSecurityNameSessionBean;

	@EJB
	private FdHoldingSessionBean fdHoldingSessionBean;

	@EJB
	private FdDailySessionBean0 fdDailySessionBean;

	@EJB
	private FdMastQuartileSessionBean fdQuartileSessionBean;

	@EJB
	private FdBestCategorySessionBean fdBestCategorySessionBean;

	@EJB
	private FdBestOverallSessionBean fdBestOverallSessionBean;

	@EJB
	private PriceDistributionSessionBean priceDistributionSessionBean;

	@EJB
	private FdPerformanceSessionBean fdPerformanceSessionBean;

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	public FundDataFacade0() {
	}

	public FdMaster getMaster(Integer id) {
		return fdMasterSessionBean.find(id);
	}

	/**
	 * return price info from FundSERV
	 * 
	 * @param mgmtCompany
	 * @param fundId
	 * @return
	 */
	public List<PriceDistribution> findPricesForFund(String mgmtCompany, String fundId) {

		return priceDistributionSessionBean.findPricesForFund(mgmtCompany, fundId);
	}

	//////////////////////
	//
	// DAILYS
	//

	/**
	 * retrieves one year of data from Fundata
	 * 
	 * @param datakey
	 * @return
	 */
	public List<FdDaily> findYearlyDailysByDataKey(Integer datakey) {

		return fdDailySessionBean.findDailysByDataKey(datakey);
	}

	/**
	 * retrieves all available price data from Fundata
	 * 
	 * @param datakey
	 * @return
	 */
	public List<FdDaily> findAllDailysByDataKey(Integer datakey) {

		return fdDailySessionBean.findAllDailysByDataKey(datakey);
	}

	//////////////////////
	//
	// FUNDSERV
	//

	public FdFundserv getFundServByCode(String fundservCode) {
		return fdFundServSessionBean.getByFundServCode(fundservCode);
	}

	public List<FdFundserv> searchFundservByLookups(String fsrvCode, Integer categoryId) {

		FdCategory category = null;

		if (categoryId != null)
			category = getCategory(categoryId);

		return fdFundServSessionBean.findFundservByCriterias(fsrvCode, category);
	}

	//////////////////////
	//
	// BESTS
	//

	public FdBestCategory getBestCategory(FdCategory category) {

		return fdBestCategorySessionBean.getBestCategory(category);
	}

	public FdBestOverall getBestOverall() {

		return fdBestOverallSessionBean.getBestOverall();
	}

	//////////////////////
	//
	// FUNDS
	//

	public FdFund getByFundatakey(Integer key) {

		return fdFundSessionBean.getByFundataKey(key);
	}

	public FdFund loadAllFundDatas(FdFund fdFund) {

		long start = System.currentTimeMillis();

		if (fdFund != null) {
			Integer dataKey = fdFund.getFundFundatakey();

			if (dataKey != null) {

//                FdPerformance fundPerf = fdFund.getPerformance();
//                if (fundPerf == null) {
//                    fundPerf = fdPerformanceFacade.getByFundataKey(dataKey);
//                    fdFund.setPerformance(fundPerf);
//                }

				List<FdAllocation> allocations = fdFund.getAllocations();
				if (allocations == null || allocations.isEmpty()) {
					allocations = fdAllocationSessionBean.getByFundataKey(dataKey);
					fdFund.setAllocations(allocations);
				}

//                List<FdHolding> holdings = fdFund.getHoldings();
//                if (holdings == null || holdings.isEmpty()) {
//                    holdings = fdHoldingFacade.getByFundataKey(dataKey);
//                    fdFund.setHoldings(holdings);
//                }

//                List<FdManager> managers = fdFund.getManagers();
//                if (managers == null || managers.isEmpty()) {
//                    managers = fdManagerFacade.getByFundataKey(dataKey);
//                    fdFund.setManagers(managers);
//                }

//                FdDaily latestDaily = fdFund.getFundDaily();
//                if (latestDaily == null) {
//                    latestDaily = fdDailySessionBean.getLatestByFundDataKey(dataKey);
//                    fdFund.setFundDaily(latestDaily);
//                }

				FdMastQuartile quartile = fdFund.getQuartile();
				if (quartile == null) {
					quartile = fdQuartileSessionBean.getByFundataKey(dataKey);
					fdFund.setQuartile(quartile);
				}

			} else {
				System.out.println(">>>>DATAKEY IS NULL!!");
			}
		} else {
			System.out.println(">>>>FUNDSERV IS NULL!!");
		}

		long end = System.currentTimeMillis();

		System.out.println("*** loadAllFundDatas :" + (end - start));

		return fdFund;
	}

	public List<FdFund> searchByAssets(int merRange, Integer categoryId, Locale locale) {

		FdCategory category = getCategory(categoryId);

		return fdFundSessionBean.findByAssets(merRange, category, locale);
	}

	public List<FdFund> searchFundByLookups(Integer masterId, Integer categoryId, boolean french) {

		FdCategory category = null;
		FdMaster master = null;

		if (categoryId != null)
			category = getCategory(categoryId);

		if (masterId != null)
			master = getMaster(masterId);

//        System.out.println("*** searchFundByLookups : master="+master + " category="+category);

		return fdFundSessionBean.findFundByCriterias(master, category, french);

	}

	// unsued!
	public FdFund loadFundServData1(Integer dataKey) {

		FdFund fund = null;
		if (dataKey != null) {

			fund = fdFundSessionBean.getByFundataKey(dataKey);

			if (fund != null) {
//                fund.setFundServ(fundservFacade.getByFundataKey(dataKey));
//                fund.setPerformance(performanceFacade.getByFundataKey(dataKey));
				fund.setAllocations(fdAllocationSessionBean.getByFundataKey(dataKey));
//                fund.setHoldings(holdingFacade.getByFundataKey(dataKey));
//                fund.setManagers(fdManagerFacade.getByFundataKey(dataKey));
//                fund.setFundDaily(fdDailySessionBean.getLatestByFundDataKey(dataKey));
				fund.setQuartile(fdQuartileSessionBean.getByFundataKey(dataKey));
			}

		} else {
			System.out.println(">>>>DATAKEY IS NULL!!");
		}

		return fund;
	}

	public List<FdFund> findAllFundsByCategory(int categoryId) {

		FdCategory category = getCategory(categoryId);

		return fdFundSessionBean.findAllByCategory(category);
	}

	//////////////////////
	//
	// HOLDINGS
	//

	public List<FdHolding> searchByHolding(Integer holdingId, String holdingName, boolean market, boolean asset) {

		return fdHoldingSessionBean.findByHoldingIdName(holdingId, holdingName, market, asset);
	}

	//////////////////////
	//
	// PERFORMANCE
	//

	public List<FdPerformance> searchTop10(int highLow, int column, Integer categoryId, Locale locale) {

		FdCategory category = getCategory(categoryId);

		return fdPerformanceSessionBean.findTop10ByCategory(highLow, column, category, locale);
	}

	public List<FdPerformance> searchTops(int rows, int highLow, int column, Integer categoryId, Locale locale) {

		FdCategory category = getCategory(categoryId);

		return fdPerformanceSessionBean.findTopsByCategory(rows, highLow, column, category, locale);
	}

	//////////////////////
	//
	// CATEGORIES
	//

	public List<FdCategory> findAllCategory() {
		return fdCategorySessionBean.findAll();
	}

	public List<FdCategory> findAllSearchableCategory() {
		return fdCategorySessionBean.findAllSearchable();
	}

	public FdCategory getCategory(Integer id) {
		return fdCategorySessionBean.find(id);
	}

	/**
	 * Used by admin to reset the dashboard. Performs the equivalent of these SQL
	 * statements:
	 * 
	 * <p/>
	 * insert into FD_MONTHLY_BEST_BY_CAT (SEQ_NUMBER) (select ytd_return_fund FROM
	 * FD_BEST_CATEGORY where FD_CATEGORY in (1019,1020,1022,1031,1043));
	 *
	 * <p/>
	 * UPDATE FD_MONTHLY_BEST_BY_CAT a SET a.CREATION_DATE=SYSDATE,
	 * a.PERF_SEQ=(SELECT b.PERf_SEQ FROM FD_PERFORMANCE b where
	 * a.seq_number=b.PERF_FUNDATAKEY);
	 * 
	 * <p/>
	 * getCategory = @NamedQuery(name = "FdCategory.findByCategoryIntId"
	 * 
	 * <p/>
	 * Called by FundNavigatorBean.
	 */
	public void resetTableFdMonthlyBestByCat() {

		List<FdBestCategory> bestCats = new ArrayList<>();

		bestCats.add(getBestCategory(getCategory(1019)));
		bestCats.add(getBestCategory(getCategory(1020)));
		bestCats.add(getBestCategory(getCategory(1022)));
		bestCats.add(getBestCategory(getCategory(1031)));
		bestCats.add(getBestCategory(getCategory(1043)));

		fdMonthlyBestByCatFacade.resetTable(bestCats); // smasse 2017-4-21 washere washere
	}

	//////////////////////
	//
	// MASTERS
	//

	public List<FdMaster> findAllMasters() {
		return fdMasterSessionBean.findAll();
	}

	public List<FdMaster> findAllMastersNoSel(String compIDs) {
		return fdMasterSessionBean.findAllMastersNoSel(compIDs);
	}

	//////////////////////
	//
	// SECURITIES
	//

	public List<FdSecurityName> findAllSecurityNames() {
		return fdSecurityNameSessionBean.findAll();
	}

	//////////////////////
	//
	// FUND FAVORITES
	//

	public List<FundFavorite> findFavoritesByUsers(Users users) {

		return favoriteSessionBean.findFavoriteByUsers(users);
	}

	public List<FundFavorite> findFavoritesByFdFund(FdFund fund) {

		return favoriteSessionBean.findFavoriteByFund(fund);
	}

	public FundFavorite addToFavorite(Users users, FdFund fund) {

		return favoriteSessionBean.addToFavorite(users, fund);
	}

	public boolean removeFavorite(FundFavorite fav) {

		return favoriteSessionBean.removeFavorite(fav);
	}

	//////////////////////
	//
	// FIX AND FK REBUILD
	//

	public void fixBestOverall() {

		Calendar now = Calendar.getInstance();

		FdBestOverall best = new FdBestOverall();
		best.setCreationDate(now.getTime());

		int[] values = { 0, 1, 2, 3, 4, 5, 10, 11, 13 };

		for (int val : values) {
			List<FdPerformance> performances = fdPerformanceSessionBean.findTopOverall(0, val);

			if (performances != null && !performances.isEmpty()) {
				FdPerformance perf = performances.get(0);

				if (val == 11) {
					best.setOneMonthReturn(perf.getPerfOneMonthReturn());
					best.setOneMonthReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 13) {
					best.setThreeMonthReturn(perf.getPerfThreeMonthReturn());
					best.setThreeMonthReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 0) {
					best.setYtdReturn(perf.getPerfYtdReturn());
					best.setYtdReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 1) {
					best.setOneYrCompoundReturn(perf.getPerfOneYrCompoundReturn());
					best.setOneYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 2) {
					best.setTwoYrCompoundReturn(perf.getPerfTwoYrCompoundReturn());
					best.setTwoYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 3) {
					best.setThreeYrCompoundReturn(perf.getPerfThreeYrCompoundReturn());
					best.setThreeYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 4) {
					best.setFourYrCompoundReturn(perf.getPerfFourYrCompoundReturn());
					best.setFourYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 5) {
					best.setFiveYrCompoundReturn(perf.getPerfFiveYrCompoundReturn());
					best.setFiveYrCompoundReturnFund(perf.getFdFund().getFundFundatakey());
				}
				if (val == 10) {
					best.setInceptionReturn(perf.getPerfInceptionReturn());
					best.setOneMonthReturnFund(perf.getFdFund().getFundFundatakey());
				}
			}
		}

		// save
		fdBestOverallSessionBean.create(best);

	}

}
