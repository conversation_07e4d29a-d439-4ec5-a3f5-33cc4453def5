/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdFundFavoritePy;
import com.insurfact.fundata.entity.FdFundFlexSearchPy;
import com.insurfact.skynet.entity.Users;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class FdFundFavoritePyFacade extends AbstractFacade<FdFundFavoritePy> {
    
    private static final String TAG = FdFundFavoritePyFacade.class.getSimpleName();
    
    private static final boolean DEBUG = true; //TODO set to false in prod

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public FdFundFavoritePyFacade() {
        super(FdFundFavoritePy.class);
    }
    
    
    public FdFundFavoritePy getFavoriteByFundAndUsers(FdFundFlexSearchPy fund, Users users){
        
        try {
            Query nq = getEntityManager().createNamedQuery("FdFundFavoritePy.findByFdFundAndUsers", FdFundFavoritePy.class);
            nq.setParameter("fundatakey", fund.getFundFundatakey());
            nq.setParameter("users", users);

            return  (FdFundFavoritePy) nq.getSingleResult();
      
        } catch (Exception e) {
          
            return null;
        }
    }
    
    /**
     * 
     * @param users
     * @param fund FdFundFlexSearchPy
     * @return FdFundFavoritePy newly created, or null when already present
     */
    public FdFundFavoritePy addToFavoritesInDatabase(Users users, FdFundFlexSearchPy fund){
        
        FdFundFavoritePy fav = getFavoriteByFundAndUsers(fund, users);
        
        //saber si existe ya dentro de la lista de favoritos
        
        if(fav != null){
            if(DEBUG) System.out.println("debug smasse 2019.6.18 "+TAG
                +".addToFavoritesInDatabase(users,fund): exiting after "
                    + "*getFavoriteByFundAndUsers(fund, users)* found existing record.");
            return null;
        }
        
        if(DEBUG) System.out.println("debug smasse 2019.6.20 "+TAG
                +".addToFavoritesInDatabase(users,fund): "
                    + "fund not in favorites table for user {"+users.getUsername()
            +"}\n fund {"+fund
            +"}\n fund.getFundSeq() {"+fund.getFundSeq()+"}");
        
        long prueba = 12;
        
        fav = new FdFundFavoritePy(prueba);
        
        
               
        //fav.setFundFavoriteIntId(fund.getFundFundatakey());
        //fav.setFundFavoriteIntId(fund.getFundSeq());//TODO washere washere use new oracle sequence 2019.6.20
       // int myuser;
       // myuser = users.getUserIntId();
        
        
        
      /*  // Code Serge, bad 
        fav.setUsers(users); 
        System.out.println("Here is saving on users, but it's wrong **************************************************************" + users.getUserIntId());
        
        // Code D
        // fav.setMyIdUser(myuser);
        
        System.out.println("Here is the correct data users .ID But you have to change the object's setUsers method to an Integer ************" + users.getUserIntId());
        System.out.println("Here Finish *******************************************************************************************");
        
        //fav.setFdFund(fund);
        fav.setFundatakey(fund.getFundFundatakey());
        System.out.println("Aqui esta el getFundFundatakey *********************************************************************" + fund.getFundFundatakey());
               

        Calendar now = Calendar.getInstance();
        fav.setCreationDate(now.getTime());
        
        //fav.setLastModificationDate(now.getTime());
         
        /* TODO washere 2019.6.14 review:
        fav.setInitialUnits(Integer.MIN_VALUE);
        fav.setInitialValue(BigDecimal.ONE);
        fav.setPurchaseDate(now.getTime());
        */
        
       // FdFun create(fav);
        System.out.println("Aqui termina crear fav *******************************************************************************************" + fav);
        
        //em.flush();
        
      ////em.getTransaction().commit();
        
        if(DEBUG) System.out.println("debug smasse 2019.6.14 "+TAG
            +".addToFavoritesInDatabase(users,fund): "
                + "exiting after *DDcreate(fav)*; fav = "+fav);

       
        return fav;
    }    
    
    
    public boolean removeFavoriteFromDatabase(FdFundFavoritePy fav){
        try{
            remove(fav);
            if(DEBUG) System.out.println("debug smasse 2019.6.20 "+TAG+".removeFavoriteFromDatabase: remove(fav) completed; fav = "+fav);
        }catch(Throwable e){
            if(DEBUG) System.out.println("debug smasse 2019.6.14 "+TAG+".removeFavoriteFromDatabase: remove(fav) raised "+e);
            return false;
        }
        
        return true;
    }   
    
    
    public List<FdFundFavoritePy> findFavoritesByUsers(Users users){
        
        try {
            TypedQuery<FdFundFavoritePy> nq = getEntityManager().createNamedQuery("FdFundFavoritePy.findByUsers", FdFundFavoritePy.class);
            nq.setParameter("users", users);

            return  nq.getResultList();
      
        } catch (Exception e) {
          
            return null;
        }
    }
}
