/*
* To change this template, choose Tools | Templates
*
* and open the template in the editor.
*/

package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.LicenseProvBoard;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class LicenseProvBoardFacade extends AbstractFacade<LicenseProvBoard> {
    
    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public LicenseProvBoardFacade() {
        super(LicenseProvBoard.class);
    }
    
    public LicenseProvBoard getByProvince(String prov) {
        try {
            Query nq = getEntityManager().createNamedQuery("LicenseProvBoard.findByProvince", LicenseProvBoard.class);
            nq.setParameter("province", prov);
            return (LicenseProvBoard) nq.getSingleResult();
            
        } catch (NoResultException e) {
            return null;
        }
    }
    
    
}
