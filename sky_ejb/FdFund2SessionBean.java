/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.fundata.entity.FdCategory2;
import com.insurfact.fundata.entity.FdFund2;
import com.insurfact.fundata.entity.FdFundCompany; //FdMaster 
import java.util.Date;
import java.util.List;
import java.util.Locale;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.metamodel.SingularAttribute;
import org.eclipse.persistence.internal.jpa.querydef.OrderImpl;

/**
 * [[[TODO]]] washere 2018-9-25 review sql
 * 
 * <AUTHOR>
 */
@Stateless
public class FdFund2SessionBean extends AbstractFacade<FdFund2> {

	@EJB
	private FdFundServSessionBean fundservFacade;

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public FdFund2SessionBean() {
		super(FdFund2.class);
	}

	public FdFund2 getByFundataKey(Integer fundatakey) {

		try {
			Query nq = em.createNamedQuery("FdFund2.findByFundFundatakey", FdFund2.class);

			nq.setParameter("fundFundatakey", fundatakey);

			return (FdFund2) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	public FdFund2 getByFundataKey(Long fundatakey) { // smasse 2018.8.24

		try {
			Query nq = em.createNamedQuery("FdFund2.findByFundFundatakey", FdFund2.class);

			nq.setParameter("fundFundatakey", fundatakey);

			return (FdFund2) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<FdFund2> getAllFunds() { // smasse 2018.8.24

		TypedQuery<FdFund2> nq = em.createQuery("SELECT f FROM FdFund2 f", FdFund2.class);

		return nq.getResultList();
	}

	public FdFund2 getByMasterNameEn(String master) {

		try {
			Query nq = em.createNamedQuery("FdFund2.findByFundMasterNameEn", FdFund2.class);

			nq.setParameter("fundMasterNameEn", master);

			return (FdFund2) nq.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	public List<String> findDistinctAttributeEntries(SingularAttribute<FdFund2, String> attribute) {

		CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<String> criteriaQuery = cBuilder.createQuery(String.class);
		Root<FdFund2> p = criteriaQuery.from(FdFund2.class);

		Order order = new OrderImpl(p.get(attribute), true);
		criteriaQuery.select(p.get(attribute)).distinct(true).orderBy(order)
				.where(cBuilder.isNotNull(p.get(attribute)));

		TypedQuery<String> typeCodeQuery = em.createQuery(criteriaQuery);

		return typeCodeQuery.getResultList();
	}

	public List<FdFund2> findByAssets(int merRange, FdCategory2 category, Locale locale) {

		/*
		 * <f:selectItem itemLabel="0 - 1%" itemValue="0"/> <f:selectItem
		 * itemLabel="1 - 1.5%" itemValue="1"/> <f:selectItem itemLabel="1.5 - 2%"
		 * itemValue="2"/> <f:selectItem itemLabel="2.0 - 2.5%" itemValue="3"/>
		 * <f:selectItem itemLabel="2.5 - 3%" itemValue="4"/> <f:selectItem
		 * itemLabel="3 - 3.5%" itemValue="5"/> <f:selectItem itemLabel="3.5-4%"
		 * itemValue="6"/> <f:selectItem itemLabel="4% +" itemValue="7"/> <f:selectItem
		 * itemLabel="ALL " itemValue="9"/>
		 */

		float start = 0, end = 0;
		List<FdFund2> funds;

		TypedQuery<FdFund2> nq = em.createQuery(
				"SELECT f FROM FdFund2 f WHERE f.fdCategoryId = :categoryId AND f.fundMer BETWEEN :startMer AND :finishMer AND f.fundEnabled ='Y' ORDER BY f.fundMer DESC",
				FdFund2.class);

		switch (merRange) {
		case 0: {
			start = 0;
			end = 1;
			break;
		}
		case 1: {
			start = 1;
			end = 1.5f;
			break;
		}
		case 2: {
			start = 1.5f;
			end = 2;
			break;
		}
		case 3: {
			start = 2;
			end = 2.5f;
			break;
		}
		case 4: {
			start = 2.5f;
			end = 3;
			break;
		}
		case 5: {
			start = 3;
			end = 3.5f;
			break;
		}
		case 6: {
			start = 3.5f;
			end = 4;
			break;
		}
		case 7: {
			start = 4;
			end = 100;
			break;
		}
		default:
			start = 0;
			end = 100;
		}

		nq.setParameter("categoryId", category.getCategoryId());
		nq.setParameter("startMer", start);
		nq.setParameter("finishMer", end);

		try {
			funds = nq.getResultList();
		} catch (NoResultException e) {
			return null;
		}

		return funds;
	}

	/**
	 * 
	 * @param master
	 * @param category
	 * @param french
	 * @return
	 */
	public List<FdFund2> findFundByCriterias(FdFundCompany master, FdCategory2 category, boolean french) {

		TypedQuery<FdFund2> nq;

		if (master != null) {
			if (category != null) {

				if (french)
//                    nq= em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND f.master = :master AND (f.fundGrade IS NOT NULL) ORDER BY f.fundNameFr",FdFund.class);
					nq = em.createQuery(
							"SELECT f FROM FdFund2 f WHERE f.fdCategoryId = :categoryId AND f.fundCompany = :master AND f.fundEnabled ='Y' AND (f.navps IS NOT NULL) ORDER BY f.fundNameFr",
							FdFund2.class);
				else
//                    nq= em.createQuery("SELECT f FROM FdFund f WHERE f.category = :category AND f.master = :master AND (f.fundGrade IS NOT NULL) ORDER BY f.fundNameEn",FdFund.class);
					nq = em.createQuery(
							"SELECT f FROM FdFund2 f WHERE f.fdCategoryId = :categoryId AND f.fundCompany = :master AND f.fundEnabled ='Y' AND (f.navps IS NOT NULL) ORDER BY f.fundNameEn",
							FdFund2.class);

				nq.setParameter("categoryId", category.getCategoryId());
				nq.setParameter("master", master);

				return nq.getResultList();
			}

			if (french)
//                nq= em.createQuery("SELECT f FROM FdFund f WHERE f.master = :master AND (f.fundGrade IS NOT NULL) ORDER BY f.fundNameFr",FdFund.class);
				nq = em.createQuery(
						"SELECT f FROM FdFund2 f WHERE f.fundCompany = :master AND f.fundEnabled ='Y' AND (f.navps IS NOT NULL) ORDER BY f.fundNameFr",
						FdFund2.class);
			else
//                nq= em.createQuery("SELECT f FROM FdFund f WHERE f.master = :master AND (f.fundGrade IS NOT NULL) ORDER BY f.fundNameEn",FdFund.class);
				nq = em.createQuery(
						"SELECT f FROM FdFund2 f WHERE f.fundCompany = :master AND f.fundEnabled ='Y' AND (f.navps IS NOT NULL) ORDER BY f.fundNameEn",
						FdFund2.class);

			nq.setParameter("master", master);

			return nq.getResultList();
		}

		return null;
	}

	public List<FdFund2> findAllByCategory(FdCategory2 category) {

		TypedQuery<FdFund2> nq = em.createQuery(
				"SELECT f FROM FdFund2 f WHERE f.fdCategoryId = :category AND f.fundEnabled ='Y' ORDER BY f.fundNameEn",
				FdFund2.class);

		nq.setParameter("category", category.getCategoryId());

		return nq.getResultList();
	}

	public List<FdFund2> findAllByCategoryId(Long categoryId) {

		TypedQuery<FdFund2> nq = em.createQuery(
				"SELECT f FROM FdFund2 f WHERE f.fdCategoryId = :category AND f.fundEnabled ='Y' ORDER BY f.fundNameEn",
				FdFund2.class);
		// Query nq= em.createQuery("SELECT f FROM FdFund2 f WHERE f.fdCategoryId =
		// :category ORDER BY f.fundNameEn",FdFund2.class);

		nq.setParameter("category", categoryId);

		return nq.getResultList();
	}

	public List<FdFund2> findFundByMasterEn(String name) {

		TypedQuery<FdFund2> nq = em.createQuery(
				"SELECT f FROM FdFund2 f WHERE f.fundMasterNameEn = :name AND f.fundEnabled ='Y'", FdFund2.class);
		nq.setParameter("name", name);

		return nq.getResultList();

	}

	public List<FdFund2> findTop5Gainers(Date date) {

		TypedQuery<FdFund2> nq = em.createQuery(
				"SELECT f FROM FdFund2 f WHERE f.navpsDate = :date ORDER BY f.average DESC", FdFund2.class);
		nq.setParameter("date", date);
		nq.setHint("eclipselink.read-only", "true");

		nq.setMaxResults(5);

		return nq.getResultList();

	}

	public List<FdFund2> findTop5Loosers(Date date) {

		TypedQuery<FdFund2> nq = em
				.createQuery("SELECT f FROM FdFund2 f WHERE f.navpsDate = :date ORDER BY f.average ASC", FdFund2.class);
		nq.setParameter("date", date);
		nq.setHint("eclipselink.read-only", "true");

		nq.setMaxResults(5);

		return nq.getResultList();
	}

}
