/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;
 
import com.insurfact.skynet.entity.Client; 
import com.insurfact.skynet.entity.ProductSupplier; 

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList; 
import java.util.Date; 
import java.util.List;
import jakarta.ejb.EJB;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery; 

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ClientFacade extends AbstractFacade<Client> {

	@EJB
	private ContactFacade contactFacade;

	@PersistenceContext(unitName = "AVue_2.2-PU")
	private EntityManager em;

	@Override
	protected EntityManager getEntityManager() {
		return em;
	}

	public ClientFacade() {
		super(Client.class);
	}

	public List<Client> findByCriteria(String clientName, Date birthdate, String clientNumber, ProductSupplier supplier,
			String policyNumber, String advisorName) {
		
		TypedQuery<Client> nq = em.createQuery(
				"SELECT a FROM Client a WHERE   a.accountClientOwnershipList.account.productSupplier = :supplier and "
				+ "Upper(a.accountClientOwnershipList.account.accountNumber) like UPPER(:policyNumber) and (a.contact.firstname like :clientName or "
				+ "a.contact.lastname like :clientName) and a.contact.birthdate like :birthdate and a.clientNumber = :clientNumber and "
				+ "(upper(a.advisorList.contact.firstname) = upper(:advisorName) or upper(a.advisorList.contact.lastname) = upper(:advisorName))",
				Client.class);
		nq.setParameter("clientName", clientName);
		nq.setParameter("birthdate", birthdate);
		nq.setParameter("clientNumber", clientNumber);
		nq.setParameter("supplier", supplier);
		nq.setParameter("policyNumber", policyNumber);
		nq.setParameter("advisorName", advisorName);
		return nq.getResultList();

		/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<Client> criteriaQuery = cBuilder.createQuery(Client.class);
		Root<Client> p = criteriaQuery.from(Client.class);

		List<Predicate> allPredicates = new ArrayList<Predicate>();

		if (supplier != null) {
			Join<Client, AccountClientOwnership> ownership = p.join(Client_.accountClientOwnershipList);
			Predicate manufacturerPredicate = cBuilder
					.equal(ownership.get(AccountClientOwnership_.account).get(Account_.productSupplier), supplier);

			allPredicates.add(manufacturerPredicate);
		}

		if (policyNumber != null && !policyNumber.isEmpty()) {

			Join<Client, AccountClientOwnership> ownershipJoin = p.join(Client_.accountClientOwnershipList);
			Join<AccountClientOwnership, Account> accountJoin = ownershipJoin.join(AccountClientOwnership_.account);

			policyNumber = "%" + policyNumber.toUpperCase() + "%";
			Predicate policyNumPredicate = cBuilder.like(cBuilder.upper(accountJoin.get(Account_.accountNumber)),
					policyNumber);

			allPredicates.add(policyNumPredicate);
		}

		if (clientName != null && !clientName.isEmpty()) {
			if (clientName.contains(" ")) {

				String[] names = clientName.split(" ");
				for (String s : names) {

					s = "%" + s.toUpperCase() + "%";
					Predicate firstNamePredicate = cBuilder
							.like(cBuilder.upper(p.get(Client_.contact).get(Contact_.firstname)), s);
					Predicate lastNamePredicate = cBuilder
							.like(cBuilder.upper(p.get(Client_.contact).get(Contact_.lastname)), s);
					Predicate clientNamePredicate = cBuilder.or(firstNamePredicate, lastNamePredicate);
					allPredicates.add(clientNamePredicate);
				}

			} else {

				clientName = "%" + clientName.toUpperCase() + "%";
				Predicate firstNamePredicate = cBuilder
						.like(cBuilder.upper(p.get(Client_.contact).get(Contact_.firstname)), clientName);
				Predicate lastNamePredicate = cBuilder
						.like(cBuilder.upper(p.get(Client_.contact).get(Contact_.lastname)), clientName);

				Predicate clientNamePredicate = cBuilder.or(firstNamePredicate, lastNamePredicate);
				allPredicates.add(clientNamePredicate);
			}
		}

		if (advisorName != null && !advisorName.isEmpty()) {
			advisorName = "%" + advisorName.toUpperCase() + "%";
			Join<Client, Advisor> advisorJoin = p.join(Client_.advisorList);
			Predicate firstNamePredicate = cBuilder
					.like(cBuilder.upper(advisorJoin.get(Advisor_.contact).get(Contact_.firstname)), advisorName);
			Predicate lastNamePredicate = cBuilder
					.like(cBuilder.upper(advisorJoin.get(Advisor_.contact).get(Contact_.lastname)), advisorName);

			Predicate advisorNamePredicate = cBuilder.or(firstNamePredicate, lastNamePredicate);
			allPredicates.add(advisorNamePredicate);
		}

		if (birthdate != null) {
			Predicate birthdatePredicate = cBuilder.equal(p.get(Client_.contact).get(Contact_.birthDate), birthdate);
			allPredicates.add(birthdatePredicate);
		}

		if (clientNumber != null && !clientNumber.isEmpty()) {
			Predicate clientNumPredicate = cBuilder.equal(p.get(Client_.clientNumber), clientNumber);
			allPredicates.add(clientNumPredicate);
		}

		Predicate queryPredicate = null;

		queryPredicate = cBuilder.and(allPredicates.toArray(new Predicate[] {}));

		if (queryPredicate != null) {
			criteriaQuery.where(queryPredicate).distinct(true);
		}

		TypedQuery<Client> clientsQuery = em.createQuery(criteriaQuery);

		return clientsQuery.getResultList();*/
	}

	/*public List<Client> findByCriteria(String clientName, Date birthdate, String clientNumber, ProductSupplier supplier,
			String policyNumber, Advisor advisor) {

		CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<Client> criteriaQuery = cBuilder.createQuery(Client.class);
		Root<Client> p = criteriaQuery.from(Client.class);

		List<Predicate> allPredicates = new ArrayList<Predicate>();

		if (supplier != null) {
			Join<Client, AccountClientOwnership> ownership = p.join(Client_.accountClientOwnershipList);
			Predicate manufacturerPredicate = cBuilder
					.equal(ownership.get(AccountClientOwnership_.account).get(Account_.productSupplier), supplier);

			allPredicates.add(manufacturerPredicate);
		}

		if (policyNumber != null && !policyNumber.isEmpty()) {

			Join<Client, AccountClientOwnership> ownershipJoin = p.join(Client_.accountClientOwnershipList);
			Join<AccountClientOwnership, Account> accountJoin = ownershipJoin.join(AccountClientOwnership_.account);

			policyNumber = "%" + policyNumber.toUpperCase() + "%";
			Predicate policyNumPredicate = cBuilder.like(cBuilder.upper(accountJoin.get(Account_.accountNumber)),
					policyNumber);

			allPredicates.add(policyNumPredicate);
		}

		if (clientName != null && !clientName.isEmpty()) {
			clientName = "%" + clientName.toUpperCase() + "%";
			Predicate firstNamePredicate = cBuilder.like(cBuilder.upper(p.get(Client_.contact).get(Contact_.firstname)),
					clientName);
			Predicate lastNamePredicate = cBuilder.like(cBuilder.upper(p.get(Client_.contact).get(Contact_.lastname)),
					clientName);

			Predicate clientNamePredicate = cBuilder.or(firstNamePredicate, lastNamePredicate);
			allPredicates.add(clientNamePredicate);
		}

		if (advisor != null) {

			Predicate advisorPredicate = cBuilder.isMember(advisor, p.get(Client_.advisorList));
			allPredicates.add(advisorPredicate);
		} else {
			return new ArrayList<Client>();
		}

		if (birthdate != null) {
			Predicate birthdatePredicate = cBuilder.equal(p.get(Client_.contact).get(Contact_.birthDate), birthdate);
			allPredicates.add(birthdatePredicate);
		}

		if (clientNumber != null && !clientNumber.isEmpty()) {
			Predicate clientNumPredicate = cBuilder.equal(p.get(Client_.clientNumber), clientNumber);
			allPredicates.add(clientNumPredicate);
		}

		Predicate queryPredicate = null;

		queryPredicate = cBuilder.and(allPredicates.toArray(new Predicate[] {}));

		if (queryPredicate != null) {
			criteriaQuery.where(queryPredicate).distinct(true);
		}

		TypedQuery<Client> clientsQuery = em.createQuery(criteriaQuery);

		return clientsQuery.getResultList();
	}*/

	public List<Client> findByClientNumber(Integer clientNumber) {
		
		TypedQuery<Client> nq = em.createQuery(
				"SELECT a FROM Client a WHERE   a.clientNumber = :clientNumber",
				Client.class);
		nq.setParameter("clientNumber", clientNumber);
		return nq.getResultList();

		/*CriteriaBuilder cBuilder = em.getCriteriaBuilder();
		CriteriaQuery<Client> criteriaQuery = cBuilder.createQuery(Client.class);
		Root<Client> p = criteriaQuery.from(Client.class);

		Predicate clientNumPredicate = cBuilder.equal(p.get(Client_.clientNumber), clientNumber);
		criteriaQuery.where(clientNumPredicate);
		TypedQuery<Client> clientsQuery = em.createQuery(criteriaQuery);
		return clientsQuery.getResultList();*/
	}

	public List<Client> findByAccountNumber(String accountNumber) {

		List<Client> clients = null;
		try {
			TypedQuery<Client> nq = getEntityManager().createNamedQuery("Client.findByAccountNumber", Client.class);
			nq.setParameter("accountNumber", "%" + accountNumber + "%");

			clients = nq.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}

		return clients;
	}

	@SuppressWarnings("unchecked")
	public List<Client> findByName(String name) {
//        System.out.println("EJB>>  ClientFacade.findByName with:" + name );
		List<Client> clients = null;
		try {
			Query nq = em.createNativeQuery(
					"SELECT cli.* from contact c, client cli WHERE c.contact_int_id = cli.client_int_id and (UPPER(c.FIRSTNAME) like UPPER('%"
							+ name + "%') or UPPER(c.MIDDLENAME) like UPPER('%" + name
							+ "%') or UPPER(c.LASTNAME) like UPPER('%" + name + "%'))",
					Client.class);
			clients = nq.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}

		return clients;
	}
	@SuppressWarnings("unchecked")
	public List<Client> findByNameFinancial(String name, String financialCenters) {
		List<Client> clients = null;
		try {
			Query nq = em.createNativeQuery(
					"SELECT cli.* from contact c inner join client cli on c.contact_int_id = cli.client_int_id INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.BRANCH in "
							+ financialCenters + " and (UPPER(c.FIRSTNAME) like UPPER('%" + name
							+ "%') or UPPER(c.MIDDLENAME) like UPPER('%" + name
							+ "%') or UPPER(c.LASTNAME) like UPPER('%" + name + "%'))",
					Client.class);
			clients = nq.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}

		return clients;
	}
	@SuppressWarnings("unchecked")
	public List<Client> findByName(String name, String lastName) {
//        System.out.println("EJB>>  ClientFacade.findByName with:" + name );
		List<Client> clients = null;
		try {
			Query nq = em.createNativeQuery(
					"SELECT cli.* from contact c, client cli WHERE c.contact_int_id = cli.client_int_id and ((UPPER(c.FIRSTNAME) like UPPER('%"
							+ name + "%') and UPPER(c.MIDDLENAME) like UPPER('%" + lastName
							+ "%')) or (UPPER(c.FIRSTNAME) like UPPER('%" + name
							+ "%') and UPPER(c.LASTNAME) like UPPER('%" + lastName + "%')) )",
					Client.class);
			clients = nq.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}

		return clients;
	}
	@SuppressWarnings("unchecked")
	public List<Client> findByNameFinancial(String name, String lastName, String financialCenters) {
//        System.out.println("EJB>>  ClientFacade.findByName with:" + name );
		List<Client> clients = null;
		try {
			Query nq = em.createNativeQuery(
					"SELECT cli.* from contact c inner join client cli on c.contact_int_id = cli.client_int_id INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE adCon.BRANCH in "
							+ financialCenters + " and ((UPPER(c.FIRSTNAME) like UPPER('%" + name
							+ "%') and UPPER(c.MIDDLENAME) like UPPER('%" + lastName
							+ "%')) or (UPPER(c.FIRSTNAME) like UPPER('%" + name
							+ "%') and UPPER(c.LASTNAME) like UPPER('%" + lastName + "%')) )",
					Client.class);
			clients = nq.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}

		return clients;
	}
	@SuppressWarnings("unchecked")
	public List<Client> findByName(String name, String secondName, String lastName) {
//        System.out.println("EJB>>  ClientFacade.findByName with:" + name );
		List<Client> clients = null;
		try {
			Query nq = em.createNativeQuery(
					"SELECT cli.* from contact c, client cli WHERE c.contact_int_id = cli.client_int_id and (UPPER(c.FIRSTNAME) like UPPER('%"
							+ name + "%') and UPPER(c.MIDDLENAME) like UPPER('%" + secondName
							+ "%') and UPPER(c.LASTNAME) like UPPER('%" + lastName + "%'))",
					Client.class);
			clients = nq.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}

		return clients;
	}
	@SuppressWarnings("unchecked")
	public List<Client> findByNameFinancial(String name, String secondName, String lastName, String financialCenters) {
//        System.out.println("EJB>>  ClientFacade.findByName with:" + name );
		List<Client> clients = null;
		try {
			Query nq = em.createNativeQuery(
					"SELECT cli.* from contact c inner join client cli on c.contact_int_id = cli.client_int_id INNER JOIN ADVISOR_CLIENT adcli on adcli.CLIENT = cli.CLIENT_INT_ID INNER JOIN contact adCon on adCon.CONTACT_INT_ID = adcli.ADVISOR  WHERE  adCon.BRANCH in "
							+ financialCenters + " and (UPPER(c.FIRSTNAME) like UPPER('%" + name
							+ "%') and UPPER(c.MIDDLENAME) like UPPER('%" + secondName
							+ "%') and UPPER(c.LASTNAME) like UPPER('%" + lastName + "%'))",
					Client.class);
			clients = nq.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}

		return clients;
	}

	public List<Client> findByAgeAtDate(int age, int atYear) {

//        System.out.println("EJB>>  ClientFacade.findByAgeAtDate with :" + age + " : "+ atYear);
		List<Client> clients = null;
		List<Client> selectedClient = new ArrayList<Client>();
		try {
			TypedQuery<Client> nq = getEntityManager().createNamedQuery("Client.findAll", Client.class);

			clients = nq.getResultList();

			//Calendar aDay = new GregorianCalendar();
			for (Client client : clients) {

				Date bd = client.getContact().getBirthDate();

				if (bd == null) {
					System.out.println("No birthdate for client: " + client.toString());
					continue;

				}
				// extract the year of birth date in numerical
				SimpleDateFormat simpleDateformat = new SimpleDateFormat("yyyy");
				String yearSTR = simpleDateformat.format(bd);
				int year = Integer.parseInt(yearSTR);

				// birthdate year is equal or smaller that atYear!
				if (atYear <= year) {
					continue;
				}

				int calulatedAge = atYear - year;

				// equal or greater
//                if(calulatedAge >= age)
				if (calulatedAge == age) {
					selectedClient.add(client);
				}

			}

		} catch (NoResultException e) {
			System.out.println("No  results found...");
			return null;
		}

		return selectedClient;
	}

	public int getNextClientId() {
		Query q = em.createNativeQuery("SELECT CLIENT_ID_SEQ.nextval from DUAL");
		BigDecimal result = (BigDecimal) q.getSingleResult();
		return result.intValue();
	}

//    public List<Client> findUsersByContact(String username) {
//        
//        List<Client> clientList = new ArrayList<Client>();
//
//        List<Contact> contacts = contactFacade.findContactByNames(username);
//        
//        if(contacts != null && !contacts.isEmpty()){
//            for(Contact contact: contacts){
//                Client client = contact.getClient();
//                if(client != null){
//                    clientList.add(client);
//                }
//            }
//        }
//        return clientList;
//    }
	// http://10.10.88.99:8080/WindwardReportsServlet/reporting/SummaryReport_6200_SANTA-MATANI.pdf?agentId=14059&clientId=22973
//    public void downloadFile(String url, String filename){
//            DownloadFile d = new DownloadFile();
//            d.download(url, filename);
//    }
	public Client refreshClient(Client selectedClient) {
		Integer clientID = selectedClient.getClientIntId();
		if (clientID != null) {
			selectedClient = em.find(selectedClient.getClass(), clientID);
		}
		return selectedClient;
	}
}
