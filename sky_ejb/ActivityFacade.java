/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.Account;
import com.insurfact.skynet.entity.Activity;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Client;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Lead;
import com.insurfact.skynet.entity.License;
import com.insurfact.skynet.entity.LicenseLiability;
import com.insurfact.skynet.entity.Opportunity;
import com.insurfact.skynet.entity.StoredFile;
import com.insurfact.skynet.entity.Users;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class ActivityFacade extends AbstractFacade<Activity> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public ActivityFacade() {
        super(Activity.class);
    }

    public void createSingleCalendarActivity(Activity activity) {

        Date now = Calendar.getInstance().getTime();
        activity.setCreationDate(now);

        em.persist(activity);
//        em.refresh(activity);
    }

    /**
     * get next valid ActivityGroup Id
     *
     * @return
     */
    public int getNextActivityGroupId() {
        Query q = em.createNativeQuery("SELECT ACTIVITY_GROUP_SEQ.nextval from DUAL");
        BigDecimal result = (BigDecimal) q.getSingleResult();
        return result.intValue();
    }

    public void createCalendarActivity(Activity activity) {

        Date now = Calendar.getInstance().getTime();
        activity.setCreationDate(now);

        em.persist(activity);
//        em.refresh(activity);
    }

    public void editCalendarActivity(Activity activity) {

        Date now = Calendar.getInstance().getTime();
        //activity.setCreationDate(now);
        activity.setLastModificationDate(now);

        em.merge(activity);
    }

    public void removeCalendarActivity(Activity activity) {

        /*Activity child = getChildActivity(activity.getActivityIntId());

        if (child != null) {
            em.remove(getEntityManager().merge(child));
        }*/

        em.remove(getEntityManager().merge(activity));
    }

    public void removeByContact(Users user) {
        Query nq = getEntityManager().createNativeQuery("delete from activity where contact = " + user.getUserIntId());
        nq.executeUpdate();
    }
    
    public void removeByOwner(Users user) {
        Query nq = getEntityManager().createNativeQuery("delete from activity where owner = " + user.getUserIntId());
        nq.executeUpdate();
    }
    public void removeByOwnerNull(Users user,Contact contact) {
        Query nq = getEntityManager().createNativeQuery("delete from ALERT where ACTIVITY in (select ACTIVITY_INT_ID from activity where owner is null and recipient = '"+contact.getFullName()+"' and  contact = " + user.getUserIntId()+")");
        nq.executeUpdate();
         nq = getEntityManager().createNativeQuery("delete from activity where owner is null and recipient = '"+contact.getFullName()+"' and  contact = " + user.getUserIntId());
        nq.executeUpdate();
    }

    public List<Activity> findByOwner(Users users) {
        try {
            TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByOwner", Activity.class);
            nq.setParameter("owner", users);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByOwnerMessage(Users users) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByOwnerMessage", Activity.class);
            nq.setParameter("owner", users);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByOpportunity(Opportunity opp) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByOpportunity", Activity.class);
            nq.setParameter("opportunity", opp);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public void fillManyToMany(Activity act, StoredFile sf) {
        Query q;
        //System.out.println("sql: " + "insert into product_supplier_tree values (" + ic.getProductSupplierIntId() + " , " + mga.getProductSupplierIntId() + ")");
        /*q = em.createNativeQuery("delete from product_supplier_tree where PRODUCT_SUPPLIER_IC = " + ic.getProductSupplierIntId() + " and PRODUCT_SUPPLIER_MGA = " + mga.getProductSupplierIntId());
        q.executeUpdate();*/
        q = em.createNativeQuery("insert into activity_attachment values (" + act.getActivityIntId() + " , " + sf.getStoredFileIntId() + ")");
        q.executeUpdate();

    }

    public List<Activity> findByClient(Client client) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByClient", Activity.class);
            nq.setParameter("client", client);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByAccount(Account account) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByAccount", Activity.class);
            nq.setParameter("account", account);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByOtpUser(String otpUser) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByOtpUser", Activity.class);
            nq.setParameter("otpUser", otpUser);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByReferenceDetails(Integer refIntId, Integer refType) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByReferenceDetails", Activity.class);
            nq.setParameter("referenceIntId", refIntId);
            nq.setParameter("referenceType", refType);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public Activity findByReferenceActTypes(Integer refIntId, Integer actType) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByReferenceType", Activity.class);
            nq.setParameter("referenceIntId", refIntId);
            nq.setParameter("activityType", actType);

            return (Activity) nq.getSingleResult();

        } catch (NoResultException e) {

            return null;
        }
    }

    public List<Activity> findByReference(Integer refIntId) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByReference", Activity.class);
            nq.setParameter("referenceIntId", refIntId);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public Activity getChildActivity(Integer id) {

        try {
        	TypedQuery<Activity> nq = getEntityManager().createQuery("select a from Activity a where a.parentActivityId = :parentId", Activity.class);
            nq.setParameter("parentId", id);
            nq.setParameter("parentId", id);

            return (Activity) nq.getSingleResult();

        } catch (NoResultException e) {

            return null;
        }
    }

    public List<Activity> findAlertByOwnerAndDateRange(Date startDate, Date endDate, Users users) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findAlertByOwnerAndDateRange", Activity.class);
            nq.setParameter("owner", users);
            nq.setParameter("startDate", startDate);
            nq.setParameter("endDate", endDate);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }

    }

    public List<Activity> findByActivityGroup(Integer groupId) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createQuery("SELECT a from Activity a WHERE a.activityGroup = :groupId ORDER BY a.recipient", Activity.class);
            nq.setParameter("groupId", groupId);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByLicense(Integer licenseId) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createQuery("SELECT a from Activity a WHERE a.referenceIntId = :licenseId AND a.activityType IN (10,14) ORDER BY a.activityType, a.activityGroup", Activity.class);
            nq.setParameter("licenseId", licenseId);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByLicenseLiability(Integer licenseLiabilityId) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createQuery("SELECT a from Activity a WHERE a.referenceIntId = :liabilityId AND a.activityType IN (11,13) ORDER BY a.activityType, a.activityGroup", Activity.class);
            nq.setParameter("liabilityId", licenseLiabilityId);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByOwnerAndDateRange(Date startDate, Date endDate, Users users) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByOwnerAndDateRange", Activity.class);
            nq.setParameter("owner", users);
            nq.setParameter("startDate", startDate);
            nq.setParameter("endDate", endDate);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByCoOwnerAndDateRange(Date startDate, Date endDate, Contact contact) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByContactAndDateRange", Activity.class);
            nq.setParameter("contact", contact);
            nq.setParameter("startDate", startDate);
            nq.setParameter("endDate", endDate);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByCoOwner(Contact contact) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByContactMessage", Activity.class);
            nq.setParameter("contact", contact);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByOwnerAndDate(Date date, Users users) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByOwnerAndToday", Activity.class);
            nq.setParameter("owner", users);
            nq.setParameter("effectiveDate", date);

            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByAssignedBy(Users users) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createNamedQuery("Activity.findByAssignedBy", Activity.class);
            nq.setParameter("assignedBy", users);
            return nq.getResultList();

        } catch (NoResultException e) {

            return new ArrayList<>();
        }
    }

    public List<Activity> findByUsersContact(Users users, Contact contact) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createQuery("SELECT a from Activity a WHERE a.owner = :users AND a.contact = :contact ORDER by a.creationDate DESC", Activity.class);

            nq.setParameter("users", users);
            nq.setParameter("contact", contact);

            return nq.getResultList();

        } catch (NoResultException e) {
        }

        return new ArrayList<>();
    }

    public List<Activity> findByOpportunityContactAdvisor(Advisor advisor, Contact contact) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createQuery("SELECT a from Activity a WHERE a.opportunity.advisor = :advisor AND (a.opportunity.contactsRelationship.firstContactIntId = :contactId or a.opportunity.contactsRelationship.secondContactIntId = :contactId ) ORDER by a.creationDate DESC", Activity.class);

            nq.setParameter("advisor", advisor);
            nq.setParameter("contactId", contact.getContactIntId());

            return nq.getResultList();

        } catch (NoResultException e) {
        }

        return new ArrayList<>();
    }

    public Activity getLastActivityByOpportunityContactAdvisor(Advisor advisor, Contact contact) {
        try {
        	TypedQuery<Activity> nq = getEntityManager().createQuery("SELECT a from Activity a WHERE a.opportunity.advisor = :advisor AND (a.opportunity.contactsRelationship.firstContactIntId = :contactId or a.opportunity.contactsRelationship.secondContactIntId = :contactId ) ORDER by a.creationDate DESC", Activity.class);

            nq.setParameter("advisor", advisor);
            nq.setParameter("contactId", contact.getContactIntId());
            nq.setMaxResults(1);

            List<Activity> list = nq.getResultList();

            if (list != null && !list.isEmpty()) {
                return list.get(0);
            }

        } catch (NoResultException e) {
        }
        return null;
    }

    @SuppressWarnings("unchecked")
	public List<Activity> getActivitiesByObject(Object o) {
        List<Activity> result = null;
        System.out.println("object: " + o);
        String sql = "";
        if (o instanceof Opportunity) {
            System.out.println("1");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 1 and TYPE_ID = " + ((Opportunity) o).getOpportunityIntId() + " ORDER by CREATION_DATE ASC";
        } else if (o instanceof Lead) {
            System.out.println("2");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 2 and TYPE_ID = " + ((Lead) o).getLeadIntId() + " ORDER by CREATION_DATE ASC";
        } else if (o instanceof Contact) {
            System.out.println("3");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 3 and TYPE_ID = " + ((Contact) o).getContactIntId() + " ORDER by CREATION_DATE ASC";
        } else if (o instanceof License) {
            System.out.println("4");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 4 and TYPE_ID = " + ((License) o).getLicenseIntId() + " ORDER by CREATION_DATE DESC";
        } else if (o instanceof LicenseLiability) {
            System.out.println("4");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 4 and TYPE_ID = " + ((License) o).getLicenseIntId() + " ORDER by CREATION_DATE DESC";
        }

        System.out.println("sql: " + sql);

        try {
            Query nq = getEntityManager().createNativeQuery(sql, Activity.class);
            result = nq.getResultList();

        } catch (NoResultException e) {
        }

        return result;
    }

    @SuppressWarnings("unchecked")
	public List<Activity> getActivitiesByObjectDesc(Object o) {
        List<Activity> result = null;
        System.out.println("object: " + o);
        String sql = "";
        /*if (o instanceof Opportunity) {
            System.out.println("1");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 1 and TYPE_ID = " + ((Opportunity) o).getOpportunityIntId() + " ORDER by CREATION_DATE DESC";
        } else if (o instanceof Lead) {
            System.out.println("2");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 2 and TYPE_ID = " + ((Lead) o).getLeadIntId() + " ORDER by CREATION_DATE ASC";
        } else if (o instanceof Contact) {
            System.out.println("3");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 3 and TYPE_ID = " + ((Contact) o).getContactIntId() + " ORDER by CREATION_DATE DESC";
        } else*/ if (o instanceof License) {
            System.out.println("10");
            sql = "SELECT * FROM ACTIVITY WHERE ACTIVITY_TYPE = 10 and REFERENCE_INT_ID = " + ((License) o).getLicenseIntId() + " ORDER by CREATION_DATE DESC";
        } else if (o instanceof LicenseLiability) {
            System.out.println("11");
            sql = "SELECT * FROM ACTIVITY WHERE ACTIVITY_TYPE = 11 and REFERENCE_INT_ID = " + ((LicenseLiability) o).getLicenseLiabilityIntId() + " ORDER by CREATION_DATE DESC";
        }

        System.out.println("sql: " + sql);

        try {
            Query nq = getEntityManager().createNativeQuery(sql, Activity.class);
            result = nq.getResultList();

        } catch (NoResultException e) {
        }

        return result;
    }

    @SuppressWarnings("unchecked")
	public List<Activity> getActivitiesByObjectAct(Object o) {
        List<Activity> result = null;
        System.out.println("object: " + o);
        String sql = "";
        /*if (o instanceof Opportunity) {
            System.out.println("1");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 1 and TYPE_ID = " + ((Opportunity) o).getOpportunityIntId() + " ORDER by CREATION_DATE DESC";
        } else if (o instanceof Lead) {
            System.out.println("2");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 2 and TYPE_ID = " + ((Lead) o).getLeadIntId() + " ORDER by CREATION_DATE ASC";
        } else if (o instanceof Contact) {
            System.out.println("3");
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 3 and TYPE_ID = " + ((Contact) o).getContactIntId() + " ORDER by CREATION_DATE DESC";
        } else*/ if (o instanceof License) {
            System.out.println("14");
            sql = "SELECT * FROM ACTIVITY WHERE ACTIVITY_TYPE = 14 and REFERENCE_INT_ID = " + ((License) o).getLicenseIntId() + " ORDER by CREATION_DATE DESC";
        } else if (o instanceof LicenseLiability) {
            System.out.println("13");
            sql = "SELECT * FROM ACTIVITY WHERE ACTIVITY_TYPE = 13 and REFERENCE_INT_ID = " + ((LicenseLiability) o).getLicenseLiabilityIntId() + " ORDER by CREATION_DATE DESC";
        }

        System.out.println("sql: " + sql);

        try {
            Query nq = getEntityManager().createNativeQuery(sql, Activity.class);
            result = nq.getResultList();

        } catch (NoResultException e) {
        }

        return result;
    }

    @SuppressWarnings("unchecked")
	public List<Activity> getActivitiesSonByObject(Object o, Activity a) {
        List<Activity> result = null;
        String sql = "";
        if (o instanceof Opportunity) {
            sql = "SELECT * FROM ACTIVITY WHERE PARENT_ACTIVITY_ID =" + a.getActivityIntId() + " AND TYPE = 1 and TYPE_ID = " + ((Opportunity) o).getOpportunityIntId() + " ORDER by CREATION_DATE ASC";
        } else if (o instanceof Lead) {
            sql = "SELECT * FROM ACTIVITY WHERE PARENT_ACTIVITY_ID =" + a.getActivityIntId() + " AND TYPE = 2 and TYPE_ID = " + ((Lead) o).getLeadIntId() + " ORDER by CREATION_DATE ASC";
        } else if (o instanceof Contact) {
            sql = "SELECT * FROM ACTIVITY WHERE PARENT_ACTIVITY_ID =" + a.getActivityIntId() + " AND TYPE = 3 and TYPE_ID = " + ((Contact) o).getContactIntId() + " ORDER by CREATION_DATE ASC";
        } else if (o instanceof Activity) {
            sql = "SELECT * FROM ACTIVITY WHERE PARENT_ACTIVITY_ID =" + a.getActivityIntId();
        }

        try {
            Query nq = getEntityManager().createNativeQuery(sql, Activity.class);
            result = nq.getResultList();

        } catch (NoResultException e) {
        }

        return result;
    }

    public Activity getLastActivityByObject(Object o) {

        String sql = "";
        if (o instanceof Opportunity) {
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 1 and TYPE_ID = " + ((Opportunity) o).getOpportunityIntId() + " ORDER by CREATION_DATE DESC ";
        } else if (o instanceof Lead) {
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 2 and TYPE_ID = " + ((Lead) o).getLeadIntId() + " ORDER by CREATION_DATE DESC ";
            System.out.println(((Lead) o).getLeadIntId());
        } else if (o instanceof Contact) {
            sql = "SELECT * FROM ACTIVITY WHERE TYPE = 3 and TYPE_ID = " + ((Contact) o).getContactIntId() + " ORDER by CREATION_DATE DESC ";
            System.out.println(((Contact) o).getContactIntId());
        }
        try {
            Query nq = getEntityManager().createNativeQuery(sql, Activity.class);
            System.out.println("results: " + nq.getMaxResults());
            if (nq.getMaxResults() > 0) {
                return (Activity) nq.getResultList().get(0);
            } else {
                return null;
            }

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
        }

        return null;
    }
    
    public Activity getLastActivityByContact(Contact o) {

        String sql  = "SELECT * FROM ACTIVITY WHERE contact = " + o.getContactIntId()+ " ORDER by CREATION_DATE DESC ";
        
        try {
            Query nq = getEntityManager().createNativeQuery(sql, Activity.class); 
            if (nq.getMaxResults() > 0) {
                return (Activity) nq.getResultList().get(0);
            } else {
                return null;
            }

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
        }

        return null;
    }
    public Activity getLastActivityByLead(Lead o) {

        String sql  = "SELECT * FROM ACTIVITY WHERE lead = " + o.getLeadIntId()+ " ORDER by CREATION_DATE DESC ";
        
        try {
            Query nq = getEntityManager().createNativeQuery(sql, Activity.class); 
            if (nq.getMaxResults() > 0) {
                return (Activity) nq.getResultList().get(0);
            } else {
                return null;
            }

        } catch (NoResultException | ArrayIndexOutOfBoundsException e) {
        }

        return null;
    }

    public void removeActivityAndChild(Activity activity) {

        List<Activity> childsToDelete = getActivitiesSonByObject(activity, activity);

        if (childsToDelete != null) {
            for (Activity a : childsToDelete) {
                System.out.println("borrando hijo: " + a);
                removeActivityAndChild(a);
                //em.remove(getEntityManager().merge(a));
            }
        }

        System.out.println(" ++++++++++ borrando leaf: " + activity);
        em.remove(getEntityManager().merge(activity));
    }
}
