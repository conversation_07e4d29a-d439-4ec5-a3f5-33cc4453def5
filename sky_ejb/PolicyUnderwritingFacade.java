/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2016 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.skynet.ejb;

import com.insurfact.skynet.entity.PolicyUnderwriting;
import com.insurfact.skynet.entity.PolicyUwComments;
import jakarta.ejb.Stateless;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 *
 * <AUTHOR>
 */
@Stateless
public class PolicyUnderwritingFacade extends AbstractFacade<PolicyUnderwriting> {

    @PersistenceContext(unitName = "AVue_2.2-PU")
    private EntityManager em;

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }

    public PolicyUnderwritingFacade() {
        super(PolicyUnderwriting.class);
    }

    public PolicyUnderwriting refreshPolicyUnderwriting(PolicyUnderwriting underwriting) {

        Integer underwritingID = underwriting.getPolicyUnderwritingIntId();
        if (underwritingID != null) {
            underwriting = em.find(underwriting.getClass(), underwritingID);
        }
        return underwriting;
    }
    
    public void saveComment(PolicyUwComments comment) {
        
        if(comment != null && comment.isDirty()){
            
            Integer commentId = comment.getPolicyUwCommentsIntId();
            
            if(commentId != null){
                if(comment.isDeleted()){
                    comment = em.find(comment.getClass(), commentId);
                    em.remove(comment);
                } else {
                    em.merge(comment);
                }
            } else {
                if(!comment.isDeleted()){
                    em.persist(comment);
                }
            }        
        }
    }
}
